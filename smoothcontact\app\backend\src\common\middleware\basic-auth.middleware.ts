import { Injectable, NestMiddleware, UnauthorizedException } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';

@Injectable()
export class BasicAuthMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const auth = req.headers.authorization;

    if (!<PERSON><PERSON>an(process.env.BASIC_AUTH_ENABLE)) {
      return next();
    }

    // ignore facebook bot
    if (req.headers['user-agent'] === 'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)') {
      return next();
    }

    if (!auth || auth.search('Basic ') !== 0) {
      res.setHeader('WWW-Authenticate', 'Basic realm="Secure Area"');
      throw new UnauthorizedException();
    }

    const [username, password] = Buffer.from(auth.split(' ')[1], 'base64').toString().split(':');

    if (username !== process.env.BASIC_AUTH_USERNAME || password !== process.env.BASIC_AUTH_PASSWORD) {
      res.setHeader('WWW-Authenticate', 'Basic realm="Secure Area"');
      throw new UnauthorizedException();
    }

    next();
  }
}
