import { defineConfig } from 'vite';

export default defineConfig({
  build: {
    cssCodeSplit: false,
    emptyOutDir: false,
    minify: true,
    // remove comments
    terserOptions: {
      format: {
        comments: false,
      },
    },
    target: 'esnext',
    rollupOptions: {
      input: './src/embed/script.js',
      output: {
        entryFileNames: 'embed.js',
      },
    },
  },
});
