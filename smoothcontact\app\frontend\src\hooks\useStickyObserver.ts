import { useEffect, useState } from 'react';
import debounce from 'lodash/debounce';

const useStickyObserver = (ref: React.RefObject<HTMLElement>) => {
  const [isSticky, setIsSticky] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([e]) => {
        setIsSticky(!e.isIntersecting);
      },
      { threshold: [1] }
    );

    const handleScroll = debounce(() => {
      setScrollPosition(window.scrollY);
      if (window.scrollY === 0) {
        setIsSticky(false);
      }
    }, 300);

    window.addEventListener('scroll', handleScroll);

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      observer.disconnect();
      window.removeEventListener('scroll', handleScroll);
      handleScroll?.cancel(); // Cancel the debounce on cleanup
    };
  }, [ref]);

  return {
    isSticky,
    scrollPosition,
  };
};

export default useStickyObserver;
