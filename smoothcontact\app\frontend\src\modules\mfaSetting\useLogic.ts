import useAxios from '@/hooks/useAxios';
import { use<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/hooks/useFormHandler';
import { useToast } from '@/provider/toastProvider';
import { createMfaCodeRequestConfig, reissueBackupCodeRequestConfig } from '@/services/mfa.service';
import { useAppDispatch, useAppSelector } from '@/store/hook';
import { appAction } from '@/store/slices/app';
import { HttpStatusCode } from 'axios';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { MfaVerifyRequestDTO } from './dto/request.dto';
import { validationSchema } from './validator';

export type FormValue = {
  code: string;
};

export default function useLogic() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { apiCaller, loading } = useAxios();
  const dispatch = useAppDispatch();
  const [backupCodes, setBackupCodes] = useState([]);
  const { profile } = useAppSelector((state) => ({
    profile: state.app.profile,
  }));

  const createMfaCode = async (formData: FormValue) => {
    const dataBody = new MfaVerifyRequestDTO();
    dataBody.code = formData.code;
    dataBody.secret = profile.base32;

    const result: any = await apiCaller(createMfaCodeRequestConfig(dataBody));

    if (result?.success) {
      toast({ isError: false, message: 'MFAがチェックされました' });
      setBackupCodes(result?.data?.backupCodes);
      dispatch(appAction.setAppState({ profile: { ...profile, isVerifiedMfa: true } }));

      return;
    }

    if (result?.statusCode !== HttpStatusCode.BadRequest) {
      toast({ isError: true, message: result.message });
    }

    formHandler.setErrors(result.messageErrors ?? {});
  };

  const reissueBackupCode = async () => {
    const result: any = await apiCaller(reissueBackupCodeRequestConfig());

    if (result?.success) {
      toast({ isError: false, message: 'バックアップコードの再発行が成功しました。' });
      setBackupCodes(result?.data);

      return;
    }

    if (result?.statusCode !== HttpStatusCode.BadRequest) {
      toast({ isError: true, message: result.message });
    }

    formHandler.setErrors(result.messageErrors ?? {});
  };

  const formHandler = useFormHandler<FormValue>({
    initialValues: { code: '' },
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: async (values) => {
      await createMfaCode(values);
    },
  });

  const cancel = () => {
    navigate('/form-builder');
  };

  return {
    loading,
    formHandler,
    profile,
    backupCodes,
    cancel,
    reissueBackupCode,
  };
}
