import { PER_PAGE } from '@/common/constants';
import { PaginationRes } from '@/common/dto/response';
import { IModalCommon } from '@/components/common/SCModalCommon';
import useAxios from '@/hooks/useAxios';
import { getByExtIdRequestConfig } from '@/services/form-submission.service';
import { OrderRequest } from '@/types/app';
import { toQueryParams } from '@/utils/helper';
import React, { ReactNode, createContext, useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FormSubmissionFilterRequestDto } from '../dto/request.dto';
import { useToast } from '@/provider/toastProvider';

const initialFilter = {
  page: 1,
  perPage: PER_PAGE,
};

export const enum FilterStatusOptions {
  ALL = 'ALL',
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  EXPIRED = 'EXPIRED',
}

export const FilterStatusLabels: { [key in FilterStatusOptions]: string } = {
  [FilterStatusOptions.ALL]: '非公開',
  [FilterStatusOptions.DRAFT]: '公開中',
  [FilterStatusOptions.PUBLISHED]: '公開予約',
  [FilterStatusOptions.EXPIRED]: '公開終了',
};

interface FormBuilderListProviderProps {
  children: ReactNode;
  setOpenModal: (newModalCommon: IModalCommon) => void;
  extId: any;
}

interface FormSubmissionListContextType {
  data: any;
  pagination: PaginationRes;
  filterModal: boolean;
  order: OrderRequest;
  orderBy: any;
  filter: FormSubmissionFilterRequestDto;
  setFilterModal: (value: boolean) => any;
  handleChangePage: (event: unknown, newPage: number) => any;
  handleChangeRowsPerPage: (value: any) => any;
  handleSort: (event: React.MouseEvent<unknown>, property: string) => any;
  handleFilter: (status: FilterStatusOptions) => any;
  setFilter: (value: any) => any;
  getAllSubmissions: (value: any) => any;
  setOpenModal: (newModalCommon: IModalCommon) => void;
}

const FormSubmissionListContext = createContext<FormSubmissionListContextType | undefined>(undefined);

const FormSubmissionListProvider: React.FC<FormBuilderListProviderProps> = ({ children, extId, setOpenModal }) => {
  const navigate = useNavigate();
  const [data, setData] = useState<{ count: number; data: any[] }>(null);
  const [filterModal, setFilterModal] = useState(false);
  const [pagination, setPagination] = useState<PaginationRes>({
    page: 1,
    perPage: 5,
    total: 0,
  });
  const [order, setOrder] = useState<OrderRequest>(OrderRequest.ASC);
  const [orderBy, setOrderBy] = useState<string>('');
  const [filter, setFilter] = useState<FormSubmissionFilterRequestDto>(initialFilter);
  const { apiCaller } = useAxios();
  const { toast } = useToast();

  const getAllSubmissions = async (dto: FormSubmissionFilterRequestDto): Promise<any> => {
    const result: any = await apiCaller(getByExtIdRequestConfig(extId, dto));

    if (!result?.success) {
      toast({ isError: true, message: result?.error });

      return;
    }

    setData(result.data.items as any);
    setPagination(result.data.pagination);
    setFilter(dto);
    navigate(`/form-builder/submission/${extId}?${toQueryParams(dto)}`);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    getAllSubmissions({ ...filter, page: newPage + 1 });
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    getAllSubmissions({ ...filter, page: 1, perPage: parseInt(event.target.value) });
  };

  const handleSort = (event: React.MouseEvent<unknown>, property: string) => {
    const isAsc = orderBy === property && order === OrderRequest.ASC;
    setOrder(isAsc ? OrderRequest.DESC : OrderRequest.ASC);
    setOrderBy(property);

    getAllSubmissions({ ...filter, order: isAsc ? OrderRequest.DESC : OrderRequest.ASC, orderBy: property });
  };

  const handleFilter = () => {
    getAllSubmissions({ ...filter });
  };

  useEffect(() => {
    getAllSubmissions(initialFilter);
  }, []);

  return (
    <FormSubmissionListContext.Provider
      value={{
        data,
        pagination,
        filterModal,
        order,
        orderBy,
        filter,
        setFilterModal,
        handleChangePage,
        handleChangeRowsPerPage,
        handleSort,
        handleFilter,
        setFilter,
        getAllSubmissions,
        setOpenModal,
      }}
    >
      {children}
    </FormSubmissionListContext.Provider>
  );
};

function useFormBuilderList() {
  const context = useContext(FormSubmissionListContext);
  if (!context) {
    throw new Error('useFormBuilderList must be used within a FormBuilderListProvider');
  }

  return context;
}

export { FormSubmissionListProvider, useFormBuilderList };
