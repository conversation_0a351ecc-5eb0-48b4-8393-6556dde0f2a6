{"name": "smooth_contact_app_be", "private": true, "license": "UNLICENSED", "scripts": {"build": "tsc && vite build", "build:embed": "vite build --config vite-embed.config.ts", "build:html-embed": "vite build --config vite-html-embed.config.ts", "dev": "vite", "lint": "eslint ./src --ext .js,.jsx,.ts,.tsx", "lint:fix": "npm run lint -- --fix", "postinstall": "node ./postinstall.js"}, "type": "module", "engines": {"node": ">= 16.x"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.15", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.15", "@mui/styles": "^5.15.16", "@mui/system": "^5.15.15", "@mui/x-charts": "^7.1.1", "@mui/x-date-pickers": "^7.1.1", "@reduxjs/toolkit": "^2.2.3", "@tinymce/tinymce-react": "^5.1.1", "@vitejs/plugin-react": "^4.2.1", "ace-builds": "^1.35.4", "axios": "^1.6.8", "buffer": "^6.0.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^3.6.0", "dayjs": "^1.11.10", "dotenv": "^16.4.5", "draft-js": "^0.11.7", "draft-js-export-html": "^1.4.1", "formik": "^2.4.6", "fs-extra": "^11.2.0", "i18next": "^23.11.5", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "mustache": "^4.2.0", "react": "^18.2.0", "react-ace": "^12.0.0", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-custom-scrollbars": "^4.2.1", "react-datepicker": "^6.6.0", "react-device-detect": "^2.2.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-html-parser": "^2.0.2", "react-i18next": "^14.1.2", "react-joyride": "^2.8.2", "react-query": "^3.39.3", "react-router-dom": "^6.22.3", "react-scripts": "^5.0.1", "react-syntax-highlighter": "^15.5.0", "reactcss": "^1.2.3", "redux-persist": "^6.0.0", "redux-saga": "^1.3.0", "reflect-metadata": "^0.2.2", "rollup-plugin-esbuild": "^6.1.1", "short-unique-id": "^5.0.3", "tinymce": "^7.6.0", "tss-react": "^4.9.6", "use-debounce": "^10.0.0", "vite": "^4.5.5", "yup": "^1.4.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.0", "@types/mustache": "^4.2.5", "@types/node": "^20.12.4", "@types/react": "^18.2.74", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-color": "^3.0.12", "@types/react-custom-scrollbars": "^4.0.13", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18.2.24", "@types/react-draft-wysiwyg": "^1.13.8", "@types/react-syntax-highlighter": "^15.5.13", "@types/reactcss": "^1.2.12", "@types/yup": "^0.32.0", "@typescript-eslint/eslint-plugin": "^7.5.0", "@typescript-eslint/parser": "^7.5.0", "esbuild": "^0.20.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "history": "^5.3.0", "jsdom": "^24.0.0", "prettier": "^3.2.5", "react-error-overlay": "6.0.11", "sass": "^1.74.1", "typescript": "^5.4.3", "vi-fetch": "^0.8.0", "vite-plugin-compression2": "^1.1.1", "vite-plugin-html": "^3.2.2"}}