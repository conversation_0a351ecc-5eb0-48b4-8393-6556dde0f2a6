import { useEffect, useState } from 'react';

declare global {
  interface Window {
    grecaptcha: any;
  }
}

type Props = {
  enabled?: boolean;
};

const useRecaptcha = ({ enabled }: Props) => {
  const siteKey = (import.meta.env.VITE_APP_GOOGLE_CAPTCHA_SITE_KEY as string) || '';
  const [token, setToken] = useState<string | null>(null);
  const [tokenTime, setTokenTime] = useState<number | null>(null);

  useEffect(() => {
    const loadScript = () => {
      const script = document.createElement('script');
      script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`;
      script.async = true;
      script.onload = () => {
        if (window.grecaptcha) {
          window.grecaptcha.ready(() => {
            window.grecaptcha.execute(siteKey, { action: 'submit' }).then((token: string) => {
              setToken(token);
            });
          });
        }
      };
      document.body.appendChild(script);
    };
    if (enabled) loadScript();
  }, [enabled]);

  useEffect(() => {
    if (tokenTime && window.grecaptcha) {
      window.grecaptcha.ready(() => {
        window.grecaptcha.execute(siteKey, { action: 'submit' }).then((token: string) => {
          setToken(token);
        });
      });
    }
  }, [tokenTime]);

  const refreshToken = () => {
    setTokenTime(Date.now());
  };

  return {
    token,
    refreshToken,
  };
};

export default useRecaptcha;
