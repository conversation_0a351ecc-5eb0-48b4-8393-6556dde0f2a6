{"name": "smooth_contact_app_be", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "lint:fix": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "cross-env NODE_ENV=production node dist/main.js", "dev": "nest start --watch", "dev-docker": "nodemon --exec nest start --ext js,ts --watch src/ --legacy-watch", "start:debug": "nest start --debug --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "prepare-test": "yarn build && export $(cat .env | grep \"^[^#;]\" |xargs) && export TEST_MODE=1 && npx typeorm-ts-node-esm migration:run -d src/modules/typeorm/typeorm.config.ts", "test": "yarn prepare-test && export $(cat .env | grep \"^[^#;]\" |xargs) && cross-env TEST_MODE=1 jest --runInBand --forceExit", "test:watch": "yarn prepare-test && cross-env TEST_MODE=1 jest --watchAll", "test:cov": "yarn prepare-test && export $(cat .env | grep \"^[^#;]\" |xargs) && cross-env TEST_MODE=1 jest --coverage --runInBand --forceExit", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "typeorm:migrate": "npm run typeorm migration:generate -- -d src/modules/typeorm/typeorm.config.ts", "typeorm:create": "npm run typeorm migration:create", "typeorm:run": "npm run typeorm migration:run -- -d src/modules/typeorm/typeorm.config.ts", "typeorm:down": "npm run typeorm migration:revert -- -d src/modules/typeorm/typeorm.config.ts", "auth-proxy": "ts-node src/utils/docker-development.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.577.0", "@aws-sdk/client-ses": "^3.554.0", "@aws-sdk/credential-providers": "^3.554.0", "@aws-sdk/s3-request-presigner": "^3.577.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^10.3.7", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.3.7", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.5", "@nestjs/platform-express": "^10.3.7", "@nestjs/schedule": "^4.1.1", "@nestjs/serve-static": "^4.0.2", "@nestjs/throttler": "^5.1.2", "@nestjs/typeorm": "^10.0.2", "@reduxjs/toolkit": "^2.2.3", "@types/multer": "^1.4.11", "@types/xml2js": "^0.4.14", "axios": "^1.7.4", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cross-env": "^7.0.3", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "csv-write-stream": "^2.0.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "mysql2": "^3.9.8", "papaparse": "^5.4.1", "qrcode": "^1.5.4", "rxjs": "^7.8.1", "short-unique-id": "^5.0.3", "typeorm": "^0.3.20", "uuidv4": "^6.2.13", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0", "xml2js": "^0.6.2", "xmldom": "^0.6.0"}, "devDependencies": {"@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.1.1", "@nestjs/testing": "^10.3.7", "@types/crypto-js": "^4.2.2", "@types/csv-write-stream": "^2.0.3", "@types/express": "^4.17.21", "@types/jest": "29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.12.4", "@types/nodemailer": "^6.4.14", "@types/papaparse": "^5.3.14", "@types/qrcode": "^1.5.5", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.5.0", "@typescript-eslint/parser": "^7.5.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^12.1.0", "jest": "29.7.0", "nodemon": "^3.1.0", "prettier": "^3.2.5", "source-map-support": "^0.5.21", "supertest": "^6.3.4", "ts-jest": "29.1.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^5.4.3", "@types/xmldom": "^0.1.34"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.service.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)": "<rootDir>/../src/$1"}}}