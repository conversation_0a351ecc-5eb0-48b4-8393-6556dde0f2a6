import React, { FC, useEffect, useState } from 'react';
import { Box, FormHelperText, Select, SelectChangeEvent, Stack, Typography } from '@mui/material';
import { padZero } from '@/utils/helper';
import dayjs from 'dayjs';
import { ISO } from '@/utils/dateTime';
import { KeyboardArrowDown } from '@mui/icons-material';

interface SCInputBirthdayProps extends React.HTMLAttributes<HTMLInputElement> {
  isRequired?: boolean;
  limitAge?: boolean;
  classes?: any;
  minAge?: number;
  maxAge?: number;
  name?: string;
  value?: any;
  format?: string;
  error?: boolean;
  helperText?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

interface Birthday {
  year: string;
  month: string;
  day?: string;
}

const DEFAULT_START_YEAR = 1900;
const DEFAULT_EMPTY_VALUE = '-';
const MAX_DAYS = 31;

const SCInputBirthday: FC<SCInputBirthdayProps> = (props) => {
  const { classes, minAge, maxAge, limitAge, format = ISO.DATE, helperText, onChange } = props;

  const [values, setValues] = useState<Birthday>({
    year: DEFAULT_EMPTY_VALUE,
    month: DEFAULT_EMPTY_VALUE,
    day: DEFAULT_EMPTY_VALUE,
  });

  const [years, setYears] = useState<string[]>([]);
  const [months, setMonths] = useState<string[]>([]);
  const [days, setDays] = useState<string[]>([]);

  // Update days array based on selected year and month
  const updateDays = (selectedYear: number, selectedMonth: number) => {
    const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate() || MAX_DAYS;
    const daysArray = Array.from({ length: daysInMonth }, (_, i) => (i + 1).toString());
    setDays(daysArray);
  };

  useEffect(() => {
    const date = dayjs(props.value, { format });
    if (date.isValid()) {
      setValues({
        year: date.format('YYYY'),
        month: date.format('MM'),
        day: date.format('DD'),
      });
    }
  }, []);

  useEffect(() => {
    const currentYear = new Date().getFullYear();
    const yearFrom = limitAge && maxAge > 0 ? currentYear - (maxAge || 0) : DEFAULT_START_YEAR;
    const yearTo = limitAge ? currentYear - (minAge || 0) : currentYear;
    const yearsArray = Array.from({ length: yearTo - yearFrom + 1 }, (_, i) => (yearFrom + i).toString());
    setYears(yearsArray);

    const monthsArray = Array.from({ length: 12 }, (_, i) => (i + 1).toString());
    setMonths(monthsArray);

    // Initialize days array with current year and month
    updateDays(currentYear, new Date().getMonth() + 1);
  }, [minAge, maxAge]);

  useEffect(() => {
    updateDays(Number(values.year), Number(values.month));
    const daysInMonth = new Date(Number(values.year), Number(values.month), 0).getDate();
    if (Number(values.day) > daysInMonth) {
      setValues({ ...values, day: daysInMonth.toString() });
    }
  }, [values.month, values.year]);

  useEffect(() => {
    if (Number(values.day) > days.length) {
      setValues({ ...values, day: days[days.length - 1] });
    }
  }, [days.length]);

  const handleUpdateDateData = (value: string, field: keyof Birthday) => {
    let formattedValue = value.toString();
    if (formattedValue !== '-') {
      if (field === 'day' || field === 'month') {
        formattedValue = formattedValue.padStart(2, '0');
      }
    }

    setValues({ ...values, [field]: formattedValue });
  };

  const handleChange = (e: SelectChangeEvent) => {
    const { name, value } = e.target;
    handleUpdateDateData(value, name as keyof Birthday);
  };

  useEffect(() => {
    const emptyFields = Object.keys(values).filter((key) => values[key] === DEFAULT_EMPTY_VALUE);

    const date = dayjs(`${values.year}-${values.month}-${values.day}`);
    const dateValue = emptyFields.length === 0 ? date.format(format) : '';
    const newEvent = {
      target: {
        name: props.name,
        value: dateValue,
      },
    };
    onChange && onChange(newEvent as React.ChangeEvent<HTMLInputElement>);
  }, [values]);

  return (
    <>
      <Stack direction="row" spacing={2}>
        <Box display="flex" alignItems="center" gap={1}>
          <Select
            IconComponent={KeyboardArrowDown}
            name="year"
            className={classes.select}
            size="small"
            variant="outlined"
            value={values.year}
            onChange={handleChange}
            error={props.error}
            sx={{ minWidth: 90 }}
            native={true}
          >
            <option key={0} value="-">
              ----
            </option>
            {years.map((year) => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </Select>
          <Typography className={classes.label} variant="body1">
            年
          </Typography>
        </Box>
        <Box display="flex" alignItems="center" gap={1}>
          <Select
            IconComponent={KeyboardArrowDown}
            name="month"
            className={classes.select}
            size="small"
            variant="outlined"
            value={values.month}
            onChange={handleChange}
            sx={{ minWidth: 70 }}
            error={props.error}
            native={true}
          >
            <option key={0} value="-">
              --
            </option>
            {months.map((month) => (
              <option key={month} value={padZero(month, 2)}>
                {padZero(month, 2)}
              </option>
            ))}
          </Select>
          <Typography className={classes.label} variant="body1">
            月
          </Typography>
        </Box>
        <Box display="flex" alignItems="center" gap={1}>
          <Select
            IconComponent={KeyboardArrowDown}
            name="day"
            className={classes.select}
            size="small"
            variant="outlined"
            value={values.day}
            onChange={handleChange}
            sx={{ minWidth: 70 }}
            error={props.error}
            disabled={days.length === 0}
            native={true}
          >
            <option key={0} value="-">
              --
            </option>
            {days.map((day) => (
              <option key={day} value={padZero(day, 2)}>
                {padZero(day, 2)}
              </option>
            ))}
          </Select>
          <Typography className={classes.label} variant="body1">
            日
          </Typography>
        </Box>
      </Stack>
      {props.error && <FormHelperText error>{helperText}</FormHelperText>}
    </>
  );
};

export default SCInputBirthday;
