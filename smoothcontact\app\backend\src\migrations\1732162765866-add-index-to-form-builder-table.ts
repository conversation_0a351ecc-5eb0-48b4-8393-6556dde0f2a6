import { MigrationInterface, QueryRunner, TableIndex } from 'typeorm';

export class AddIndexToFormBuilderTable1732162765866 implements MigrationInterface {
  TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}form_builder`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'idx_unique_form_builder_ext_id',
        columnNames: ['ext_id'],
        isUnique: true,
      }),
    );
    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'idx_form_builder_created_by',
        columnNames: ['created_by'],
      }),
    );
    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'idx_form_builder_updated_at',
        columnNames: ['updated_at'],
      }),
    );
    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'idx_form_builder_status',
        columnNames: ['status'],
      }),
    );
    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'idx_form_builder_release_start_date',
        columnNames: ['release_start_date'],
      }),
    );
    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'idx_form_builder_release_end_date',
        columnNames: ['release_end_date'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(this.TABLE_NAME, 'idx_unique_form_builder_ext_id');
    await queryRunner.dropIndex(this.TABLE_NAME, 'idx_form_builder_created_by');
    await queryRunner.dropIndex(this.TABLE_NAME, 'idx_form_builder_updated_at');
    await queryRunner.dropIndex(this.TABLE_NAME, 'idx_form_builder_status');
    await queryRunner.dropIndex(this.TABLE_NAME, 'idx_form_builder_release_start_date');
    await queryRunner.dropIndex(this.TABLE_NAME, 'idx_form_builder_release_end_date');
  }
}
