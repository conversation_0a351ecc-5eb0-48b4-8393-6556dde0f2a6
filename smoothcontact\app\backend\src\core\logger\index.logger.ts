import * as fs from 'fs';
import * as morgan from 'morgan';
import * as path from 'path';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';

const logDir = path.resolve('./logs');

if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

const { combine, timestamp, printf, errors, colorize, prettyPrint } = winston.format;

const logFormat = printf(({ timestamp, level, message }) => `${timestamp} ${level}: ${message}`);

/*
 * Log Level
 * error: 0, warn: 1, info: 2, http: 3, verbose: 4, debug: 5, silly: 6
 */
const logger = winston.createLogger({
  format: combine(colorize(), prettyPrint(), errors({ stack: true }), timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), logFormat),
  transports: [
    // info log setting
    new DailyRotateFile({
      level: 'info',
      datePattern: 'YYYY-MM-DD',
      dirname: logDir + '/info', // log file /logs/info/*.log in save
      filename: `%DATE%.log`,
      maxFiles: 10, // 10 Days saved
      json: false,
      zippedArchive: true,
    }),
    new DailyRotateFile({
      level: 'info',
      datePattern: 'YYYY-MM-DD',
      dirname: logDir + '/warn', // log file /logs/warn/*.log in save
      filename: `%DATE%.log`,
      maxFiles: 10, // 10 Days saved
      json: false,
      zippedArchive: true,
    }),
    // error log setting
    new DailyRotateFile({
      level: 'error',
      datePattern: 'YYYY-MM-DD',
      dirname: logDir + '/error', // log file /logs/error/*.log in save
      filename: `%DATE%.error.log`,
      maxFiles: 30, // 30 Days saved
      handleExceptions: true,
      json: false,
      zippedArchive: true,
    }),
  ],
});

logger.add(
  new winston.transports.Console({
    format: winston.format.combine(winston.format.splat(), winston.format.colorize(), winston.format.simple()),
  }),
);

const stream = {
  write: (message: string) => {
    logger.info(message.substring(0, message.lastIndexOf('\n')));
  },
};

const morganMiddleware = morgan(
  (tokens, req, res) => {
    if (Number(tokens['response-time'](req, res)) > 3000) {
      const requestDetail = {
        method: tokens.method(req, res),
        url: tokens.url(req, res),
        body: JSON.stringify((req as unknown as Request)?.body ?? {}),
      };
      logger.warn(`The request is too slow: ${Number(tokens['response-time'](req, res))} --- ${JSON.stringify(requestDetail)}`);
    }

    return [
      tokens.method(req, res),
      tokens.url(req, res),
      tokens.status(req, res),
      tokens.res(req, res, 'content-length'),
      '-',
      tokens['response-time'](req, res),
      'ms',
    ].join(' ');
  },
  {
    stream,
  },
);

export { logger, morganMiddleware, stream };
