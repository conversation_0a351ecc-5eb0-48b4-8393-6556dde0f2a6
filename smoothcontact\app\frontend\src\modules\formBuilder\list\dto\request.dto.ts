import { OrderRequest, PaginationRequest } from '@/types/app';
import { assignDataToInstance } from '@/utils/helper';
import { FilterStatusOptions } from '../store/FormBuilderListProvider';

export class SortReq {
  order?: string;
  orderBy?: string;
}

export class FormBuilderFilterRequestDto extends PaginationRequest {
  status?: FilterStatusOptions[];

  from?: Date | null = null;

  to?: Date | null = null;

  order?: OrderRequest;

  orderBy?: string;

  constructor(data?: Partial<FormBuilderFilterRequestDto>) {
    super();
    assignDataToInstance(data, this);
  }
}
