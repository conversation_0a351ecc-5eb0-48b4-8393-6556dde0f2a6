/* eslint-disable @typescript-eslint/no-unused-vars */
import { Body, Controller, Get, HttpStatus, Param, ParseFilePipe, Post, Request, UploadedFile, UploadedFiles, UseInterceptors } from '@nestjs/common';
import { AnyFilesInterceptor, FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';

import { BaseController } from '@/core/controllers/api.controller';
import { FormSubmissionData, HtmlFormSubmissionData, UploadDto } from '@/modules/embed/dto/dto.request';
import { EmbedUploadTemporaryResponseDTO, GetEmbedFormBuilderResponseDto } from '@/modules/embed/dto/dto.response';
import { FormBuilderEntity } from '@/modules/form-builder/entities/form-builder.entity';
import { FormBuilderService } from '@/modules/form-builder/form-builder.service';
import { FormSubmissionEntity } from '@/modules/submission/entities/form-submission.entity';
import { SubmissionService } from '@/modules/submission/submission.service';
import { SubmissionSwitchContactHandler } from '@/modules/submission/submission-switch-contact.handler';
import { UploadService } from '@/modules/upload/upload.service';
import { formatDate } from '@/utils/helpers';

import { Course } from '../account/entities/account.entity';
import { FormType } from '../form-builder/common/common';

@Controller('api/embed')
export class EmbedController extends BaseController {
  constructor(
    private readonly formBuilderService: FormBuilderService,
    private readonly uploadService: UploadService,
    private readonly submissionService: SubmissionService,
    private readonly submissionSwitchContactHandler: SubmissionSwitchContactHandler,
  ) {
    super();
  }

  @Get('/:extId')
  async getEmbed(@Request() request: Request, @Param('extId') extId: string) {
    const formType = request.query.type as FormType;
    const form = await this.formBuilderService.getAvailableFormByExtId(extId, formType);

    return this.successResponse(
      {
        data: form,
      },
      GetEmbedFormBuilderResponseDto,
    );
  }

  @Post('/:extId/upload')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
      }),
      limits: { fileSize: ******** /* 10MB */ },
    }),
  )
  async upload(
    @Param('extId') extId: string,
    @UploadedFile(new ParseFilePipe({ fileIsRequired: true })) file: Express.Multer.File,
    @Body() body: UploadDto,
  ) {
    const { date } = body;
    const originalName = file.originalname.split('.').slice(0, -1).join('.');
    const newFileName = Buffer.from(`${originalName}-${date ?? formatDate(new Date(), 'YYYY.MM.DD_HH.mm.ss')}`, 'latin1').toString('utf8');
    const upload = await this.uploadService.uploadFile(extId, file, newFileName);

    return this.successResponse(
      {
        data: {
          ...upload,
        },
      },
      EmbedUploadTemporaryResponseDTO,
    );
  }

  @Post('/:extId/submit')
  async submit(@Request() request, @Param('extId') extId: string) {
    try {
      const formData = request.body as FormSubmissionData;
      const formBuilder = await this.formBuilderService.getFormBuilderByExtId(extId);

      if (formBuilder?.mode === FormType.HTML) {
        return this.failResponse({
          message: 'フォームが見つかりませんでした。フォームURLが誤っているか、フォームが非公開または削除された可能性があります。',
        });
      }

      this.submissionService.setFormBuilderData(formBuilder as FormBuilderEntity);
      if (formBuilder.formGeneralSetting?.isSettingReCAPTCHA) {
        await this.submissionService.verifyCaptcha(formData.token);
      }

      await this.submissionService.verifyFormState();
      const submission = await this.submissionService.create({
        formValues: formData.formValues,
        receivedDataSaveFlag: formBuilder?.formGeneralSetting?.receivedDataSaveFlag,
      });
      await this.submissionService.confirmAndMoveFiles(submission);
      this.submissionService.setFormBuilderData(formBuilder as FormBuilderEntity).sendEmails(submission);

      const formOwner = await this.formBuilderService.getFormOwnerByExtId(extId);

      if (formOwner?.course === Course.ENTERPRISE) {
        this.submissionSwitchContactHandler.setFormBuilder(formBuilder).setFormSubmission(submission).handle();
      }

      return this.successResponse(
        {
          data: submission,
        },
        FormSubmissionEntity,
      );
    } catch (e) {
      this.failResponse({
        statusCode: HttpStatus.BAD_REQUEST,
        message: e.message,
        data: e.message,
      });
    }
  }

  @Post('/:extId/raw-submit')
  @UseInterceptors(
    AnyFilesInterceptor({
      storage: diskStorage({
        destination: './uploads',
      }),
      limits: { fileSize: ******** /* 10MB */ },
    }),
  )
  async rawSubmit(@Param('extId') extId: string, @UploadedFiles() files: Array<Express.Multer.File>, @Body() body: HtmlFormSubmissionData) {
    const submission = await this.submissionService.rawSubmission(extId, files, body);

    if (!submission) {
      return this.failResponse({ message: 'Submission failed' });
    }

    return this.successResponse({ data: submission }, FormSubmissionEntity);
  }
}
