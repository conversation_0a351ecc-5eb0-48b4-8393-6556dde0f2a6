PORT=9000
NODE_ENV=development
GOOGLE_CAPTCHA_SECRET_KEY=6LeCDexxx
APP_URL=http://localhost:9000

# DATABASE
DATABASE_TYPE=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=smooth_contact_db
DATABASE_USER=root
DATABASE_PASSWORD=root
DATABASE_MIGRATIONS_RUN=true
ENTITY_PREFIX=sm_

# -- SECURITY ---------
CRYPTO_SALT=210bebbe20994f2551de9f318c02516d

#-- AUTHENTICATION ---------
JWT_AUTH_IN=5h
JWT_REFRESH_IN=7d
JWT_RESET_PASSWORD_IN=30m

# LOGGING
# Log level: error|warn|info|http|verbose|debug|silly
LOG_LEVEL=info
# Log request access or not: 0|1
LOG_REQUEST=1
# Log request body or not: 0|1
LOG_REQUEST_BODY=0

# -- SEND MAIL ---------
SYSTEM_SENDER=<EMAIL>
SMTP_HOST=email-smtp.ap-northeast-1.amazonaws.com
SMTP_PORT=465
SMTP_USER=XXX
SMTP_PASSWORD=XXX

# -- AWS S3 -----------
AWS_ACCESS_KEY=
AWS_SECRET_KEY=
AWS_S3_BUCKET_NAME=
AWS_S3_REGION=

# Shopify app jwt Key
SHOPIFY_JWT_SECRET=abcd1234

# -- BASIC AUTH ---------
BASIC_AUTH_ENABLE=true
BASIC_AUTH_USERNAME=smoothcontact
BASIC_AUTH_PASSWORD=wl@123

# -- Clean up inactive user (unit: month)
INACTIVE_ACCOUNT_DURATION=6

# My page external API
MYPAGE_AUTH_URL=
MYPAGE_SCLINK_URL=
WEBLIFE_COURSE_API_URL=

# BINDUP SAK
SAK_KEY=
SAK_IV=

# web font
J_FONTS_URL=
GOOGLE_FONTS_API_URL=
GOOGLE_FONTS_API_KEY=