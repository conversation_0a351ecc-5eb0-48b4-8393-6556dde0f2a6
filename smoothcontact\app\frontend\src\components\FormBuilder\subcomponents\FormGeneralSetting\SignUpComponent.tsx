import { FC } from 'react';
import { FormikValues } from 'formik';
import { Box, Checkbox, FormControlLabel, Typography } from '@mui/material';
import SettingItem from './SettingItemComponent';
import SwitchStyled from '@/components/common/SCToggleSwitch';
import CheckBoxOutlineBlankRoundedIcon from '@mui/icons-material/CheckBoxOutlineBlankRounded';

interface SignUpProps {
  form: FormikValues;
}

const SignUp: FC<SignUpProps> = ({ form }) => {
  return (
    <>
      <SettingItem
        label="会員登録"
        description="フォームに「会員登録する」のチェックボックスを表示するかを選択します"
        isEnable={form?.values?.isDisplaySignUpSample}
      />
      {form?.values?.isDisplaySignUpSample && (
        <>
          <Typography variant="body1" fontSize={12} sx={{ pl: 1 }}>
            表示サンプル
          </Typography>
          <FormControlLabel
            control={
              <Checkbox
                value={true}
                checked={form?.values?.isDisplaySignUpSample}
                name="isDisplaySignUpSample"
                {...form.register('isDisplaySignUpSample', { nameOfValueProps: 'checked' })}
                icon={<CheckBoxOutlineBlankRoundedIcon fontSize={'small'} />}
              />
            }
            label="会員登録する"
            sx={{ '.MuiFormControlLabel-label': { fontSize: '12px' }, background: '#F7F7F7' }}
          />
        </>
      )}
      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'left', alignItems: 'center', pl: 2 }}>
        <SwitchStyled checked={form?.values?.isDisplaySignUp} name="isDisplaySignUp" {...form.register('isDisplaySignUp')} />
        <Typography variant="body1" fontSize={12} color="text.secondary">
          設定は無効です
        </Typography>
      </Box>
    </>
  );
};

export default SignUp;
