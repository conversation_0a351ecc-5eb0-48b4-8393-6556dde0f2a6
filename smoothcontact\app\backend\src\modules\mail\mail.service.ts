import { Injectable, Logger } from '@nestjs/common';
import { ISendMailOptions, MailerService } from '@nestjs-modules/mailer';
import * as smtpTransport from 'nodemailer/lib/smtp-transport';

import { RootService } from '@/core/services/root.service';

@Injectable()
export class MailService extends RootService {
  private readonly logger = new Logger(MailService.name);

  constructor(private readonly _mailerService: MailerService) {
    super();
  }

  async sendMail(options: ISendMailOptions): Promise<boolean> {
    if (!process.env.ALLOW_SEND_MAIL) {
      return false;
    }

    if (!options.to) {
      return false;
    }

    if (typeof options.to === 'string') {
      if (!this.isEmailAllowed(options.to)) {
        return false;
      }
    }

    if (Array.isArray(options.to)) {
      const isEmailAllowed = options.to.some((email: string & { address: string }) => {
        if (email.address) {
          return this.isEmailAllowed(email.address);
        }

        return this.isEmailAllowed(email);
      });

      if (!isEmailAllowed) {
        return false;
      }
    }

    try {
      const sendMail = await this._mailerService.sendMail(options);

      this.logger.log('Email Sent Successfully', {
        dateSent: new Date(),
        fromEmail: sendMail?.from,
        receiverEmail: sendMail?.to,
        mailOptions: options,
      });
    } catch (error) {
      this.logger.log('Email Sent Failed', {
        dateSent: new Date(),
        receiverEmail: options?.to,
        mailOptions: options,
        error: error?.message,
      });
    }

    return true;
  }

  addTransporter(transporterName: string, config: string | smtpTransport | smtpTransport.Options): string {
    return this._mailerService.addTransporter(transporterName, config);
  }

  private isEmailAllowed(email: string): boolean {
    try {
      const allowedDomains = new RegExp(process.env.EMAIL_ALLOWED_DOMAINS_REGEX || '.*');
      const match = email.match(/@(.+)$/);

      if (match) {
        const domain = match[1];

        return allowedDomains.test(domain);
      }

      return false;
    } catch (e) {
      return false;
    }
  }
}
