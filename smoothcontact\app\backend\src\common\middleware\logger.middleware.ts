import { Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';

import { LoggerService } from '../../modules/logger/logger.service';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  constructor(private readonly loggerService: LoggerService) {}

  use(request: Request, response: Response, next: NextFunction): void {
    if (process.env.LOG_REQUEST === '1' && request.baseUrl.toLowerCase().startsWith('/api/health') === false) {
      response.locals.requestLog = {
        api: `${request.method} ${request.baseUrl}`,
      };
      const authorization: string = request.header('authorization');
      if (authorization && authorization.startsWith('Bearer ')) {
        try {
          const bearers: string[] = authorization.substring(7).split('.');
          if (bearers.length === 3) {
            const payload = JSON.parse(Buffer.from(bearers[1], 'base64').toString('utf-8'));

            if (payload.dest) {
              const shop: string = payload.dest.startsWith('https://') ? payload.dest.substring(8) : payload.dest;

              response.locals.shop = shop;
            }
          }
        } catch {}
      }

      if (process.env.LOG_REQUEST_BODY === '1') {
        const requestBody: string = JSON.stringify(request.body);
        if (requestBody !== '{}') {
          response.locals.requestLog.body = requestBody;
        }
      }

      this.loggerService.info(response.locals.shop, `Start ${response.locals.requestLog.api}`, {
        meta: response.locals.requestLog,
      });
    }

    response.on('close', () => {
      if (response.locals.requestLog) {
        this.loggerService.info(response.locals.shop, `End ${response.locals.requestLog.api}`, {
          meta: {
            ...response.locals.requestLog,
            status: response.statusCode,
          },
        });
      }
    });

    next();
  }
}
