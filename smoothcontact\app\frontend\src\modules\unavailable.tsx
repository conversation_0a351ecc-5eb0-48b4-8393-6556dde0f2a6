import { Box, Button, Container, CssBaseline, Typography } from '@mui/material';
import React from 'react';

export const UnavailableModule: React.FC = () => {
  return (
    <Container component="main" maxWidth="xs">
      <CssBaseline />
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
        gap={1}
      >
        <Typography variant="h1" sx={{ fontWeight: 'bold', fontSize: 40 }} gutterBottom>
          Service Unavailable
        </Typography>
        <Button href="/form-builder" variant="contained" color="secondary">
          Back To Home
        </Button>
      </Box>
    </Container>
  );
};
