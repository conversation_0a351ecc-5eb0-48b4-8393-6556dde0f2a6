import { MfaVerifyRequestDTO } from '@/modules/mfaSetting/dto/request.dto';
import { AxiosRequestConfig } from 'axios';

export const getMfaCodeRequestConfig = (): AxiosRequestConfig => ({
  url: `/api/mfa`,
  method: 'get',
});

export const createMfaCodeRequestConfig = (data: MfaVerifyRequestDTO): AxiosRequestConfig => ({
  url: `/api/mfa/create`,
  method: 'post',
  data,
});

export const disableMfaCodeRequestConfig = (): AxiosRequestConfig => ({
  url: `/api/mfa/disable`,
  method: 'put',
});

export const reissueBackupCodeRequestConfig = (): AxiosRequestConfig => ({
  url: `/api/mfa/reissue`,
  method: 'post',
});
