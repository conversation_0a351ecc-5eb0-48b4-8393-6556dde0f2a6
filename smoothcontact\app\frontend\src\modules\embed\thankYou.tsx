import FormDisplayMessage from '@/components/FormBuilder/subcomponents/FormDisplayMessage';
import useFormStyles from '@/hooks/useFormStyle';
import { FormColorSetting, TemplateType } from '@/types/FormTemplateTypes';
import { FORM_COLOR_SETTING_INIT, FormTemplateModePresentType, FormTemplateModePresets } from '@/utils/formBuilderUtils';
import { displayFontFamily, getGoogleTagIdFromSnippet } from '@/utils/helper';
import { Box, CssBaseline } from '@mui/material';
import React, { useEffect } from 'react';

declare global {
  interface Window {
    pageData: any;
  }
}

const handleFormAction = (action: string, data: Record<string, any>) => {
  window.parent.postMessage(JSON.stringify({ action, ...data }), '*');
};

const ThankYouModule: React.FC = () => {
  const template = (window?.pageData?.form || {}) as TemplateType;
  if (!template?.formColorSetting || !template?.formElements || !template?.formMailSetting) {
    return 'Invalid template data';
  }

  const { classes } = useFormStyles({ colorSetting: template?.formColorSetting });
  const formTemplateModePreset = FormTemplateModePresets[template?.formColorSetting?.templateModeColor || FormTemplateModePresentType.BASIC];
  const isBlackTemplate = template?.formColorSetting?.templateModeColor === FormTemplateModePresentType.BLACK;
  const currentFormColorSetting =
    template?.formColorSetting?.optionMode === 'template_mode'
      ? ({
          ...FORM_COLOR_SETTING_INIT,
          optionMode: template?.formColorSetting?.optionMode,
          layoutMode: template?.formColorSetting?.layoutMode,
          templateModeColor: template?.formColorSetting?.templateModeColor,
          bgColor: formTemplateModePreset?.bgColor,
          titleSettings: {
            ...FORM_COLOR_SETTING_INIT.titleSettings,
            color: formTemplateModePreset?.buttonColor,
          },
          generalSettings: {
            ...FORM_COLOR_SETTING_INIT.generalSettings,
            color: isBlackTemplate ? '#FFF' : (formTemplateModePreset?.color ?? '#0c0d0e'),
          },
          labelSettings: {
            ...FORM_COLOR_SETTING_INIT.labelSettings,
            color: isBlackTemplate ? '#FFF' : (formTemplateModePreset?.color ?? '#0c0d0e'),
          },
          descriptionSettings: {
            ...FORM_COLOR_SETTING_INIT.descriptionSettings,
            color: isBlackTemplate ? '#FFF' : (formTemplateModePreset?.color ?? '#0c0d0e'),
          },
          entryFormSettings: {
            ...FORM_COLOR_SETTING_INIT.entryFormSettings,
            color: isBlackTemplate ? '#555' : (formTemplateModePreset?.color ?? '#0c0d0e'),
          },
          buttonSettings: {
            ...FORM_COLOR_SETTING_INIT.buttonSettings,
            bgColor: formTemplateModePreset?.buttonColor,
            borderColor: formTemplateModePreset?.borderColor,
          },
        } as FormColorSetting)
      : { ...template?.formColorSetting, bgcolor: 'transparent' };

  useEffect(() => {
    const friendlyKey = template.extId || '';
    handleFormAction('setHeight', { height: document.body.scrollHeight, friendlyKey });
    if (template?.formEmbedAppSetting?.isEnableGoogleAdsSetting) {
      const eventSnippet = template.formEmbedAppSetting?.eventSnippet || '';
      handleFormAction('google_ads', {
        id: getGoogleTagIdFromSnippet(eventSnippet),
        script: eventSnippet,
      });
    }

    if (template?.formEmbedAppSetting?.isLinkageYahoo) {
      handleFormAction('yahoo_ads', {
        script: template.formEmbedAppSetting?.conversionMeasurementTags || '',
      });
    }
  }, []);

  return (
    <Box component="main" className={classes.container}>
      <CssBaseline />
      <Box p={1}>
        <Box
          sx={{
            backgroundColor: 'transparent',
            fontSize: `${currentFormColorSetting?.generalSettings?.fontSize}${currentFormColorSetting?.generalSettings?.fontSizeUnit}`,
            fontFamily: displayFontFamily(currentFormColorSetting?.generalSettings?.fontFamily),
            color: currentFormColorSetting?.generalSettings?.color,
          }}
        >
          <FormDisplayMessage
            formElements={template?.formElements}
            colorSetting={template?.formColorSetting}
            message={template?.formMailSetting?.message || 'ご提出ありがとうございました！'}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default ThankYouModule;
