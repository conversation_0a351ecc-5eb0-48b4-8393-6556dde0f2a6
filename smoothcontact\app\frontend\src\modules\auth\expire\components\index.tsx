import { useFormHandler } from '@/hooks/useFormHandler';
import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Typography from '@mui/material/Typography';

export interface Props {
  formHandler: ReturnType<typeof useFormHandler>;
  loading?: boolean;
}

const ExpireComponent = () => {
  return (
    <Box sx={{ textAlign: 'center' }}>
      <Typography component="h4" variant="h5">
        「セクションが切れました。Shopify画面から再度ログインしてください。」
      </Typography>

      <Typography sx={{ mt: 2 }}>
        <Link underline="none" href="https://admin.shopify.com/login">
          Shopify画面に移動する
        </Link>
      </Typography>
    </Box>
  );
};

export default ExpireComponent;
