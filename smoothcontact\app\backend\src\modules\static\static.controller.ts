import { Controller, Get, Req, Request, Res } from '@nestjs/common';
import { Response } from 'express';
import { join } from 'path';

import { getS3Url } from '@/utils/helpers';

import { StaticService } from './static.service';

@Controller()
export class StaticController {
  constructor(private readonly staticService: StaticService) {}

  @Get('/front/output/:id([a-z0-9]+)')
  async embed(@Req() req: Request, @Res() res: Response) {
    const htmlFile = join(__dirname, '../../../../', 'frontend/dist/index.html');
    const form = await this.staticService.getFormBuilderByExtId(req.params.id);

    if (!form) {
      return res.status(404).send('<div style="text-align: center;">受付は終了しました。たくさんの投稿ありがとうございました。</div>');
    }

    const formFonts = this.staticService.extractFontFamilies(form.formColorSetting);

    const content = await this.staticService.renderTemplateByHtmlFile(
      htmlFile,
      {
        title: form.name,
        url: process.env.APP_URL + req.originalUrl,
        image: getS3Url(form.formGeneralSetting.oGPImage),
        allowSearchEngine: form.formGeneralSetting.isDisplaySearchEngine,
        GATrackingId: form.formEmbedAppSetting.isEnableGA4Setting && form.formEmbedAppSetting.gA4TrackingID,
        GoogleAdsGlobalTag: form.formEmbedAppSetting.isEnableGoogleAdsSetting && form.formEmbedAppSetting.globalSiteTag,
        GoogleAdsEventSnippet: form.formEmbedAppSetting.isEnableGoogleAdsSetting && form.formEmbedAppSetting.eventSnippet,
        hasYahooAds: form.formEmbedAppSetting.isLinkageYahoo,
        formFonts,
      },
      true,
    );

    return res.status(200).send(content);
  }

  @Get('/front/output/:id([a-z0-9]+)/thank-you')
  async thankYou(@Req() req: Request, @Res() res: Response) {
    const htmlFile = join(__dirname, '../../../../', 'frontend/dist/index.html');

    const form = await this.staticService.getFormBuilderByExtId(req.params.id);

    if (!form) {
      return res.status(404).send('<div style="text-align: center;">受付は終了しました。たくさんの投稿ありがとうございました。</div>');
    }

    const pageData = JSON.stringify({
      thankYouMessage: form.formMailSetting.message,
      form: {
        extId: form.extId,
        formElements: form.formElements,
        formColorSetting: form.formColorSetting,
        formMailSetting: form.formMailSetting,
      },
    });

    const content = await this.staticService.renderTemplateByHtmlFile(
      htmlFile,
      {
        title: form.name,
        url: process.env.APP_URL + req.originalUrl,
        image: getS3Url(form.formGeneralSetting.oGPImage),
        allowSearchEngine: form.formGeneralSetting.isDisplaySearchEngine,
        GATrackingId: form.formEmbedAppSetting.isEnableGA4Setting && form.formEmbedAppSetting.gA4TrackingID,
        GoogleAdsGlobalTag: form.formEmbedAppSetting.isEnableGoogleAdsSetting && form.formEmbedAppSetting.globalSiteTag,
        GoogleAdsEventSnippet: form.formEmbedAppSetting.isEnableGoogleAdsSetting && form.formEmbedAppSetting.eventSnippet,
        YahooAdsEventSnippet: form.formEmbedAppSetting.isLinkageYahoo && form.formEmbedAppSetting.conversionMeasurementTags,
        hasYahooAds: form.formEmbedAppSetting.isLinkageYahoo,
        pageData: pageData,
      },
      true,
    );

    return res.status(200).send(content);
  }
}
