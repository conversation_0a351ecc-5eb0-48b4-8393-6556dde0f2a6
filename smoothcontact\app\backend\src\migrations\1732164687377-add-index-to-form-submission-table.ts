import { MigrationInterface, QueryRunner, TableIndex } from 'typeorm';

export class AddIndexToFormSubmissionTable1732164687377 implements MigrationInterface {
  TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}form_submission`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'idx_form_submission_form_ext_id',
        columnNames: ['form_ext_id'],
      }),
    );
    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'idx_form_submission_status',
        columnNames: ['status'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(this.TABLE_NAME, 'idx_form_submission_form_ext_id');
    await queryRunner.dropIndex(this.TABLE_NAME, 'idx_form_submission_status');
  }
}
