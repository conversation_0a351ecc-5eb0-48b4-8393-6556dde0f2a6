version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 20
    on-failure: ABORT

  pre_build:
    on-failure: ABORT
    commands:
      - echo "Init params..."
      - REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
      - CI_REGISTRY_IMAGE=$REPOSITORY_URI/$IMAGE_REPO_NAME
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=$IMAGE_TAG

      - echo "Logging in to Amazon ECR..."
      - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URI

  build:
    on-failure: ABORT
    commands:
      - echo Build frontend
      - cd $CODEBUILD_SRC_DIR/app/frontend
      - yarn install --production=false
      - yarn build
      - yarn build:embed
      - yarn build:html-embed
      - yarn postinstall
      - echo "Generating sha256 private & public key..."
      - mkdir -p $CODEBUILD_SRC_DIR/app/backend/configs/shaKey
      - openssl genrsa -out $CODEBUILD_SRC_DIR/app/backend/configs/shaKey/private.key 2048
      - openssl rsa -in $CODEBUILD_SRC_DIR/app/backend/configs/shaKey/private.key -pubout -out $CODEBUILD_SRC_DIR/app/backend/configs/shaKey/public.key
      - cat $CODEBUILD_SRC_DIR/app/backend/configs/shaKey/public.key
      - echo Pull latest image for cache
      - cd $CODEBUILD_SRC_DIR
      - docker pull $CI_REGISTRY_IMAGE:$IMAGE_TAG || true

      - echo Building the Docker image...
      - >
        docker build
        --cache-from $CI_REGISTRY_IMAGE:$IMAGE_TAG
        --tag $CI_REGISTRY_IMAGE:$COMMIT_HASH
        --tag $CI_REGISTRY_IMAGE:$IMAGE_TAG .

  post_build:
    on-failure: ABORT
    commands:
      - echo Pushing the Docker images
      - docker push $CI_REGISTRY_IMAGE:$COMMIT_HASH
      - docker push $CI_REGISTRY_IMAGE:$IMAGE_TAG
      - printf '[{"name":"%s","imageUri":"%s"}]' $CONTAINER_NAME $CI_REGISTRY_IMAGE:$IMAGE_TAG > imagedefinitions.json
      - cat imagedefinitions.json

artifacts:
  files: imagedefinitions.json
