import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddReleaseDateFieldOnFormBuilder1717575148476 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}form_builder`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'release_start_date',
        type: 'datetime',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'release_end_date',
        type: 'datetime',
        isNullable: true,
        default: null,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'release_start_date',
        type: 'datetime',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'release_end_date',
        type: 'datetime',
        isNullable: true,
        default: null,
      }),
    ]);
  }
}
