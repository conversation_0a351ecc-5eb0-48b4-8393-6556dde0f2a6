import { Controller, Get, Query, Res } from '@nestjs/common';
import { Response } from 'express';

import { BaseController } from '@/core/controllers/api.controller';
import { BindStartService } from '@/modules/bind-start/bind-start.service';

@Controller('api/bindstart')
export class BindStartController extends BaseController {
  constructor(private readonly bindStartService: BindStartService) {
    super();
  }

  @Get('/')
  async index(@Query() queryParams: any, @Res() res: Response) {
    // Redirect to the callback URL with the access token
    const queryString = new URLSearchParams(queryParams).toString();
    const redirectUrl = `${process.env.APP_URL}/login/via-oem?${queryString}`;

    return res.redirect(redirectUrl);
  }
}
