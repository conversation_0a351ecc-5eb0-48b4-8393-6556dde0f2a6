import { FC } from 'react';
import { FormikValues } from 'formik';
import { useTranslation } from 'react-i18next';
import SettingItem from './SettingItemComponent';
import SwitchStyled from '@/components/common/SCToggleSwitch';
import { Box, Checkbox, FormControlLabel, InputAdornment, TextField, Typography } from '@mui/material';
import CheckBoxOutlineBlankRoundedIcon from '@mui/icons-material/CheckBoxOutlineBlankRounded';
import HttpsIcon from '@mui/icons-material/Https';

interface PrivacyPolicyProps {
  form: FormikValues;
}

const PrivacyPolicy: FC<PrivacyPolicyProps> = ({ form }) => {
  const { t } = useTranslation();

  const isEnable = form?.values?.isSettingPrivacyPolicy ?? false;
  const checkboxLabel = isEnable ? t('form_builder.privacy_policy.checkbox_text_enable') : t('form_builder.privacy_policy.checkbox_text_disable');

  return (
    <>
      <SettingItem label={t('form_builder.privacy_policy.label')} description={t('form_builder.privacy_policy.description')} isEnable={isEnable} />
      {isEnable && (
        <>
          <Typography sx={{ pl: 1 }}>{t('form_builder.privacy_policy.display_sample_label')}</Typography>
          <FormControlLabel
            control={<Checkbox icon={<CheckBoxOutlineBlankRoundedIcon fontSize={'small'} />} />}
            label={
              <Typography>
                <a href={form?.values?.policyLink} target="_blank" rel="noopener noreferrer" style={{ color: '#1DA1A8', textDecoration: 'none' }}>
                  プライバシーポリシー
                </a>
                に同意にする
              </Typography>
            }
            sx={{ background: '#F7F7F7' }}
          />
        </>
      )}
      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'left', alignItems: 'center', pb: 1 }}>
        <SwitchStyled value={true} checked={isEnable} {...form.register('isSettingPrivacyPolicy', { nameOfValueProps: 'checked' })} />
        <Typography color="text.secondary">{checkboxLabel}</Typography>
      </Box>
      {isEnable && (
        <TextField
          name="policyLink"
          label={t('form_builder.privacy_policy.policy_link')}
          placeholder="https://example.com/privacy-policy"
          error={!!form?.errors?.policyLink}
          helperText={form?.errors?.policyLink && '有効なURLを入力してください'}
          value={form?.values?.policyLink ?? ''}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start" sx={{ color: '#4CAF50' }}>
                <HttpsIcon />
              </InputAdornment>
            ),
          }}
          {...form.register('policyLink')}
        />
      )}
    </>
  );
};

export default PrivacyPolicy;
