import '@/locales/i18n';
import { ThemeProvider } from '@mui/material/styles';
import { Provider } from 'react-redux';
import 'reflect-metadata';
import LoadingSpinner from './components/layout/LoadingSpinner';
import { PersistGate } from './containers/persistGate';
import { LoadingProvider } from './provider/loadingProvider';
import { ToastProvider } from './provider/toastProvider';
import Routes from './routes';
import { persistor, store } from './store/store';
import './styles/style.css';
import theme from './styles/theme/themeConfig';

export default function App() {
  const pages = import.meta.glob('./pages/**/!(*.test.[jt]sx)*.([jt]sx)', {
    eager: true,
  });

  return (
    <>
      <ThemeProvider theme={theme}>
        <Provider store={store}>
          <LoadingProvider>
            <>
              <PersistGate persistor={persistor}>
                <LoadingSpinner />
                <ToastProvider>
                  <Routes pages={pages} />
                </ToastProvider>
              </PersistGate>
            </>
          </LoadingProvider>
        </Provider>
      </ThemeProvider>
    </>
  );
}
