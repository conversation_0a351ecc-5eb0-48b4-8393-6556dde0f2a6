import { Controller, Get, Query, Res } from '@nestjs/common';
import { Response } from 'express';

import { BaseController } from '@/core/controllers/api.controller';
import { OEMStartService } from '@/modules/oem-start/oem-start.service';

@Controller('api/oemstart')
export class OEMStartController extends BaseController {
  constructor(private readonly oemStartService: OEMStartService) {
    super();
  }

  @Get('/')
  async index(@Query() queryParams: any, @Res() res: Response) {
    // Redirect to the callback URL with the access token
    const queryString = new URLSearchParams(queryParams).toString();
    const redirectUrl = `${process.env.APP_URL}/login/oemstart?${queryString}`;

    return res.redirect(redirectUrl);
  }
}
