import { useState } from 'react';
import DoneIcon from '@mui/icons-material/Done';
import SCModal from '@/components/common/SCModal';
import SCButton from '@/components/common/SCButton';
import { Checkbox, FormControlLabel, FormGroup, List, ListItem, ListItemIcon, ListItemText, Typography } from '@mui/material';
interface ChangeFormTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => void;
  checklistItems: string[];
  title: string;
}

const ChangeFormTypeModal: React.FC<ChangeFormTypeModalProps> = ({ isOpen, onClose, onSubmit, checklistItems, title }) => {
  const [agree, setAgree] = useState<boolean>(false);

  const handleSubmit = () => {
    onSubmit();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <SCModal
      width={400}
      isOpen={isOpen}
      closeBtnLabel={'キャンセル'}
      primaryAction={
        <SCButton disabled={!agree} color="primary" onClick={handleSubmit}>
          変更を実行
        </SCButton>
      }
      onClose={onClose}
    >
      <Typography variant="h6">{title}</Typography>
      <List sx={{ width: '100%', maxWidth: 360, bgcolor: 'background.paper' }}>
        {checklistItems?.map?.((item, index) => (
          <ListItem key={index}>
            <ListItemIcon sx={{ minWidth: 30 }}>
              <DoneIcon />
            </ListItemIcon>
            <ListItemText primary={item} sx={{ my: 0 }} />
          </ListItem>
        ))}
      </List>
      <FormGroup>
        <FormControlLabel
          key={'「全部確認しました」'}
          control={<Checkbox checked={agree} onChange={() => setAgree((pre) => !pre)} />}
          label={'「全部確認しました」'}
        />
      </FormGroup>
    </SCModal>
  );
};

export default ChangeFormTypeModal;
