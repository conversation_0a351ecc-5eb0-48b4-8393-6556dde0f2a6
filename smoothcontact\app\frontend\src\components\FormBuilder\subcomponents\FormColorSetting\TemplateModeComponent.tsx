import { FormTemplateModePresent, FormTemplateModePresentType, FormTemplateModePresets } from '@/utils/formBuilderUtils';
import { Box, FormControl, FormControlLabel, Radio, RadioGroup, Stack } from '@mui/material';
import React, { FC } from 'react';

type TemplateModeComponentProps = {
  value: FormTemplateModePresentType;
  onChangeTemplateMode: (templateMode: FormTemplateModePresentType) => void;
};

const TemplateModeComponent: FC<TemplateModeComponentProps> = ({ value, onChangeTemplateMode }) => {
  const handleChangeOption = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChangeTemplateMode(event?.target.value as FormTemplateModePresentType);
  };

  const templateModeRender = () => {
    return Object.values(FormTemplateModePresentType)?.map?.((templateMode) => {
      const presetOption: FormTemplateModePresent = FormTemplateModePresets[templateMode];

      return (
        <Stack
          direction="column"
          sx={{
            justifyContent: 'center',
            alignItems: 'center',
          }}
          key={templateMode}
        >
          <Box sx={{ width: '100%' }}>
            <img
              src={presetOption.screenShot}
              alt={`template-thumb-${templateMode}`}
              style={{ maxWidth: '100%', height: 'auto', border: '1px solid rgba(191, 191, 191, 0.6)', padding: '2px' }}
            />
          </Box>
          <FormControlLabel value={`${templateMode}`} label={`プリセット / ${templateMode}`} control={<Radio sx={{ paddingLeft: 2 }} />} />
        </Stack>
      );
    });
  };

  return (
    <Stack spacing={2}>
      <FormControl>
        <RadioGroup name="radio-buttons-new-template-group" value={value ?? ''} onChange={handleChangeOption}>
          {templateModeRender()}
        </RadioGroup>
      </FormControl>
    </Stack>
  );
};

export default TemplateModeComponent;
