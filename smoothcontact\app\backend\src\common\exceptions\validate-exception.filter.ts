import { ArgumentsHost, Catch, ExceptionFilter } from '@nestjs/common';
import { HttpArgumentsHost } from '@nestjs/common/interfaces';
import { Response } from 'express';

import { LoggerService } from '@/modules/logger/logger.service';

import { ValidateException } from './validate.exception';

@Catch(ValidateException)
export class ValidateExceptionFilter implements ExceptionFilter {
  constructor(private readonly loggerService: LoggerService) {}

  catch(exception: ValidateException, host: ArgumentsHost) {
    const ctx: HttpArgumentsHost = host.switchToHttp();
    const response: Response = ctx.getResponse<Response>();
    const status: number = exception.getStatus();

    if (response.locals.requestLog) {
      this.loggerService.info(response.locals.shop, exception.message, {
        meta: response.locals.requestLog,
      });
      response.locals.requestLog = undefined;
    }

    response.status(status).json({ data: exception.message });
  }
}
