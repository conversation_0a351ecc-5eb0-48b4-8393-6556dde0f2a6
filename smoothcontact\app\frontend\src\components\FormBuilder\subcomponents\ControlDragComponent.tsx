import FormItemButton from '@/components/common/FormItemButton';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormContainerType, FormElementChildrenType } from '@/types/FormTemplateTypes';
import { FunctionComponent } from 'react';
import { Draggable } from 'react-beautiful-dnd';

interface ControlDragComponentProps {
  item: FormElementChildrenType | FormContainerType;
  index?: any;
}

const ControlDragComponent: FunctionComponent<ControlDragComponentProps> = ({ item, index }) => {
  const { selectedTemplate, error, handleItemAdded, setSelectedParentControl } = useFormBuilder();

  const handleClickFormItemButton = () => {
    if (error) return;

    handleItemAdded(
      {
        ...item,
        containerId: selectedTemplate?.formElements?.[0]?.container?.id,
        parentId: null,
        level: 0,
      },
      selectedTemplate?.formElements?.[0]?.container?.id,
      selectedTemplate?.formElements?.[0]?.children?.length ?? 0,
      false
    );
    setSelectedParentControl?.(null);
  };

  return (
    <Draggable key={`control_draggable_${item.controlName}`} draggableId={item.controlName} index={index} isDragDisabled={error}>
      {(provided, snapshot) => (
        <>
          <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps} style={provided.draggableProps.style}>
            <FormItemButton isDragging={snapshot.isDragging} text={item.displayText} icon={item.icon} onClick={handleClickFormItemButton} />
          </div>
          {snapshot.isDragging && (
            <div>
              <FormItemButton isDragging={snapshot.isDragging} text={item.displayText} icon={item.icon} onClick={handleClickFormItemButton} />
            </div>
          )}
        </>
      )}
    </Draggable>
  );
};

export default ControlDragComponent;
