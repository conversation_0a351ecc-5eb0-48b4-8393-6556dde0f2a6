import useAxios from '@/hooks/useAxios';
import { useForm<PERSON>and<PERSON> } from '@/hooks/useFormHandler';
import { useToast } from '@/provider/toastProvider';
import { updateProfileRequestConfig } from '@/services/account.service';
import { useAppDispatch, useAppSelector } from '@/store/hook';
import { appAction } from '@/store/slices/app';
import { useEffect, useState } from 'react';
import * as Yup from 'yup';

export default function useLogic() {
  const { toast } = useToast();
  const dispatch = useAppDispatch();
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const { apiCaller, loading } = useAxios();
  const { profile } = useAppSelector((state) => ({
    profile: state.app.profile,
  }));
  const validationSchema = Yup.object().shape({
    name: Yup.string().trim().required('名前を入力してください'),
    avatar: Yup.mixed().nullable(),
  });

  const form = useFormHandler({
    initialValues: {
      name: '',
      avatar: '',
      clearAvatar: false,
    },
    validationSchema,
    validateOnChange: true,
    validateOnBlur: false,
    onSubmit: (e) => {
      updateProfile(e);
    },
  });

  const updateProfile = async (formData: any) => {
    const data = new FormData();
    data.append('avatar', formData?.avatar);
    data.append('name', formData?.name);
    data.append('clearAvatar', formData?.clearAvatar);

    const result: any = await apiCaller(updateProfileRequestConfig(profile?.id, data));

    if (!result?.success) {
      toast({ isError: true, message: result?.error });

      return;
    }

    dispatch(appAction.setAppState({ profile: result.data, profileLoaded: true, profileFetchStatus: { forceGetProfile: false } }));
    toast({ isError: false, message: '個人情報が正常に更新されました' });
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.currentTarget.files && event.currentTarget.files[0];

    if (file) {
      setAvatarPreview(URL.createObjectURL(file));
      form?.setFieldValue?.('avatar', file);
    }
  };

  const resetAvatar = () => {
    setAvatarPreview(null);
    form?.setFieldValue?.('clearAvatar', true);
    form?.setFieldValue?.('avatar', null);
  };

  useEffect(() => {
    if (profile) {
      form.resetForm({
        values: {
          name: profile?.name ?? '',
          avatar: profile?.avatar ?? '',
          clearAvatar: false,
        },
      });

      setAvatarPreview(profile?.avatar ? `${import.meta.env.VITE_AWS_S3_BUCKET_URL}/${profile?.avatar}` : '');
    }
  }, [profile]);

  return { updateProfile, handleFileChange, resetAvatar, avatarPreview, loading, form };
}
