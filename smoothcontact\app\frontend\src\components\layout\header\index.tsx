import LogoSvg from '@/assets/logo.svg';
import { Course } from '@/common/constants';
import SCIconButton from '@/components/common/SCIconButton';
import { SetOpenModal, withModal } from '@/hoc/withModal';
import { useConfirm } from '@/provider/confirmProvider';
import { isSessionAuthenticated } from '@/utils/helper';
import { Logout, Settings } from '@mui/icons-material';
import DynamicFormIcon from '@mui/icons-material/DynamicForm';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import { Divider, ListItemIcon } from '@mui/material';
import AppBar from '@mui/material/AppBar';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import MLink from '@mui/material/Link';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import { Stack } from '@mui/system';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { MfaQRCodeModal } from './components/mfaQRCodeModal';
import { MfaSettingModal } from './components/mfaSettingModal';
import useLogic from './useLogic';

interface AppBarProps {
  showAvatar?: boolean;
  logout?: () => void;
  setOpenModal?: SetOpenModal;
}

function ResponsiveAppBar({ showAvatar = true, logout }: AppBarProps) {
  const {
    anchorElUser,
    profile,
    theme,
    isFromOem,
    isAuthenticated,
    showMfaSetting,
    showMfaQRCode,
    formHandler,
    backupCodes,
    handleOpenUserMenu,
    handleCloseUserMenu,
    redirectDashboard,
    redirectProfile,
    handleMfaSetting,
    setShowMfaSetting,
    setShowMfaQRCode,
    handleEnableMfa,
    disableMfaSetting,
    reissueBackupCode,
    downloadBackupCodes,
  } = useLogic();

  const confirm = useConfirm();
  const { t } = useTranslation();
  const handleOemLogout = () => {
    confirm.show({
      title: t('end'),
      subtitle: t('are_you_sure_to_quit'),
      confirmText: t('yes'),
      cancelText: t('no'),
      type: 'warning',
      onConfirm: () => {
        window.close();
      },
    });
  };

  const isShowProfileMenu = showAvatar && (!isSessionAuthenticated() || isAuthenticated);

  const getAvatarFromName = (name: string) => {
    if (!name) return '';

    const nameArr = name.split(' ');
    if (nameArr.length === 1) {
      return nameArr[0].charAt(0).toUpperCase();
    }

    return `${nameArr[0].charAt(0)}${nameArr[nameArr.length - 1].charAt(0)}`.toUpperCase();
  };

  return (
    <Box>
      <AppBar
        position="static"
        sx={{
          backgroundColor: theme.palette.common.white,
          boxShadow: 'none',
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Container maxWidth={false}>
          <Toolbar disableGutters>
            <Link to={'/'}>
              <img src={LogoSvg} alt="logo" style={{ height: '14px', width: '130px' }} />
            </Link>
            <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center', px: 0 }}>
              <Box sx={{ display: { xs: 'none', md: 'flex' } }}>{/* Menu here */}</Box>
            </Box>
            {isFromOem && (
              <Box sx={{ flexGrow: 0 }}>
                <MLink onClick={handleOemLogout} href="#">
                  {t('end')}
                </MLink>
              </Box>
            )}
            {isShowProfileMenu && !isFromOem && (
              <Box sx={{ flexGrow: 0 }}>
                <SCIconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
                  {profile?.avatar ? (
                    <Avatar src={`${import.meta.env.VITE_AWS_S3_BUCKET_URL}/${profile.avatar}`} sx={{ width: 32, height: 32 }} />
                  ) : (
                    <Avatar sx={{ width: 32, height: 32, color: theme.palette.common.white, bgcolor: theme.palette.grey[500] }}>
                      {getAvatarFromName(profile?.name || '')}
                    </Avatar>
                  )}
                </SCIconButton>
                <Menu
                  sx={{ mt: '45px' }}
                  anchorEl={anchorElUser}
                  anchorOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                  keepMounted
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                  open={Boolean(anchorElUser)}
                  onClose={handleCloseUserMenu}
                >
                  <MenuItem>
                    <Stack direction="column">
                      <Typography variant="h6" sx={{ fontSize: '1.1rem', pb: '4px' }}>
                        {profile?.name}
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                        {profile?.email}
                      </Typography>
                    </Stack>
                  </MenuItem>
                  <Divider />
                  <MenuItem onClick={redirectDashboard}>
                    <ListItemIcon>
                      <DynamicFormIcon fontSize="small" />
                    </ListItemIcon>
                    ダッシュボード
                  </MenuItem>
                  <MenuItem onClick={redirectProfile}>
                    <ListItemIcon>
                      <Settings fontSize="small" />
                    </ListItemIcon>
                    設定
                  </MenuItem>
                  {profile?.course === Course.ENTERPRISE && (
                    <MenuItem onClick={handleMfaSetting}>
                      <ListItemIcon>
                        <LockOpenIcon fontSize="small" />
                      </ListItemIcon>
                      二段階認証の設定
                    </MenuItem>
                  )}
                  <MenuItem onClick={logout}>
                    <ListItemIcon>
                      <Logout fontSize="small" />
                    </ListItemIcon>
                    ログアウト
                  </MenuItem>
                </Menu>
              </Box>
            )}
          </Toolbar>
        </Container>
      </AppBar>
      {showMfaSetting && (
        <MfaSettingModal
          title={'二段階認証の設定'}
          open={showMfaSetting}
          onClose={() => setShowMfaSetting(false)}
          onSubmit={() => {
            if (profile?.isVerifiedMfa) {
              disableMfaSetting();

              return;
            }

            handleEnableMfa();
          }}
          onGenerateBackupCode={reissueBackupCode}
          profile={profile}
        />
      )}
      {showMfaQRCode && (
        <MfaQRCodeModal
          title={'二段階認証の設定'}
          open={showMfaQRCode}
          onClose={() => setShowMfaQRCode(false)}
          formHandler={formHandler}
          data={profile}
          backupCodes={backupCodes}
          downloadBackupCode={downloadBackupCodes}
        />
      )}
    </Box>
  );
}
export default withModal(ResponsiveAppBar);
