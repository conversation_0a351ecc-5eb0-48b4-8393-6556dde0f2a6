import { FormColorSetting } from '@/types/FormTemplateTypes';
import { alphaColor, displayFontFamily } from '@/utils/helper';
import { makeStyles } from 'tss-react/mui';

interface AnimationStyles {
  [key: string]: React.CSSProperties;
}

const useFormStyles = makeStyles<{ colorSetting: FormColorSetting; isSettingPreview?: boolean }>()((
  theme,
  { colorSetting, isSettingPreview = false }
) => {
  const baseStyles = {
    container: {
      backgroundColor: isSettingPreview ? (colorSetting?.bgColor ?? '#F0F8FF') : 'transparent',
      fontSize: `${colorSetting?.generalSettings?.fontSize}${colorSetting?.generalSettings?.fontSizeUnit}`,
      fontFamily: displayFontFamily(colorSetting?.generalSettings?.fontFamily),
      color: colorSetting?.generalSettings?.color,
    },
    title: {
      fontSize: `${colorSetting?.titleSettings?.fontSize}${colorSetting?.titleSettings?.fontSizeUnit}`,
      color: colorSetting?.titleSettings?.color,
      fontFamily: displayFontFamily(colorSetting?.titleSettings?.fontFamily),
    },
    general: {
      fontSize: `${colorSetting?.generalSettings?.fontSize}${colorSetting?.generalSettings?.fontSizeUnit}`,
      color: colorSetting?.generalSettings?.color,
      fontFamily: displayFontFamily(colorSetting?.generalSettings?.fontFamily),
    },
    label: {
      fontSize: `${colorSetting?.labelSettings?.fontSize}${colorSetting?.labelSettings?.fontSizeUnit}`,
      color: colorSetting?.labelSettings?.color,
      fontFamily: displayFontFamily(colorSetting?.labelSettings?.fontFamily),
    },
    description: {
      fontSize: `${colorSetting?.descriptionSettings?.fontSize}${colorSetting?.descriptionSettings?.fontSizeUnit}`,
      color: colorSetting?.descriptionSettings?.color,
      fontFamily: displayFontFamily(colorSetting?.descriptionSettings?.fontFamily),
    },
    buttonBase: {
      color: colorSetting?.buttonSettings?.color,
      backgroundColor: colorSetting?.buttonSettings?.bgColor,
      borderColor: colorSetting?.buttonSettings?.borderColor,
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      '&:hover': {
        backgroundColor: alphaColor(colorSetting?.buttonSettings?.bgColor, 0.8, '#1B367B'),
        borderColor: alphaColor(colorSetting?.buttonSettings?.borderColor, 0.4, '#1B367B'),
      },
      '&:disabled': {
        backgroundColor: alphaColor(colorSetting?.buttonSettings?.bgColor, 0.7, "'#1B367B'"),
        borderColor: alphaColor(colorSetting?.buttonSettings?.borderColor, 0.3, '#1B367B'),
        cursor: 'not-allowed',
      },
    },
  };

  // Function to generate animation styles based on the animation setting
  const getAnimationStyles = (animationType: string) => {
    const animationStyles: AnimationStyles = {
      spread: {
        opacity: 0.8,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          zIndex: '-1',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 0,
          height: '200%',
          backgroundColor: colorSetting?.buttonSettings?.bgColor,
          transition: 'width 1.5s ease, height 1.5s ease',
        },
        '&:hover::before': {
          width: '200%',
        },
      },
      close_down: {
        position: 'relative',
        transition: 'all 0.8s ease',
        backgroundColor: colorSetting?.buttonSettings?.bgColor,
        color: colorSetting?.buttonSettings?.color,
        '&::before': {
          content: '""',
          position: 'absolute',
          zIndex: '-1',
          top: 0,
          left: 0,
          width: '100%',
          height: 0,
          backgroundColor: '#fff',
          opacity: 0,
          transition: 'height 0.3s ease, opacity 0.3s ease',
          borderRadius: `${colorSetting?.buttonSettings?.borderRadius}${colorSetting?.buttonSettings?.borderRadiusUnit}`,
        },
        '&:hover::before': {
          height: '100%',
          opacity: 1,
        },
        '&:hover': {
          color: colorSetting?.buttonSettings?.color,
          backgroundColor: colorSetting?.buttonSettings?.bgColor,
          opacity: 0.6,
        },
      },
      square: {
        transition: 'all 0.5s ease',
        boxShadow: `0 4px ${colorSetting?.buttonSettings?.bgColor}`,
        borderColor: colorSetting?.buttonSettings?.borderColor,
        '&:hover': {
          top: '4px',
          boxShadow: 'none',
          opacity: 0.6,
        },
      },
    } as AnimationStyles;

    return animationStyles[animationType] || {};
  };

  // Combining base button styles with animation styles
  const submitButtonStyles = {
    ...baseStyles.buttonBase,
    ...getAnimationStyles(colorSetting?.animationSettings?.itemButton),
  };

  // Final styles object
  return {
    container: baseStyles.container,
    formGroup: {
      '& .MuiTypography-root': {
        '& p': {
          display: 'contents',
        },
      },
      '& .MuiFormHelperText-root': {
        fontSize: '0.8em',
      },
      '& .MuiCheckbox-root': {
        color: colorSetting?.labelSettings?.color,
      },
      '& .MuiButtonBase-root.Mui-checked': {
        color: colorSetting?.labelSettings?.color,
      },
    },
    title: baseStyles.title,
    general: baseStyles.general,
    label: baseStyles.label,
    description: baseStyles.description,
    button: {
      width: '100%',
      padding: theme.spacing(1),
      borderWidth: '1px',
      borderStyle: 'solid',
      color: colorSetting?.buttonSettings?.color,
      backgroundColor: colorSetting?.buttonSettings?.bgColor,
      borderColor: colorSetting?.buttonSettings?.borderColor,
      fontSize: `${colorSetting?.buttonSettings?.fontSize}${colorSetting?.buttonSettings?.fontSizeUnit}`,
      borderRadius: `${colorSetting?.buttonSettings?.borderRadius}${colorSetting?.buttonSettings?.borderRadiusUnit}`,
      fontFamily: displayFontFamily(colorSetting?.buttonSettings?.fontFamily),
      transition: 'all 0.3s ease',
      cursor: 'pointer',
      '&:hover': {
        borderColor: alphaColor(colorSetting?.buttonSettings?.borderColor, 0.4, '#1B367B'),
      },
      '&[type=submit]': submitButtonStyles,
      '&[type=button]': submitButtonStyles,
      '& .MuiCircularProgress-root': {
        color: colorSetting?.buttonSettings?.color,
        marginRight: 8,
      },
    },
  };
});

export default useFormStyles;
