import { Typography } from '@mui/material';
import { FormikValues } from 'formik';
import { FC, useMemo } from 'react';
import UploadFileComponent, { UploadFileValue } from '../UploadFileComponent';
import { useTranslation } from 'react-i18next';

interface OGPComponentProps {
  form: FormikValues;
  isResetTrigger?: boolean;
}

const OGPComponent: FC<OGPComponentProps> = ({ form, isResetTrigger }) => {
  const { t } = useTranslation();

  const handleFileChange = (value: UploadFileValue) => {
    form.setFieldValue('oGPImage', value?.key ?? '');
  };

  const getPreviewUrl = useMemo(() => {
    const previewUrl = form?.values?.oGPImage || '';
    if (!previewUrl) return '';

    return `${import.meta.env.VITE_AWS_S3_BUCKET_URL}/${previewUrl}`;
  }, [form?.values?.oGPImage]);

  return (
    <>
      <Typography variant="body1" fontSize={12} color="text.secondary">
        {t('form_builder.ogp_setting.label')}
      </Typography>
      <Typography fontSize={12} color="text.secondary">
        {t('form_builder.ogp_setting.description')}
      </Typography>
      <UploadFileComponent isResetTrigger={isResetTrigger} previewUrl={getPreviewUrl} onChangeFile={handleFileChange} />
    </>
  );
};

export default OGPComponent;
