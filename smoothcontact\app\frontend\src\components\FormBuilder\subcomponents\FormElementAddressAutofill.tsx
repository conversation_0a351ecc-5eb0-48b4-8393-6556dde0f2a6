import ResponsiveStack from '@/components/common/ResponsiveStack';
import { FormEntrySetting, FormItemValue, ItemAddressValue } from '@/types/FormTemplateTypes';
import { KeyboardArrowDown } from '@mui/icons-material';
import { FormHelperText, Select, Stack } from '@mui/material';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import styled from '@mui/system/styled';
import { useFormik } from 'formik';
import React, { useCallback, useEffect, useMemo } from 'react';
import * as Yup from 'yup';

import { JAPAN_POSTAL_CODE_LINK } from '@/common/constants';
import { prefectures } from '@/common/prefectures';
import { useAddress } from '@/hooks/useAddress';
import { parseJapanesePostalCode } from '@/utils/helper';
import { Link } from 'react-router-dom';
import InputFactory from './FormColorSetting/AnimationCustom/text/InputFactoryComponent';

interface FormElementAddressAutofillProps<T> {
  required?: boolean;
  oneFieldPostcode?: boolean;
  displayPostCodeLink?: boolean;
  classLabel?: string;
  classInput?: string;
  classSelect?: string;
  entryFormSetting: FormEntrySetting;
  isSubmitting?: boolean;
  value?: FormItemValue<T>;
  onChange?: (value: ItemAddressValue | null, isValid?: boolean) => void;
  layout?: 'vertical' | 'horizontal';
  inputAnimation?: string;
}

interface AddressForm {
  postalCode?: string;
  postalCodePart1?: string;
  postalCodePart2?: string;
  prefecture: string;
  city: string;
  town: string;
  street: string;
  building: string;
}

const ItemLabel = styled(Box)`
  width: 160px;
`;

const ArrowDownIcon = () => <KeyboardArrowDown />;

const FormElementAddressAutofill: React.FC<FormElementAddressAutofillProps<any>> = ({
  required,
  oneFieldPostcode,
  displayPostCodeLink,
  onChange,
  classLabel,
  classInput,
  classSelect,
  entryFormSetting,
  isSubmitting,
  value,
  layout,
  inputAnimation,
}) => {
  const { getAddress } = useAddress();
  const previousPostalCodeRef = React.useRef('');
  const previousPostalCodeIsValidRef = React.useRef(true);

  const validationSchema = required
    ? Yup.object().shape({
        postalCode: oneFieldPostcode
          ? Yup.string()
              .required('郵便番号を入力してください')
              .matches(/^\d{3}-?\d{4}$/, '無効郵便番号')
              .test('validate-postal-code', '無効郵便番号', async (postalCode) => {
                if (previousPostalCodeRef.current === postalCode) return previousPostalCodeIsValidRef.current;

                const isValid = await validatePostalCode(postalCode);

                return isValid;
              })
          : Yup.string(),
        postalCodePart1: !oneFieldPostcode
          ? Yup.string()
              .required('郵便番号を入力してください')
              .matches(/^\d{3}$/, '3桁の数字を入力してください')
          : Yup.string(),
        postalCodePart2: !oneFieldPostcode
          ? Yup.string()
              .required('郵便番号を入力してください')
              .matches(/^\d{4}$/, '4桁の数字を入力してください')
              .test('validate-postal-code-2', '無効郵便番号', async (value) => {
                if (previousPostalCodeRef.current === `${form?.values?.postalCodePart1 ?? ''}${value ?? ''}`) {
                  return previousPostalCodeIsValidRef.current;
                }

                const isValid = await validatePostalCode(`${form.values.postalCodePart1 ?? ''}${value ?? ''}`);

                return isValid;
              })
          : Yup.string(),
        prefecture: Yup.string().required('都道府県を選択してください'),
        city: Yup.string().required('市区町村を入力してください'),
        town: Yup.string().required('町名を入力してください'),
        street: Yup.string().required('番地を入力してください'),
        building: Yup.string(),
      })
    : Yup.object().shape({
        postalCode: oneFieldPostcode
          ? Yup.string()
              .matches(/^\d{3}-?\d{4}$/, '無効郵便番号')
              .test('validate-postal-code-2', '無効郵便番号', async (value) => {
                if (!value) {
                  return true;
                }

                if (previousPostalCodeRef.current === value) return previousPostalCodeIsValidRef.current;

                const isValid = await validatePostalCode(`${value}`);

                return isValid;
              })
          : Yup.string(),
        postalCodePart1: !oneFieldPostcode ? Yup.string().matches(/^\d{3}$/, '3桁の数字を入力してください') : Yup.string(),
        postalCodePart2: !oneFieldPostcode
          ? Yup.string()
              .matches(/^\d{4}$/, '4桁の数字を入力してください')
              .test('validate-postal-code-2', '無効郵便番号', async (value) => {
                if (!value && !form.values.postalCodePart1) {
                  return true;
                }

                if (previousPostalCodeRef.current === `${form?.values?.postalCodePart1 ?? ''}${value ?? ''}`) {
                  return previousPostalCodeIsValidRef.current;
                }

                const isValid = await validatePostalCode(`${form?.values?.postalCodePart1 ?? ''}${value ?? ''}`);

                return isValid;
              })
          : Yup.string(),
      });

  const formInitialValues: AddressForm = {
    postalCode: value?.postalCode ?? '',
    postalCodePart1: parseJapanesePostalCode(value?.postalCode)?.[0] ?? '',
    postalCodePart2: parseJapanesePostalCode(value?.postalCode)?.[1] ?? '',
    prefecture: value?.prefecture ?? '',
    city: value?.city ?? '',
    town: value?.town ?? '',
    street: value?.street ?? '',
    building: value?.building ?? '',
  };

  const form = useFormik<AddressForm>({
    initialValues: formInitialValues,
    validationSchema,
    validateOnBlur: oneFieldPostcode ? false : true,
    validateOnChange: true,
    onSubmit: () => {},
  });

  const validatePostalCode = useCallback(async (postalCode: string) => {
    if (!required && !postalCode) return true;

    if (!postalCode || !/^\d{3}-?\d{4}$/.test(postalCode)) {
      return false;
    }

    const data = await getAddress(postalCode);

    if (data) {
      previousPostalCodeIsValidRef.current = true;
      previousPostalCodeIsValidRef.current = true;

      return true;
    }

    previousPostalCodeRef.current = postalCode;

    previousPostalCodeIsValidRef.current = false;

    return false;
  }, []);

  useEffect(() => {
    if (form.dirty) {
      const e = form.values;

      const value: ItemAddressValue | null = form.isValid
        ? {
            postalCode: oneFieldPostcode ? e.postalCode : `${e.postalCodePart1}${e.postalCodePart2}`,
            prefecture: e.prefecture,
            city: e.city,
            town: e.town,
            street: e.street,
            building: e.building,
          }
        : null;

      onChange && onChange(value, form.isValid);
    }
  }, [form.dirty, form.isValid, form.values]);

  useEffect(() => {
    if (isSubmitting && !form.dirty) {
      required &&
        form.setTouched({
          postalCode: oneFieldPostcode ? true : false,
          postalCodePart1: !oneFieldPostcode ? true : false,
          postalCodePart2: !oneFieldPostcode ? true : false,
          prefecture: true,
          city: true,
          town: true,
          street: true,
        });
      form.validateForm();
    }
  }, [isSubmitting, form.dirty]);

  const postalCodePart2Ref = React.useRef<HTMLInputElement>();
  const streetRef = React.useRef<HTMLInputElement>();
  useEffect(() => {
    if (form.values.postalCodePart1 && form.values.postalCodePart1.length >= 3 && !form.errors.postalCodePart1 && !form.values.postalCodePart2) {
      const timeout = setTimeout(() => {
        postalCodePart2Ref?.current?.focus();
      }, 10);

      return () => {
        clearTimeout(timeout);
      };
    }
  }, [form.values.postalCodePart1, form.errors.postalCodePart1]);

  const finalPostalCode = useMemo(() => {
    if (form.values) {
      if (form?.errors?.postalCode && oneFieldPostcode) {
        return;
      }

      if ((form?.errors?.postalCodePart1 || form?.errors?.postalCodePart2) && !oneFieldPostcode) {
        return;
      }

      return oneFieldPostcode ? (form?.values?.postalCode ?? '') : `${form?.values?.postalCodePart1 ?? ''}${form?.values?.postalCodePart2 ?? ''}`;
    }
  }, [form.values, form?.errors]);

  useEffect(() => {
    if (form?.errors?.postalCode && oneFieldPostcode) {
      return;
    }

    if ((form?.errors?.postalCodePart1 || form?.errors?.postalCodePart2) && !oneFieldPostcode) {
      return;
    }

    if (!required && !finalPostalCode) {
      const e = form.values;
      const value: ItemAddressValue | null = form.isValid
        ? {
            postalCode: oneFieldPostcode ? e.postalCode : `${e.postalCodePart1}${e.postalCodePart2}`,
            prefecture: e.prefecture,
            city: e.city,
            town: e.town,
            street: e.street,
            building: e.building,
          }
        : null;

      onChange && onChange(value, form.isValid);
    }

    const fetchAddress = async (postalCode: string) => {
      if (!required && !postalCode) return;

      if (!postalCode || !/^\d{3}-?\d{4}$/.test(postalCode)) {
        return;
      }

      if (postalCode === previousPostalCodeRef.current) return;

      previousPostalCodeRef.current = postalCode;

      const data = await getAddress(postalCode);

      if (data) {
        form.setValues(
          (prev) => ({
            ...prev,
            prefecture: data.prefcode || '',
            city: data.address2 || '',
            town: data.address3 || '',
          }),
          false
        );

        form.setFieldError('prefecture', undefined);
        form.setFieldError('city', undefined);
        form.setFieldError('town', undefined);

        form.setTouched({ prefecture: true, city: true, town: true, street: true }, false);
      }
    };
    fetchAddress(finalPostalCode);
  }, [finalPostalCode, form.errors.postalCode, form.errors.postalCodePart1, form.errors.postalCodePart2]);

  return (
    <Box>
      <Stack spacing={2}>
        <Box>
          <ResponsiveStack
            forceDirection={layout === 'vertical' ? 'column' : undefined}
            forceAlignItems={layout === 'vertical' ? 'left' : undefined}
            alignItems="center"
            direction="row"
            mobileDirection="column"
            spacing={1}
          >
            <ItemLabel>
              <Typography className={classLabel} variant="body1">
                郵便番号
              </Typography>
            </ItemLabel>
            {oneFieldPostcode && (
              <>
                <Box
                  width={300}
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}
                  gap={2}
                >
                  <InputFactory
                    name="postalCode"
                    placeholder=""
                    className={classInput}
                    value={form.values.postalCode}
                    error={!!form.errors.postalCode}
                    onChange={form.handleChange}
                    onBlur={form.handleBlur}
                    // onBlur={(e) => debouncedValidatePostalCode(e, e.target.value)}
                    borderColor={entryFormSetting?.borderColor}
                    backgroundColor={entryFormSetting?.bgColor}
                    borderRadius={entryFormSetting?.borderRadius?.toString()}
                    fontSize={entryFormSetting?.fontSize?.toString()}
                    inputAnimation={inputAnimation}
                    inputProps={{ maxLength: 8 }}
                  />
                  {displayPostCodeLink && (
                    <Link to={JAPAN_POSTAL_CODE_LINK} target="_blank" rel="noreferrer" className={classLabel} style={{ flexShrink: 0 }}>
                      郵便番号を検索
                    </Link>
                  )}
                </Box>
                {form.errors.postalCode && <FormHelperText error={!!form.errors.postalCode}>{form.errors.postalCode}</FormHelperText>}
              </>
            )}
            {!oneFieldPostcode && (
              <Box display={'flex'} flexDirection={'column'}>
                <Stack direction="row" alignItems="center" gap={2}>
                  <Box width={100}>
                    <InputFactory
                      name="postalCodePart1"
                      placeholder=""
                      className={classInput}
                      value={form.values.postalCodePart1}
                      error={!!form?.errors?.postalCodePart1}
                      onChange={form.handleChange}
                      onBlur={form.handleBlur}
                      // onBlur={(v) => debouncedValidatePostalCode(v, `${form?.values?.postalCodePart1}${v.target.value}`)}
                      borderColor={entryFormSetting?.borderColor}
                      backgroundColor={entryFormSetting?.bgColor}
                      borderRadius={entryFormSetting?.borderRadius?.toString()}
                      fontSize={entryFormSetting?.fontSize?.toString()}
                      inputAnimation={inputAnimation}
                      marginTop="0"
                      inputProps={{ maxLength: 3 }}
                    />
                  </Box>
                  <Box className={classLabel}>-</Box>
                  <Box width={100}>
                    <InputFactory
                      inputRef={postalCodePart2Ref}
                      name="postalCodePart2"
                      placeholder=""
                      className={classInput}
                      value={form.values.postalCodePart2}
                      error={!!form?.errors?.postalCodePart2}
                      onChange={form.handleChange}
                      onBlur={form.handleBlur}
                      // onBlur={(v) => debouncedValidatePostalCode(v, `${form?.values?.postalCodePart1}${v.target.value}`)}
                      borderColor={entryFormSetting?.borderColor}
                      backgroundColor={entryFormSetting?.bgColor}
                      borderRadius={entryFormSetting?.borderRadius?.toString()}
                      fontSize={entryFormSetting?.fontSize?.toString()}
                      inputAnimation={inputAnimation}
                      marginTop="0"
                      inputProps={{ maxLength: 4 }}
                    />
                  </Box>
                  {displayPostCodeLink && (
                    <Link to={JAPAN_POSTAL_CODE_LINK} target="_blank" rel="noreferrer">
                      郵便番号を検索
                    </Link>
                  )}
                </Stack>
                {(!!form?.errors?.postalCodePart1 || !!form?.errors?.postalCodePart2) && (
                  <FormHelperText error>{form?.errors?.postalCodePart1 ?? form?.errors?.postalCodePart2}</FormHelperText>
                )}
              </Box>
            )}
          </ResponsiveStack>
        </Box>
        <ResponsiveStack
          forceDirection={layout === 'vertical' ? 'column' : undefined}
          forceAlignItems={layout === 'vertical' ? 'left' : undefined}
          alignItems="center"
          direction="row"
          mobileDirection="column"
          spacing={1}
        >
          <ItemLabel>
            <Typography className={classLabel} variant="body1">
              都道府県
            </Typography>
          </ItemLabel>
          <Box flexGrow={1}>
            <Select
              IconComponent={ArrowDownIcon}
              name="prefecture"
              className={classSelect}
              fullWidth
              size="small"
              variant="outlined"
              sx={entryFormSetting}
              value={form.values.prefecture}
              error={form.touched.prefecture && !!form.errors.prefecture}
              onChange={form.handleChange}
              onBlur={form.handleBlur}
              MenuProps={{
                autoFocus: false,
                disableAutoFocus: false,
                disableEnforceFocus: false,
                disableScrollLock: true,
              }}
              native={true}
            >
              <option key={0} value="">
                選択してください
              </option>
              {prefectures?.map?.((prefecture) => (
                <option key={prefecture.iso} value={prefecture.iso}>
                  {prefecture.prefecture_kanji}
                </option>
              ))}
            </Select>
            {form.touched.prefecture && form.errors.prefecture && <FormHelperText error>{form.errors.prefecture}</FormHelperText>}
          </Box>
        </ResponsiveStack>
        <ResponsiveStack
          forceDirection={layout === 'vertical' ? 'column' : undefined}
          forceAlignItems={layout === 'vertical' ? 'left' : undefined}
          alignItems="center"
          direction="row"
          mobileDirection="column"
          spacing={1}
        >
          <ItemLabel>
            <Typography className={classLabel} variant="body1">
              市区町村
            </Typography>
          </ItemLabel>
          <Box maxWidth={300}>
            <InputFactory
              name="city"
              placeholder=""
              className={classInput}
              value={form.values.city}
              error={form.touched.city && !!form.errors.city}
              helperText={form.touched.city && form.errors.city}
              onChange={form.handleChange}
              onBlur={form.handleBlur}
              borderColor={entryFormSetting?.borderColor}
              backgroundColor={entryFormSetting?.bgColor}
              borderRadius={entryFormSetting?.borderRadius?.toString()}
              fontSize={entryFormSetting?.fontSize?.toString()}
              inputAnimation={inputAnimation}
              marginTop="0"
            />
          </Box>
          <Box>
            <Typography className={classLabel} variant="body1">
              例）〇〇市〇〇区
            </Typography>
          </Box>
        </ResponsiveStack>
        <ResponsiveStack
          forceDirection={layout === 'vertical' ? 'column' : undefined}
          forceAlignItems={layout === 'vertical' ? 'left' : undefined}
          alignItems="center"
          direction="row"
          mobileDirection="column"
          spacing={1}
        >
          <ItemLabel>
            <Typography className={classLabel} variant="body1">
              町名
            </Typography>
          </ItemLabel>
          <Box maxWidth={300}>
            <InputFactory
              name="town"
              placeholder=""
              className={classInput}
              value={form.values.town}
              error={form.touched.town && !!form.errors.town}
              helperText={form.touched.town && form.errors.town}
              onChange={form.handleChange}
              onBlur={form.handleBlur}
              borderColor={entryFormSetting?.borderColor}
              backgroundColor={entryFormSetting?.bgColor}
              borderRadius={entryFormSetting?.borderRadius?.toString()}
              fontSize={entryFormSetting?.fontSize?.toString()}
              inputAnimation={inputAnimation}
              marginTop="0"
            />
          </Box>
          <Box>
            <Typography className={classLabel} variant="body1" whiteSpace="nowrap">
              例）〇〇町
            </Typography>
          </Box>
        </ResponsiveStack>
        <ResponsiveStack
          forceDirection={layout === 'vertical' ? 'column' : undefined}
          forceAlignItems={layout === 'vertical' ? 'left' : undefined}
          alignItems="center"
          direction="row"
          mobileDirection="column"
          spacing={1}
        >
          <ItemLabel>
            <Typography className={classLabel} variant="body1">
              番地等
            </Typography>
          </ItemLabel>
          <Box maxWidth={300}>
            <InputFactory
              inputRef={streetRef}
              name="street"
              placeholder=""
              className={classInput}
              value={form.values.street}
              error={form.touched.street && !!form.errors.street}
              helperText={form.touched.street && form.errors.street}
              onChange={form.handleChange}
              onBlur={form.handleBlur}
              borderColor={entryFormSetting?.borderColor}
              backgroundColor={entryFormSetting?.bgColor}
              borderRadius={entryFormSetting?.borderRadius?.toString()}
              fontSize={entryFormSetting?.fontSize?.toString()}
              inputAnimation={inputAnimation}
              marginTop="0"
            />
          </Box>
          <Box>
            <Typography className={classLabel} variant="body1" whiteSpace="nowrap">
              例）1-1-1
            </Typography>
          </Box>
        </ResponsiveStack>
        <ResponsiveStack
          forceDirection={layout === 'vertical' ? 'column' : undefined}
          forceAlignItems={layout === 'vertical' ? 'left' : undefined}
          alignItems="center"
          direction="row"
          mobileDirection="column"
          spacing={1}
        >
          <ItemLabel>
            <Typography className={classLabel} variant="body1">
              建物名
            </Typography>
          </ItemLabel>
          <Box maxWidth={300}>
            <InputFactory
              name="building"
              placeholder=""
              className={classInput}
              value={form.values.building}
              error={form.touched.building && !!form.errors.building}
              helperText={form.touched.building && form.errors.building}
              onChange={form.handleChange}
              onBlur={form.handleBlur}
              borderColor={entryFormSetting?.borderColor}
              backgroundColor={entryFormSetting?.bgColor}
              borderRadius={entryFormSetting?.borderRadius?.toString()}
              fontSize={entryFormSetting?.fontSize?.toString()}
              inputAnimation={inputAnimation}
              marginTop="0"
            />
          </Box>
          <Box>
            <Typography className={classLabel} variant="body1" whiteSpace="nowrap">
              例）〇〇マンション101
            </Typography>
          </Box>
        </ResponsiveStack>
        <ResponsiveStack
          forceDirection={layout === 'vertical' ? 'column' : undefined}
          forceAlignItems={layout === 'vertical' ? 'left' : undefined}
          direction="row"
          mobileDirection="column"
          spacing={1}
        >
          <ItemLabel />
          <Typography className={classLabel} variant="caption">
            建物名は必要に応じてご入力ください。
          </Typography>
        </ResponsiveStack>
      </Stack>
    </Box>
  );
};

export default FormElementAddressAutofill;
