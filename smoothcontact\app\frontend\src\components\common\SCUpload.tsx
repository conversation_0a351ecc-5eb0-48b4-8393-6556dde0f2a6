import SCIconButton from '@/components/common/SCIconButton';
import useAxios from '@/hooks/useAxios';
import { ItemUploadValue } from '@/types/FormTemplateTypes';
import { fileSizeFormatted, getBaseFileName, isImageFile } from '@/utils/helper';
import ClearIcon from '@mui/icons-material/Clear';
import CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';
import FilePresentOutlinedIcon from '@mui/icons-material/FilePresentOutlined';
import { Box, CircularProgress, FormHelperText, alpha } from '@mui/material';
import styled from '@mui/system/styled';
import dayjs from 'dayjs';
import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import { makeStyles } from 'tss-react/mui';

interface SCUploadProps extends React.HTMLAttributes<HTMLInputElement> {
  className?: string;
  title?: string;
  uploadApi?: string;
  name?: string;
  error?: boolean;
  value?: ItemUploadValue;
  helperText?: string;
  onChangeFile?: (value: ItemUploadValue) => void;
}

const useStyles = makeStyles()((theme) => ({
  root: {
    position: 'relative',
    '& .btn-delete': {
      position: 'absolute',
      top: 0,
      right: 0,
      zIndex: 1,
      color: theme.palette.error.main,
    },
  },
  previewWrapper: {
    marginBottom: theme.spacing(1),
    '.thumbnail': {
      display: 'block',
      maxWidth: '100%',
      maxHeight: '180px',
      borderRadius: theme.shape.borderRadius,
    },
    '.fileIcon': {
      color: theme.palette.grey[600],
    },
    '.loading': {
      display: 'flex',
      backgroundColor: alpha(theme.palette.common.black, 0.3),
      alignItems: 'center',
      justifyContent: 'center',
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      '.MuiCircularProgress-root': {
        color: theme.palette.common.white,
      },
    },
  },
}));

const SCUpload: FC<SCUploadProps> = (props) => {
  const { className, title, value, uploadApi } = props;
  const { apiCaller, cancelRequest, loading } = useAxios<any>();
  const [file, setFile] = useState<File | null>(null); // Add this line
  const fileInputRef = useRef<HTMLInputElement>(null); // Create a ref
  const { classes } = useStyles();
  const [errorMessage, setErrorMessage] = useState('');

  const handleIconClick = () => {
    if (loading || hasPreview) return;

    fileInputRef.current?.click(); // Trigger click event of the hidden file input
  };

  const revokeObjectURL = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFile(event.target.files ? event.target.files[0] : null);
  };

  const handleDelete = () => {
    cancelRequest();
    revokeObjectURL();
    props.onChangeFile?.(null);
    setFile(null);
    fileInputRef.current!.value = ''; // Clear the file input value
  };

  const hasPreview = useMemo(() => {
    return isImageFile(value?.type || file?.type || '');
  }, [file?.type, value?.type]);

  const previewUrl = useMemo(() => {
    if (!hasPreview) return '';

    return value?.url || URL.createObjectURL(file);
  }, [hasPreview]);

  useEffect(() => {
    if (props.value?.url && props.value?.url.startsWith('blob')) {
      URL.revokeObjectURL(props.value?.url);
    }

    return () => {
      setFile(null);
    };
  }, []);

  useEffect(() => {
    const uploadFile = async () => {
      if (!file || !uploadApi) {
        return;
      }

      try {
        const data = new FormData();
        data.append('file', file);
        data.append('date', dayjs().format('YYYY.MM.DD_HH.mm.ss'));
        const result = await apiCaller({
          url: uploadApi,
          method: 'POST',
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          data,
        });

        if (result?.success && result?.data && !loading) {
          props.onChangeFile?.({
            file: result?.data?.key || '',
            size: fileInfo?.fileSize || 0,
            url: result?.data?.url || '',
            type: fileInfo?.type || '',
          });
          setFile(null);
          setErrorMessage('');
        }

        if (!result?.success) {
          setErrorMessage(result?.error || 'アップロードに失敗しました。');
        }

        revokeObjectURL();
      } catch (e) {
        console.error('uploadFile', e);
      }
    };
    uploadFile();
  }, [file]);

  const fileInfo = useMemo(() => {
    if (!file && !value) {
      return null;
    }

    return {
      fileName: getBaseFileName(value?.file || file.name),
      fileSize: value?.size || file.size,
      url: value?.url || previewUrl,
      type: value?.type || file.type,
    };
  }, [file, value]);

  const fileSizeMB = fileSizeFormatted(fileInfo?.fileSize || 0);

  const IconContainer = styled('div')(({ theme }) => {
    return {
      color: theme.palette.divider,
    };
  });

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDragEnter = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    if (files.length) {
      setFile(files[0]);
    }
  };

  return (
    <Box
      className={`${className} ${classes.root}`}
      p={1}
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={handleIconClick}
    >
      {fileInfo && (
        <SCIconButton className="btn-delete" onClick={handleDelete}>
          <ClearIcon />
        </SCIconButton>
      )}
      {fileInfo ? (
        <Box className={classes.previewWrapper} position="relative">
          {hasPreview ? (
            <>
              <img className="thumbnail" alt={fileInfo.fileName} src={fileInfo.url} />
              {loading && (
                <Box className="loading">
                  <CircularProgress size={30} />
                </Box>
              )}
            </>
          ) : (
            <Box className="fileIcon">{loading ? <CircularProgress size={30} /> : <FilePresentOutlinedIcon fontSize="large" />}</Box>
          )}
        </Box>
      ) : (
        <IconContainer
          sx={{
            cursor: 'pointer',
          }}
        >
          <CloudUploadOutlinedIcon fontSize="large" />
        </IconContainer>
      )}
      <Box>{fileInfo ? `${fileInfo.fileName} (${fileSizeMB})` : title}</Box>
      {props.helperText && <FormHelperText error>{props.helperText}</FormHelperText>}
      {errorMessage && <FormHelperText error>{errorMessage}</FormHelperText>}
      <input type="file" ref={fileInputRef} onChange={handleFileChange} style={{ display: 'none' }} />
    </Box>
  );
};

export default SCUpload;
