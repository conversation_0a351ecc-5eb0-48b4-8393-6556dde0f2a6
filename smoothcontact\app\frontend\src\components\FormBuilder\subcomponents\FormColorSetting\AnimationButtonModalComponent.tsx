import React, { FC } from 'react';
import SpreadButton from './AnimationCustom/button/SpreadButton';
import Square3DButton from './AnimationCustom/button/Square3DButton';
import CloseDownButton from './AnimationCustom/button/CloseDownButton';
import NoAnimationButton from './AnimationCustom/button/NoAnimationButton';
import { Box, FormControl, FormControlLabel, Grid, Radio, RadioGroup } from '@mui/material';
import { FormButtonAnimationTypes } from '@/utils/formBuilderUtils';

interface AnimationButtonModalComponentProps {
  selection: string;
  handleButtonChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const buttonConfigs = [
  { value: FormButtonAnimationTypes.NO_ANIMATION, label: 'アニメーションなし', ButtonComponent: NoAnimationButton },
  { value: FormButtonAnimationTypes.SPREAD, label: 'スプレッド', ButtonComponent: SpreadButton },
  { value: FormButtonAnimationTypes.CLOSE_DOWN, label: 'クローズダウン', ButtonComponent: CloseDownButton },
  { value: FormButtonAnimationTypes.SQUARE, label: '3Dスクエア', ButtonComponent: Square3DButton },
];

const AnimationButtonModalComponent: FC<AnimationButtonModalComponentProps> = ({ selection, handleButtonChange }) => {
  return (
    <Box sx={{ width: '100%' }}>
      <FormControl>
        <RadioGroup value={selection} onChange={handleButtonChange}>
          <Grid container spacing={{ xs: 2, md: 3 }} columns={{ xs: 4, sm: 8, md: 12 }}>
            {buttonConfigs?.map?.(({ value, label, ButtonComponent }) => (
              <Grid item xs={6} key={value}>
                <FormControlLabel value={value} control={<Radio />} label={label} style={{ width: '100%' }} />
                <ButtonComponent label="送信" />
              </Grid>
            ))}
          </Grid>
        </RadioGroup>
      </FormControl>
    </Box>
  );
};

export default AnimationButtonModalComponent;
