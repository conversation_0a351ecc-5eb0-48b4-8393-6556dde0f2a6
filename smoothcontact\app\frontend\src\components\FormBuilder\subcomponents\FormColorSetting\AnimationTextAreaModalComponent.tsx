import React, { FC, ReactElement } from 'react';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import { FormControl, FormControlLabel, Radio, RadioGroup, TextField } from '@mui/material';
import LabelTopInputComponent from './AnimationCustom/text/LabelTopInputComponent';
import FocusInInputTabComponent from './AnimationCustom/text/FocusInInputTabComponent';
import { FormInputAnimationTypes } from '@/utils/formBuilderUtils';

interface AnimationTextAreaModalComponentProps {
  selection: string;
  handleTextAreaChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

interface RadioOption {
  value: string;
  label: string;
  Component: ReactElement;
}

const radioOptions: RadioOption[] = [
  {
    value: FormInputAnimationTypes.NO_ANIMATION,
    label: 'アニメーションなし',
    Component: <TextField placeholder="ラベル" sx={{ width: '100%', pt: '30px' }} />,
  },
  {
    value: FormInputAnimationTypes.LABEL_TOP,
    label: 'ラベルトップ',
    Component: <LabelTopInputComponent label="ラベル" marginTop="2rem" />,
  },
  {
    value: FormInputAnimationTypes.LINE_COLOR,
    label: 'ラインカラー',
    Component: (
      <TextField
        label="ラベル"
        variant="standard"
        color="secondary"
        sx={{
          width: '100%',
          '& .MuiInputBase-root': {
            background: 'transparent!important',
          },
        }}
      />
    ),
  },
  {
    value: FormInputAnimationTypes.FOCUS_IN,
    label: 'フォーカスイン',
    Component: <FocusInInputTabComponent placeholder="ラベル" />,
  },
];

const GridItemComponent: FC<{ option: RadioOption }> = ({ option }) => (
  <Grid item xs={6}>
    <FormControlLabel value={option.value} control={<Radio />} label={option.label} sx={{ width: '100%' }} />
    {option.Component}
  </Grid>
);

const AnimationTextAreaModalComponent: FC<AnimationTextAreaModalComponentProps> = ({ selection, handleTextAreaChange }) => {
  return (
    <Box sx={{ width: '100%' }}>
      <FormControl>
        <RadioGroup value={selection} onChange={handleTextAreaChange}>
          <Grid container spacing={{ xs: 2, md: 3 }} columns={{ xs: 4, sm: 8, md: 12 }}>
            {radioOptions?.map?.((option) => <GridItemComponent key={option.value} option={option} />)}
          </Grid>
        </RadioGroup>
      </FormControl>
    </Box>
  );
};

export default AnimationTextAreaModalComponent;
