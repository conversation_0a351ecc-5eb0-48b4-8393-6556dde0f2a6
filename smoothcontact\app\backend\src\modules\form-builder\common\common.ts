import { Transform, Type } from 'class-transformer';
import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsPositive,
  IsString,
  IsUrl,
  Matches,
  Max,
  <PERSON><PERSON><PERSON>th,
  <PERSON>,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import { DOMAIN_REGEX } from '@/common/constant';
import { IsCustomOptional } from '@/core/decorator/customOptional.decorator';

export const DEFAULT_FONT = 'Noto Sans JP';
export const DEFAULT_COLOR = '#000';
export const DEFAULT_TEMPLATE_COLOR = 'BASIC';
export const DEFAULT_LAYOUT_MODE = 'vertical';

export enum FontSize {
  SIZE_12 = 12,
  SIZE_16,
}

export enum FontSizeUnit {
  PX = 'px',
  REM = 'rem',
}

export enum SideBarType {
  ELEMENT = 'ELEMENT',
  GENERAL = 'GENERAL',
  COLOR = 'COLOR',
  MAIL = 'MAIL',
  SCHEDULE = 'SCHEDULE',
  EMBED_APP = 'EMBED_APP',
}

export enum FormType {
  MANUAL = 'manual',
  HTML = 'dev',
}

export enum FormCategory {
  TEXT_ELEMENT = 'TEXT_ELEMENT',
  DATE_ELEMENT = 'DATE_ELEMENT',
  PERSONAL_ELEMENT = 'PERSONAL_ELEMENT',
  CHOICE_ELEMENT = 'CHOICE_ELEMENT',
  MEDIA_ELEMENT = 'MEDIA_ELEMENT',
}

export enum FormControlNames {
  STEP_CONTAINER = 'step-container',
  INPUT_TEXTFIELD = 'text-field',
  INPUT_MULTILINE = 'multiline-text-field',
  COMMENT = 'comment',
  CHECKBOX = 'checkbox',
  RADIO = 'radio-group',
  DROPDOWN = 'select-drop-down',
  DATE = 'date-field',
  TIME = 'time-field',
  FILE_UPLOAD = 'file-upload',
  IMAGE_UPLOAD = 'image-upload',
  TOGGLE = 'toggle',
  CHECKLIST = 'checklist',
  SIGNATURE = 'signature',
  MULTI_CHOICES = 'multi-choices',
  SCAN_CODE = 'scan-code',
  VERIFIED_ID = 'verified-id',
  FULL_NAME = 'fullname',
  PHONE = 'phone',
  EMAIL = 'email',
  ADDRESS = 'address',
  BIRTHDAY = 'birthday',
}

export enum FormItemTypes {
  CONTROL = 'control',
  CONTAINER = 'container',
}

export class SwitchContact {
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @IsString()
  sender: string;

  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  content: string;

  @IsBoolean()
  isAutoReply: boolean;

  @IsEmail()
  autoReplyEmailAddress: string;

  @IsString()
  autoReplySenderName: string;

  @IsString()
  autoReplySubject: string;

  @IsString()
  autoReplyBody: string;
}

export class SwitchContactSetting {
  @IsString()
  id: string;

  @Type(() => SwitchContact)
  @ValidateIf((o) => o.enabled)
  contact: SwitchContact;

  @IsBoolean()
  enabled: boolean;
}

export class FormGeneralSetting {
  oGPImage: string;
  linkageDomain: string;

  @IsBoolean()
  receivedDataSaveFlag: boolean;

  @IsBoolean()
  isDisplaySearchEngine: boolean;

  @IsBoolean()
  isSettingReCAPTCHA: boolean;

  @IsBoolean()
  isSettingPrivacyPolicy: boolean;

  @IsBoolean()
  isDisplayTermsUse: boolean;

  @IsBoolean()
  isCombineIntoOneCheckbox: boolean;

  @IsBoolean()
  isDisplaySignUp: boolean;

  @IsBoolean()
  isDisplaySignUpSample: boolean;

  @IsUrl()
  @ValidateIf((o) => o.isDisplayTermsUse)
  termsUse: string;

  @IsUrl()
  @ValidateIf((o) => o.isSettingPrivacyPolicy)
  policyLink: string;

  @IsArray()
  @IsString({ each: true })
  contactPerson: string[];

  @IsCustomOptional()
  @IsArray()
  @Matches(DOMAIN_REGEX, { each: true, message: 'Invalid whitelisted domain' })
  whitelistedDomain: string[];

  @IsString()
  @IsCustomOptional()
  captchaKey: string;
}

export class GeneralSetting {
  @IsNumber()
  spacing: number;

  @IsString()
  spacingUnit: string;

  @IsNumber()
  fontSize: number;

  @IsString()
  fontSizeUnit: string;

  @IsString()
  color: string;

  @IsString()
  @Transform(({ value }) => value ?? DEFAULT_FONT)
  fontFamily: string;

  @IsString()
  @IsCustomOptional()
  fontName?: string;
}

export class TitleSetting {
  @IsNumber()
  @Transform(({ value }) => value ?? FontSize.SIZE_16)
  fontSize: number;

  @IsString()
  @Transform(({ value }) => value ?? FontSizeUnit.PX)
  fontSizeUnit: string;

  @IsString()
  @Transform(({ value }) => value ?? DEFAULT_COLOR)
  color: string;

  @IsString()
  @Transform(({ value }) => value ?? DEFAULT_FONT)
  fontFamily: string;

  @IsString()
  @IsCustomOptional()
  fontName?: string;
}

export class LabelsSetting {
  @IsNumber()
  @Transform(({ value }) => value ?? FontSize.SIZE_12)
  fontSize: number;

  @IsString()
  @Transform(({ value }) => value ?? FontSizeUnit.PX)
  fontSizeUnit: string;

  @IsString()
  @Transform(({ value }) => value ?? DEFAULT_COLOR)
  color: string;

  @IsString()
  @Transform(({ value }) => value ?? DEFAULT_FONT)
  fontFamily: string;

  @IsString()
  @IsCustomOptional()
  fontName?: string;
}

export class DescriptionSetting {
  @IsNumber()
  @Transform(({ value }) => value ?? FontSize.SIZE_12)
  fontSize?: number;

  @IsString()
  @Transform(({ value }) => value ?? FontSizeUnit.PX)
  fontSizeUnit?: string;

  @IsString()
  @Transform(({ value }) => value ?? DEFAULT_COLOR)
  color?: string;

  @IsString()
  @Transform(({ value }) => value ?? DEFAULT_FONT)
  fontFamily?: string;

  @IsString()
  @IsCustomOptional()
  fontName?: string;
}

export class EntryFormSetting {
  @IsNumber()
  fontSize: number;

  @IsString()
  fontSizeUnit: string;

  @IsNumber()
  borderRadius: number;

  @IsString()
  borderRadiusUnit: string;

  @IsCustomOptional()
  @IsString()
  @Transform(({ value }) => value ?? DEFAULT_COLOR)
  color: string;

  @IsString()
  bgColor: string;

  @IsString()
  borderColor: string;
}

export class ChoiceSetting {
  @IsString()
  color: string;
}

export class ButtonsSetting {
  @IsCustomOptional()
  @IsString()
  text?: string;

  @IsCustomOptional()
  @IsString()
  @Transform(({ value }) => value ?? '確認')
  confirmText: string;

  @IsCustomOptional()
  @IsString()
  @Transform(({ value }) => value ?? '送信')
  submitText: string;

  @IsNumber()
  fontSize: number;

  @IsString()
  fontSizeUnit: string;

  @IsNumber()
  borderRadius: number;

  @IsString()
  borderRadiusUnit: string;

  @IsString()
  color: string;

  @IsString()
  bgColor: string;

  @IsString()
  borderColor: string;

  @IsString()
  @Transform(({ value }) => value ?? DEFAULT_FONT)
  fontFamily: string;

  @IsString()
  @IsCustomOptional()
  fontName?: string;
}

export class AnimationSetting {
  @IsString()
  textEntryArea: string;

  @IsString()
  itemButton: string;
}

export class FormColorSetting {
  @IsEnum(['custom_mode', 'template_mode', 'store_theme_mode'], {
    message: "optionMode must be 'custom_mode' or 'template_mode' or 'store_theme_mode'",
  })
  optionMode: 'custom_mode' | 'template_mode' | 'store_theme_mode';

  @IsString()
  templateModeColor: string;

  @IsEnum(['vertical', 'horizontal'], { message: "layoutMode must be 'vertical' or 'horizontal'" })
  layoutMode: 'vertical' | 'horizontal';

  @IsCustomOptional()
  @IsString()
  @Transform(({ value }) => value ?? DEFAULT_COLOR)
  color: string;

  @IsString()
  bgColor: string;

  @ValidateNested()
  @Type(() => GeneralSetting)
  generalSettings: GeneralSetting;

  @ValidateNested()
  @Type(() => TitleSetting)
  titleSettings: TitleSetting;

  @ValidateNested()
  @Type(() => LabelsSetting)
  labelSettings: LabelsSetting;

  @ValidateNested()
  @Type(() => DescriptionSetting)
  descriptionSettings: DescriptionSetting;

  @ValidateNested()
  @Type(() => EntryFormSetting)
  entryFormSettings: EntryFormSetting;

  @ValidateNested()
  @Type(() => ChoiceSetting)
  choiceSettings: ChoiceSetting;

  @ValidateNested()
  @Type(() => ButtonsSetting)
  buttonSettings: ButtonsSetting;

  @ValidateNested()
  @Type(() => AnimationSetting)
  animationSettings: AnimationSetting;
}

export class FormMailSetting {
  @IsCustomOptional()
  @IsArray()
  @IsString({ each: true })
  @IsEmail({}, { each: true, message: 'メールアドレスが無効です' })
  @Transform(({ value }) => (value && Array.isArray(value) ? value?.filter?.((item) => item !== null) : []))
  emailAddress: string[];

  @IsEnum(['display_message', 'specified_url'], { message: "screenAfterSendingType must be 'display_message' or 'specified_url'" })
  screenAfterSendingType: 'display_message' | 'specified_url';

  @IsString()
  @IsCustomOptional()
  message: string;

  @IsUrl()
  @IsCustomOptional()
  specifiedUrl: string;

  @IsBoolean()
  isAutoReply: boolean;

  @IsEmail({}, { message: 'メールアドレスが無効です' })
  @IsCustomOptional()
  autoReplyEmailAddress: string;

  @IsString()
  @IsCustomOptional()
  autoReplySenderName: string;

  @IsString()
  @IsCustomOptional()
  autoReplySubject: string;

  @IsCustomOptional()
  @IsBoolean()
  isTextMail: boolean;

  @IsString()
  @IsCustomOptional()
  textMailBody: string;

  @IsCustomOptional()
  @IsBoolean()
  isHtmlMail: boolean;

  @IsCustomOptional()
  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) => o.isAutoReply)
  receiveEmailField?: string;

  @IsBoolean()
  useCustomSMTP: boolean;

  @IsString()
  @IsCustomOptional()
  smtpHost: string;

  @IsNumber()
  @IsCustomOptional()
  smtpPort: number;

  @IsString()
  @IsCustomOptional()
  smtpUsername: string;

  @IsString()
  @IsCustomOptional()
  smtpPassword: string;

  @IsEmail()
  @IsCustomOptional()
  smtpFromEmail: string;

  @IsCustomOptional()
  htmlFieldDisplays?: string[];
}

export class FormScheduleSetting {
  @IsCustomOptional()
  @IsString()
  @MaxLength(65353)
  displayTextBeforePublicForm: string;

  @IsCustomOptional()
  @IsString()
  @MaxLength(65353)
  displayTextAfterPublicForm: string;

  @IsCustomOptional()
  @IsString()
  @MaxLength(65353)
  displayTextHiddenForm: string;

  @IsCustomOptional()
  @IsBoolean()
  hideHiddenText: boolean;

  @IsCustomOptional()
  @IsNumber()
  maximumNumberFormsReceived: number | null;
}

export class FormEmbedAppSetting {
  @IsBoolean()
  isEnableGA4Setting: boolean;

  @IsNotEmpty()
  @IsString()
  @ValidateIf((o) => o.isEnableGA4Setting)
  gA4TrackingID: string;

  @IsBoolean()
  isEnableGoogleAdsSetting: boolean;

  @IsString()
  @ValidateIf((o) => o.isEnableGoogleAdsSetting)
  globalSiteTag?: string;

  @IsNotEmpty()
  @IsString()
  @ValidateIf((o) => o.isEnableGoogleAdsSetting)
  eventSnippet: string;

  @IsBoolean()
  isLinkageYahoo: boolean;

  @IsNotEmpty()
  @IsString()
  @ValidateIf((o) => o.isLinkageYahoo)
  conversionMeasurementTags: string;
}

export interface FormHistoryType {
  lastPublishedAt: number;
  formElements: FormElements[];
}

export class FormContainerType {
  @IsNotEmpty()
  @IsEnum(FormControlNames)
  controlName: FormControlNames;

  @IsCustomOptional()
  @IsString()
  displayText: string;

  @IsEnum(FormItemTypes)
  itemType: FormItemTypes;

  @IsNotEmpty()
  @IsString()
  heading: string;

  @IsCustomOptional()
  @IsString()
  description: string;

  @IsNotEmpty()
  @IsString()
  id: string;

  @IsCustomOptional()
  @IsInt()
  @IsPositive()
  desktopWidth?: number;

  @IsCustomOptional()
  @IsBoolean()
  required?: boolean;

  @IsCustomOptional()
  @IsIn(['none', 'left', 'center', 'right'])
  display?: 'none' | 'left' | 'center' | 'right';
}

export class FormElementChildrenType {
  @IsNotEmpty()
  @IsEnum(FormControlNames)
  controlName: FormControlNames;

  @IsCustomOptional()
  @IsString()
  @MaxLength(256)
  displayText: string;

  @IsCustomOptional()
  @IsString()
  @MaxLength(65535)
  description: string;

  @ValidateIf((o) => ![FormControlNames.FULL_NAME, FormControlNames.ADDRESS, FormControlNames.COMMENT].includes(o.controlName))
  @IsNotEmpty()
  @IsString()
  @MaxLength(256)
  labelName: string;

  @IsNotEmpty()
  @IsEnum(FormItemTypes)
  itemType: FormItemTypes;

  @IsCustomOptional()
  @IsBoolean()
  required?: boolean;

  @IsCustomOptional()
  @IsArray()
  @Type(() => FormElementChildrenItemsType)
  items?: FormElementChildrenItemsType[];

  @IsNotEmpty()
  @IsEnum(FormCategory)
  category: FormCategory;

  @IsCustomOptional()
  @IsInt()
  index?: number;

  @IsCustomOptional()
  @IsString()
  @MaxLength(256)
  id?: string;

  @IsCustomOptional()
  @IsString()
  @MaxLength(256)
  parentId?: string;

  @IsCustomOptional()
  condition?: string | string[];

  @IsCustomOptional()
  @IsInt()
  level?: number;

  @IsCustomOptional()
  @IsString()
  containerId?: string;

  @IsCustomOptional()
  @IsString()
  @MaxLength(65353)
  placeholder?: string;

  @IsCustomOptional()
  @IsInt()
  @IsPositive()
  rows?: number;

  @IsCustomOptional()
  @IsString()
  dataType?: string;

  @IsCustomOptional()
  @IsInt()
  position?: number;

  @IsCustomOptional()
  @IsIn(['text', 'number', 'alphanumeric'])
  characterType?: 'text' | 'number' | 'alphanumeric';

  @IsCustomOptional()
  @Transform(({ value }) => (value ? Number(value) : ''))
  min?: number;

  @IsCustomOptional()
  @Transform(({ value }) => (value ? Number(value) : ''))
  max?: number;

  @IsCustomOptional()
  @IsBoolean()
  showMinuteStep?: boolean;

  @IsCustomOptional()
  @IsIn(['none', 'future', 'past'])
  dateLimit?: 'none' | 'future' | 'past';

  @IsCustomOptional()
  @IsInt()
  @Min(1)
  @Max(60)
  minuteStep?: number;

  @IsCustomOptional()
  @IsBoolean()
  isUseFurigana?: boolean;

  @IsCustomOptional()
  @IsBoolean()
  isAutoFill?: boolean;

  @IsCustomOptional()
  @IsIn(['hiragana', 'katakana'])
  autoFillType?: 'hiragana' | 'katakana';

  @IsCustomOptional()
  @IsBoolean()
  isReduceFullName?: boolean;

  @IsCustomOptional()
  @IsBoolean()
  verifyEmail?: boolean;

  @IsCustomOptional()
  @IsString()
  verifyEmailPlaceholder?: string;

  @IsCustomOptional()
  @IsBoolean()
  oneFieldPostcode?: boolean;

  @IsCustomOptional()
  @IsBoolean()
  displayPostCodeLink?: boolean;

  @IsCustomOptional()
  @IsBoolean()
  displayOtherOption?: boolean;

  @IsCustomOptional()
  @IsBoolean()
  isBottomDescription?: boolean;

  @IsCustomOptional()
  @IsIn(['vertical', 'horizontal'])
  displayMode?: 'vertical' | 'horizontal';

  @IsCustomOptional()
  @IsBoolean()
  limitedAgeRequired?: boolean;

  @IsCustomOptional()
  @IsInt()
  limitedAge?: number;

  @IsCustomOptional()
  @Type(() => SwitchContactSetting)
  @ValidateNested({ each: true })
  @Transform(({ value }) => (value?.every?.((item) => !item?.hasOwnProperty?.('id')) ? [] : value))
  switchContacts?: SwitchContactSetting[];
}

export class FormElementChildrenItemsType {
  id?: string;

  @IsNotEmpty()
  value: string;

  label?: string;
}

export class FormElements {
  @ValidateNested({ each: true })
  @Type(() => FormContainerType)
  container: FormContainerType;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FormElementChildrenType)
  children: FormElementChildrenType[];
}
