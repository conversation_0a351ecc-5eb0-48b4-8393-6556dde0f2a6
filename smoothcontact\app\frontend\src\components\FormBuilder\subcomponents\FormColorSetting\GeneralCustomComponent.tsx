import SCColorPicker from '@/components/common/SCColorPicker';
import SCFontSelector from '@/components/common/SCFontSelector';
import { KeyboardArrowDown } from '@mui/icons-material';
import { FormControl, Grid, MenuItem, Select, Stack, TextField, Typography } from '@mui/material';
import { FC } from 'react';
import { CustomModeComponentProps } from './CustomModeComponent';

const GeneralCustomComponent: FC<CustomModeComponentProps> = (props) => {
  const { form, webFonts } = props;

  return (
    <>
      <Typography variant="body2">全体</Typography>
      <Stack direction="row" spacing={2}>
        <TextField
          label="項目の間隔"
          variant="outlined"
          type="number"
          {...form.register('generalSettings.spacing')}
          value={form?.values?.generalSettings?.spacing}
          error={!!form?.errors?.generalSettings?.spacing}
          helperText={!!form?.errors?.generalSettings?.spacing && '整数を半角で入力してください'}
        />
        <Select
          IconComponent={() => <KeyboardArrowDown />}
          {...form.register('generalSettings.spacingUnit')}
          value={form?.values?.generalSettings?.spacingUnit}
          displayEmpty
        >
          <MenuItem value={'px'}>px</MenuItem>
          <MenuItem value={'rem'}>rem</MenuItem>
          <MenuItem value={'em'}>em</MenuItem>
          <MenuItem value={'%'}>%</MenuItem>
        </Select>
      </Stack>
      <Stack direction="row" spacing={2}>
        <TextField
          label="文字サイズ"
          variant="outlined"
          type="number"
          {...form.register('generalSettings.fontSize')}
          value={form?.values?.generalSettings?.fontSize}
          error={!!form?.errors?.generalSettings?.fontSize}
          helperText={form?.errors?.generalSettings?.fontSize ? '整数を半角で入力してください' : ''}
        />
        <Select
          IconComponent={() => <KeyboardArrowDown />}
          {...form.register('generalSettings.fontSizeUnit')}
          value={form?.values?.generalSettings?.fontSizeUnit}
          displayEmpty
        >
          <MenuItem value={'px'}>px</MenuItem>
          <MenuItem value={'rem'}>rem</MenuItem>
        </Select>
      </Stack>
      <Grid container alignItems="center">
        <Grid item xs={4} container justifyContent="flex-end">
          <Typography variant="body2">文字色</Typography>
        </Grid>
        <Grid item xs={6} sx={{ pl: 2 }}>
          <SCColorPicker name="generalSettings.color" color={form?.values?.generalSettings.color} form={form} />
        </Grid>
      </Grid>
      <Grid container alignItems="center">
        <Grid item xs={4} container justifyContent="flex-end">
          <Typography variant="body2">分割線の色</Typography>
        </Grid>
        <Grid item xs={6} sx={{ pl: 2 }}>
          <SCColorPicker name="generalSettings.borderColor" color={form?.values?.generalSettings.borderColor} form={form} />
        </Grid>
      </Grid>
      <FormControl fullWidth>
        <SCFontSelector
          name={'generalSettings.fontFamily'}
          fontFamily={form?.values?.generalSettings?.fontFamily ?? ''}
          fontName={form?.values?.generalSettings?.fontName ?? form?.values?.generalSettings?.fontFamily ?? ''}
          source={webFonts}
          onFontChange={(font) => {
            form?.setFieldValue('generalSettings.fontFamily', `${font?.fontFamily}`);
            form?.setFieldValue('generalSettings.fontName', `${font?.fontName}`);
          }}
        />
      </FormControl>
    </>
  );
};

export default GeneralCustomComponent;
