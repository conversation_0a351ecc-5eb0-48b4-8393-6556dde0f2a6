import React, { FC, useEffect, useState } from 'react';
import { Bar<PERSON>hart } from '@mui/x-charts/BarChart';
import { axisClasses } from '@mui/x-charts/ChartsAxis';

interface ReportBarChartAllTimeProps {
  chartData: Record<string, number>;
}

interface DatasetEntry {
  date: string;
  submissionsTotal: number;
}

const generateLastTenYearsData = (): DatasetEntry[] => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  let year = currentYear - 10;

  const data: DatasetEntry[] = [];

  for (let i = 0; i <= 10; i++) {
    const date = `${year}`;
    data.push({ date, submissionsTotal: 0 });
    year++;
  }

  return data;
};

const chartSettings = {
  legend: {
    hidden: true,
  },
  series: [{ dataKey: 'submissionsTotal', label: '' }],
  height: 400,
  borderRadius: 10,
  sx: {
    [`.${axisClasses.left} .${axisClasses.label}`]: {
      transform: 'translate(-5px, 0)',
    },
  },
  yAxis: [
    {
      min: 0,
      tickMinStep: 1,
    },
  ],
};

const ReportBarChartAllTime: FC<ReportBarChartAllTimeProps> = ({ chartData }) => {
  const [dataset, setDataset] = useState<DatasetEntry[]>([]);

  useEffect(() => {
    const data = generateLastTenYearsData().map(({ date }) => ({
      date,
      submissionsTotal: chartData[date] || 0,
    }));
    setDataset(data);
  }, [chartData]);

  return (
    <BarChart
      disableAxisListener
      skipAnimation
      dataset={dataset as any}
      xAxis={[
        {
          scaleType: 'band',
          dataKey: 'date',
          categoryGapRatio: 0.4,
          colorMap: {
            type: 'piecewise',
            thresholds: [new Date(2024, 1, 1), new Date(2050, 1, 1)],
            colors: ['#24CBD4'],
          },
        } as any,
      ]}
      {...chartSettings}
    />
  );
};

export default ReportBarChartAllTime;
