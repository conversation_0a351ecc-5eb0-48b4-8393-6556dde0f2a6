import { Expose } from 'class-transformer';

import { Pagination } from '@/types/response.type';

export class FormSubmissionResponseDto {
  @Expose()
  id: number;

  @Expose()
  formExtId: string;

  @Expose()
  status: number;

  @Expose()
  formValues: any[];

  @Expose()
  formElements: any[];

  @Expose()
  updatedAt: Date;
}

export class ListFormSubmissionResponseDto extends Pagination<FormSubmissionResponseDto> {
  @Expose()
  items: FormSubmissionResponseDto[];
}
