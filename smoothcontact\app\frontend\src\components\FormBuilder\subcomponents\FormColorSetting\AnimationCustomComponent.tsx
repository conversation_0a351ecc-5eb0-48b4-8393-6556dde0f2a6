import React, { FC, useEffect, useState } from 'react';
import { Typography, IconButton, Button } from '@mui/material';
import SCModal from '@/components/common/SCModal';
import ManageSearchIcon from '@mui/icons-material/ManageSearch';
import AnimationButtonModalComponent from './AnimationButtonModalComponent';
import AnimationTextAreaModalComponent from './AnimationTextAreaModalComponent';
import { CustomModeComponentProps } from './CustomModeComponent';
import { FormTextAnimationSetting, FormButtonAnimationSetting } from '@/utils/formBuilderUtils';

interface AnimationSettings {
  textEntryArea: keyof typeof FormTextAnimationSetting;
  itemButton: keyof typeof FormButtonAnimationSetting;
}

interface ModalConfig {
  title: string;
  type: 'textarea' | 'button';
  isOpen: boolean;
}

const AnimationCustomComponent: FC<CustomModeComponentProps> = ({ form }) => {
  const initialAnimationSettings: AnimationSettings = form?.values?.animationSettings || { textEntryArea: '', itemButton: '' };
  const [textAreaSetting, setTextAreaSetting] = useState(initialAnimationSettings.textEntryArea);
  const [buttonSetting, setButtonSetting] = useState(initialAnimationSettings.itemButton);
  const [modalConfig, setModalConfig] = useState<ModalConfig>({ title: '', type: 'textarea', isOpen: false });

  const handleEdit = (title: string, type: 'textarea' | 'button') => {
    setModalConfig({ title, type, isOpen: true });
  };

  const handleSaveAnimationSettings = () => {
    form.setFieldValue('animationSettings.textEntryArea', textAreaSetting);
    form.setFieldValue('animationSettings.itemButton', buttonSetting);
    setModalConfig((prev) => ({ ...prev, isOpen: false }));
  };

  useEffect(() => {
    setTextAreaSetting(initialAnimationSettings.textEntryArea);
    setButtonSetting(initialAnimationSettings.itemButton);
  }, [initialAnimationSettings.textEntryArea, initialAnimationSettings.itemButton]);

  return (
    <>
      <Typography variant="body1">アニメーション</Typography>
      {/* Text Area */}
      <Typography fontSize={12} color="text.secondary">
        テキスト入力エリア
      </Typography>
      <Typography variant="caption" sx={{ '&&': { display: 'flex', alignItems: 'center', pl: 2, mt: 0 } }}>
        <span style={{ minWidth: '62px' }}>{FormTextAnimationSetting[initialAnimationSettings.textEntryArea]}</span>
        <IconButton onClick={() => handleEdit('テキスト項目のアニメーション', 'textarea')}>
          <ManageSearchIcon />
        </IconButton>
      </Typography>

      {/* Button */}
      <Typography fontSize={12} color="text.secondary">
        ボタン
      </Typography>
      <Typography variant="caption" sx={{ '&&': { display: 'flex', alignItems: 'center', pl: 2, mt: 0 } }}>
        <span style={{ minWidth: '62px' }}>{FormButtonAnimationSetting[initialAnimationSettings.itemButton]}</span>
        <IconButton onClick={() => handleEdit('ボタンのアニメーション', 'button')}>
          <ManageSearchIcon />
        </IconButton>
      </Typography>

      {/* Modal for Editing */}
      <SCModal
        title={modalConfig.title}
        isOpen={modalConfig.isOpen}
        width={600}
        closeBtnLabel={'キャンセル'}
        onClose={() => setModalConfig((prev) => ({ ...prev, isOpen: false }))}
        primaryAction={
          <Button variant="contained" onClick={handleSaveAnimationSettings} sx={{ color: 'white' }}>
            保存
          </Button>
        }
      >
        {modalConfig.type === 'textarea' ? (
          <AnimationTextAreaModalComponent
            selection={textAreaSetting}
            handleTextAreaChange={(event: React.ChangeEvent<HTMLInputElement>) =>
              setTextAreaSetting(event.target.value as keyof typeof FormTextAnimationSetting)
            }
          />
        ) : (
          <AnimationButtonModalComponent
            selection={buttonSetting}
            handleButtonChange={(event: React.ChangeEvent<HTMLInputElement>) =>
              setButtonSetting(event.target.value as keyof typeof FormButtonAnimationSetting)
            }
          />
        )}
      </SCModal>
    </>
  );
};

export default AnimationCustomComponent;
