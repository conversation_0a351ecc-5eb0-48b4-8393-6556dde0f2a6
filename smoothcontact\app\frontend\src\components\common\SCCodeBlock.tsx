import CheckIcon from '@mui/icons-material/Check';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import { Box, IconButton, Tooltip } from '@mui/material';
import React, { useState } from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { solarizedlight } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface CodeBlockProps {
  codeString: string;
  language: string;
}

const SCCodeBlock: React.FC<CodeBlockProps> = ({ codeString, language }) => {
  const [hasCopied, setHasCopied] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard
      .writeText(codeString)
      .then(() => {
        setHasCopied(true);
        setTimeout(() => setHasCopied(false), 2000);
      })
      .catch((err) => {
        console.error('Failed to copy: ', err);
      });
  };

  return (
    <Box style={{ position: 'relative', marginBottom: '1em', paddingLeft: '1em', marginTop: '1em' }}>
      <SyntaxHighlighter customStyle={{ fontSize: '14px' }} language={language} style={solarizedlight}>
        {codeString}
      </SyntaxHighlighter>
      <Tooltip title={hasCopied ? 'Copied!' : 'Copy to Clipboard'}>
        <IconButton
          onClick={copyToClipboard}
          size="small"
          style={{
            position: 'absolute',
            top: '2px',
            right: '2px',
            zIndex: 1,
          }}
        >
          {hasCopied ? <CheckIcon /> : <ContentCopyIcon />}
        </IconButton>
      </Tooltip>
    </Box>
  );
};

export default SCCodeBlock;
