import useAxios, { ApiResponse } from '@/hooks/useAxios';
import { useToast } from '@/provider/toastProvider';
import {
  downloadCsvSubmissionRequestConfig,
  downloadCsvSubmissionStatictisRequestConfig,
  getReportByExtIdRequestConfig,
  getCrossTabulationSelectionsRequestConfig,
} from '@/services/form-submission.service';
import { TemplateType } from '@/types/FormTemplateTypes';
import { FormControlNames } from '@/utils/formBuilderUtils';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

type Statistics = Record<string, number>;

interface Element {
  controlName: FormControlNames;
  labelName: string;
  statistics: Statistics;
  list: string[];
}

interface Form {
  name: string;
  extId: string;
  totalSubmission: number;
}

interface General {
  allTime: Statistics;
  lastMonth: Statistics;
  lastSixMonths: Statistics;
  lastYear: Statistics;
}

interface FormReportSubmissionsProps {
  form: Form;
  general: General;
  elements: Record<string, Element>;
}

export default function useLogic() {
  const { toast } = useToast();
  const { id: extId } = useParams();

  const [data, setData] = useState<FormReportSubmissionsProps>(null);
  const [crossTabulationData, setCrossTabulationData] = useState<any>(null);

  const { apiCaller, loading } = useAxios<TemplateType>();

  const get = async (extId: string) => {
    const { data, success, error }: ApiResponse<any> = await apiCaller(getReportByExtIdRequestConfig(extId));
    if (!success) {
      toast({
        isError: true,
        message: error,
      });

      return;
    }

    setData(data);
  };

  const downloadCsv = async ({ extId, isCombine }: { extId: string; isCombine: boolean }) => {
    const { success, error }: ApiResponse<any> = await apiCaller(downloadCsvSubmissionRequestConfig(extId, isCombine));

    if (!success) {
      toast({
        isError: true,
        message: error,
      });

      return;
    }
  };

  const downloadStatictisCsv = async ({ extId }: { extId: string; isCombine: boolean }) => {
    const { success, error }: ApiResponse<any> = await apiCaller(downloadCsvSubmissionStatictisRequestConfig(extId));

    if (!success) {
      toast({
        isError: true,
        message: error,
      });

      return;
    }
  };

  const getCrossTabulationSelections = async ({ targetTabulation, crossTabulation }: { targetTabulation: string; crossTabulation: string }) => {
    const { success, error, data }: ApiResponse<any> = await apiCaller(
      getCrossTabulationSelectionsRequestConfig(extId, targetTabulation, crossTabulation)
    );

    if (!success) {
      toast({
        isError: true,
        message: error,
      });

      return;
    }

    setCrossTabulationData(data);
  };

  useEffect(() => {
    if (!extId) {
      return;
    }

    get(extId);
  }, [extId]);

  return {
    data,
    loading,
    downloadCsv,
    downloadStatictisCsv,
    crossTabulationData,
    getCrossTabulationSelections,
  };
}
