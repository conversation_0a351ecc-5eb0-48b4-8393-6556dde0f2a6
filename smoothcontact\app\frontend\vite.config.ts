import react from '@vitejs/plugin-react';
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';
import { defineConfig, loadEnv } from 'vite';
import { compression } from 'vite-plugin-compression2';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  const PORT = (env.VITE_PORT ?? 3000) as number;

  const proxyOptions = {
    target: `http://127.0.0.1:${env.VITE_BACKEND_PORT ?? 9000}`,
    changeOrigin: false,
    secure: true,
    ws: false,
  };

  return {
    root: dirname(fileURLToPath(import.meta.url)),
    plugins: [react(), compression()],
    define: {},
    resolve: {
      preserveSymlinks: true,
      alias: [{ find: '@', replacement: path.resolve(__dirname, 'src') }],
    },
    server: {
      port: PORT,
      proxy: {
        '^/api(/|(\\?.*)?$)': proxyOptions,
      },
    },
    optimizeDeps: {
      include: ['@mui/material/Tooltip', '@emotion/styled'],
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            if (id.includes('lodash')) {
              return 'lodash';
            }

            if (id.includes('node_modules')) {
              return 'vendor';
            }
          },
        },
      },
    },
  };
});
