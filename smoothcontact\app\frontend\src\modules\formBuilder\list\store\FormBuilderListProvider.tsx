import { PER_PAGE } from '@/common/constants';
import { PaginationRes } from '@/common/dto/response';
import { IModalCommon } from '@/components/common/SCModalCommon';
import useAxios, { ApiResponse } from '@/hooks/useAxios';
import { IApplyBindResponseDto } from '@/modules/formBuilder/list/dto/response.dto';
import { useToast } from '@/provider/toastProvider';
import { completeTourRequestConfig } from '@/services/account.service';
import {
  createEmptyFormRequestConfig,
  deleteFormBuilderRequestConfig,
  duplicateFormBuilderRequestConfig,
  getAllRequestConfig,
  updateFormRequestConfig,
} from '@/services/form-builder.service';
import { useAppSelector } from '@/store/hook';
import { OrderRequest } from '@/types/app';
import { FormTemplateType, FormTemplateTypeContent } from '@/utils/formBuilderUtils';
import { generateID, getQueryParam, toQueryParams } from '@/utils/helper';
import React, { ReactNode, createContext, useContext, useEffect, useMemo, useState } from 'react';
import { STATUS } from 'react-joyride';
import { useNavigate } from 'react-router-dom';
import { FormBuilderFilterRequestDto } from '../dto/request.dto';

const initialFilter = {
  page: 1,
  perPage: PER_PAGE,
};

export const enum FilterStatusOptions {
  ALL = 'ALL',
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  PREPUBLISHED = 'PREPUBLISHED',
  EXPIRED = 'EXPIRED',
}

export const FilterStatusLabels: { [key in FilterStatusOptions]: string } = {
  [FilterStatusOptions.ALL]: 'すべて',
  [FilterStatusOptions.DRAFT]: '非公開',
  [FilterStatusOptions.PUBLISHED]: '公開中',
  [FilterStatusOptions.PREPUBLISHED]: '公開予約',
  [FilterStatusOptions.EXPIRED]: '公開終了',
};

interface FormBuilderListProviderProps {
  children: ReactNode;
  setOpenModal: (newModalCommon: IModalCommon) => void;
}

type FormBuilderStatistics = {
  draftCount: number;
  publishedCount: number;
  expiredCount: number;
  prePublishedCount: number;
};

export const tourSteps = [
  {
    name: 'step-create-form',
    target: '.step-create-form',
    content: 'クリックして、新しいフォームを作成します',
  },
  {
    name: 'step-create-from-template',
    target: '.step-create-from-template',
    content: 'テンプレートからフォームを作成します',
  },
  {
    name: 'step-handle-form',
    target: '.step-handle-form',
    content: 'ここで削除、共有、編集、レポートを見ることができる',
  },
  {
    name: 'step-handle-form-status',
    target: '.step-handle-form-status',
    content: 'ここでステータスを変更することができる',
  },
];

interface FormBuilderListContextType {
  data: any;
  statistics: FormBuilderStatistics;
  pagination: PaginationRes;
  profile: any;
  openDrawer: boolean;
  sharingModal: boolean;
  filterModal: boolean;
  scheduleModal: boolean;
  changeFormTypeModal: boolean;
  selectedRow: any;
  order: OrderRequest;
  orderBy: any;
  filter: FormBuilderFilterRequestDto;
  runTour: boolean;
  isFromBindUp: boolean;
  isFromOem: boolean;
  createEmptyForm: (templateName?: FormTemplateType, formMode?: string) => any;
  duplicateForm: (value?: any) => any;
  handleOpenDetailModal: (value?: any, isOpen?: boolean) => any;
  handleOpenShareModal: (value?: any, isOpen?: boolean) => any;
  setFilterModal: (value: boolean) => any;
  setScheduleModal: (value: boolean) => any;
  setChangeFormTypeModal: (value: boolean) => any;
  openConfirmDeleteForm: (value?: any) => any;
  handleChangePage: (event: unknown, newPage: number) => any;
  handleChangeRowsPerPage: (value: any) => any;
  handleSort: (event: React.MouseEvent<unknown>, property: string) => any;
  handleFilter: (status: FilterStatusOptions) => any;
  setFilter: (value: any) => any;
  getAllForms: (value: any) => any;
  saveForm: (value: any) => any;
  setOpenModal: (newModalCommon: IModalCommon) => void;
  setSelectedRow: (value: any) => void;
  handleTourCallback: (data: any) => void;
  applyToBindUp: (id: number) => any;
}

const FormBuilderListContext = createContext<FormBuilderListContextType | undefined>(undefined);

const FormBuilderListProvider: React.FC<FormBuilderListProviderProps> = ({ children, setOpenModal }) => {
  const navigate = useNavigate();
  const [data, setData] = useState<{ count: number; data: any[] }>(null);
  const [selectedRow, setSelectedRow] = useState(null);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [sharingModal, setSharingModal] = useState(false);
  const [filterModal, setFilterModal] = useState(false);
  const [scheduleModal, setScheduleModal] = useState(false);
  const [changeFormTypeModal, setChangeFormTypeModal] = useState(false);
  const [statistics, setStatistics] = useState<FormBuilderStatistics>({
    draftCount: 0,
    publishedCount: 0,
    expiredCount: 0,
    prePublishedCount: 0,
  });
  const [pagination, setPagination] = useState<PaginationRes>({
    page: 1,
    perPage: 5,
    total: 0,
  });
  const [order, setOrder] = useState<OrderRequest>(OrderRequest.ASC);
  const [orderBy, setOrderBy] = useState<string>('');
  const [filter, setFilter] = useState<FormBuilderFilterRequestDto>(initialFilter);
  const { profile } = useAppSelector((state) => ({
    profile: state.app.profile,
  }));
  const [runTour, setRunTour] = useState(false);
  const { toast } = useToast();
  const { apiCaller } = useAxios();

  useEffect(() => {
    if (profile && !profile?.tourCompleted) {
      setTimeout(() => {
        setRunTour(true);
      }, 2000);
    }
  }, [profile]);

  const isFromBindUp: boolean = useMemo(() => {
    return !!(profile?.callbackUrl && profile?.setting);
  }, [profile]);

  const isFromOem: boolean = useMemo(() => {
    return !!profile?.oemId;
  }, [profile]);

  const handleTourCallback = async (data: any) => {
    const { status } = data;

    if ([STATUS.FINISHED, STATUS.SKIPPED].includes(status)) {
      setRunTour(false);
      await apiCaller(completeTourRequestConfig(profile?.id));
    }
  };

  const applyToBindUp = async (id: number) => {
    const result = await apiCaller({
      url: `/form/list/applyBiND`,
      method: 'POST',
      data: { scFormId: id },
    });

    if (result.success) {
      const data = result.data as IApplyBindResponseDto;
      window.location.replace(data.url);
    }
  };

  const createEmptyForm = async (templateName: FormTemplateType = FormTemplateType.EMPTY_FORM, formMode: string = 'manual') => {
    try {
      const formTemplateInit = FormTemplateTypeContent[templateName];
      const formElements = formTemplateInit?.formElements?.map((formElement) => ({
        ...formElement,
        children: formElement?.children?.map?.((item) => ({ ...item, id: generateID(), containerId: formElement?.container?.id })),
      }));
      const formMailSetting = { ...formTemplateInit?.formMailSetting, emailAddress: [] as string[] };

      formTemplateInit.mode = formMode;

      const result: any = await apiCaller(createEmptyFormRequestConfig({ ...formTemplateInit, formElements, formMailSetting }));
      if (result?.success) {
        navigate(`/form-builder/edit/${result?.data?.extId}`);
      }
    } catch (e) {
      toast({ isError: true, message: 'エラーが発生しました' });
    }
  };

  const getAllForms = async (dto: FormBuilderFilterRequestDto): Promise<any> => {
    const result: any = await apiCaller(getAllRequestConfig(dto));

    if (!result?.success) {
      toast({ isError: true, message: result?.error });

      return;
    }

    const newFilter = {
      ...dto,
      page: result.data.pagination?.page,
      perPage: result.data.pagination?.perPage,
      status: dto?.status?.length ? dto?.status : undefined,
    };

    setData(result.data.items);
    setPagination(result.data.pagination);
    setStatistics(result.data.statistics);
    setFilter(newFilter);

    navigate(`/form-builder?${toQueryParams(newFilter)}`, {
      replace: true,
    });
  };

  const duplicateForm = async (extId: string): Promise<any> => {
    try {
      const result: any = await apiCaller(duplicateFormBuilderRequestConfig(extId));

      if (result?.success) {
        getAllForms(initialFilter);
      }
    } catch (e) {
      console.log(e);
    }
  };

  const deleteForm = async (extId: string): Promise<any> => {
    try {
      const result: any = await apiCaller(deleteFormBuilderRequestConfig(extId));

      if (!result?.success) {
        toast({ isError: true, message: result?.message });
      }

      await getAllForms(initialFilter);

      toast({ isError: false, message: '削除が成功しました' });
    } catch (e) {
      console.log(e);
    }
  };

  const saveForm = async (saveRequest: any, message?: string) => {
    const { success, error }: ApiResponse<any> = await apiCaller(updateFormRequestConfig(saveRequest));

    if (!success) {
      toast({
        isError: true,
        message: error || '保存に失敗しました。',
      });

      return false;
    }

    setScheduleModal(false);
    getAllForms({ ...filter });

    toast({
      isError: false,
      message: message ?? '保存が成功しました。',
    });

    return true;
  };

  const handleOpenDetailModal = (row: any, isOpen: boolean) => {
    if (isOpen) {
      setSelectedRow(row);
      setOpenDrawer(true);
    } else {
      setSelectedRow(null);
      setOpenDrawer(false);
    }
  };

  const handleOpenShareModal = (row: any, isOpen: boolean) => {
    if (isOpen) {
      setSelectedRow(row);
      setSharingModal(true);
    } else {
      setSelectedRow(null);
      setSharingModal(false);
    }
  };

  const openConfirmDeleteForm = (item: any) => {
    setOpenModal({
      open: true,
      title: 'フォームを削除しますか？',
      subtitle: '選択したフォームを削除します。この操作は元に戻せません。',
      onConfirm: async () => {
        await deleteForm(item.extId);
      },
      cancelText: 'キャンセル',
      confirmText: 'フォームを削除',
    });
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    getAllForms({ ...filter, page: newPage + 1 });
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    getAllForms({ ...filter, page: 1, perPage: parseInt(event.target.value) });
  };

  const handleSort = (event: React.MouseEvent<unknown>, property: string) => {
    const isAsc = orderBy === property && order === OrderRequest.ASC;
    setOrder(isAsc ? OrderRequest.DESC : OrderRequest.ASC);
    setOrderBy(property);

    getAllForms({ ...filter, order: isAsc ? OrderRequest.DESC : OrderRequest.ASC, orderBy: property });
  };

  const handleFilter = (status: FilterStatusOptions) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getAllForms({ ...filter, page: 1, status: [status] });
  };

  useEffect(() => {
    const page = getQueryParam('page') ? Number(getQueryParam('page')) : initialFilter?.page;
    const perPage = getQueryParam('perPage') ? Number(getQueryParam('perPage')) : initialFilter?.perPage;
    const status = getQueryParam('status') ? (getQueryParam('status') ?? undefined) : undefined;

    getAllForms({
      ...initialFilter,
      page,
      perPage,
      status:
        status === '' || !status
          ? undefined
          : (status
              .split(',')
              ?.filter((v: FilterStatusOptions) =>
                [
                  FilterStatusOptions.ALL,
                  FilterStatusOptions.DRAFT,
                  FilterStatusOptions.PREPUBLISHED,
                  FilterStatusOptions.PUBLISHED,
                  FilterStatusOptions.EXPIRED,
                ].includes(v)
              ) as FilterStatusOptions[]),
    });
  }, []);

  return (
    <FormBuilderListContext.Provider
      value={{
        data,
        statistics,
        pagination,
        profile,
        openDrawer,
        sharingModal,
        filterModal,
        scheduleModal,
        changeFormTypeModal,
        selectedRow,
        order,
        orderBy,
        filter,
        runTour,
        isFromBindUp,
        isFromOem,
        createEmptyForm,
        duplicateForm,
        handleOpenDetailModal,
        handleOpenShareModal,
        setFilterModal,
        setScheduleModal,
        setChangeFormTypeModal,
        openConfirmDeleteForm,
        handleChangePage,
        handleChangeRowsPerPage,
        handleSort,
        handleFilter,
        setFilter,
        getAllForms,
        saveForm,
        setOpenModal,
        setSelectedRow,
        handleTourCallback,
        applyToBindUp,
      }}
    >
      {children}
    </FormBuilderListContext.Provider>
  );
};

function useFormBuilderList() {
  const context = useContext(FormBuilderListContext);
  if (!context) {
    throw new Error('useFormBuilderList must be used within a FormBuilderListProvider');
  }

  return context;
}

export { FormBuilderListProvider, useFormBuilderList };
