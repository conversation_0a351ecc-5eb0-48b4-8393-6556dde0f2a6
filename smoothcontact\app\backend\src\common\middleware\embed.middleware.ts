import { Injectable, NestMiddleware, Req } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';

import { StaticService } from '@/modules/static/static.service';

@Injectable()
export class EmbedMiddleware implements NestMiddleware {
  constructor(private readonly staticService: StaticService) {}
  async use(@Req() req: Request, res: Response, next: NextFunction) {
    const extId = req.params?.id || '';
    const referer = req.headers.referer;
    if (!referer || referer.includes(req.headers.host)) {
      return next();
    }

    // if referer is subdomain of bindcloud.jp..., allow
    const internalDomain = ['bindcloud.jp', 'blks.jp'];
    if (internalDomain.some((domain) => referer?.includes(domain))) {
      return next();
    }

    try {
      const { formGeneralSetting } = await this.staticService.getFormBuilderByExtId(extId);
      // whitelist is an array of domain names ['example.com', 'example2.com']
      const whitelist = formGeneralSetting?.whitelistedDomain ?? [];
      const isWhitelisted = whitelist.some((domain) => referer?.includes(domain));
      if (whitelist.length > 0 && !isWhitelisted) {
        return res.status(403).send('Not allowed');
      }
    } catch (e) {
      return res.status(404).send('');
    }

    next();
  }
}
