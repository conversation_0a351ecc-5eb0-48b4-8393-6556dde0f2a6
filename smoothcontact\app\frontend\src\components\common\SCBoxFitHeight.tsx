import React, { useEffect, useRef, useState } from 'react';
import { Box, BoxProps } from '@mui/material';

interface SCBlockFitHeightProps extends BoxProps {
  offsetBottom?: number;
  onContentHeightChange?: (height: number) => void;
}

const SCBlockFitHeight: React.FC<SCBlockFitHeightProps> = ({ offsetBottom = 10, onContentHeightChange = () => {}, ...props }) => {
  const ref = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState<string | undefined>(undefined);

  useEffect(() => {
    const handleResize = () => {
      if (ref.current) {
        const offsetTop = ref.current.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;
        setHeight(`${windowHeight - offsetTop - offsetBottom}px`);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [offsetBottom]);

  useEffect(() => {
    if (onContentHeightChange) {
      onContentHeightChange(parseInt(height || '0'));
    }
  }, [height, onContentHeightChange]);

  return <Box ref={ref} {...props} style={{ ...props.style, height }} />;
};

export default SCBlockFitHeight;
