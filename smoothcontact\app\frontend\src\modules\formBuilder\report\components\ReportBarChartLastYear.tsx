import React, { FC, useEffect, useState } from 'react';
import { BarChart } from '@mui/x-charts/BarChart';
import { axisClasses } from '@mui/x-charts/ChartsAxis';

interface ReportBarChartLastYearProps {
  chartData: Record<string, number>;
}

interface DatasetEntry {
  date: string;
  submissionsTotal: number;
}

const generateLastYearData = (): DatasetEntry[] => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;

  let year = currentYear;
  let month = currentMonth - 11;

  if (month <= 0) {
    month += 12;
    year -= 1;
  }

  const data: DatasetEntry[] = [];

  for (let i = 0; i < 12; i++) {
    const date = `${year}-${String(month).padStart(2, '0')}`;
    data.push({ date, submissionsTotal: 0 });
    month++;
    if (month > 12) {
      month = 1;
      year++;
    }
  }

  return data;
};

const chartSettings = {
  legend: {
    hidden: true,
  },
  series: [{ dataKey: 'submissionsTotal', label: '' }],
  height: 400,
  borderRadius: 10,
  sx: {
    [`.${axisClasses.left} .${axisClasses.label}`]: {
      transform: 'translate(-5px, 0)',
    },
  },
  yAxis: [
    {
      min: 0,
      tickMinStep: 1,
    },
  ],
};

const ReportBarChartLastYear: FC<ReportBarChartLastYearProps> = ({ chartData }) => {
  const [dataset, setDataset] = useState<DatasetEntry[]>([]);

  useEffect(() => {
    const data = generateLastYearData().map(({ date }) => ({
      date,
      submissionsTotal: chartData[date] || 0,
    }));
    setDataset(data);
  }, [chartData]);

  return (
    <BarChart
      disableAxisListener
      skipAnimation
      dataset={dataset as any}
      xAxis={[
        {
          scaleType: 'band',
          dataKey: 'date',
          categoryGapRatio: 0.4,
          colorMap: {
            type: 'piecewise',
            thresholds: [new Date(2024, 1, 1), new Date(2050, 1, 1)],
            colors: ['#24CBD4'],
          },
        } as any,
      ]}
      {...chartSettings}
    />
  );
};

export default ReportBarChartLastYear;
