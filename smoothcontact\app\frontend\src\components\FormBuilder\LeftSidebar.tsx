import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormElementChildrenType } from '@/types/FormTemplateTypes';
import { FormCategory, FormCategoryText, FormControlList } from '@/utils/formBuilderUtils';
import { Box, Stack, Typography } from '@mui/material';
import { FunctionComponent } from 'react';
import { Droppable } from 'react-beautiful-dnd';
import FormItemButton from '../common/FormItemButton';
import ControlDragComponent from './subcomponents/ControlDragComponent';

interface LeftSidebarProps {
  allowDrag?: boolean;
}

const LeftSidebar: FunctionComponent<LeftSidebarProps> = (props) => {
  const enumKeys = Object.keys(FormCategory).filter((key) => isNaN(Number(key)));
  const { handleItemAdded, selectedParentControl, selectedTemplate, setSelectedParentControl } = useFormBuilder();
  const handleOnClick = (item: FormElementChildrenType, hoverIndex: number) => {
    handleItemAdded(
      {
        ...item,
        containerId: selectedParentControl.containerId,
        parentId: selectedParentControl?.id,
        level: (selectedParentControl?.level ?? 0) + 1,
      },
      selectedParentControl.containerId,
      hoverIndex,
      false
    );
    setSelectedParentControl?.(null);
  };

  const renderConditionControl = () => {
    return (
      <Stack direction="column" spacing={2}>
        <Typography variant="h5">要素を追加</Typography>
        {enumKeys?.map?.((key) => (
          <Box key={key}>
            <Typography component="h6" sx={{ mb: 2 }}>
              {FormCategoryText[key]}
            </Typography>
            <Stack direction="row" justifyContent="flex-start" gap={1} alignItems="baseline" flexWrap="wrap">
              {FormControlList?.filter?.((control) => control.category === key)?.map?.((control) => {
                return (
                  <Box key={control.controlName}>
                    <FormItemButton
                      text={control.displayText}
                      icon={control.icon}
                      onClick={(e) => {
                        e.preventDefault();
                        handleOnClick(
                          control as FormElementChildrenType,
                          selectedTemplate?.formElements?.[0]?.children?.findIndex((control) => control.id === selectedParentControl.id) + 1
                        );
                      }}
                      allowDrag={props.allowDrag}
                    />
                  </Box>
                );
              })}
            </Stack>
          </Box>
        ))}
      </Stack>
    );
  };

  const renderDraggableControl = () => {
    return (
      <>
        <Droppable droppableId="controls_droppable" type="controls" isDropDisabled={true}>
          {(provided) => (
            <Stack direction="column" spacing={2}>
              <Typography variant="h5">要素を追加</Typography>
              {enumKeys?.map?.((key) => (
                <Box key={key}>
                  <Typography component="h6">{FormCategoryText[key]}</Typography>
                  <Stack
                    direction="row"
                    justifyContent="flex-start"
                    gap={1}
                    alignItems="baseline"
                    flexWrap="wrap"
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    position={'absolute'}
                  >
                    {FormControlList?.filter?.((control) => control.category === key)?.map?.((control, index) => {
                      return <ControlDragComponent key={control.controlName} item={control as FormElementChildrenType} index={index} />;
                    })}
                  </Stack>
                </Box>
              ))}
              {provided.placeholder}
            </Stack>
          )}
        </Droppable>
      </>
    );
  };

  return <>{!props.allowDrag ? renderConditionControl() : renderDraggableControl()}</>;
};

export default LeftSidebar;
