import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateFormBuilder1716190545771 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}form_builder`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    const templateIdColumn: TableColumn = new TableColumn({
      name: 'template_id',
      type: 'integer',
      isNullable: true,
    });
    await queryRunner.addColumn(this.TABLE_NAME, templateIdColumn);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn(this.TABLE_NAME, 'template_id');
  }
}
