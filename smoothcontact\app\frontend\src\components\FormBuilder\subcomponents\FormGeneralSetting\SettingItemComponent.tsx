import SCChip from '@/components/common/SCChip';
import { SettingFlag } from '@/utils/formBuilderUtils';
import { Box, Typography } from '@mui/material';
import { FC } from 'react';

interface SettingItemComponentProps {
  label: string;
  isEnable: boolean;
  description?: string;
}

const SettingItemComponent: FC<SettingItemComponentProps> = ({ label, isEnable, description }) => {
  const chipLabel = isEnable ? SettingFlag.enabled : SettingFlag.disabled;
  const chipColor = isEnable ? 'success' : 'default';

  return (
    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'left', alignItems: 'center', mb: 1 }}>
      <Typography color="text.secondary">{label}</Typography>
      <SCChip label={chipLabel} size="small" color={chipColor} />
      {description && <Typography color="text.secondary">{description}</Typography>}
    </Box>
  );
};

export default SettingItemComponent;
