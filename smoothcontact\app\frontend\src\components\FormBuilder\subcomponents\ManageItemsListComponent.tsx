import { TextField } from '@mui/material';
import { FormikValues } from 'formik';
import React, { FC, useEffect, useState } from 'react';

interface ManageItemsListProps {
  form: FormikValues;
  name: string;
  helperText?: string;
}

const ManageItemsList: FC<ManageItemsListProps> = ({ name, form, helperText }) => {
  const [itemValues, setItemValues] = useState('');

  useEffect(() => {
    if (!form?.values?.items || !form?.values?.items.length) {
      setItemValues('');
    } else {
      setItemValues(form?.values?.items?.map?.((item: any) => item.value).join('\n'));
    }
  }, [form.values]);

  const handleTextareaChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setItemValues(event.target.value);
  };

  const handleTextareaBlur = () => {
    const trimmedValue = itemValues
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line !== '');
    setItemValues(trimmedValue.join('\n'));

    updateFormItems();
  };

  const updateFormItems = () => {
    const trimmedValue = itemValues.split('\n');

    form.setFieldValue(
      name,
      trimmedValue.map?.((value) => ({ label: value, value }))
    );
  };

  useEffect(() => {
    updateFormItems();
  }, [itemValues]);

  return (
    <TextField
      value={itemValues}
      placeholder="選択肢を1行に1つずつ入力"
      multiline
      rows={4}
      onChange={handleTextareaChange}
      onBlur={handleTextareaBlur}
      helperText={helperText}
    />
  );
};

export default ManageItemsList;
