import { Expose, Transform } from 'class-transformer';
import { <PERSON>N<PERSON><PERSON>, IsOptional, <PERSON>, <PERSON> } from 'class-validator';
import { toNumber } from 'lodash';

const MAX_PER_PAGE = 20;
const MIN_PER_PAGE = 5;

export class PaginationRequestDto {
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10000)
  @Expose()
  @Transform(({ value }) => (toNumber(value) > MAX_PER_PAGE ? MAX_PER_PAGE : value || MIN_PER_PAGE))
  perPage?: number = MIN_PER_PAGE;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Expose()
  page?: number = 1;

  getSkip() {
    return (toNumber(this.page) - 1) * toNumber(this.perPage);
  }
}
