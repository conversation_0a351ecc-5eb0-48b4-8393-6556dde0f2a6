import { Outlet, Navigate, useLocation } from 'react-router-dom';
import { getAppAccessToken } from '@/utils/helper';

const PrivateRoute = () => {
  const accessToken = getAppAccessToken();
  const location = useLocation();
  const redirectUrl = encodeURIComponent(location.pathname + location.search);

  const isAuthenticated = !!accessToken;

  if (!isAuthenticated) {
    return <Navigate to={`/login?redirect=${redirectUrl}`} replace />;
  }

  return <Outlet />;
};

export default PrivateRoute;
