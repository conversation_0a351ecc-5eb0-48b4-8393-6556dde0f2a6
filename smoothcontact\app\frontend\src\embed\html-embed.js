import dayjs from 'dayjs';
import styles from './html-embed.css';

const prefectures = [
  {
    iso: '1',
    prefecture_kanji: '北海道',
    prefecture_romaji: 'Hokkaido',
    prefecture_kana: 'ほっかいどう',
  },
  {
    iso: '2',
    prefecture_kanji: '青森県',
    prefecture_romaji: 'Aomori Ken',
    prefecture_kana: 'あおもりけん',
  },
  {
    iso: '3',
    prefecture_kanji: '岩手県',
    prefecture_romaji: 'Iwate Ken',
    prefecture_kana: 'いわてけん',
  },
  {
    iso: '4',
    prefecture_kanji: '宮城県',
    prefecture_romaji: 'Miyagi Ken',
    prefecture_kana: 'みやぎけん',
  },
  {
    iso: '5',
    prefecture_kanji: '秋田県',
    prefecture_romaji: 'Akita Ken',
    prefecture_kana: 'あきたけん',
  },
  {
    iso: '6',
    prefecture_kanji: '山形県',
    prefecture_romaji: 'Yamagata Ken',
    prefecture_kana: 'やまがたけん',
  },
  {
    iso: '7',
    prefecture_kanji: '福島県',
    prefecture_romaji: 'Fukushima Ken',
    prefecture_kana: 'ふくしまけん',
  },
  {
    iso: '8',
    prefecture_kanji: '茨城県',
    prefecture_romaji: 'I<PERSON><PERSON> Ken',
    prefecture_kana: 'いばらきけん',
  },
  {
    iso: '9',
    prefecture_kanji: '栃木県',
    prefecture_romaji: 'Tochigi Ken',
    prefecture_kana: 'とちぎけん',
  },
  {
    iso: '10',
    prefecture_kanji: '群馬県',
    prefecture_romaji: 'Gunma Ken',
    prefecture_kana: 'ぐんまけん',
  },
  {
    iso: '11',
    prefecture_kanji: '埼玉県',
    prefecture_romaji: 'Saitama Ken',
    prefecture_kana: 'さいたまけん',
  },
  {
    iso: '12',
    prefecture_kanji: '千葉県',
    prefecture_romaji: 'Chiba Ken',
    prefecture_kana: 'ちばけん',
  },
  {
    iso: '13',
    prefecture_kanji: '東京都',
    prefecture_romaji: 'Tokyo To',
    prefecture_kana: 'とうきょうと',
  },
  {
    iso: '14',
    prefecture_kanji: '神奈川県',
    prefecture_romaji: 'Kanagawa Ken',
    prefecture_kana: 'かながわけん',
  },
  {
    iso: '15',
    prefecture_kanji: '新潟県',
    prefecture_romaji: 'Niigata Ken',
    prefecture_kana: 'にいがたけん',
  },
  {
    iso: '16',
    prefecture_kanji: '富山県',
    prefecture_romaji: 'Toyama Ken',
    prefecture_kana: 'とやまけん',
  },
  {
    iso: '17',
    prefecture_kanji: '石川県',
    prefecture_romaji: 'Ishikawa Ken',
    prefecture_kana: 'いしかわけん',
  },
  {
    iso: '18',
    prefecture_kanji: '福井県',
    prefecture_romaji: 'Fukui Ken',
    prefecture_kana: 'ふくいけん',
  },
  {
    iso: '19',
    prefecture_kanji: '山梨県',
    prefecture_romaji: 'Yamanashi Ken',
    prefecture_kana: 'やまなしけん',
  },
  {
    iso: '20',
    prefecture_kanji: '長野県',
    prefecture_romaji: 'Nagano Ken',
    prefecture_kana: 'ながのけん',
  },
  {
    iso: '21',
    prefecture_kanji: '岐阜県',
    prefecture_romaji: 'Gifu Ken',
    prefecture_kana: 'ぎふけん',
  },
  {
    iso: '22',
    prefecture_kanji: '静岡県',
    prefecture_romaji: 'Shizuoka Ken',
    prefecture_kana: 'しずおかけん',
  },
  {
    iso: '23',
    prefecture_kanji: '愛知県',
    prefecture_romaji: 'Aichi Ken',
    prefecture_kana: 'あいちけん',
  },
  {
    iso: '24',
    prefecture_kanji: '三重県',
    prefecture_romaji: 'Mie Ken',
    prefecture_kana: 'みえけん',
  },
  {
    iso: '25',
    prefecture_kanji: '滋賀県',
    prefecture_romaji: 'Shiga Ken',
    prefecture_kana: 'しがけん',
  },
  {
    iso: '26',
    prefecture_kanji: '京都府',
    prefecture_romaji: 'Kyoto Fu',
    prefecture_kana: 'きょうとふ',
  },
  {
    iso: '27',
    prefecture_kanji: '大阪府',
    prefecture_romaji: 'Osaka Fu',
    prefecture_kana: 'おおさかふ',
  },
  {
    iso: '28',
    prefecture_kanji: '兵庫県',
    prefecture_romaji: 'Hyōgo Ken',
    prefecture_kana: 'ひょうごけん',
  },
  {
    iso: '29',
    prefecture_kanji: '奈良県',
    prefecture_romaji: 'Nara Ken',
    prefecture_kana: 'ならけん',
  },
  {
    iso: '30',
    prefecture_kanji: '和歌山県',
    prefecture_romaji: 'Wakayama Ken',
    prefecture_kana: 'わかやまけん',
  },
  {
    iso: '31',
    prefecture_kanji: '鳥取県',
    prefecture_romaji: 'Tottori Ken',
    prefecture_kana: 'とっとりけん',
  },
  {
    iso: '32',
    prefecture_kanji: '島根県',
    prefecture_romaji: 'Shimane Ken',
    prefecture_kana: 'しまねけん',
  },
  {
    iso: '33',
    prefecture_kanji: '岡山県',
    prefecture_romaji: 'Okayama Ken',
    prefecture_kana: 'おかやまけん',
  },
  {
    iso: '34',
    prefecture_kanji: '広島県',
    prefecture_romaji: 'Hiroshima Ken',
    prefecture_kana: 'ひろしまけん',
  },
  {
    iso: '35',
    prefecture_kanji: '山口県',
    prefecture_romaji: 'Yamaguchi Ken',
    prefecture_kana: 'やまぐちけん',
  },
  {
    iso: '36',
    prefecture_kanji: '徳島県',
    prefecture_romaji: 'Tokushima Ken',
    prefecture_kana: 'とくしまけん',
  },
  {
    iso: '37',
    prefecture_kanji: '香川県',
    prefecture_romaji: 'Kagawa Ken',
    prefecture_kana: 'かがわけん',
  },
  {
    iso: '38',
    prefecture_kanji: '愛媛県',
    prefecture_romaji: 'Ehime Ken',
    prefecture_kana: 'えひめけん',
  },
  {
    iso: '39',
    prefecture_kanji: '高知県',
    prefecture_romaji: 'Kōchi Ken',
    prefecture_kana: 'こうちけん',
  },
  {
    iso: '40',
    prefecture_kanji: '福岡県',
    prefecture_romaji: 'Fukuoka Ken',
    prefecture_kana: 'ふくおかけん',
  },
  {
    iso: '41',
    prefecture_kanji: '佐賀県',
    prefecture_romaji: 'Saga Ken',
    prefecture_kana: 'さがけん',
  },
  {
    iso: '42',
    prefecture_kanji: '長崎県',
    prefecture_romaji: 'Nagasaki Ken',
    prefecture_kana: 'ながさきけん',
  },
  {
    iso: '43',
    prefecture_kanji: '熊本県',
    prefecture_romaji: 'Kumamoto Ken',
    prefecture_kana: 'くまもとけん',
  },
  {
    iso: '44',
    prefecture_kanji: '大分県',
    prefecture_romaji: 'Ōita Ken',
    prefecture_kana: 'おおいたけん',
  },
  {
    iso: '45',
    prefecture_kanji: '宮崎県',
    prefecture_romaji: 'Miyazaki Ken',
    prefecture_kana: 'みやざきけん',
  },
  {
    iso: '46',
    prefecture_kanji: '鹿児島県',
    prefecture_romaji: 'Kagoshima Ken',
    prefecture_kana: 'かごしまけん',
  },
  {
    iso: '47',
    prefecture_kanji: '沖縄県',
    prefecture_romaji: 'Okinawa Ken',
    prefecture_kana: 'おきなわけん',
  },
];

const FormType = {
  MANUAL: 'manual',
  HTML: 'dev',
};

const FORM_STATUS = {
  INIT: 'INIT',
  CONFIRMED: 'CONFIRMED',
  SUBMITTED: 'SUBMITTED',
};

const DATA_SC_ELEMENT_TYPE = {
  EMAIL: 'email',
  EMAIL_CONFIRMATION: 'email_confirmation',
  PHONE: 'phone',
  NUMBER: 'number',
  TEXT: 'text',
  CHECKBOX: 'checkbox',
  DATE: 'date',
  POSTAL_CODE: 'postal_code',
  PREFECTURE: 'prefecture',
  REGION: 'region',
  LOCALITY: 'locality',
  STREET_ADDRESS: 'street_address',
  FILE: 'file',
  FULL_NAME: 'full_name',
};

const DATA_SC_SELECTOR = {
  EMAIL: '[data-sc-type="email"]',
  EMAIL_CONFIRMATION: '[data-sc-type="email_confirmation"]',
  PHONE: '[data-sc-type="tel"]',
  NUMBER: '[data-sc-type="number"]',
  TEXT: '[data-sc-type="text"]',
  CHECKBOX: 'input[type="checkbox"]',
  DATE: 'input[type="date"]',
  POSTAL_CODE: '[data-sc-type="postal_code"]',
  PREFECTURE: '[data-sc-type="prefecture"]',
  REGION: '[data-sc-type="region"]',
  LOCALITY: '[data-sc-type="locality"]',
  STREET_ADDRESS: '[data-sc-type="street_address"]',
  LOCALITY_AND_STREET_ADDRESS: '[data-sc-type="locality street_address"]',
  FILE: 'input[type="file"]',
  FULL_NAME: '[data-sc-type="full_name"]',
};

const DATA_SC_ATTRIBUTE = {
  FORM_CONFIRM: 'data-sc-confirm',
  FORM_CONFIRMED: 'data-sc-confirmed',
  FORM_SUBMITTED: 'data-sc-submitted',
  SUBMIT_ERROR_TEXT: 'data-sc-error-text',
  SUBMIT_CONFIRMED_TEXT: 'data-sc-confirm-text',
  SUBMIT_DEFAULT_TEXT: 'data-sc-default-text',
  SUBMITTED_TEXT: 'data-sc-submitted-text',
  SHOW_IF_SUCCESS: 'data-sc-show-if-success',
  SHOW_IF_ERROR: 'data-sc-show-if-error',
  REQUIRED: 'data-sc-required',
  MIN_CHARS: 'data-sc-chars-min',
  MAX_CHARS: 'data-sc-chars-max',
  MIN: 'data-sc-min',
  MAX: 'data-sc-max',
  SHOW_IF_FORM_UNAVAILABLE: 'data-sc-show-if-form-unavailable',
  HIDE_IF_FORM_UNAVAILABLE: 'data-sc-hide-if-form-unavailable',
  SHOW_THANK_YOU_MESSAGE: 'data-sc-show-thank-you-message',
  DATA_SHOW_IF_CONFIRM: 'data-sc-show-if-confirm',
  DATA_CONFIRM_VALUE: 'data-sc-confirm-value',
  SHOW_API_ERROR: 'data-sc-show-api-error',
};

const DATA_SC_CLASS = {
  SYSTEM_SHOW: 'sc-system-show',
  SYSTEM_HIDE: 'sc-system-hide',
  SYSTEM_CONFIRM: 'sc-system-confirm',
  SYSTEM_SUBMIT: 'sc-system-submit',
  HAS_SUCCESS: 'sc-has-success',
  HAS_ERROR: 'sc-has-error',
};

const FORM_STATUS_API = {
  OK: 'ok',
  NG: 'ng',
};

const APP_URL = import.meta.env.VITE_APP_URL;

class SmoothContactHtmlEmbed {
  constructor() {
    this.formData = { status: FORM_STATUS_API.NG, message: '', data: null };
    this.initialize();
  }

  initialize() {
    const styleSheet = document.createElement('style');
    styleSheet.type = 'text/css';
    styleSheet.innerText = styles;
    document.head.appendChild(styleSheet);

    // init variables
    this.recaptchaTokenV3 = null;

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', this.renderHtmlEmbeds.bind(this), false);
    } else {
      this.renderHtmlEmbeds();
    }
  }

  hasClass = (el, className) => {
    if (el?.classList) return el?.classList?.contains?.(className);

    return !!el?.className?.match?.(new RegExp('(\\s|^)' + className + '(\\s|$)'));
  };

  addClass = (el, className) => {
    if (el?.classList) el?.classList?.add?.(className);
    else if (!this.hasClass(el, className)) el.className += ' ' + className;
  };

  removeClass = (el, className) => {
    if (el?.classList) el?.classList?.remove?.(className);
    else if (this.hasClass(el, className)) {
      var reg = new RegExp('(\\s|^)' + className + '(\\s|$)');
      el.className = el.className.replace(reg, ' ');
    }
  };

  removeNonKana = (text) => {
    const kanaRegex = /[\u3040-\u309F\u30A0-\u30FF]/g;

    return text.match(kanaRegex)?.join('') || '';
  };

  convertKatakanaToHiragana = (text) => {
    return text.replace(/[\u30A1-\u30F6]/g, (match) => {
      const chr = match.charCodeAt(0) - 0x60;

      return String.fromCharCode(chr);
    });
  };

  convertHiraganaToKatakana = (text) => {
    return text.replace(/[\u3041-\u3096]/g, (match) => {
      const chr = match.charCodeAt(0) + 0x60;

      return String.fromCharCode(chr);
    });
  };

  handleErrorDisplay = (form, field, showError) => {
    if (!field) return;

    const fieldName = field.getAttribute('name');
    const classToAdd = showError ? `${DATA_SC_CLASS.HAS_ERROR}` : `${DATA_SC_CLASS.HAS_SUCCESS}`;
    const classToRemove = showError ? `${DATA_SC_CLASS.HAS_SUCCESS}` : `${DATA_SC_CLASS.HAS_ERROR}`;

    this.addClass(field, classToAdd);
    this.removeClass(field, classToRemove);

    const errorMessageElement = form.querySelector(`[${DATA_SC_ATTRIBUTE.SHOW_IF_ERROR}="${fieldName}"]`);

    if (errorMessageElement) {
      showError
        ? this.addClass(errorMessageElement, `${DATA_SC_CLASS.SYSTEM_SHOW}`)
        : this.removeClass(errorMessageElement, `${DATA_SC_CLASS.SYSTEM_SHOW}`);
    }
  };

  validateRequiredFields = (form) => {
    const requiredFields = form.querySelectorAll('[data-sc-required]');

    let isValid = true;

    requiredFields.forEach((field) => {
      const showError = !this.validateFormElement(field);

      if (showError) isValid = false;
    });

    return isValid;
  };

  validateEmailFields = (form) => {
    const emailFields = form.querySelectorAll(DATA_SC_SELECTOR.EMAIL);
    let isValid = true;
    emailFields.forEach((field) => {
      const showError = !this.validateFormElement(field);
      if (showError) isValid = false;
    });

    return isValid;
  };

  validateConfirmedEmailFields = (form) => {
    const confirmedEmailFields = form.querySelectorAll(DATA_SC_SELECTOR.EMAIL_CONFIRMATION);
    let isValid = true;

    confirmedEmailFields.forEach((field) => {
      const showError = !this.validateFormElement(field);
      if (showError) isValid = false;
    });

    return isValid;
  };

  validatePhoneFields = (form) => {
    const phoneFields = form.querySelectorAll('[data-sc-type="phone"]');
    let isValid = true;

    phoneFields.forEach((field) => {
      const showError = !this.validateFormElement(field);
      if (showError) isValid = false;
    });

    return isValid;
  };

  validateNumberFields = (form) => {
    const numberFields = form.querySelectorAll('[data-sc-type="number"]');
    let isValid = true;

    numberFields.forEach((field) => {
      const showError = !this.validateFormElement(field);
      if (showError) isValid = false;
    });

    return isValid;
  };

  validateTextFields = (form) => {
    const textFields = form.querySelectorAll('[data-sc-type="text"]');
    let isValid = true;

    textFields.forEach((field) => {
      const showError = !this.validateFormElement(field);
      if (showError) isValid = false;
    });

    return isValid;
  };

  validateCheckboxGroups = (form) => {
    const checkboxGroups = form.querySelectorAll('input[type="checkbox"]');
    let isValid = true;

    checkboxGroups.forEach((field) => {
      const showError = !this.validateFormElement(field);
      if (showError) isValid = false;
    });

    return isValid;
  };

  validateRadioGroups = (form) => {
    const radioGroups = form.querySelectorAll('input[type="radio"]');
    let isValid = true;

    radioGroups.forEach((field) => {
      const showError = !this.validateFormElement(field);
      if (showError) isValid = false;
    });

    return isValid;
  };

  validateDateFields = (form) => {
    const dateFields = form.querySelectorAll('input[type="date"]');
    let isValid = true;
    dateFields.forEach((field) => {
      const showError = !this.validateFormElement(field);
      if (showError) isValid = false;
    });

    return isValid;
  };

  validatePostalCodeFields = (form) => {
    const postalCodeFields = form.querySelectorAll('[data-sc-type="postal_code"]');
    let isValid = true;
    postalCodeFields.forEach((field) => {
      const showError = !this.validateFormElement(field);
      if (showError) isValid = false;
    });

    return isValid;
  };

  handlePostCodeChange = async (event) => {
    const element = event.target;
    const form = element?.form;
    const postalCode = event.target.value;
    if (/^[0-9]{3}-?[0-9]{4}$/.test(postalCode)) {
      try {
        const response = await fetch(`https://zipcloud.ibsnet.co.jp/api/search?zipcode=${postalCode}`);

        if (response.ok) {
          const { results } = await response.json();
          const prefectureField = form?.querySelector(DATA_SC_SELECTOR.PREFECTURE);
          const regionField = form?.querySelector(DATA_SC_SELECTOR.REGION);
          const localityField = form?.querySelector(DATA_SC_ATTRIBUTE.LOCALITY);
          const locallyAndStreetAddressField = form?.querySelector(DATA_SC_SELECTOR.LOCALITY_AND_STREET_ADDRESS);

          if (prefectureField) {
            prefectureField.value = results?.[0]?.address1 || '';
            this.updateConfirmFormElement(prefectureField);
          }

          if (regionField) {
            regionField.value = results?.[0]?.address2 || '';
            this.updateConfirmFormElement(regionField);
          }

          if (localityField) {
            localityField.value = results?.[0]?.address3 || '';
            this.updateConfirmFormElement(localityField);
          }

          if (locallyAndStreetAddressField) locallyAndStreetAddressField.value = `${results?.[0]?.address3}` || '';

          this.updateConfirmFormElement(element);
        } else {
          console.error('Failed to fetch address information');
        }
      } catch (error) {
        console.error('Error fetching address information:', error);
      }
    }
  };

  // eslint-disable-next-line prettier/prettier
  handleFullNameChange = async () => {};

  handleEmailConfirm = (event) => {
    const element = event.target;
    const form = element.form;
    const confirmedEmailField = form.querySelector(`[data-sc-confirmed-name="${element.name}"]`);

    if (confirmedEmailField) {
      this.validateFormElement(confirmedEmailField);
    }
  };

  validateFormElement = (element) => {
    let isValid = true;
    const form = element.form;
    const isRequired = element?.dataset?.scRequired !== undefined;
    const type = element?.dataset?.scType ?? '';

    if (isRequired) {
      if (element.type === 'checkbox' || element.type === 'radio') {
        const checkboxGroup = form.querySelectorAll(`input[name="${element.name}"]`);
        const isChecked = Array.from(checkboxGroup).some((cb) => cb.checked);

        if (!isChecked) {
          this.handleErrorDisplay(form, element, true);

          return false;
        }
      }

      if (!element.value.trim()) {
        this.handleErrorDisplay(form, element, true);

        return false;
      }
    }

    if (!element.value) {
      return true;
    }

    switch (type) {
      case 'email':
        const emailRegex = /^(?!.*\.\.@)(?!.*\.\.)[+\w\-_][+\w\-._]*@[\w\-._]+\.[A-Za-z]+$/;
        isValid = emailRegex.test(element.value);
        break;

      case 'email_confirmation':
        const confirmedName = element.getAttribute('data-sc-confirmed-name');
        const originalField = form.querySelector(`[name="${confirmedName}"]`);
        isValid = element?.value === originalField?.value;
        break;

      case 'text':

      case 'textarea':
        const minChar = element.getAttribute(DATA_SC_ATTRIBUTE.MIN_CHARS) ?? '';
        const maxChar = element.getAttribute(DATA_SC_ATTRIBUTE.MAX_CHARS) ?? '';

        if (minChar !== '' && maxChar !== '') {
          isValid = element.value.length >= parseFloat(minChar) && element.value.length <= parseFloat(maxChar);
        } else if (minChar !== '') {
          isValid = element.value.length >= parseFloat(minChar);
        } else if (maxChar !== '') {
          isValid = element.value.length <= parseFloat(maxChar);
        }

        break;

      case 'number':
        const number = parseFloat(element.value);
        const min = element.getAttribute(DATA_SC_ATTRIBUTE.MIN) ?? '';
        const max = element.getAttribute(DATA_SC_ATTRIBUTE.MAX) ?? '';

        if (min !== '' && max !== '') {
          isValid = number >= parseFloat(min) && number <= parseFloat(max);
        } else if (min !== '') {
          isValid = number >= parseFloat(min);
        } else if (max !== '') {
          isValid = number <= parseFloat(max);
        }

        break;

      case 'date':
        const dateRegex = /^\d{2}[/-]\d{2}[/-]\d{4}$/;

        if (!dateRegex.test(element.value)) {
          return false;
        }

        const [year, month, day] = element.value.split(/[/-]/).map(Number);
        const dateObj = new Date(year, month - 1, day);
        isValid = dateObj.getFullYear() === year && dateObj.getMonth() === month - 1 && dateObj.getDate() === day;
        break;

      case 'phone':
        const phoneRegex = /^(\+\d{1,2}\s?)?1?-?\.?\s?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/;
        isValid = phoneRegex.test(element.value);

        break;

      case 'postal_code':
        const postalCodeRegex = /^[0-9]{3}-?[0-9]{4}$/;
        isValid = postalCodeRegex.test(element.value);
        break;

      default:
        break;
    }

    this.handleErrorDisplay(element.form, element, !isValid);

    return isValid;
  };

  onFormElementChange(event) {
    const element = event.target;
    const type = element?.dataset?.scType ?? '';

    if (!this.validateFormElement(element)) {
      return;
    }

    if (type === DATA_SC_ELEMENT_TYPE.POSTAL_CODE) {
      this.handlePostCodeChange(event);
    }

    if (type === DATA_SC_ELEMENT_TYPE.FULL_NAME) {
      this.handleFullNameChange(event);
    }

    if (type === DATA_SC_ELEMENT_TYPE.EMAIL) {
      this.handleEmailConfirm(event);
    }

    this.updateConfirmFormElement(element);
    this.updateSubmitButton(element.form, FORM_STATUS.INIT);
  }

  onLoadPrefecture = (element) => {
    if (element.getAttribute('data-sc-type') !== DATA_SC_ELEMENT_TYPE.PREFECTURE) {
      return;
    }

    const option = document.createElement('option');
    option.value = '';
    option.text = 'Select';
    element.appendChild(option);

    prefectures.forEach((prefecture) => {
      const option = document.createElement('option');
      option.value = prefecture.prefecture_kanji;
      option.text = prefecture.prefecture_kanji;
      element.appendChild(option);
    });
  };

  generateFieldId = () => {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  };

  parseFormData = (form) => {
    const formData = new FormData(form);
    const formValues = [];
    const body = new FormData();

    for (const [name, value] of formData.entries()) {
      if (value instanceof File) {
        body.append(name, value);
      }
      // Check if the value is a string or other data type
      else if (typeof value === 'string') {
        const element = form.querySelector(`[name="${name}"]`);
        if (element) {
          formValues.push({
            controlName: element.type,
            labelName: name,
            value: value,
          });
        }
      }
      // For other types of inputs
      else {
        console.log(`Field "${name}" has an unknown type.`);
      }
    }

    if (formValues.length) {
      body.append('formValues', JSON.stringify(formValues));
    }

    body.append('date', dayjs().format('YYYY.MM.DD_HH.mm.ss'));

    if (this.recaptchaTokenV3) {
      body.append('gRecaptchaResponse', this.recaptchaTokenV3);
    }

    return body;
  };

  isValidUrl = (urlString) => {
    try {
      if (!urlString) {
        return false;
      }

      new URL(urlString);

      return true;
    } catch (e) {
      return false;
    }
  };

  onFormSubmit = async (event) => {
    event.preventDefault();

    try {
      const form = event.target;

      if (!form) {
        return;
      }

      if (this.isSubmittedForm(form)) {
        return;
      }

      if (!this.isAvailableForm() || !this.isValidForm(form)) {
        return;
      }

      if (!this.isConfirmedForm(form)) {
        this.updateFormStatus(form, FORM_STATUS.CONFIRMED);

        return;
      }

      const extId = form.getAttribute('data-sc-form');
      const body = this.parseFormData(form);

      if (!body) {
        return;
      }

      const response = await fetch(`${APP_URL}/api/embed/${extId}/raw-submit`, {
        method: 'POST',
        'content-type': 'multipart/form-data',
        body,
      });

      this.refreshRecaptchaTokenV3(form?.dataset?.scRecaptchaSiteKey);

      const data = await response.json();

      if (!data?.success) {
        const apiErrorElement = document.querySelector(`[${DATA_SC_ATTRIBUTE.SHOW_API_ERROR}]`);

        if (!apiErrorElement) {
          return false;
        }

        this.addClass(apiErrorElement, DATA_SC_CLASS.HAS_ERROR);
        apiErrorElement.innerText = data?.message;

        return false;
      }

      this.updateFormStatus(form, FORM_STATUS.SUBMITTED);

      if (this.isFormRedirectAfterSubmit()) {
        this.redirectForm();

        return;
      }

      if (!this.isFormRedirectAfterSubmit()) {
        const message = this.getFormThankYouMessage();
        this.setThankyouMessage(message);

        return;
      }

      return;
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  isValidForm = (form) => {
    const isValid = [
      this.validateRequiredFields(form),
      this.validateEmailFields(form),
      this.validateConfirmedEmailFields(form),
      this.validatePhoneFields(form),
      this.validateNumberFields(form),
      this.validateTextFields(form),
      this.validateCheckboxGroups(form),
      this.validateRadioGroups(form),
      this.validateDateFields(form),
      this.validatePostalCodeFields(form),
    ].every((validateFunc) => validateFunc);

    return isValid;
  };

  allowConfirmForm = (form) => {
    if (!form) {
      return false;
    }

    return form?.hasAttribute?.(DATA_SC_ATTRIBUTE.FORM_CONFIRM);
  };

  isConfirmedForm = (form) => {
    if (!form) {
      return false;
    }

    if (!this.allowConfirmForm(form)) {
      return true;
    }

    return form?.hasAttribute?.(DATA_SC_ATTRIBUTE.FORM_CONFIRMED);
  };

  isSubmittedForm = (form) => {
    if (!form) {
      return false;
    }

    return form?.hasAttribute?.(DATA_SC_ATTRIBUTE.FORM_SUBMITTED);
  };

  setThankyouMessage = (message) => {
    const thankYouElement = document.querySelector(`[${DATA_SC_ATTRIBUTE.SHOW_THANK_YOU_MESSAGE}]`);

    if (thankYouElement) {
      thankYouElement.innerText = message;
    }
  };

  redirectForm = () => {
    const redirectLink = this.getFormRedirectLink();

    if (this.isValidUrl(redirectLink)) {
      window.location.href = redirectLink;
    }

    return;
  };

  updateSubmitButton = (form, formStatus = FORM_STATUS.INIT) => {
    if (!form) {
      return;
    }

    const submitButton = form.querySelector('[type="submit"]');

    if (!submitButton) {
      return;
    }

    if (formStatus === FORM_STATUS.INIT) {
      const defaultSubmitText = submitButton.getAttribute(DATA_SC_ATTRIBUTE.SUBMIT_DEFAULT_TEXT) ?? 'Submit';
      submitButton.disabled = false;
      submitButton.innerText = defaultSubmitText;

      return;
    }

    const isValid = this.isValidForm(form);

    if (!isValid) {
      const errorText = submitButton.getAttribute(DATA_SC_ATTRIBUTE.SUBMIT_ERROR_TEXT) ?? 'Some required fields are missing.';
      submitButton.innerText = errorText;

      return;
    }

    if (formStatus === FORM_STATUS.CONFIRMED) {
      const confirmText = submitButton.getAttribute(DATA_SC_ATTRIBUTE.SUBMIT_CONFIRMED_TEXT) ?? 'Confirm';
      submitButton.innerText = confirmText;

      return;
    }

    if (formStatus === FORM_STATUS.SUBMITTED) {
      const submittedText = submitButton.getAttribute(DATA_SC_ATTRIBUTE.SUBMITTED_TEXT) ?? 'Done';
      submitButton.innerText = submittedText;
      submitButton.disabled = true;

      return;
    }

    return;
  };

  updateConfirmFormElement = (element) => {
    if (!element) {
      return;
    }

    const form = element.form;
    const name = element.getAttribute('name');

    const confirmElement = form.querySelector(`[data-sc-confirm-value="${name}"]`);

    if (!confirmElement) {
      return;
    }

    if (element.type === 'checkbox') {
      const checkboxes = form.querySelectorAll(`input[name="${name}"]:checked`);
      const values = Array.from(checkboxes)?.map?.((checkbox) => checkbox.value);

      confirmElement.innerText = values.join(', ');

      return;
    }

    if (element.type === 'file') {
      const files = element.files;
      const fileNames = Array.from(files)?.map?.((file) => file.name);

      confirmElement.innerText = fileNames.join(', ');

      return;
    }

    confirmElement.innerText = element.value;
  };

  updateFullNamePronunciation = (element, value) => {
    if (!element) {
      return;
    }

    const form = element.form;
    const name = element.getAttribute('name');
    const pronunciationElement = form.querySelector(`[data-sc-full-name="${name}"]`);

    if (!pronunciationElement) {
      return;
    }

    pronunciationElement.value = value;
    this.removeClass(pronunciationElement, DATA_SC_CLASS.SYSTEM_SHOW);
  };

  updateFormStatus = (form, formStatus = FORM_STATUS.INIT) => {
    if (!form) {
      return;
    }

    if (formStatus === FORM_STATUS.CONFIRMED) {
      form.setAttribute(DATA_SC_ATTRIBUTE.FORM_CONFIRMED, '');
      this.addClass(document.querySelector('body'), DATA_SC_CLASS.SYSTEM_CONFIRM);
      this.updateSubmitButton(form, FORM_STATUS.CONFIRMED);

      return;
    }

    if (formStatus === FORM_STATUS.SUBMITTED) {
      this.addClass(document.querySelector('body'), DATA_SC_CLASS.SYSTEM_SUBMIT);
      this.removeClass(document.querySelector(`[${DATA_SC_ATTRIBUTE.SHOW_API_ERROR}]`), DATA_SC_CLASS.HAS_ERROR);
      this.updateSubmitButton(form, FORM_STATUS.SUBMITTED);
      form.setAttribute(DATA_SC_ATTRIBUTE.FORM_SUBMITTED, '');
      form.reset();

      return;
    }

    form.removeAttribute(DATA_SC_ATTRIBUTE.FORM_CONFIRMED);
    form.removeAttribute(DATA_SC_ATTRIBUTE.FORM_SUBMITTED);
    this.removeClass(document.querySelector('body'), DATA_SC_CLASS.SYSTEM_CONFIRM);
    this.removeClass(document.querySelector('body'), DATA_SC_CLASS.SYSTEM_SUBMIT);
    this.removeClass(document.querySelector(`[${DATA_SC_ATTRIBUTE.SHOW_API_ERROR}]`), DATA_SC_CLASS.HAS_ERROR);
    this.updateSubmitButton(form, FORM_STATUS.INIT);
    this.setThankyouMessage('');
  };

  showLoading = (form) => {
    let loadingElement = form.querySelector('.sc-loading-indicator');

    if (!loadingElement) {
      loadingElement = document.createElement('div');
      loadingElement.className = 'sc-loading-indicator';
      loadingElement.textContent = 'Loading...';
      form.appendChild(loadingElement);
    }

    loadingElement.style.display = 'block';
  };

  hideLoading = (form) => {
    const loadingElement = form.querySelector('.sc-loading-indicator');
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
  };

  getFormInfo = async (form) => {
    const formId = form.getAttribute('data-sc-form');

    try {
      this.showLoading(form);

      if (!formId) {
        this.formData = { status: FORM_STATUS_API.NG, message: 'フォームが見つかりません。', data: null };

        return;
      }

      const response = await fetch(`${APP_URL}/api/embed/${formId}?type=dev`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const { success, message, data } = await response.json();

      if (!success) {
        this.formData = { status: FORM_STATUS_API.NG, message: message, data: null };

        return;
      }

      const maximumNumberFormsReceived = data?.formScheduleSetting?.maximumNumberFormsReceived ?? 999999999;
      const isFormClosed =
        data?.mode !== FormType.HTML ||
        dayjs().isAfter(data?.releaseEndDate) ||
        parseInt(maximumNumberFormsReceived) <= parseInt(data?.submissionCount) ||
        !data?.formGeneralSetting?.whitelistedDomain.some((domain) => window?.location?.host === domain);

      if (dayjs().isBefore(data.releaseStartDate)) {
        this.formData = { status: FORM_STATUS_API.NG, message: data.formScheduleSetting.displayTextBeforePublicForm };

        return;
      } else if (isFormClosed) {
        this.formData = { status: FORM_STATUS_API.NG, message: data.formScheduleSetting.displayTextAfterPublicForm };

        return;
      }

      this.formData = { status: FORM_STATUS_API.OK, message: '', data };

      return;
    } catch (error) {
      this.handleFormStatus(this.getUnavailableFormMessage());

      return;
    } finally {
      this.hideLoading(form);
    }
  };

  isAvailableForm = () => {
    return this.formData?.status === FORM_STATUS_API.OK;
  };

  getUnavailableFormMessage = () => {
    return this.formData?.message ?? '';
  };

  getFormThankYouMessage = () => {
    return this.formData?.data?.formMailSetting?.message ?? '';
  };

  getFormRedirectLink = () => {
    return this.formData?.data?.formMailSetting?.specifiedUrl ?? '';
  };

  isFormRedirectAfterSubmit = () => {
    return this.formData?.data?.formMailSetting?.screenAfterSendingType === 'specified_url';
  };

  handleFormStatus = () => {
    const message = this.getUnavailableFormMessage();

    const showErrorElement = document.querySelector(`[${DATA_SC_ATTRIBUTE.SHOW_IF_FORM_UNAVAILABLE}]`);

    if (!this.isAvailableForm() && showErrorElement && message) {
      this.addClass(showErrorElement, `${DATA_SC_CLASS.SYSTEM_SHOW}`);
      showErrorElement.innerText = message;
    }

    const hideErrorElement = document.querySelector(`[${DATA_SC_ATTRIBUTE.HIDE_IF_FORM_UNAVAILABLE}]`);

    if (this.isAvailableForm() && hideErrorElement) {
      this.addClass(hideErrorElement, `${DATA_SC_CLASS.SYSTEM_SHOW}`);
    }
  };

  initFormElements = (form) => {
    Array.from(form.elements).forEach((element) => {
      const type = element?.dataset?.scType ?? '';

      element.hasAttribute('data-sc-back-button') &&
        element.addEventListener('click', () => {
          this.updateFormStatus(form, FORM_STATUS.INIT);
        });

      if (type === DATA_SC_ELEMENT_TYPE.FULL_NAME) {
        element.addEventListener('compositionupdate', (event) => {
          const nonKanaValue = this.removeNonKana(event.data || '');
          const inputFieldValue = this.removeNonKana(event.target.value || '');
          const result = nonKanaValue.length > inputFieldValue.length ? nonKanaValue : inputFieldValue;

          if (result) {
            this.updateFullNamePronunciation(element, this.convertHiraganaToKatakana(result));
          }
        });
      }

      if (type === DATA_SC_ELEMENT_TYPE.EMAIL || type === DATA_SC_ELEMENT_TYPE.EMAIL_CONFIRMATION) {
        element.addEventListener('keydown', (event) => {
          if (event.key === ' ') {
            event.preventDefault();
          }
        });
      }

      this.onLoadPrefecture(element);

      element.addEventListener('change', (e) => this.onFormElementChange(e));

      !this.isAvailableForm() && element.setAttribute('disabled', true);
    });
  };

  renderHtmlEmbeds = () => {
    Array.prototype.forEach.call(document.querySelectorAll('.sc-html-embed'), async (form) => {
      if (form.dataset.rendered === 'true') {
        return;
      }

      this.renderRecaptchaV3(form);

      await this.getFormInfo(form);

      this.handleFormStatus();

      this.initFormElements(form);

      form.addEventListener('submit', (event) => this.onFormSubmit(event));
    });
  };

  renderRecaptchaV3 = async (form) => {
    const siteKey = form?.dataset?.scRecaptchaSiteKey;
    if (!siteKey) return;

    await this._loadScriptAsync(`https://www.google.com/recaptcha/api.js?render=${siteKey}`);
    this.refreshRecaptchaTokenV3(siteKey);
  };

  refreshRecaptchaTokenV3 = async (siteKey) => {
    if (!siteKey || window.grecaptcha === undefined) return;

    try {
      window.grecaptcha.ready(async () => {
        this.recaptchaTokenV3 = await window.grecaptcha.execute(siteKey, { action: 'submit' });
      });
    } catch (error) {
      console.error('Error refreshing recaptcha token:', error);
      this.recaptchaTokenV3 = null;
    }
  };

  _loadScriptAsync = (src) => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.async = true;
      script.defer = true;
      script.onload = () => {
        resolve();
      };
      script.onerror = (error) => {
        reject(error);
      };
      document.head.appendChild(script);
    });
  };
}

new SmoothContactHtmlEmbed();
