import { forwardRef, Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AWSS3Module } from '@/libs/AWSS3';
import { FormBuilderModule } from '@/modules/form-builder/form-builder.module';
import { MailModule } from '@/modules/mail/mail.module';
import { FormSubmissionEntity } from '@/modules/submission/entities/form-submission.entity';
import { SubmissionService } from '@/modules/submission/submission.service';
import { SubmissionSwitchContactHandler } from '@/modules/submission/submission-switch-contact.handler';

import { CsvModule } from '../csv/csv.module';
import { FormSubmissionController } from './submission.controller';

@Global()
@Module({
  imports: [TypeOrmModule.forFeature([FormSubmissionEntity]), forwardRef(() => FormBuilderModule), MailModule, AWSS3Module, CsvModule],
  controllers: [FormSubmissionController],
  providers: [ConfigService, SubmissionService, SubmissionSwitchContactHandler],
  exports: [SubmissionService, SubmissionSwitchContactHandler],
})
export class SubmissionModule {}
