import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';

import { dataSourceOptions } from './typeorm.config';

@Injectable()
export class TypeOrmConfigService implements TypeOrmOptionsFactory {
  @Inject(ConfigService)
  private readonly config: ConfigService;

  public createTypeOrmOptions(): TypeOrmModuleOptions {
    return {
      ...dataSourceOptions,
      synchronize: false,
      autoLoadEntities: true,
      logging: this.config.get('DATABASE_LOGGING') === 'true',
    };
  }
}
