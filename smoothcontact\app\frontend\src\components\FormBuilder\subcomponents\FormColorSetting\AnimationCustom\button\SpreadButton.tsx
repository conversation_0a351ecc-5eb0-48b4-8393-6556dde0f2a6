import React from 'react';
import { styled } from '@mui/system';

const Button = styled('button')`
  width: 100%;
  height: 40px;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  background-color: #3aaaff;
  color: #fff;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 200%;
    background-color: #008ffc;
    transition:
      width 1s ease,
      height 1s ease;
  }

  &:hover::before {
    width: 200%;
  }

  &:hover {
    opacity: 0.8;
  }
`;

const SpreadButton: React.FC<{ label: string }> = ({ label }) => {
  return <Button>{label}</Button>;
};

export default SpreadButton;
