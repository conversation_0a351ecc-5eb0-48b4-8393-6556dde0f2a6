import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import useAxios, { ApiResponse } from '@/hooks/useAxios';

const FormBuilderEditProxyModule: React.FC = () => {
  const navigate = useNavigate();
  const { id: formId } = useParams();
  const { apiCaller, loading } = useAxios();

  const getFormDataAndNavigate = async () => {
    const { data: form, success }: ApiResponse<any> = await apiCaller({ url: `/api/form-builder/get-by-id/${formId}` });
    if (success && form?.extId) {
      navigate(`/form-builder/edit/${form.extId}`);

      return;
    }

    navigate('/form-builder');
  };

  useEffect(() => {
    getFormDataAndNavigate();
  }, []);

  return <div>{loading ? 'Loading...' : 'Redirecting...'}</div>;
};

export default FormBuilderEditProxyModule;
