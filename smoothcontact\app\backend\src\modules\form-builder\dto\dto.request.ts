import { Transform, Type } from 'class-transformer';
import { IsArray, IsEnum, IsIn, MaxLength, ValidateNested } from 'class-validator';

import { assignDataToInstance } from '@/common/helper';
import { IsCustomOptional } from '@/core/decorator/customOptional.decorator';
import { IsEndDateAfterStartDate } from '@/core/decorator/endDateAfterStartDate.decorator';
import { PaginationRequestDto } from '@/types/request.type';

import {
  FormColorSetting,
  FormElements,
  FormEmbedAppSetting,
  FormGeneralSetting,
  FormHistoryType,
  FormMailSetting,
  FormScheduleSetting,
} from '../common/common';
import { PublishStatus } from '../entities/form-builder.entity';

export class CreateFormBuilderRequestDTO {
  @IsCustomOptional()
  @MaxLength(256)
  name: string;

  lastPublishedAt?: number;

  @IsEnum(PublishStatus)
  status: PublishStatus;

  @IsCustomOptional()
  @Transform(({ value }) => value ?? null)
  releaseStartDate: Date | null | string;

  @IsCustomOptional()
  @Transform(({ value }) => value ?? null)
  @IsEndDateAfterStartDate('releaseStartDate', {
    message: '終了日は開始日より後の日付を指定してください。',
  })
  releaseEndDate: Date | null | string;

  @IsCustomOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FormElements)
  formElements?: FormElements[];

  publishHistory?: FormHistoryType[];

  @IsCustomOptional()
  @ValidateNested({ each: true })
  @Type(() => FormGeneralSetting)
  formGeneralSetting?: FormGeneralSetting;

  @IsCustomOptional()
  @ValidateNested({ each: true })
  @Type(() => FormColorSetting)
  formColorSetting?: FormColorSetting;

  @IsCustomOptional()
  @ValidateNested({ each: true })
  @Type(() => FormMailSetting)
  formMailSetting?: FormMailSetting;

  @IsCustomOptional()
  @ValidateNested({ each: true })
  @Type(() => FormScheduleSetting)
  formScheduleSetting?: FormScheduleSetting;

  @IsCustomOptional()
  @ValidateNested({ each: true })
  @Type(() => FormEmbedAppSetting)
  formEmbedAppSetting?: FormEmbedAppSetting;

  @IsCustomOptional()
  mode?: string;

  constructor(data?: Partial<any>) {
    assignDataToInstance(this, data);
  }
}

export class UpdateFormBuilderRequestDTO extends CreateFormBuilderRequestDTO {
  id: number;
}

export class UpdateMemoFormBuilderRequestDTO {
  id: number;
  memo?: string;
}

export const enum FilterStatusOptions {
  ALL = 'ALL',
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  PREPUBLISHED = 'PREPUBLISHED',
  EXPIRED = 'EXPIRED',
}

export class GetFormBuilderRequestDTO extends PaginationRequestDto {
  @IsCustomOptional()
  name?: string;

  @IsCustomOptional()
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return value?.map?.((v) => v.toLowerCase())?.includes('all') ? [] : value?.map((v) => v.toUpperCase());
    }

    return value
      ?.split(',')
      ?.map?.((v) => v.toLowerCase())
      ?.includes('all')
      ? []
      : value?.split(',')?.map((v) => v.toUpperCase());
  })
  status?: FilterStatusOptions[] = [];

  @IsCustomOptional()
  from?: Date | '';

  @IsCustomOptional()
  to?: Date | '';

  @IsIn(['ASC', 'DESC'])
  @IsCustomOptional()
  @Transform(({ value }) => value.toUpperCase())
  order?: 'ASC' | 'DESC' = 'DESC';

  @IsIn(['id', 'name', 'status', 'updateddate', 'view'])
  @IsCustomOptional()
  @Transform(({ value }) => value.toLowerCase())
  orderBy?: 'id' | 'name' | 'status' | 'updateddate' | 'view';
}
