import SCColorPicker from '@/components/common/SCColorPicker';
import { Grid, MenuItem, Select, Stack, TextField, Typography } from '@mui/material';
import { FC } from 'react';
import { CustomModeComponentProps } from './CustomModeComponent';
import { KeyboardArrowDown } from '@mui/icons-material';

const EntryFormCustomComponent: FC<CustomModeComponentProps> = (props) => {
  const { form } = props;

  return (
    <>
      <Typography variant="body2">入力フォーム</Typography>
      <Stack direction="row" spacing={2}>
        <TextField
          label="文字サイズ"
          {...form.register('entryFormSettings.fontSize')}
          value={form?.values?.entryFormSettings?.fontSize}
          variant="outlined"
          type="number"
          error={!!form?.errors?.entryFormSettings?.fontSize}
          helperText={form?.errors?.entryFormSettings?.fontSize ? '整数を半角で入力してください' : ''}
        />
        <Select
          IconComponent={() => <KeyboardArrowDown />}
          {...form.register('entryFormSettings.fontSizeUnit')}
          value={form?.values?.entryFormSettings?.fontSizeUnit}
          displayEmpty
        >
          <MenuItem value="px">px</MenuItem>
          <MenuItem value={'rem'}>rem</MenuItem>
          <MenuItem value={'em'}>em</MenuItem>
          <MenuItem value={'%'}>%</MenuItem>
        </Select>
      </Stack>
      <Stack direction="row" spacing={2}>
        <TextField
          label="角丸"
          {...form.register('entryFormSettings.borderRadius')}
          value={form?.values?.entryFormSettings?.borderRadius}
          variant="outlined"
          type="number"
          error={!!form?.errors?.entryFormSettings?.borderRadius}
          helperText={form?.errors?.entryFormSettings?.borderRadius ? '整数を半角で入力してください' : ''}
        />
        <Select
          IconComponent={() => <KeyboardArrowDown />}
          {...form.register('entryFormSettings.borderRadiusUnit')}
          value={form?.values?.entryFormSettings?.borderRadiusUnit}
          displayEmpty
        >
          <MenuItem value={'px'}>px</MenuItem>
          <MenuItem value={'rem'}>rem</MenuItem>
        </Select>
      </Stack>
      <Grid container alignItems="center">
        <Grid item xs={4} container justifyContent="flex-end">
          <Typography variant="body2">文字色</Typography>
        </Grid>
        <Grid item xs={6} sx={{ pl: 2 }}>
          <SCColorPicker name="entryFormSettings.color" color={form?.values?.entryFormSettings?.color} form={form} />
        </Grid>
      </Grid>
      <Grid container alignItems="center">
        <Grid item xs={4} container justifyContent="flex-end">
          <Typography variant="body2">背景色</Typography>
        </Grid>
        <Grid item xs={6} sx={{ pl: 2 }}>
          <SCColorPicker name="entryFormSettings.bgColor" color={form?.values?.entryFormSettings?.bgColor} form={form} />
        </Grid>
      </Grid>
      <Grid container alignItems="center">
        <Grid item xs={4} container justifyContent="flex-end">
          <Typography variant="body2">枠線の色</Typography>
        </Grid>
        <Grid item xs={6} sx={{ pl: 2 }}>
          <SCColorPicker name="entryFormSettings.borderColor" color={form?.values?.entryFormSettings?.borderColor} form={form} />
        </Grid>
      </Grid>
    </>
  );
};

export default EntryFormCustomComponent;
