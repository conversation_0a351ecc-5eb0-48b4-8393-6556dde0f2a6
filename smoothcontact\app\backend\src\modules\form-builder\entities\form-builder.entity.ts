import { Column, Entity } from 'typeorm';

import { BaseEntity } from '@/core/entity/base.entity';

import { FormColorSetting, FormElements, FormEmbedAppSetting, FormGeneralSetting, FormMailSetting, FormScheduleSetting } from '../common/common';

export enum PublishStatus {
  DRAFT = 0,
  PUBLISHED = 1,
  EXPIRED = 3,
}

export enum NewFormMode {
  MANUAL = 'manual',
  DEV = 'dev',
}

export type FromViewSetting = 'pc' | 'mobile';

@Entity('form_builder')
export class FormBuilderEntity extends BaseEntity {
  @Column({
    name: 'shop',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  shop: string;

  @Column({
    name: 'ext_id',
    type: 'varchar',
    length: 64,
    nullable: false,
  })
  extId: string;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  name: string;

  @Column({
    name: 'status',
    type: 'tinyint',
    nullable: false,
  })
  status: PublishStatus;

  @Column({
    name: 'version',
    type: 'integer',
    nullable: true,
  })
  version: number;

  @Column({ name: 'view', type: 'varchar', length: 10, nullable: true })
  view: FromViewSetting;

  @Column({
    name: 'form_elements',
    type: 'simple-json',
    nullable: true,
  })
  formElements: FormElements[];

  @Column({
    name: 'form_general_setting',
    type: 'simple-json',
    nullable: true,
  })
  formGeneralSetting: FormGeneralSetting;

  @Column({
    name: 'form_color_setting',
    type: 'simple-json',
    nullable: true,
  })
  formColorSetting: FormColorSetting;

  @Column({
    name: 'form_mail_setting',
    type: 'simple-json',
    nullable: true,
  })
  formMailSetting: FormMailSetting;

  @Column({
    name: 'form_schedule_setting',
    type: 'simple-json',
    nullable: true,
  })
  formScheduleSetting: FormScheduleSetting;

  @Column({
    name: 'form_embed_app_setting',
    type: 'simple-json',
    nullable: true,
  })
  formEmbedAppSetting: FormEmbedAppSetting;

  @Column({
    name: 'last_published_at',
    type: 'datetime',
    nullable: true,
  })
  lastPublishedAt: Date;

  @Column({
    name: 'created_by',
    type: 'integer',
    nullable: false,
  })
  createdBy: number;

  @Column({
    name: 'release_start_date',
    type: 'datetime',
    nullable: true,
  })
  releaseStartDate: Date;

  @Column({
    name: 'release_end_date',
    type: 'datetime',
    nullable: true,
  })
  releaseEndDate: Date;

  @Column({
    name: 'memo',
    length: 255,
    nullable: true,
  })
  memo: string;

  @Column({
    name: 'mode',
    type: 'varchar',
    length: 6,
    nullable: false,
  })
  mode: NewFormMode | string;
}
