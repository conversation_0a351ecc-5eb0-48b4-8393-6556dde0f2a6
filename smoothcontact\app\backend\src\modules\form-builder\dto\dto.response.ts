import { Expose, Transform, Type } from 'class-transformer';

import { Pagination } from '@/types/response.type';

import {
  DEFAULT_COLOR,
  DEFAULT_FONT,
  DEFAULT_LAYOUT_MODE,
  DEFAULT_TEMPLATE_COLOR,
  FontSize,
  FontSizeUnit,
  FormCategory,
  FormControlNames,
  FormItemTypes,
} from '../common/common';
import { PublishStatus } from '../entities/form-builder.entity';

export class UploadResponseDTO {
  @Expose()
  link: string;
}

export class SwitchContactResponse {
  @Expose()
  email: string;

  @Expose()
  sender: string;

  @Expose()
  title: string;

  @Expose()
  content: string;

  @Expose()
  isAutoReply: boolean;

  @Expose()
  autoReplyEmailAddress: string;

  @Expose()
  autoReplySenderName: string;

  @Expose()
  autoReplySubject: string;

  @Expose()
  autoReplyBody: string;
}

export class SwitchContactSettingResponse {
  @Expose()
  id: string;

  @Expose()
  contact: SwitchContactResponse;

  @Expose()
  enabled: boolean;
}

export class FormScheduleSettingResponse {
  @Expose()
  displayTextBeforePublicForm: string;

  @Expose()
  displayTextAfterPublicForm: string;

  @Expose()
  displayTextHiddenForm: string;

  @Expose()
  hideHiddenText: boolean;

  @Expose()
  maximumNumberFormsReceived: number | null;
}

export class FormEmbedAppSettingResponse {
  @Expose()
  isEnableGA4Setting: boolean;

  @Expose()
  gA4TrackingID: string;

  @Expose()
  isEnableGoogleAdsSetting: boolean;

  @Expose()
  globalSiteTag?: string;

  @Expose()
  eventSnippet: string;

  @Expose()
  isLinkageYahoo: boolean;

  @Expose()
  conversionMeasurementTags: string;
}

export class FormMailSettingResponse {
  @Expose()
  @Transform(({ value }) => (value ? value?.filter?.((item: any) => item !== null) : []))
  emailAddress: string[];

  @Expose()
  screenAfterSendingType: 'display_message' | 'specified_url';

  @Expose()
  message: string;

  @Expose()
  specifiedUrl: string;

  @Expose()
  isAutoReply: boolean;

  @Expose()
  autoReplyEmailAddress: string;

  @Expose()
  autoReplySenderName: string;

  @Expose()
  autoReplySubject: string;

  @Expose()
  isTextMail: boolean;

  @Expose()
  textMailBody: string;

  @Expose()
  isHtmlMail: boolean;

  @Expose()
  receiveEmailField?: string;

  @Expose()
  useCustomSMTP: boolean;

  @Expose()
  smtpHost: string;

  @Expose()
  smtpPort: number;

  @Expose()
  smtpUsername: string;

  @Expose()
  smtpPassword: string;

  @Expose()
  smtpFromEmail: string;

  @Expose()
  htmlFieldDisplays?: string[];
}

export class FormGeneralSettingResponse {
  @Expose()
  oGPImage: string;

  @Expose()
  linkageDomain: string;

  @Expose()
  @Transform(({ value }) => value ?? true)
  receivedDataSaveFlag: boolean;

  @Expose()
  isDisplaySearchEngine: boolean;

  @Expose()
  isSettingReCAPTCHA: boolean;

  @Expose()
  isSettingPrivacyPolicy: boolean;

  @Expose()
  isDisplayTermsUse: boolean;

  @Expose()
  isCombineIntoOneCheckbox: boolean;

  @Expose()
  isDisplaySignUp: boolean;

  @Expose()
  isDisplaySignUpSample: boolean;

  @Expose()
  termsUse: string;

  @Expose()
  policyLink: string;

  @Expose()
  contactPerson: string[];

  @Expose()
  whitelistedDomain: string[];

  @Expose()
  captchaKey: string;
}

export class GeneralSettingResponse {
  @Expose()
  spacing: number;

  @Expose()
  spacingUnit: string;

  @Expose()
  fontSize: number;

  @Expose()
  fontSizeUnit: string;

  @Expose()
  color: string;

  @Expose()
  fontFamily: string;

  @Expose()
  fontName?: string;
}

export class TitleSettingResponse {
  @Expose()
  fontSize: number;

  @Expose()
  fontSizeUnit: string;

  @Expose()
  color: string;

  @Expose()
  fontFamily: string;

  @Expose()
  fontName?: string;
}

export class LabelsSettingResponse {
  @Expose()
  fontSize: number;

  @Expose()
  fontSizeUnit: string;

  @Expose()
  color: string;

  @Expose()
  fontFamily: string;

  @Expose()
  fontName?: string;

  constructor() {
    this.fontSize = FontSize.SIZE_12;
    this.fontSizeUnit = FontSizeUnit.PX;
    this.color = DEFAULT_COLOR;
    this.fontFamily = DEFAULT_FONT;
  }
}

export class DescriptionSettingResponse {
  @Expose()
  fontSize?: number;

  @Expose()
  fontSizeUnit?: string;

  @Expose()
  color?: string;

  @Expose()
  fontFamily?: string;

  @Expose()
  fontName?: string;

  constructor() {
    this.fontSize = FontSize.SIZE_12;
    this.fontSizeUnit = FontSizeUnit.PX;
    this.color = DEFAULT_COLOR;
    this.fontFamily = DEFAULT_FONT;
  }
}

export class EntryFormSettingResponse {
  @Expose()
  fontSize: number;

  @Expose()
  fontSizeUnit: string;

  @Expose()
  borderRadius: number;

  @Expose()
  borderRadiusUnit: string;

  @Expose()
  color: string;

  @Expose()
  bgColor: string;

  @Expose()
  borderColor: string;
}

export class ChoiceSettingResponse {
  @Expose()
  color: string;
}

export class ButtonsSettingResponse {
  @Expose()
  text?: string;

  @Expose()
  confirmText: string;

  @Expose()
  submitText: string;

  @Expose()
  fontSize: number;

  @Expose()
  fontSizeUnit: string;

  @Expose()
  borderRadius: number;

  @Expose()
  borderRadiusUnit: string;

  @Expose()
  color: string;

  @Expose()
  bgColor: string;

  @Expose()
  borderColor: string;

  @Expose()
  fontFamily: string;

  @Expose()
  fontName?: string;
}

export class AnimationSettingResponse {
  @Expose()
  textEntryArea: string;

  @Expose()
  itemButton: string;
}

export class FormColorSettingResponse {
  @Expose()
  optionMode: 'custom_mode' | 'template_mode' | 'store_theme_mode';

  @Expose()
  @Transform(({ value }) => value ?? DEFAULT_TEMPLATE_COLOR)
  templateModeColor: string;

  @Expose()
  @Transform(({ value }) => value ?? DEFAULT_LAYOUT_MODE)
  layoutMode: 'vertical' | 'horizontal';

  @Expose()
  bgColor: string;

  @Expose()
  @Type(() => GeneralSettingResponse)
  generalSettings: GeneralSettingResponse;

  @Expose()
  @Type(() => TitleSettingResponse)
  titleSettings: TitleSettingResponse;

  @Expose()
  @Type(() => LabelsSettingResponse)
  @Transform(({ value }) => value ?? new LabelsSettingResponse())
  labelSettings: LabelsSettingResponse;

  @Expose()
  @Type(() => DescriptionSettingResponse)
  @Transform(({ value }) => value ?? new DescriptionSettingResponse())
  descriptionSettings: DescriptionSettingResponse;

  @Expose()
  @Type(() => EntryFormSettingResponse)
  entryFormSettings: EntryFormSettingResponse;

  @Expose()
  @Type(() => ChoiceSettingResponse)
  choiceSettings: ChoiceSettingResponse;

  @Expose()
  @Type(() => ButtonsSettingResponse)
  buttonSettings: ButtonsSettingResponse;

  @Expose()
  @Type(() => AnimationSettingResponse)
  animationSettings: AnimationSettingResponse;
}

export class FormContainerTypeResponse {
  @Expose()
  controlName: FormControlNames;

  @Expose()
  displayText: string;

  @Expose()
  itemType: FormItemTypes;

  @Expose()
  heading: string;

  @Expose()
  description: string;

  @Expose()
  id: string;

  @Expose()
  desktopWidth?: number;

  @Expose()
  required?: boolean;

  @Expose()
  display?: 'none' | 'left' | 'center' | 'right';
}

export class FormElementChildrenTypeResponse {
  @Expose()
  controlName: FormControlNames;

  @Expose()
  displayText: string;

  @Expose()
  description: string;

  @Expose()
  labelName: string;

  @Expose()
  itemType: FormItemTypes;

  @Expose()
  required?: boolean;

  @Expose()
  @Type(() => FormElementChildrenItemsTypeResponse)
  items?: FormElementChildrenItemsTypeResponse[];

  @Expose()
  category: FormCategory;

  @Expose()
  index?: number;

  @Expose()
  id?: string;

  @Expose()
  parentId?: string;

  @Expose()
  condition?: string | string[];

  @Expose()
  level?: number;

  @Expose()
  containerId?: string;

  @Expose()
  placeholder?: string;

  @Expose()
  rows?: number;

  @Expose()
  dataType?: string;

  @Expose()
  position?: number;

  @Expose()
  characterType?: 'text' | 'number' | 'alphanumeric';

  @Expose()
  min?: number;

  @Expose()
  max?: number;

  @Expose()
  showMinuteStep?: boolean;

  @Expose()
  dateLimit?: 'none' | 'future' | 'past';

  @Expose()
  minuteStep?: number;

  @Expose()
  isUseFurigana?: boolean;

  @Expose()
  isAutoFill?: boolean;

  @Expose()
  autoFillType?: 'hiragana' | 'katakana';

  @Expose()
  isReduceFullName?: boolean;

  @Expose()
  verifyEmail?: boolean;

  @Expose()
  verifyEmailPlaceholder?: string;

  @Expose()
  oneFieldPostcode?: boolean;

  @Expose()
  displayPostCodeLink?: boolean;

  @Expose()
  displayOtherOption?: boolean;

  @Expose()
  @Transform(({ value }) => value ?? false)
  isBottomDescription?: boolean;

  @Expose()
  displayMode?: 'vertical' | 'horizontal';

  @Expose()
  @Transform(({ value, obj }) => {
    if (value) {
      return value;
    }

    if (obj.controlName === FormControlNames.BIRTHDAY) {
      return false;
    }

    return undefined;
  })
  limitedAgeRequired?: boolean;

  @Expose()
  @Transform(({ value, obj }) => {
    if (value) {
      return value;
    }

    if (obj.controlName === FormControlNames.BIRTHDAY) {
      return 18;
    }

    return undefined;
  })
  limitedAge?: number;

  @Expose()
  fullNamePlaceholder?: any;

  @Expose()
  @Transform(({ obj }) => {
    if (![FormControlNames.CHECKLIST, FormControlNames.RADIO, FormControlNames.DROPDOWN].includes(obj.controlName)) {
      return undefined;
    }

    if (!obj?.switchContacts?.length) {
      return obj?.items?.map((item) => {
        return {
          id: item?.value,
          contact: {
            email: '',
            sender: '',
            title: '',
            content: '',
            isAutoReply: false,
            autoReplyEmailAddress: '',
            autoReplySenderName: '',
            autoReplySubject: '',
            autoReplyBody: '',
          },
          enabled: false,
        };
      });
    }

    return obj.switchContacts;
  })
  switchContacts?: SwitchContactSettingResponse[];
}

export class FormElementChildrenItemsTypeResponse {
  id?: string;

  @Expose()
  value: string;

  @Expose()
  label?: string;
}

export class FormElementsResponse {
  @Expose()
  @Type(() => FormContainerTypeResponse)
  container: FormContainerTypeResponse;

  @Expose()
  @Type(() => FormElementChildrenTypeResponse)
  children: FormElementChildrenTypeResponse[];
}

export class GetFormBuilderResponseDto {
  @Expose()
  id: number;

  @Expose()
  extId: string;

  @Expose()
  name: string;

  @Expose()
  status: PublishStatus;

  @Expose()
  @Type(() => FormElementsResponse)
  formElements?: FormElementsResponse[];

  @Expose()
  @Type(() => FormColorSettingResponse)
  formColorSetting: FormColorSettingResponse;

  @Expose()
  @Type(() => FormGeneralSettingResponse)
  formGeneralSetting: FormGeneralSettingResponse;

  @Expose()
  @Type(() => FormMailSettingResponse)
  formMailSetting: FormMailSettingResponse;

  @Expose()
  @Type(() => FormScheduleSettingResponse)
  formScheduleSetting: FormScheduleSettingResponse;

  @Expose()
  @Type(() => FormEmbedAppSettingResponse)
  formEmbedAppSetting: FormEmbedAppSettingResponse;

  @Expose()
  memo: string;

  @Expose()
  releaseStartDate: Date;

  @Expose()
  releaseEndDate: Date;

  @Expose()
  updatedAt?: Date;

  @Expose()
  submissionCount?: number;

  @Expose()
  @Transform(({ value }) => (value !== 'dev' ? 'manual' : 'dev'))
  mode?: string;
}

export class FormBuilderStatisticsResponseDto {
  @Expose()
  draftCount: number;

  @Expose()
  publishedCount: number;

  @Expose()
  expiredCount: number;

  @Expose()
  prePublishedCount: number;
}

export class FormBuilderResponseDto extends GetFormBuilderResponseDto {}

export class ListFormBuilderResponseDto extends Pagination<FormBuilderResponseDto> {
  @Expose()
  @Type(() => FormBuilderResponseDto)
  items: FormBuilderResponseDto[];

  @Expose()
  @Type(() => FormBuilderStatisticsResponseDto)
  statistics: FormBuilderStatisticsResponseDto;
}
