#== BASE =============================================================
FROM public.ecr.aws/docker/library/node:20.12.2-alpine as base

WORKDIR /web
ENV NODE_ENV=production

# Set Timezone
RUN apk add -U tzdata
ENV TZ="Asia/Tokyo"
RUN cp /usr/share/zoneinfo/Asia/Tokyo /etc/localtime

# Install Yarn
RUN set -eux apk add --no-cache yarn
# Init folder
RUN mkdir -p ./app/backend && mkdir -p ./app/frontend

#== BUILD ============================================================
FROM base as build

RUN yarn global add @nestjs/cli
# Build and Clean
COPY ./app/frontend/dist ./app/frontend/dist
COPY ./app/backend ./app/backend
RUN echo "Building backend"
RUN cd ./app/backend && yarn install --production=false && yarn build
# Remove dev dependencies
RUN cd ./app/backend && yarn install --production=true

#== PREPARE TO CREATE FINAL IMAGE ====================================
FROM base as prepare_final

COPY --from=build /web /web
RUN rm -rf /web/app/backend/src

#== CREATE IMAGE =====================================================
FROM base

COPY --from=prepare_final /web /web

EXPOSE 3000

WORKDIR /web/app/backend

CMD ["node", "./dist/main.js"]