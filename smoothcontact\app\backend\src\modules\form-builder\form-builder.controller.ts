import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseFilePipe,
  Post,
  Put,
  Query,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';

import { BaseController } from '@/core/controllers/api.controller';
import { AuthGuard } from '@/core/guard/auth.guard';
import { UploadService } from '@/modules/upload/upload.service';
import { formatDate } from '@/utils/helpers';

import {
  CreateFormBuilderRequestDTO,
  GetFormBuilderRequestDTO,
  UpdateFormBuilderRequestDTO,
  UpdateMemoFormBuilderRequestDTO,
} from './dto/dto.request';
import { GetFormBuilderResponseDto, ListFormBuilderResponseDto } from './dto/dto.response';
import { FormBuilderService } from './form-builder.service';

@UseGuards(AuthGuard)
@Controller('api/form-builder')
export class FormBuilderController extends BaseController {
  constructor(
    private readonly formBuilderService: FormBuilderService,
    private readonly uploadService: UploadService,
  ) {
    super();
  }

  @Get('/:extId')
  async getOne(@Request() request: Request, @Param('extId') extId: string) {
    const result = await this.formBuilderService.getFormBuilder(request?.user?.id, extId);

    if (!result) {
      return this.failResponse({ message: 'フォームが見つかりません' });
    }

    return this.successResponse({ data: result }, GetFormBuilderResponseDto);
  }

  @Get('/get-by-id/:id')
  async getById(@Request() request: Request, @Param('id') id: number) {
    const result = await this.formBuilderService.getFormBuilderById(request?.user?.id, id);

    if (!result) {
      return this.failResponse({ message: 'フォームが見つかりません' });
    }

    return this.successResponse({ data: result }, GetFormBuilderResponseDto);
  }

  @Get('/')
  async getAll(@Request() request: Request, @Query() queryParams: GetFormBuilderRequestDTO) {
    const result = await this.formBuilderService.getFormBuilders(
      request?.user?.id,
      queryParams,
      !!(request?.user?.callbackUrl && request?.user?.setting),
    );

    if (!result) {
      return this.failResponse({ message: 'フォームが見つかりません' });
    }

    return this.successResponse(
      {
        data: {
          items: result.data,
          statistics: result.statistics,
          pagination: { page: queryParams?.page, perPage: queryParams.perPage, total: result.count },
        },
      },
      ListFormBuilderResponseDto,
    );
  }

  @Post('create-empty')
  async createEmpty(@Request() request: Request, @Body() data: CreateFormBuilderRequestDTO) {
    const result = await this.formBuilderService.createEmpty({ userId: request?.user?.id, data });

    if (!result) {
      return this.failResponse({ message: 'error.form-builder.create_error' });
    }

    return this.successResponse({ data: result }, GetFormBuilderResponseDto);
  }

  @Put('/')
  async update(@Request() request: Request, @Body() data: UpdateFormBuilderRequestDTO) {
    const result = await this.formBuilderService.update({ userId: request?.user?.id, data });

    if (!result) {
      return this.failResponse({ message: 'error.form-builder.update_error' });
    }

    return this.successResponse({ data: result }, GetFormBuilderResponseDto);
  }

  @Delete('/:extId')
  async delete(@Request() request: Request, @Param('extId') extId: string) {
    const result = await this.formBuilderService.deleteFormBuilder({ extId, userId: request?.user?.id });

    if (!result) {
      return this.failResponse({ message: 'error.form-builder.delete_error' });
    }

    return this.successResponse();
  }

  @Post('/duplicate/:extId')
  async duplicate(@Request() request: Request, @Param('extId') extId: string) {
    const result = await this.formBuilderService.duplicate({ extId, userId: request?.user?.id });

    if (!result) {
      return this.failResponse({ message: 'error.form-builder.duplicate_error' });
    }

    return this.successResponse({ data: result }, GetFormBuilderResponseDto);
  }

  @Post('/:extId/upload-og-image')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
      }),
      limits: { fileSize: 10485760 /* 10MB */ },
    }),
  )
  async uploadOgImage(
    @Request() request: Request,
    @Param('extId') extId: string,
    @UploadedFile(new ParseFilePipe({ fileIsRequired: true })) file: Express.Multer.File,
  ) {
    const originalName = file.originalname.split('.').slice(0, -1).join('.');
    const newFileName = Buffer.from(`${originalName}-${formatDate(new Date(), 'YYYY.MM.DD_HH.mm.ss')}`, 'latin1').toString('utf8');
    const result = await this.uploadService.uploadFileWithCustomDir(extId, newFileName, file);

    if (!result) {
      return this.failResponse({ message: 'アップロードに失敗しました。' });
    }

    return this.successResponse({ data: result });
  }

  @Put('/memo')
  async updateMemo(@Request() request: Request, @Body() data: UpdateMemoFormBuilderRequestDTO) {
    try {
      const result = await this.formBuilderService.updateMemo({ userId: request?.user?.id, data });

      if (!result) {
        return this.failResponse({ message: 'error.form-builder.update_error' });
      }

      return this.successResponse({ data: result });
    } catch (e) {
      return this.failResponse({ message: e.message });
    }
  }
}
