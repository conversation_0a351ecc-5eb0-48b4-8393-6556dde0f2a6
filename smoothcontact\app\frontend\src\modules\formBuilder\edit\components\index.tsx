import SCSideMenu from '@/components/common/SCSideMenu';
import { SideBarLabel, SideBarType } from '@/types/FormTemplateTypes';
import { Box } from '@mui/material';
import BrushIcon from '@mui/icons-material/Brush';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import ElectricalServicesIcon from '@mui/icons-material/ElectricalServices';
import EmailOutlinedIcon from '@mui/icons-material/EmailOutlined';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import LayersOutlinedIcon from '@mui/icons-material/LayersOutlined';
import SettingsIcon from '@mui/icons-material/Settings';
import { useFormBuilder } from '../store/FormBuilderProvider';
import FormBuilder from '@/components/FormBuilder/FormBuilder';
import { useWarnIfUnsavedChanges } from '@/hooks/useWarnIfUnsavedChanges';

const FormBuilderEditComponent: React.FC = () => {
  const { sideBarType, isFormChanged, changeSettingType, error, resetSelectedTemplate, isHtmlForm } = useFormBuilder();

  useWarnIfUnsavedChanges({
    isDirty: isFormChanged || error,
    title: 'このページから移動しますか？',
    subtitle: '行った変更が保存されない可能性があります',
    cancelText: 'このページに留まる',
    confirmText: 'このページから移動する',
    width: 400,
    onConfirm: () => {
      resetSelectedTemplate();
    },
  });

  return (
    <Box flexDirection="row" display="flex">
      <Box pt={2} pl={1}>
        <SCSideMenu
          items={[
            {
              key: SideBarType.ELEMENT,
              icon: LayersOutlinedIcon,
              title: SideBarLabel[SideBarType.ELEMENT],
              enable: !isHtmlForm,
            },
            { key: SideBarType.COLOR, icon: BrushIcon, title: SideBarLabel[SideBarType.COLOR], enable: !isHtmlForm },
            { key: SideBarType.MAIL, icon: EmailOutlinedIcon, title: SideBarLabel[SideBarType.MAIL] },
            { key: SideBarType.SCHEDULE, icon: CalendarMonthIcon, title: SideBarLabel[SideBarType.SCHEDULE] },
            { key: SideBarType.GENERAL, icon: SettingsIcon, title: SideBarLabel[SideBarType.GENERAL] },
            {
              key: SideBarType.EMBED_APP,
              icon: ElectricalServicesIcon,
              title: SideBarLabel[SideBarType.EMBED_APP],
              enable: !isHtmlForm,
            },
            { key: SideBarType.HELP, icon: HelpOutlineIcon, title: SideBarLabel[SideBarType.HELP] },
          ]}
          selectedKey={sideBarType}
          onClick={changeSettingType}
        />
      </Box>
      <Box p={2} flexGrow={1}>
        <FormBuilder />
      </Box>
    </Box>
  );
};

export default FormBuilderEditComponent;
