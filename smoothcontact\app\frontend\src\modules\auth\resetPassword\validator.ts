import { isEmailHalfSize } from '@/utils/validate';
import * as Yup from 'yup';

const EMAIL_REGEX = /^(?!.*\.\.@)(?!.*\.\.)[+\w\-_][+\w\-._]*@[\w\-._]+\.[A-Za-z]+$/;

export const validationSchema = Yup.object().shape({
  email: Yup.string()
    .required('メールアドレスが必須')
    .email('メールアドレスの形式ではありません。')
    .max(50, 'メールアドレスは半角英数字記号、50文字以内で入力してください。')
    .test('is-email-halfSize', 'メールアドレスは半角英数字記号、50文字以内で入力してください。', isEmailHalfSize)
    .matches(EMAIL_REGEX, 'メールアドレスの形式ではありません。'),
});
