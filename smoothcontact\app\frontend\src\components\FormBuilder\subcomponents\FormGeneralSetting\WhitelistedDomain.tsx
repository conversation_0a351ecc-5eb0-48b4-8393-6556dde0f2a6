import { DOMAIN_REGEX } from '@/common/constants';
import DomainIcon from '@mui/icons-material/Domain';
import { Button, FormHelperText, InputAdornment, List, ListItem, TextField, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { FormikValues } from 'formik';
import { isArray } from 'lodash';
import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface WhitelistedDomainProps {
  form: FormikValues;
}

const WhitelistedDomain: FC<WhitelistedDomainProps> = ({ form }) => {
  const { t } = useTranslation();

  const [domain, setDomain] = useState('');
  const canAddDomain = domain.length > 0 && DOMAIN_REGEX.test(domain) && !form?.values?.whitelistedDomain?.includes(domain);

  return (
    <>
      <Typography variant="body1" fontSize={12} color="text.secondary">
        {t('form_builder.whitelist_domain.title')}
      </Typography>
      <FormHelperText>公開URLに独自ドメインを使用するかを選択します</FormHelperText>
      <Stack direction="row" alignItems={'baseline'} justifyContent={'space-around'} spacing={2}>
        <TextField
          size="small"
          fullWidth
          value={domain}
          onChange={(e) => {
            setDomain(e.target.value);
          }}
          error={!!domain.length && !canAddDomain}
          helperText={domain.length && !canAddDomain ? '正しいドメインを入力して下さい' : ''}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start" sx={{ color: '#4CAF50' }}>
                <DomainIcon />
              </InputAdornment>
            ),
          }}
        />
        <Button
          disabled={!canAddDomain}
          onClick={() => {
            const newDomains = isArray(form?.values?.whitelistedDomain) ? form?.values?.whitelistedDomain : [form?.values?.whitelistedDomain];
            form?.setFieldValue('whitelistedDomain', [...newDomains, domain]);
            setDomain('');
          }}
          variant="contained"
          color="primary"
        >
          追加
        </Button>
      </Stack>
      <List style={{ margin: 0 }} dense={true}>
        {form?.values?.whitelistedDomain?.map?.((domain: any, index: any) => (
          <ListItem key={index} sx={{ padding: '2.5px 0' }}>
            <Typography variant="body2" noWrap>
              {domain}
            </Typography>
            <Button
              variant="outlined"
              color="warning"
              onClick={() => {
                const newDomains = form?.values?.whitelistedDomain.filter((value: any) => value !== domain);
                form?.setFieldValue('whitelistedDomain', newDomains);
              }}
              sx={{ padding: '2.5px 0', marginLeft: 'auto' }}
            >
              解除する
            </Button>
          </ListItem>
        ))}
      </List>
    </>
  );
};

export default WhitelistedDomain;
