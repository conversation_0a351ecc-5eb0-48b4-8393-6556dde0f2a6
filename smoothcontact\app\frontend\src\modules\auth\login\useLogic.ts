import useAxios from '@/hooks/useAxios';
import { use<PERSON><PERSON><PERSON>and<PERSON> } from '@/hooks/useFormHandler';
import { useToast } from '@/provider/toastProvider';
import { loginRequestConfig, verifyBackupRequestConfig, verifyMfaRequestConfig } from '@/services/account.service';
import { HttpStatusCode } from 'axios';
import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { LoginRequestDTO, MfaVerifyRequestDTO } from './dto/request.dto';
import { loginValidationSchema, mfaValidationSchema } from './validator';
import { getAppAccessToken, setAppAuthTokens } from '@/utils/helper';

export type FormValue = {
  email: string;
  pwd: string;
};

export default function useLogic() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { apiCaller, loading } = useAxios();
  const [account, setAccount] = useState<{ id: number; mfaEnabled?: boolean }>(null);
  const [verifyMode, setVerifyMode] = useState<'otp' | 'backup'>('otp');
  const accessToken = getAppAccessToken();
  // get query params on URL by react-router-dom
  const [searchParams] = useSearchParams();

  const isAuthenticated = !!accessToken;
  // get query params redirect from URL
  const redirect = searchParams.get('redirect') || '/form-builder';
  useEffect(() => {
    if (isAuthenticated) {
      navigate(redirect);
    }
  }, []);

  const login = async (formData: FormValue) => {
    const dataBody = new LoginRequestDTO();
    dataBody.email = formData.email;
    dataBody.pwd = formData.pwd;
    if (searchParams.get('fromOem') === 'true') {
      dataBody.cb = searchParams.get('cb') || undefined;
      dataBody.s = searchParams.get('s') || undefined;
    }

    const result: any = await apiCaller(loginRequestConfig(dataBody));

    if (result?.success) {
      if (result?.data?.account?.mfaEnabled) {
        setAccount(result?.data?.account);

        return;
      }

      setAppAuthTokens(result.data.accessToken, result.data.refreshToken);

      toast({ isError: false, message: 'ログインに成功しました' });
      navigate(redirect);

      return;
    }

    if (result?.statusCode !== HttpStatusCode.BadRequest) {
      toast({ isError: true, message: result.message });
    }

    loginFormHandler.setErrors(result.messageErrors ?? {});
  };

  const verifyLoginCode = async (formData: { code: string }) => {
    const dataBody = new MfaVerifyRequestDTO();
    dataBody.code = formData.code;
    dataBody.accountId = account.id;
    if (searchParams.get('fromOem') === 'true') {
      dataBody.cb = searchParams.get('cb') || undefined;
      dataBody.s = searchParams.get('s') || undefined;
    }

    const result: any =
      verifyMode === 'otp' ? await apiCaller(verifyMfaRequestConfig(dataBody)) : await apiCaller(verifyBackupRequestConfig(dataBody));

    if (result?.success) {
      setAppAuthTokens(result.data.accessToken, result.data.refreshToken);

      toast({ isError: false, message: 'ログインに成功しました' });
      navigate(redirect);

      return;
    }

    if (result?.statusCode !== HttpStatusCode.BadRequest) {
      toast({ isError: true, message: result.message });
    }

    mfaFormHandler.setErrors(result.messageErrors ?? {});
  };

  const loginFormHandler = useFormHandler<FormValue>({
    initialValues: { email: '', pwd: '' },
    validationSchema: loginValidationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: async (values) => {
      await login(values);
    },
  });

  const mfaFormHandler = useFormHandler<{ code: string }>({
    initialValues: { code: '' },
    validationSchema: mfaValidationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: async (values) => {
      await verifyLoginCode(values);
    },
  });

  const backToLogin = () => {
    setAccount((prev) => ({ ...prev, mfaEnabled: false }));
  };

  const switchVerifyMode = () => {
    mfaFormHandler.resetForm({
      values: {
        code: '',
      },
    });
    setVerifyMode(verifyMode === 'otp' ? 'backup' : 'otp');
  };

  return { loginFormHandler, mfaFormHandler, loading, account, backToLogin, verifyMode, switchVerifyMode };
}
