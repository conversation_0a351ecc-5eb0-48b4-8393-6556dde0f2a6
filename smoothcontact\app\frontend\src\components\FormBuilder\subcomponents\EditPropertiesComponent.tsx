import SCTextEditor from '@/components/common/SCTextEditor';
import useFadeAnimate from '@/hooks/useFadeAnimate';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormButtonType, FormContainerType, FormElementChildrenType } from '@/types/FormTemplateTypes';
import { FormControlNames, FormItemTypes } from '@/utils/formBuilderUtils';
import { Box, Checkbox, Divider, FormControl, FormControlLabel, FormLabel, Radio, RadioGroup, Stack, TextField, Typography } from '@mui/material';
import { isUndefined } from 'lodash';
import { FC, useEffect, useMemo } from 'react';
import * as Yup from 'yup';
import { object } from 'yup';
import AddressFieldProperty from './ElementProperties/AddressFieldProperty';
import ConditionProperty from './ElementProperties/ConditionProperty';
import ContainerProperty from './ElementProperties/ContainerProperty';
import DateFieldProperty from './ElementProperties/DateFieldProperty';
import FullNameFieldProperty from './ElementProperties/FullNameFieldProperty';
import MultilineFieldProperty from './ElementProperties/MultilineFieldProperty';
import TextFieldProperty from './ElementProperties/TextFieldProperty';
import ManageItemsList from './ManageItemsListComponent';

export interface EditPropertiesComponentProps {}

const EditPropertiesComponent: FC<EditPropertiesComponentProps> = () => {
  const { selectedControl: selectedControlBase, setError, editControlProperties, editContainerProperties, editButtonProperties } = useFormBuilder();
  const ANIMATION_DURATION = 300;
  const { valueAnimated: selectedControl, classes } = useFadeAnimate<FormElementChildrenType | FormContainerType | FormButtonType>(
    ANIMATION_DURATION,
    selectedControlBase,
    selectedControlBase?.id
  );

  const controlItem: FormElementChildrenType | FormContainerType | FormButtonType | null = useMemo(() => {
    if (selectedControl?.itemType === FormItemTypes.CONTROL) {
      return selectedControl as FormElementChildrenType;
    }

    if (selectedControl?.itemType === FormItemTypes.CONTAINER) {
      return selectedControl as FormContainerType;
    }

    if (selectedControl?.itemType === FormItemTypes.BUTTON) {
      return selectedControl as FormButtonType;
    }
  }, [selectedControl]);

  const validationSchema = useMemo(
    () =>
      object({
        labelName: Yup.string().when(['controlName', 'itemType'], {
          is: (controlName: FormControlNames, itemType: string) =>
            itemType === FormItemTypes.CONTROL &&
            ![FormControlNames.COMMENT, FormControlNames.FULL_NAME, FormControlNames.ADDRESS].includes(controlName),
          then: (schema) => schema.required('項目タイトルを入力してください'),
        }),
        items: Yup.array().of(Yup.object({ value: Yup.string().trim().required('選択肢を入力してください').min(1, '選択肢を入力してください') })),
        characterType: Yup.string().optional(),
        min: Yup.number()
          .optional()
          .when(['characterType', 'itemType'], {
            is: (val: FormElementChildrenType, itemType: string) =>
              [FormControlNames.INPUT_TEXTFIELD, FormControlNames.INPUT_MULTILINE].includes(val?.controlName) &&
              (isUndefined(val?.characterType) || val?.characterType !== 'text') &&
              itemType === FormItemTypes.CONTROL,
            then: (schema) => schema.required('郵便番号を入力してください'),
          }),
        max: Yup.number()
          .optional()
          .when(['characterType', 'itemType'], {
            is: (characterType: FormElementChildrenType, itemType: string) =>
              [FormControlNames.INPUT_TEXTFIELD, FormControlNames.INPUT_MULTILINE].includes(characterType?.controlName) &&
              (isUndefined(characterType?.characterType) || characterType?.characterType !== 'text') &&
              itemType === FormItemTypes.CONTROL,
            then: (schema) => schema.required('郵便番号を入力してください'),
          }),
        rows: Yup.number().min(1, '1以上の数値を入力してください').max(10, '10以下の数値を入力してください'),
        heading: Yup.string().when('itemType', {
          is: (itemType: string) => itemType === FormItemTypes.CONTAINER,
          then: (schema) => schema.required('項目タイトルを入力してください'),
        }),
        minuteStep: Yup.number().when('showMinuteStep', {
          is: (showMinuteStep: boolean) => showMinuteStep,
          then: (schema) => schema.required('項目タイトルを入力してください').min(1).max(59),
        }),
      }),
    [selectedControl]
  );

  const form = useFormHandler<any>({
    initialValues: {
      controlName: '',
      confirmText: '',
      submitText: '',
      displayText: '',
      description: '',
      labelName: '',
      heading: '',
      itemType: '',
      icon: null,
      required: false,
      items: [],
      category: '',
      index: 0,
      id: '',
      parentId: '',
      condition: [],
      level: 0,
      containerId: '',
      placeholder: '',
      rows: 3,
      dataType: 'date',
      position: 0,
      characterType: 'text',
      min: 0,
      max: 1000,
      showMinuteStep: false,
      dateLimit: 'none',
      minuteStep: 5,
      isUseFurigana: false,
      isAutoFill: false,
      autoFillType: 'hiragana',
      isReduceFullName: false,
      verifyEmail: false,
      verifyEmailPlaceholder: '',
      oneFieldPostcode: false,
      displayPostCodeLink: false,
      displayOtherOption: false,
      displayMode: 'horizontal',
      display: 'none',
      limitedAgeRequired: false,
      limitedAge: 0,
    },
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: (e) => {
      console.log('submit', e);
    },
  });

  useEffect(() => {
    if (form?.isValid && form?.dirty) {
      if (!controlItem) {
        return;
      }

      if (controlItem?.itemType === FormItemTypes.CONTROL) {
        editControlProperties({ ...controlItem, ...form.values } as FormElementChildrenType);
      }

      if (controlItem?.itemType === FormItemTypes.CONTAINER) {
        editContainerProperties({ ...controlItem, ...form.values } as FormContainerType);
      }

      if (controlItem?.itemType === FormItemTypes.BUTTON) {
        editButtonProperties({ ...controlItem, ...form.values } as FormButtonType);
      }
    }

    setError?.(!form.isValid);
  }, [form.isValid, form.values]);

  useEffect(() => {
    form.resetForm({
      values: selectedControl as FormContainerType | FormElementChildrenType | FormButtonType,
    });
  }, [selectedControl]);

  return (
    <Box className={classes}>
      {controlItem ? (
        <>
          {controlItem?.itemType === FormItemTypes.CONTAINER ? (
            <ContainerProperty form={form} />
          ) : (
            <Box className="main-form">
              <form onSubmit={form.handleSubmit} style={{ minWidth: '100%' }}>
                <Stack direction="column" spacing={2}>
                  {controlItem?.itemType === FormItemTypes.BUTTON ? (
                    <>
                      <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
                        <Typography variant="body2">確認ボタンの文言</Typography>
                        <TextField name="confirmText" variant="outlined" {...form.register('confirmText')} value={form?.values?.confirmText ?? ''} />
                        <Typography variant="body2">送信ボタンの文言</Typography>
                        <TextField name="submitText" variant="outlined" {...form.register('submitText')} value={form?.values?.submitText ?? ''} />
                      </Box>
                      <Divider />
                    </>
                  ) : (
                    <>
                      {(controlItem as FormElementChildrenType)?.parentId && <ConditionProperty form={form} key={controlItem.id} />}
                      {controlItem?.controlName !== FormControlNames.COMMENT && (
                        <>
                          <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
                            <Typography variant="body2">項目タイトル</Typography>
                            <TextField
                              name="labelName"
                              variant="outlined"
                              helperText={form?.errors?.labelName?.toString()}
                              error={!!form?.errors?.labelName}
                              {...form.register('labelName')}
                              value={form?.values?.labelName ?? ''}
                            />
                          </Box>
                          <Divider />
                        </>
                      )}
                      {controlItem.controlName === FormControlNames.FULL_NAME ? (
                        <>
                          <FullNameFieldProperty form={form} />
                          <Divider />
                        </>
                      ) : null}
                      {controlItem.controlName === FormControlNames.INPUT_TEXTFIELD ||
                      controlItem.controlName === FormControlNames.INPUT_MULTILINE ||
                      controlItem.controlName === FormControlNames.PHONE ||
                      controlItem.controlName === FormControlNames.EMAIL ? (
                        <>
                          <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
                            <Typography variant="body2">プレースホルダー</Typography>
                            <TextField
                              name="placeholder"
                              variant="outlined"
                              {...form.register('placeholder')}
                              value={form?.values?.placeholder ?? ''}
                            />
                          </Box>
                          <Divider />
                        </>
                      ) : null}
                      {controlItem.controlName === FormControlNames.FULL_NAME ? (
                        <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
                          <Box>
                            <Typography variant="body2">項目プレースホルダー</Typography>
                            <Box sx={{ display: 'flex', flexDirection: 'row' }} gap={2} mt={1}>
                              {form?.values?.isReduceFullName && (
                                <TextField
                                  fullWidth
                                  variant="outlined"
                                  {...form.register('fullNamePlaceholder.full')}
                                  value={form?.values?.fullNamePlaceholder?.full ?? ''}
                                />
                              )}
                              {!form?.values?.isReduceFullName && (
                                <>
                                  <TextField
                                    fullWidth
                                    variant="outlined"
                                    {...form.register('fullNamePlaceholder.last')}
                                    value={form?.values?.fullNamePlaceholder?.last ?? ''}
                                  />
                                  <TextField
                                    fullWidth
                                    variant="outlined"
                                    {...form.register('fullNamePlaceholder.first')}
                                    value={form?.values?.fullNamePlaceholder?.first ?? ''}
                                  />
                                </>
                              )}
                            </Box>
                          </Box>
                          {form?.values?.isUseFurigana && (
                            <Box gap={2}>
                              <Typography variant="body2">フリガナプレースホルダー</Typography>
                              <Box sx={{ display: 'flex', flexDirection: 'row' }} gap={2} mt={1}>
                                {form?.values?.isReduceFullName && (
                                  <TextField
                                    fullWidth
                                    variant="outlined"
                                    {...form.register('fullNamePlaceholder.fullPronunciation')}
                                    value={form?.values?.fullNamePlaceholder?.fullPronunciation ?? ''}
                                  />
                                )}
                                {!form?.values?.isReduceFullName && (
                                  <>
                                    <TextField
                                      fullWidth
                                      variant="outlined"
                                      {...form.register('fullNamePlaceholder.lastPronunciation')}
                                      value={form?.values?.fullNamePlaceholder?.lastPronunciation ?? ''}
                                    />
                                    <TextField
                                      fullWidth
                                      variant="outlined"
                                      {...form.register('fullNamePlaceholder.firstPronunciation')}
                                      value={form?.values?.fullNamePlaceholder?.firstPronunciation ?? ''}
                                    />
                                  </>
                                )}
                              </Box>
                            </Box>
                          )}
                          <Divider />
                        </Box>
                      ) : null}
                      <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={1}>
                        <Typography variant="body2">項目説明文</Typography>
                        <SCTextEditor
                          key={form?.values?.id}
                          id={form?.values?.id}
                          initialValue={form?.values.description ?? ''}
                          onContentChange={(html: string) => form?.setFieldValue('description', html)}
                        />
                        {controlItem.controlName !== FormControlNames.COMMENT && (
                          <FormControlLabel
                            control={
                              <Checkbox
                                value={true}
                                {...form.register('isBottomDescription', { nameOfValueProps: 'checked' })}
                                checked={!!form?.values?.isBottomDescription}
                              />
                            }
                            label="要素の下に表示する"
                          />
                        )}
                      </Box>
                      <Divider />
                      {controlItem.controlName === FormControlNames.RADIO ||
                      controlItem.controlName === FormControlNames.CHECKLIST ||
                      controlItem.controlName === FormControlNames.DROPDOWN ? (
                        <>
                          <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
                            <Typography variant="body2">選択肢（選択肢を1行に1つずつ入力）</Typography>
                            <ManageItemsList name="items" form={form} helperText={form?.errors?.items ? '選択肢を入力してください' : ''} />
                            {(controlItem.controlName === FormControlNames.CHECKLIST || controlItem.controlName === FormControlNames.RADIO) && (
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    key={controlItem?.id}
                                    value={true}
                                    {...form.register('displayOtherOption', { nameOfValueProps: 'checked' })}
                                    checked={!!form?.values.displayOtherOption}
                                  />
                                }
                                label="選択肢に「その他」を表示します"
                              />
                            )}
                            <Divider />
                          </Box>
                        </>
                      ) : null}
                      {controlItem.controlName === FormControlNames.RADIO || controlItem.controlName === FormControlNames.CHECKLIST ? (
                        <>
                          <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
                            <Typography variant="body2">ラジオボタン設定</Typography>
                            <FormControl>
                              <FormLabel id="displayMode-label">ラジオボタンの表示方法</FormLabel>
                              <RadioGroup name="displayMode" {...form.register('displayMode')} value={form?.values?.displayMode ?? 'vertical'}>
                                <FormControlLabel value="horizontal" control={<Radio value="horizontal" />} label="横並び" />
                                <FormControlLabel value="vertical" control={<Radio value="vertical" />} label="縦並び" />
                              </RadioGroup>
                            </FormControl>
                          </Box>
                          <Divider />
                        </>
                      ) : null}

                      {controlItem.controlName === FormControlNames.INPUT_TEXTFIELD ? (
                        <>
                          <TextFieldProperty formHandler={form} />
                          <Divider />
                        </>
                      ) : null}

                      {controlItem.controlName === FormControlNames.INPUT_MULTILINE ? (
                        <>
                          <MultilineFieldProperty
                            formHandler={form}
                            editControlProperties={editControlProperties}
                            selectedControl={controlItem as FormElementChildrenType}
                          />
                          <Divider />
                        </>
                      ) : null}

                      {controlItem.controlName === FormControlNames.ADDRESS ? (
                        <>
                          <AddressFieldProperty form={form} />
                          <Divider />
                        </>
                      ) : null}

                      {controlItem.controlName === FormControlNames.DATE ? (
                        <>
                          <DateFieldProperty form={form} />
                          <Divider />
                        </>
                      ) : null}

                      {controlItem.controlName === FormControlNames.EMAIL ? (
                        <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                value={true}
                                {...form.register('verifyEmail', { nameOfValueProps: 'checked' })}
                                checked={!!form?.values.verifyEmail}
                              />
                            }
                            label="確認用メール項目を設置"
                          />
                          {form?.values.verifyEmail ? (
                            <>
                              <Typography variant="body2">プレースホルダー</Typography>
                              <TextField
                                name="verifyEmailPlaceholder"
                                variant="outlined"
                                {...form.register('verifyEmailPlaceholder')}
                                value={form?.values.verifyEmailPlaceholder ?? ''}
                              />
                            </>
                          ) : null}

                          <Divider />
                        </Box>
                      ) : null}

                      {controlItem.controlName === FormControlNames.BIRTHDAY && (
                        <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                value={true}
                                {...form.register('limitedAgeRequired', { nameOfValueProps: 'checked' })}
                                checked={!!form?.values.limitedAgeRequired}
                              />
                            }
                            label="年齢制限「〇〇歳以上」"
                          />
                          {form?.values.limitedAgeRequired === true && (
                            <Box>
                              <Typography variant="body2" sx={{ mb: 1 }}>
                                年齢制限
                              </Typography>
                              <TextField
                                type="number"
                                InputProps={{ inputProps: { min: 0 } }}
                                size="small"
                                variant="outlined"
                                {...form.register('limitedAge')}
                                value={form?.values.limitedAge ?? 18}
                              />
                            </Box>
                          )}
                          <Divider />
                        </Box>
                      )}

                      {controlItem.controlName !== FormControlNames.COMMENT && (
                        <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                value={true}
                                {...form.register('required', { nameOfValueProps: 'checked' })}
                                checked={!!form?.values.required}
                              />
                            }
                            label="回答を必須にする"
                          />
                        </Box>
                      )}
                    </>
                  )}
                </Stack>
              </form>
            </Box>
          )}
        </>
      ) : (
        <>
          <Box role="alert">
            <Typography variant="h6" sx={{ pb: 1 }}>
              項目を選択してください
            </Typography>
            <Typography variant="body2">選択すると、ここに詳細設定が表示されます</Typography>
          </Box>
        </>
      )}
    </Box>
  );
};

export default EditPropertiesComponent;
