import { HocModalProps, withModal } from '@/hoc/withModal';
import React from 'react';
import FormSubmissionListComponent from './components';
import { FormSubmissionListProvider } from './store/FormSubmissionListProvider';
import { useParams } from 'react-router-dom';

const FormSubmissionIndexModule: React.FC = ({ setOpenModal }: HocModalProps) => {
  const { id: extId } = useParams();

  return (
    <FormSubmissionListProvider extId={extId} setOpenModal={setOpenModal}>
      <FormSubmissionListComponent />
    </FormSubmissionListProvider>
  );
};

export default withModal(FormSubmissionIndexModule);
