import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AccountModule } from '../account/account.module';
import { MfaBackupEntity } from './entities/mfa-backup.entity';
import { <PERSON><PERSON><PERSON>ontroller } from './mfa.controller';
import { MfaService } from './mfa.service';

@Module({
  imports: [TypeOrmModule.forFeature([MfaBackupEntity]), forwardRef(() => AccountModule)],
  controllers: [MfaController],
  providers: [MfaService],
  exports: [MfaService],
})
export class MfaModule {}
