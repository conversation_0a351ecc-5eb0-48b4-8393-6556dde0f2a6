import { COLOR_DEFAULT, FormBorderColors } from '@/common/constants';
import FormElementAddressAutofill from '@/components/FormBuilder/subcomponents/FormElementAddressAutofill';
import FormElementCheckList from '@/components/FormBuilder/subcomponents/FormElementCheckList';
import FormElementEmail from '@/components/FormBuilder/subcomponents/FormElementEmail';
import FormElementFullName from '@/components/FormBuilder/subcomponents/FormElementFullName';
import FormElementRadio from '@/components/FormBuilder/subcomponents/FormElementRadio';
import SCInputBirthday from '@/components/common/SCInputBirthday';
import SCToggleSwitch from '@/components/common/SCToggleSwitch';
import SCUpload from '@/components/common/SCUpload';
import {
  FormColorSetting,
  FormElementChildrenType,
  FormItemValue,
  ItemAddressValue,
  ItemFullNameValue,
  ItemUploadValue,
} from '@/types/FormTemplateTypes';
import { ISO } from '@/utils/dateTime';
import { FormControlNames, FormInputAnimationTypes } from '@/utils/formBuilderUtils';
import { alphaColor, displayFontFamily, getIdFromUrl } from '@/utils/helper';
import { KeyboardArrowDown } from '@mui/icons-material';
import { Checkbox, FormControl, FormHelperText, Select } from '@mui/material';
import { DateTimePicker, LocalizationProvider, PickerValidDate } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import dayjs from 'dayjs';
import React, { FC, useEffect } from 'react';
import { makeStyles } from 'tss-react/mui';
import renderInput from './RenderInput';

export interface RenderItemProps {
  item: FormElementChildrenType;
  formColorSetting: FormColorSetting;
  value?: FormItemValue;
  onChange?: (e: React.ChangeEvent<any>) => void;
  onForceChange?: (id: string, value: PickerValidDate | FormItemValue) => void;
  onBlur?: (e: React.FocusEvent<any>) => void;
  onValid?: (isValid: boolean) => void;
  helperText?: string;
  isPreview?: boolean;
  isError?: boolean;
  formHandler?: any;
  isSubmitting?: boolean;
  inputAnimation?: string;
  layout?: 'vertical' | 'horizontal';
}

const useStyles = makeStyles<{
  formColorSetting: FormColorSetting;
  inputAnimation?: string;
}>()((_theme, { formColorSetting, inputAnimation }) => ({
  input: {
    '& input': {
      color: formColorSetting?.entryFormSettings?.color ?? COLOR_DEFAULT,
      borderColor: formColorSetting?.entryFormSettings?.borderColor ?? FormBorderColors.DEFAULT,
    },
    '& .MuiInputBase-root': {
      background: formColorSetting?.entryFormSettings?.bgColor,
      color: formColorSetting?.entryFormSettings?.color ?? COLOR_DEFAULT,
      fontSize: `${formColorSetting?.entryFormSettings?.fontSize}${formColorSetting?.entryFormSettings?.fontSizeUnit}`,
      borderRadius: `${formColorSetting?.entryFormSettings?.borderRadius}${formColorSetting?.entryFormSettings?.borderRadiusUnit}`,
      '& .MuiInputBase-input': {
        fontSize: `${formColorSetting?.entryFormSettings?.fontSize}${formColorSetting?.entryFormSettings?.fontSizeUnit}`,
      },
      '&:hover': {
        '.MuiOutlinedInput-notchedOutline': {
          borderColor: alphaColor(formColorSetting?.entryFormSettings?.borderColor, 0.5, FormBorderColors.DEFAULT),
        },
      },
    },
    '& .MuiOutlinedInput-notchedOutline': {
      borderColor: formColorSetting?.entryFormSettings?.borderColor,
      borderRadius: `${formColorSetting?.entryFormSettings?.borderRadius}${formColorSetting?.entryFormSettings?.borderRadiusUnit}`,
    },
    '& .MuiFormHelperText-root': {
      marginLeft: 0,
      marginRight: 0,
    },
    '& .MuiSelect-select.MuiSelect-outlined.MuiInputBase-input.MuiOutlinedInput-input': {
      paddingRight: 0,
    },
  },
  textarea: {
    '& textarea': {
      color: formColorSetting?.entryFormSettings?.color ?? COLOR_DEFAULT,
      borderColor: formColorSetting?.entryFormSettings?.borderColor ?? FormBorderColors.DEFAULT,
    },
    '& .MuiInputBase-root': {
      background: formColorSetting?.entryFormSettings?.bgColor,
      color: formColorSetting?.entryFormSettings?.color ?? COLOR_DEFAULT,
      fontSize: `${formColorSetting?.entryFormSettings?.fontSize}${formColorSetting?.entryFormSettings?.fontSizeUnit}`,
      borderRadius: `${formColorSetting?.entryFormSettings?.borderRadius}${formColorSetting?.entryFormSettings?.borderRadiusUnit}`,
      padding: 0,
      '& .MuiInputBase-inputMultiline': {
        padding: '8.5px 14px',
        fontSize: `${formColorSetting?.entryFormSettings?.fontSize}${formColorSetting?.entryFormSettings?.fontSizeUnit}`,
      },
      '& .MuiInputBase-inputMultiline:hover': {
        borderColor: alphaColor(formColorSetting?.entryFormSettings?.borderColor, 0.5, FormBorderColors.DEFAULT),
      },
      '&:hover': {
        '.MuiOutlinedInput-notchedOutline': {
          borderColor: alphaColor(formColorSetting?.entryFormSettings?.borderColor, 0.5, FormBorderColors.DEFAULT),
        },
      },
    },
    '& .MuiOutlinedInput-notchedOutline': {
      borderColor: formColorSetting?.entryFormSettings?.borderColor,
      borderRadius: `${formColorSetting?.entryFormSettings?.borderRadius}${formColorSetting?.entryFormSettings?.borderRadiusUnit}`,
    },
    '& .MuiFormHelperText-root': {
      marginLeft: 0,
      marginRight: 0,
    },
    '& .MuiSelect-select.MuiSelect-outlined.MuiInputBase-input.MuiOutlinedInput-input': {
      paddingRight: 0,
    },
  },
  select: {
    '&.MuiInputBase-root': {
      background: inputAnimation === FormInputAnimationTypes.LABEL_TOP ? formColorSetting?.bgColor : formColorSetting?.entryFormSettings?.bgColor,
      fontSize: `${formColorSetting?.entryFormSettings?.fontSize}${formColorSetting?.entryFormSettings?.fontSizeUnit}`,
      borderRadius:
        inputAnimation === FormInputAnimationTypes.LABEL_TOP
          ? 0
          : `${formColorSetting?.entryFormSettings?.borderRadius}${formColorSetting?.entryFormSettings?.borderRadiusUnit}`,
      '& .MuiNativeSelect-select.MuiInputBase-input.MuiOutlinedInput-input:not([multiple]) option': {
        background: inputAnimation === FormInputAnimationTypes.LABEL_TOP ? formColorSetting?.bgColor : formColorSetting?.entryFormSettings?.bgColor,
      },
      minHeight: '38px',
    },
    minHeight: 'auto',
    color: formColorSetting?.entryFormSettings?.color,
    background: inputAnimation === FormInputAnimationTypes.LABEL_TOP ? formColorSetting?.bgColor : formColorSetting?.entryFormSettings?.bgColor,
    '& .MuiOutlinedInput-notchedOutline': {
      borderColor: formColorSetting?.entryFormSettings?.borderColor ?? FormBorderColors.DEFAULT,
    },
    '&:hover': {
      '.MuiOutlinedInput-notchedOutline': {
        borderColor: alphaColor(formColorSetting?.entryFormSettings?.borderColor, 0.5, FormBorderColors.DEFAULT),
      },
    },
  },
  radio: {
    '&.MuiFormControlLabel-root': {
      '& .MuiFormControlLabel-label': {
        color: formColorSetting?.generalSettings?.color,
      },
      '& .MuiButtonBase-root.MuiRadio-root': {
        color: formColorSetting?.generalSettings?.color,
      },
      '& .MuiButtonBase-root.MuiCheckbox-root': {
        color: formColorSetting?.generalSettings?.color,
      },
      '& .MuiButtonBase-root.MuiRadio-root.Mui-checked': {
        color: formColorSetting?.choiceSettings?.color ?? FormBorderColors.DEFAULT,
        '& .MuiSvgIcon-root': {
          fill: formColorSetting?.choiceSettings?.color ?? '#333333',
        },
      },
      '& .MuiButtonBase-root.MuiCheckbox-root.Mui-checked': {
        color: formColorSetting?.choiceSettings?.color ?? FormBorderColors.DEFAULT,
        '& .MuiSvgIcon-root': {
          fill: formColorSetting?.choiceSettings?.color ?? '#333333',
        },
      },
    },
  },
  label: {
    color: formColorSetting?.labelSettings?.color,
    fontFamily: displayFontFamily(formColorSetting?.labelSettings?.fontFamily),
    fontSize: `${formColorSetting?.labelSettings?.fontSize}${formColorSetting?.labelSettings?.fontSizeUnit}`,
  },
  general: {
    color: formColorSetting?.generalSettings?.color,
    fontFamily: displayFontFamily(formColorSetting?.generalSettings?.fontFamily),
    fontSize: `${formColorSetting?.generalSettings?.fontSize}${formColorSetting?.generalSettings?.fontSizeUnit}`,
  },
  formGroup: {
    '& .MuiButtonBase-root.Mui-checked': {
      color: formColorSetting?.labelSettings?.color,
    },
  },
  toggle: {
    '& .MuiSwitch-switchBase': {
      '&.Mui-checked': {
        '& + .MuiSwitch-track': {
          background: formColorSetting?.labelSettings?.color,
        },
      },
    },
  },
  attachment: {
    border: `1px solid ${formColorSetting?.entryFormSettings?.borderColor ?? FormBorderColors.DEFAULT}`,
    borderRadius: `${formColorSetting?.entryFormSettings?.borderRadius}${formColorSetting?.entryFormSettings?.borderRadiusUnit}`,
    minHeight: '100px',
    '.fileIcon': {
      '.MuiCircularProgress-root': {
        color: formColorSetting?.labelSettings?.color,
      },
    },
    cursor: 'pointer',
  },
}));

const ArrowDownIcon = () => <KeyboardArrowDown />;

const RenderItem: FC<RenderItemProps> = (props) => {
  const {
    item,
    formColorSetting,
    value,
    helperText,
    isError,
    isPreview,
    onChange,
    onBlur,
    onForceChange,
    onValid,
    isSubmitting,
    inputAnimation,
    layout,
  } = props;

  const { classes } = useStyles({ formColorSetting, inputAnimation });
  const [checked, setChecked] = React.useState(false);

  useEffect(() => {
    return () => {
      onForceChange && onForceChange(item.id, '');
    };
  }, []);

  switch (item.controlName) {
    case FormControlNames.INPUT_TEXTFIELD:
      return <>{renderInput(inputAnimation, item, props, classes.input, formColorSetting?.entryFormSettings)}</>;

    case FormControlNames.INPUT_MULTILINE:
      return <>{renderInput(inputAnimation, item, props, classes.textarea, formColorSetting?.entryFormSettings)}</>;

    case FormControlNames.CHECKBOX:
      return (
        <>
          <Checkbox />
          {isError && <FormHelperText error={isError}>{helperText}</FormHelperText>}
        </>
      );

    case FormControlNames.DATE:
      const handleDateChange = (value: PickerValidDate, format = '') => {
        if (value === null) {
          onForceChange && onForceChange(item.id, '');

          return;
        }

        onForceChange && onForceChange(item.id, dayjs(value.toDate()).format(format));
      };
      const fieldSlotProps: any = {
        textField: {
          fullWidth: true,
          error: isError,
          size: 'small',
        },
        popper: {
          keepMounted: true,
        },
      };
      switch (item.dataType) {
        case 'date':
          const dateValue = value ? dayjs(value as string, ISO.DATE) : null;

          return (
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                className={classes.input}
                name={item.id}
                slotProps={fieldSlotProps}
                slots={{ popper: null }}
                format={ISO.DATE}
                value={dateValue}
                onChange={(value) => handleDateChange(value, ISO.DATE)}
              />
              <FormHelperText error={isError}>{helperText}</FormHelperText>
            </LocalizationProvider>
          );

        case 'time':
          const timeValue = value ? dayjs(value as string, ISO.TIME) : null;

          return (
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <TimePicker
                className={classes.input}
                name={item.id}
                slotProps={fieldSlotProps}
                timeSteps={{ minutes: item.showMinuteStep ? Number(item.minuteStep ?? 1) : 1 }}
                format={ISO.TIME}
                value={timeValue}
                onChange={(value) => handleDateChange(value, ISO.TIME)}
                closeOnSelect={false}
              />
              <FormHelperText error={isError}>{helperText}</FormHelperText>
            </LocalizationProvider>
          );

        case 'dateTime':
          const dateTimeValue = value ? dayjs(value as string, ISO.DATE_TIME) : null;

          return (
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DateTimePicker
                className={classes.input}
                name={item.id}
                slotProps={fieldSlotProps}
                disableFuture={item.dateLimit === 'past'}
                disablePast={item.dateLimit === 'future'}
                timeSteps={{ minutes: item.showMinuteStep ? Number(item.minuteStep ?? 1) : 1 }}
                format={ISO.DATE_TIME}
                value={dateTimeValue}
                onChange={(value) => handleDateChange(value, ISO.DATE_TIME)}
                closeOnSelect={false}
              />
              <FormHelperText error={isError}>{helperText}</FormHelperText>
            </LocalizationProvider>
          );
      }
      break;

    case FormControlNames.TIME:
      return (
        <LocalizationProvider dateAdapter={AdapterMoment}>
          <TimePicker slotProps={{ textField: { fullWidth: true } }} />
        </LocalizationProvider>
      );

    case FormControlNames.FILE_UPLOAD:
      const handleChangeFile = (value: ItemUploadValue) => {
        onForceChange && onForceChange(item.id, value);
      };
      const uploadApi = isPreview ? '' : `/api/embed/${getIdFromUrl()}/upload`;

      return (
        <SCUpload
          name={item.id}
          title={item.labelName}
          className={classes.attachment}
          error={isError}
          helperText={helperText}
          value={value as ItemUploadValue}
          uploadApi={uploadApi}
          onChangeFile={handleChangeFile}
          onBlur={onBlur}
        />
      );

    case FormControlNames.IMAGE_UPLOAD:
      return (
        <>
          <input style={{ display: 'none' }} id={item.controlName + item.id} type="file" />
          <label className="control-input-trigger-buttons" htmlFor={item.controlName + item.id} aria-label="Upload Image">
            <i className="far fa-image"></i>
          </label>
        </>
      );

    case FormControlNames.SCAN_CODE:
      return (
        <>
          <input style={{ display: 'none' }} id={item.controlName + item.id} type="file" />
          <label className="control-input-trigger-buttons" htmlFor={item.controlName + item.id} aria-label="Scan QRCode">
            <i className="fas fa-qrcode"></i>
          </label>
        </>
      );

    case FormControlNames.FULL_NAME:
      const onChangeFullName = (value: ItemFullNameValue, isValid: boolean) => {
        onValid && onValid(isValid);
        onForceChange && onForceChange(item.id, value);
      };

      return (
        <FormElementFullName
          classInput={classes.input}
          classLabel={classes.general}
          label={item.labelName}
          name={item.id}
          placeholder={item.placeholder}
          fullNamePlaceholder={item.fullNamePlaceholder}
          value={value}
          isError={isError}
          onlyOneName={item.isReduceFullName}
          hasPronunciation={item.isUseFurigana}
          required={item.required}
          isSubmitting={isSubmitting}
          inputAnimation={inputAnimation}
          helperText={helperText}
          isAutoFill={item.isAutoFill}
          autoFillType={item.autoFillType}
          onChange={onChangeFullName}
          entryFormSetting={formColorSetting?.entryFormSettings}
        />
      );

    case FormControlNames.PHONE:
      return <>{renderInput(inputAnimation, item, props, classes.input, formColorSetting?.entryFormSettings)}</>;

    case FormControlNames.EMAIL:
      const onChangeEmail = (value: FormItemValue, isValid: boolean) => {
        onValid && onValid(isValid);
        onForceChange && onForceChange(item.id, value);
      };

      return (
        <FormElementEmail
          classInput={classes.input}
          label={item.labelName}
          name={item.id}
          placeholder={item.placeholder}
          verifyEmailPlaceholder={item.verifyEmailPlaceholder}
          isError={isError}
          value={value}
          inputAnimation={inputAnimation}
          required={item.required}
          isSubmitting={isSubmitting}
          hasConfirmEmail={item.verifyEmail}
          onChange={onChangeEmail}
          entryFormSetting={formColorSetting?.entryFormSettings}
        />
      );

    case FormControlNames.ADDRESS:
      const handleAddressChange = (value: ItemAddressValue, isValid?: boolean) => {
        onValid && onValid(isValid);
        onForceChange && onForceChange(item.id, value);
      };

      return (
        <FormElementAddressAutofill
          required={item.required}
          oneFieldPostcode={Boolean(item.oneFieldPostcode)}
          displayPostCodeLink={item.displayPostCodeLink}
          classLabel={classes.general}
          classInput={classes.input}
          classSelect={classes.select}
          entryFormSetting={formColorSetting?.entryFormSettings}
          isSubmitting={isSubmitting}
          value={value}
          onChange={handleAddressChange}
          layout={layout}
          inputAnimation={inputAnimation}
        />
      );

    case FormControlNames.BIRTHDAY:
      return (
        <SCInputBirthday
          limitAge={item.limitedAgeRequired}
          minAge={item.limitedAge ?? 0}
          name={item.id}
          classes={classes}
          value={value}
          error={isError}
          helperText={helperText}
          onChange={onChange}
        />
      );

    case FormControlNames.SIGNATURE:
      return (
        <label className="control-input-trigger-buttons" style={{ width: '270px' }} htmlFor={item.controlName + item.id}>
          <span className="sign-label">Sign Here</span>
        </label>
      );

    case FormControlNames.TOGGLE:
      return (
        <SCToggleSwitch
          className={classes.toggle}
          checked={checked}
          onChange={(e) => setChecked(e.target.checked)}
          inputProps={{ 'aria-label': 'controlled' }}
        />
      );

    case FormControlNames.CHECKLIST:
      return (
        <FormElementCheckList
          key={item.id}
          classContainer={classes.radio}
          classInput={classes.input}
          value={value as string[]}
          items={item.items || []}
          name={item.id}
          error={isError}
          helperText={helperText}
          isVertical={!item?.displayMode || item?.displayMode === 'vertical'}
          displayOtherOption={item.displayOtherOption}
          onChange={(value) => {
            onForceChange && onForceChange(item.id, value);
          }}
          inputAnimation={inputAnimation}
          entryFormSetting={formColorSetting?.entryFormSettings}
        />
      );

    case FormControlNames.RADIO:
      return (
        <FormElementRadio
          classContainer={classes.radio}
          classInput={classes.input}
          name={item.id}
          value={value as string}
          items={item.items}
          error={isError}
          helperText={helperText}
          isVertical={!item?.displayMode || item?.displayMode === 'vertical'}
          displayOtherOption={item.displayOtherOption}
          onChange={onChange}
          entryFormSetting={formColorSetting?.entryFormSettings}
          inputAnimation={inputAnimation}
        />
      );

    case FormControlNames.DROPDOWN:
      return (
        <FormControl fullWidth>
          <Select
            IconComponent={ArrowDownIcon}
            MenuProps={{
              autoFocus: false,
              disableAutoFocus: false,
              disableEnforceFocus: false,
              disableScrollLock: true,
            }}
            name={item.id}
            className={classes.select}
            size="small"
            variant="outlined"
            sx={{
              fontSize: `${formColorSetting?.entryFormSettings?.fontSize}${formColorSetting?.entryFormSettings?.fontSizeUnit}`,
              borderRadius: `${formColorSetting?.entryFormSettings?.borderRadius}${formColorSetting?.entryFormSettings?.borderRadiusUnit}`,
              color: formColorSetting?.entryFormSettings?.color,
              borderColor: formColorSetting?.entryFormSettings?.borderColor,
              backgroundColor: formColorSetting?.entryFormSettings?.bgColor,
            }}
            value={value}
            onChange={onChange}
            onBlur={onBlur}
            native={true}
          >
            <option key={0} value="">
              ----
            </option>
            {item.items?.map?.((i) => (
              <option key={i.value} value={i.value}>
                {i.label}
              </option>
            ))}
          </Select>
          {isError && <FormHelperText error={isError}>{helperText}</FormHelperText>}
        </FormControl>
      );

    case FormControlNames.COMMENT:
      return <></>;
  }

  return <></>;
};

export default React.memo(RenderItem);
