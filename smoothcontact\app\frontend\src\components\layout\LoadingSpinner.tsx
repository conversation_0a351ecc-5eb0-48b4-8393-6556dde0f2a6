import { useLoading } from '@/provider/loadingProvider';
import { Backdrop, CircularProgress } from '@mui/material';
import React from 'react';

const LoadingSpinner: React.FC = () => {
  const { isLoading } = useLoading();

  if (!isLoading) return null;

  return (
    <div>
      <Backdrop sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }} open={isLoading}>
        <CircularProgress color="inherit" />
      </Backdrop>
    </div>
  );
};

export default LoadingSpinner;
