import MfaSetting from './components/setting';
import useLogic from './useLogic';

const MfaSettingIndexModule: React.FC = () => {
  const { profile, loading, formHandler, backupCodes, cancel, reissueBackupCode } = useLogic();

  if (!profile) {
    return null;
  }

  return (
    <MfaSetting
      formHandler={formHandler}
      reissueBackupCode={reissueBackupCode}
      cancel={cancel}
      data={profile}
      loading={loading}
      backupCodes={backupCodes}
    />
  );
};

export default MfaSettingIndexModule;
