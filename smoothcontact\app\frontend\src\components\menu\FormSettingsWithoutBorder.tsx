import React, { FC, useMemo } from 'react';
import SCChip from '../common/SCChip';
import { TemplateType } from '@/types/FormTemplateTypes';
import { Table, TableBody, TableCell, TableRow } from '@mui/material';
import { OptionAfterSubmitForm, SettingFlag } from '@/utils/formBuilderUtils';
import { isArray } from 'lodash';
import { Link } from 'react-router-dom';
import styled from '@emotion/styled';

interface FormSettingsWithoutBorderProps {
  item: TemplateType;
}

interface FormSetting {
  name: string;
  status?: string;
  value: string | string[];
}

const StyledLink = styled(Link)`
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
`;

const createFormSettingWithStatus = (name: string, status: string, value: any) => ({ name, status, value });

const getScreenAfterSendingType = (screenAfterSendingType: string, message: string, specificUrl: string) => {
  switch (screenAfterSendingType) {
    case OptionAfterSubmitForm.displayMessage:
      return (
        <>
          {message
            ? message.split('\n')?.map?.((line, index) => (
                <React.Fragment key={index}>
                  {line}
                  <br />
                </React.Fragment>
              ))
            : 'ポップアップメッセージを表示'}
        </>
      );

    case OptionAfterSubmitForm.specifiedUrl:
      return specificUrl ?? '指定URLに遷移';

    default:
      return '';
  }
};

const getStatus = (condition: boolean) => (condition ? SettingFlag.enabled : SettingFlag.disabled);

const renderCustomSMTPText = (smtpHost?: string, smtpPort?: number, smtpUsername?: string, smtpFromEmail?: string) => (
  <>
    {smtpHost}:{smtpPort.toString()}
    <br />
    {smtpUsername}
    <br />
    {smtpFromEmail}
  </>
);

const renderWhitelistedDomains = (domains: string[] | undefined) => {
  if (!domains || domains.length === 0) {
    return '';
  }

  return domains?.map?.((domain, index) => (
    <React.Fragment key={index}>
      {domain}
      {index < domains.length - 1 && <br />}
    </React.Fragment>
  ));
};

const renderSettingValue = (setting: FormSetting) => {
  if (setting.name === 'プライバシーポリシー' || setting.name === '利用規約') {
    return (
      <StyledLink to={isArray(setting.value) ? setting.value[0] : setting.value} target="_blank" rel="noopener noreferrer" color="secondary">
        {isArray(setting.value) ? setting.value[0] : setting.value}
      </StyledLink>
    );
  }

  return setting.value;
};

const createFormSettingsWithoutBorderArray = (item: TemplateType) => {
  const { formMailSetting, formGeneralSetting } = item;

  return [
    createFormSettingWithStatus(
      '送信完了後の画面',
      SettingFlag.enabled,
      getScreenAfterSendingType(formMailSetting?.screenAfterSendingType || '', formMailSetting?.message || '', formMailSetting?.specifiedUrl || '')
    ),
    createFormSettingWithStatus(
      '自動返信メール',
      getStatus(!!formMailSetting?.isAutoReply),
      formMailSetting?.isAutoReply ? formMailSetting.autoReplyEmailAddress || '' : ''
    ),
    createFormSettingWithStatus(
      'SMTP情報',
      getStatus(!!formMailSetting?.useCustomSMTP),
      formMailSetting?.useCustomSMTP
        ? renderCustomSMTPText(formMailSetting.smtpHost, formMailSetting.smtpPort, formMailSetting.smtpUsername, formMailSetting.smtpFromEmail)
        : ''
    ),
    createFormSettingWithStatus(
      '公開URLのドメイン',
      getStatus(formGeneralSetting?.whitelistedDomain?.length > 0),
      formGeneralSetting?.whitelistedDomain?.length > 0 ? renderWhitelistedDomains(formGeneralSetting?.whitelistedDomain) : ''
    ),
    createFormSettingWithStatus('reCAPTCHA設定', getStatus(!!formGeneralSetting?.isSettingReCAPTCHA), ''),
    createFormSettingWithStatus(
      '検索ロボット巡回設定',
      getStatus(!!formGeneralSetting?.isDisplaySearchEngine),
      formGeneralSetting?.isDisplaySearchEngine ? '検索結果に公開ページを表示する' : '公開ページを検索結果に表示させない'
    ),
    createFormSettingWithStatus(
      'プライバシーポリシー',
      getStatus(!!formGeneralSetting?.isSettingPrivacyPolicy),
      formGeneralSetting?.isSettingPrivacyPolicy ? formGeneralSetting?.policyLink || '' : ''
    ),
    createFormSettingWithStatus(
      '利用規約',
      getStatus(!!formGeneralSetting?.isDisplayTermsUse),
      formGeneralSetting?.isDisplayTermsUse ? formGeneralSetting?.termsUse || '' : ''
    ),
  ];
};

const FormSettingsWithoutBorder: FC<FormSettingsWithoutBorderProps> = ({ item }) => {
  const formSettingWithoutBorder = useMemo(() => createFormSettingsWithoutBorderArray(item), [item]);

  const renderTableRow = (setting: FormSetting) => (
    <TableRow key={setting.name}>
      <TableCell component="th" sx={{ maxWidth: '140px', pb: '4px' }}>
        {setting.name}
      </TableCell>
      <TableCell sx={{ width: 80, pb: '4px' }}>
        {setting.status && (
          <SCChip
            sx={{ fontSize: '0.76rem', height: '22px' }}
            size="small"
            label={setting.status}
            color={setting.status === SettingFlag.enabled ? 'success' : 'default'}
          />
        )}
      </TableCell>
      <TableCell sx={{ pb: '4px' }}>{renderSettingValue(setting)}</TableCell>
    </TableRow>
  );

  return (
    <Table aria-label="form details without border" sx={{ border: 'none' }}>
      <TableBody>{formSettingWithoutBorder?.map?.(renderTableRow)}</TableBody>
    </Table>
  );
};

export default FormSettingsWithoutBorder;
