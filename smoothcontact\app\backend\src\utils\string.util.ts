import * as path from 'path';
import ShortUniqueId from 'short-unique-id';

export class StringUtil {
  static uid: ShortUniqueId = new ShortUniqueId({
    length: 32,
    dictionary: [
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      'q',
      'w',
      'e',
      'r',
      't',
      'y',
      'u',
      'i',
      'o',
      'p',
      'a',
      's',
      'd',
      'f',
      'g',
      'h',
      'j',
      'k',
      'l',
      'z',
      'x',
      'c',
      'v',
      'b',
      'n',
      'm',
    ],
  });

  static replaceAll(str: string, oldValue: string, newValue: string): string {
    const regex: RegExp = new RegExp(oldValue, 'g');

    return str.replace(regex, newValue);
  }

  static getUid(): string {
    return this.uid.randomUUID();
  }

  static isNumeric(value: string): boolean {
    return /^-?\d+$/.test(value);
  }

  static encodeFilename(filePath: string): string {
    const directory = path.dirname(filePath);
    const filename = path.basename(filePath);
    const encodedFilename = encodeURIComponent(filename);

    return path.join(directory, encodedFilename);
  }

  static convertNewlinesToHtmlBreaks(str: string): string {
    return str.replace(/(?:\r\n|\r|\n)/g, '<br>');
  }
}
