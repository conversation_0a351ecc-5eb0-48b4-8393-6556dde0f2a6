import React, { FC, useEffect, useState } from 'react';
import { Bar<PERSON>hart } from '@mui/x-charts/BarChart';
import { axisClasses } from '@mui/x-charts/ChartsAxis';

interface ReportBarChartLastSixMonthsProps {
  chartData: Record<string, number>;
}

interface DatasetEntry {
  date: string;
  submissionsTotal: number;
}

const generateLastSixMonthsData = (): DatasetEntry[] => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;

  let year = currentYear;
  let month = currentMonth - 5;

  if (month <= 0) {
    month += 12;
    year -= 1;
  }

  const data: DatasetEntry[] = [];

  for (let i = 0; i < 6; i++) {
    const date = `${year}-${String(month).padStart(2, '0')}`;
    data.push({ date, submissionsTotal: 0 });
    month++;
    if (month > 12) {
      month = 1;
      year++;
    }
  }

  return data;
};

const chartSettings = {
  legend: {
    hidden: true,
  },
  series: [{ dataKey: 'submissionsTotal', label: '' }],
  height: 400,
  borderRadius: 10,
  sx: {
    [`.${axisClasses.left} .${axisClasses.label}`]: {
      transform: 'translate(-5px, 0)',
    },
  },
  yAxis: [
    {
      min: 0,
      tickMinStep: 1,
    },
  ],
};

const ReportBarChartLastSixMonths: FC<ReportBarChartLastSixMonthsProps> = ({ chartData }) => {
  const [dataset, setDataset] = useState<DatasetEntry[]>([]);

  useEffect(() => {
    const data = generateLastSixMonthsData().map(({ date }) => ({
      date,
      submissionsTotal: chartData[date] || 0,
    }));
    setDataset(data);
  }, [chartData]);

  return (
    <BarChart
      dataset={dataset as any}
      xAxis={[
        {
          scaleType: 'band',
          dataKey: 'date',
          categoryGapRatio: 0.4,
          colorMap: {
            type: 'piecewise',
            thresholds: [new Date(2024, 1, 1), new Date(2050, 1, 1)],
            colors: ['#24CBD4'],
          },
        } as any,
      ]}
      {...chartSettings}
    />
  );
};

export default ReportBarChartLastSixMonths;
