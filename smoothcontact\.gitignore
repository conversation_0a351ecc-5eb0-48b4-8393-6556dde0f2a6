# Environment Configuration
.env
.idea
.yarn
app/backend/.yarn
app/frontend/.yarn
app/backend/configs/*
app/frontend/public/tinymce/

# Ignore upload files
app/backend/uploads/*

# Dependency directory
node_modules

# Test coverage directory
coverage

# Ignore Apple macOS Desktop Services Store
.DS_Store

# Logs
logs
*.log

# ngrok tunnel file
config/tunnel.pid

# vite build output
dist/

# extensions build output
extensions/*/build

# Node library SQLite database
web/database.sqlite
db-volume

# Docker Sql
*.sql

*-audit.json
node_modules

docker-compose.yml
.yarnrc.yml

db-volumn

# deploy info
*.sh
*.pem