import React, { useEffect, useRef, useState } from 'react';
import { Box, CircularProgress, CssBaseline, Typography } from '@mui/material';
import { useParams } from 'react-router-dom';
import useLogic from '@/modules/embed/useLogic';
import useFormStyles from '@/hooks/useFormStyle';
import { displayFontFamily, MESSAGE_MAP } from '@/utils/helper';
import { FormColorSetting, FormItemValue, TemplateType } from '@/types/FormTemplateTypes';
import FormStandalone from '@/components/FormBuilder/subcomponents/FormStandalone';
import FormDisplayMessage from '@/components/FormBuilder/subcomponents/FormDisplayMessage';
import FormConfirmStandalone from '@/components/FormBuilder/subcomponents/FormConfirmStandalone';
import { FORM_COLOR_SETTING_INIT, FormTemplateModePresentType, FormTemplateModePresets } from '@/utils/formBuilderUtils';

interface FormFactoryProps {
  template: TemplateType;
  error: boolean;
  formMessage?: string;
  formData: Record<string, FormItemValue>;
  submitError: string;
  loading: boolean;
  submitting: boolean;
  isShowConfirm: boolean;
  handleConfirm: (values: Record<string, FormItemValue>) => void;
  handleSubmit: () => void;
  setIsShowConfirm: (isShow: boolean) => void;
}
const FormFactory: React.FC<FormFactoryProps> = ({
  template,
  error,
  formData,
  submitError,
  loading,
  submitting,
  formMessage,
  isShowConfirm,
  handleConfirm,
  handleSubmit,
  setIsShowConfirm,
}) => {
  const formTemplateModePreset = FormTemplateModePresets[template?.formColorSetting?.templateModeColor || FormTemplateModePresentType.BASIC];
  const isBlackTemplate = template?.formColorSetting?.templateModeColor === FormTemplateModePresentType.BLACK;
  const currentFormColorSetting =
    template?.formColorSetting?.optionMode === 'template_mode'
      ? ({
          ...FORM_COLOR_SETTING_INIT,
          optionMode: template?.formColorSetting?.optionMode,
          layoutMode: template?.formColorSetting?.layoutMode,
          templateModeColor: template?.formColorSetting?.templateModeColor,
          bgColor: formTemplateModePreset?.bgColor,
          titleSettings: {
            ...FORM_COLOR_SETTING_INIT.titleSettings,
            color: formTemplateModePreset?.buttonColor,
          },
          generalSettings: {
            ...FORM_COLOR_SETTING_INIT.generalSettings,
            color: isBlackTemplate ? '#FFF' : (formTemplateModePreset?.color ?? '#0c0d0e'),
          },
          labelSettings: {
            ...FORM_COLOR_SETTING_INIT.labelSettings,
            color: isBlackTemplate ? '#FFF' : (formTemplateModePreset?.color ?? '#0c0d0e'),
          },
          descriptionSettings: {
            ...FORM_COLOR_SETTING_INIT.descriptionSettings,
            color: isBlackTemplate ? '#FFF' : (formTemplateModePreset?.color ?? '#0c0d0e'),
          },
          entryFormSettings: {
            ...FORM_COLOR_SETTING_INIT.entryFormSettings,
            color: isBlackTemplate ? '#555' : (formTemplateModePreset?.color ?? '#0c0d0e'),
          },
          buttonSettings: {
            ...FORM_COLOR_SETTING_INIT.buttonSettings,
            bgColor: formTemplateModePreset?.buttonColor,
            borderColor: formTemplateModePreset?.borderColor,
          },
        } as FormColorSetting)
      : { ...template?.formColorSetting, bgcolor: 'transparent' };

  if (error) {
    return formMessage ? (
      <Box p={1} id="error" textAlign="center">
        <Typography
          variant="body2"
          dangerouslySetInnerHTML={{
            __html: formMessage?.replace(/\n/g, '<br>') ?? MESSAGE_MAP.FORM_NOT_FOUND,
          }}
        ></Typography>
      </Box>
    ) : (
      ''
    );
  }

  if (loading) {
    return (
      <Box
        sx={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  const boxStyle = {
    backgroundColor: 'transparent',
    fontSize: `${currentFormColorSetting?.generalSettings?.fontSize}${currentFormColorSetting?.generalSettings?.fontSizeUnit}`,
    fontFamily: displayFontFamily(currentFormColorSetting?.generalSettings?.fontFamily),
    color: currentFormColorSetting?.generalSettings?.color,
  };

  if (formMessage) {
    return (
      <Box p={1} sx={boxStyle}>
        <FormDisplayMessage formElements={template?.formElements ?? []} colorSetting={template?.formColorSetting} message={formMessage} />
      </Box>
    );
  }

  if (isShowConfirm) {
    return (
      <Box p={1} sx={boxStyle}>
        <FormConfirmStandalone
          formElements={template?.formElements}
          colorSetting={currentFormColorSetting}
          values={formData}
          error={submitError}
          submitting={submitting}
          onSubmit={handleSubmit}
          onBack={() => setIsShowConfirm(false)}
        />
      </Box>
    );
  }

  return (
    <Box p={1} sx={boxStyle}>
      <FormStandalone
        formElements={template?.formElements}
        colorSetting={currentFormColorSetting}
        generalSetting={template?.formGeneralSetting}
        screenType="pc"
        initialValues={formData}
        onSubmit={handleConfirm}
      />
    </Box>
  );
};

const EmbedModule: React.FC = () => {
  const formContainerRef = useRef(null);
  const { id: extId } = useParams();
  const [formHeight, setFormHeight] = useState<number>(document.documentElement.scrollHeight);
  const { loading, template, error, formData, submitting, submitError, formMessage, setFormData, submitForm, handleFormAction } = useLogic();
  const [isShowConfirm, setIsShowConfirm] = useState<boolean>(false);
  const { classes } = useFormStyles({ colorSetting: template?.formColorSetting });

  const handleConfirm = (values: Record<string, FormItemValue>) => {
    setFormData(values);
    setIsShowConfirm(true);
  };

  const handleSubmit = () => {
    submitForm();
  };

  const handleChangeFormHeight = (height: number = document.documentElement.scrollHeight, friendlyKey: string = '') => {
    const newHeight = Math.ceil(height);
    handleFormAction('setHeight', { height: newHeight, friendlyKey });

    // Send message to parent window to resize iframe (for BiNDup)
    console.log('Send message to parent window to resize iframe (for BiNDup) with height:', newHeight);
    window.parent.postMessage({ type: 'resize', uuid: friendlyKey, height: newHeight }, '*');
  };

  useEffect(() => {
    if (!isShowConfirm && extId) {
      handleChangeFormHeight(formHeight, extId);
    }
  }, [formHeight, isShowConfirm, extId]);

  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setFormHeight(entry.contentRect.height);
      }
    });

    if (formContainerRef.current) {
      resizeObserver.observe(formContainerRef.current);
    }

    return () => {
      if (formContainerRef.current) {
        resizeObserver.unobserve(formContainerRef.current);
      }
    };
  }, []);

  return (
    <Box component="main" ref={formContainerRef} className={classes.container}>
      <CssBaseline />
      <FormFactory
        template={template}
        error={error}
        formMessage={formMessage}
        formData={formData}
        submitError={submitError}
        loading={loading}
        submitting={submitting}
        isShowConfirm={isShowConfirm}
        handleConfirm={handleConfirm}
        handleSubmit={handleSubmit}
        setIsShowConfirm={setIsShowConfirm}
      />
    </Box>
  );
};

export default EmbedModule;
