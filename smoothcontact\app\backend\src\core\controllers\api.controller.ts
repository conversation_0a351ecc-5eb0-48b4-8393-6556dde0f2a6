import { UseInterceptors } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';

import { HttpException } from '@/types/httpException.type';
import { APIResponseBase } from '@/types/response.type';

import { TransformInterceptor } from '../interceptor/transform.interceptor';

type DTO<T> = new () => T;

@UseInterceptors(TransformInterceptor)
export class BaseController {
  public successResponse<T>(dataResponse?: APIResponseBase<T>, dataResponseClass?: DTO<T | unknown>): APIResponseBase<T> {
    const dataResponseIns = new APIResponseBase<T>(dataResponse);
    if (dataResponse?.data && dataResponseClass) {
      const dataParse = plainToInstance(dataResponseClass, dataResponse?.data, {
        excludeExtraneousValues: true,
      });
      dataResponseIns.data = dataParse as T;
    }

    return dataResponseIns;
  }

  public failResponse<T>(dataResponse?: APIResponseBase<T>) {
    throw new HttpException<T>(dataResponse);
  }
}
