@charset "utf-8";
#a-header,
#a-ghost_header,
#a-billboard,
#a-site_contents,
#a-footer {
  max-width: 100%;
}

@media only screen and (max-width: 641px) {
  #a-site_contents {
    max-width: 100%;
  }
}

@media only screen and (max-width: 641px) {
  #a-header {
    padding: 0;
  }

  #a-ghost_header {
    padding: 0;
  }

  #a-billboard {
    padding: 0;
  }

  #a-main {
    padding: 0;
  }

  #a-side-a {
    padding: 0;
  }

  #a-side-b {
    padding: 0;
  }

  #a-footer {
    padding: 0;
  }
}

#bk11820 > div {
  margin: 0 auto;
  max-width: 100%;
}

#bk11821 {
  background-color: #f6f5ef;
  background-image: url(_src/8876/diagonal_04.png);
  background-repeat: repeat;
}

#bk11821 > div {
  padding-left: 30px;
  padding-top: 30px;
  padding-right: 0;
  padding-bottom: 60px;
}

#bk11839 > div {
  padding-left: 90px;
  padding-top: 60px;
  padding-right: 90px;
  padding-bottom: 30px;
}

#bk11822 > div {
  padding-left: 30px;
  padding-top: 0;
  padding-right: 30px;
  padding-bottom: 10px;
}

#bk11826 > div {
  padding-left: 30px;
  padding-top: 0;
  padding-right: 30px;
  padding-bottom: 60px;
}

#bk11827 {
  background-color: #f6f5ef;
}

#bk11827 > div {
  padding-left: 30px;
  padding-top: 30px;
  padding-right: 30px;
  padding-bottom: 0;
}

#bk11828 {
  background-color: #f6f5ef;
}

#bk11828 > div {
  padding-left: 30px;
  padding-top: 0;
  padding-right: 30px;
  padding-bottom: 15px;
}

#bk11829 {
  background-color: #f6f5ef;
}

#bk11829 > div {
  padding-left: 30px;
  padding-top: 0;
  padding-right: 30px;
  padding-bottom: 60px;
}

#bk11830 > div {
  padding-left: 30px;
  padding-top: 30px;
  padding-right: 30px;
  padding-bottom: 60px;
}

#bk11831 > div {
  padding-left: 30px;
  padding-top: 0;
  padding-right: 30px;
  padding-bottom: 0;
}

#bk11834 > div {
  padding-left: 30px;
  padding-top: 0;
  padding-right: 30px;
  padding-bottom: 0;
}

#bk11832 > div {
  padding-left: 30px;
  padding-top: 0;
  padding-right: 30px;
  padding-bottom: 30px;
}

#bk11835 > div {
  padding-left: 30px;
  padding-top: 0;
  padding-right: 30px;
  padding-bottom: 0;
}

#bk11836 > div {
  padding-left: 30px;
  padding-top: 0;
  padding-right: 30px;
  padding-bottom: 30px;
}

#bk11838 > div {
  padding-left: 30px;
  padding-top: 0;
  padding-right: 30px;
  padding-bottom: 0;
}

#bk11837 > div {
  padding-left: 30px;
  padding-top: 0;
  padding-right: 30px;
  padding-bottom: 30px;
}

#bk11833 > div {
  padding-left: 30px;
  padding-top: 0;
  padding-right: 30px;
  padding-bottom: 90px;
}

.a-header .b-plain > .column,
.a-header .b-plain > .g-column,
.a-header .b-both_diff > .column,
.a-header .b-both_diff > .g-column,
.a-header .b-headlines > .column,
.a-header .b-headlines > .g-column,
.a-header .b-album > .column,
.a-header .b-album > .g-column,
.a-header .b-tab > .column,
.a-header .b-tab > .g-column,
.a-header .b-accordion > .column,
.a-header .b-accordion > .g-column,
.a-ghost_header .b-plain > .column,
.a-ghost_header .b-plain > .g-column,
.a-ghost_header .b-both_diff > .column,
.a-ghost_header .b-both_diff > .g-column,
.a-ghost_header .b-headlines > .column,
.a-ghost_header .b-headlines > .g-column,
.a-ghost_header .b-album > .column,
.a-ghost_header .b-album > .g-column,
.a-ghost_header .b-tab > .column,
.a-ghost_header .b-tab > .g-column,
.a-ghost_header .b-accordion > .column,
.a-ghost_header .b-accordion > .g-column,
.a-billboard .b-plain > .column,
.a-billboard .b-plain > .g-column,
.a-billboard .b-both_diff > .column,
.a-billboard .b-both_diff > .g-column,
.a-billboard .b-headlines > .column,
.a-billboard .b-headlines > .g-column,
.a-billboard .b-album > .column,
.a-billboard .b-album > .g-column,
.a-billboard .b-tab > .column,
.a-billboard .b-tab > .g-column,
.a-billboard .b-accordion > .column,
.a-billboard .b-accordion > .g-column,
.a-main .b-plain > .column,
.a-main .b-plain > .g-column,
.a-main .b-both_diff > .column,
.a-main .b-both_diff > .g-column,
.a-main .b-headlines > .column,
.a-main .b-headlines > .g-column,
.a-main .b-album > .column,
.a-main .b-album > .g-column,
.a-main .b-tab > .column,
.a-main .b-tab > .g-column,
.a-main .b-accordion > .column,
.a-main .b-accordion > .g-column,
.a-footer .b-plain > .column,
.a-footer .b-plain > .g-column,
.a-footer .b-both_diff > .column,
.a-footer .b-both_diff > .g-column,
.a-footer .b-headlines > .column,
.a-footer .b-headlines > .g-column,
.a-footer .b-album > .column,
.a-footer .b-album > .g-column,
.a-footer .b-tab > .column,
.a-footer .b-tab > .g-column,
.a-footer .b-accordion > .column,
.a-footer .b-accordion > .g-column {
  margin: 0 auto;
  max-width: 1200px;
}

@media only screen and (max-width: 641px) {
  .a-header .b-plain > .column,
  .a-header .b-plain > .g-column,
  .a-header .b-both_diff > .column,
  .a-header .b-both_diff > .g-column,
  .a-header .b-headlines > .column,
  .a-header .b-headlines > .g-column,
  .a-header .b-album > .column,
  .a-header .b-album > .g-column,
  .a-header .b-tab > .column,
  .a-header .b-tab > .g-column,
  .a-header .b-accordion > .column,
  .a-header .b-accordion > .g-column,
  .a-ghost_header .b-plain > .column,
  .a-ghost_header .b-plain > .g-column,
  .a-ghost_header .b-both_diff > .column,
  .a-ghost_header .b-both_diff > .g-column,
  .a-ghost_header .b-headlines > .column,
  .a-ghost_header .b-headlines > .g-column,
  .a-ghost_header .b-album > .column,
  .a-ghost_header .b-album > .g-column,
  .a-ghost_header .b-tab > .column,
  .a-ghost_header .b-tab > .g-column,
  .a-ghost_header .b-accordion > .column,
  .a-ghost_header .b-accordion > .g-column,
  .a-billboard .b-plain > .column,
  .a-billboard .b-plain > .g-column,
  .a-billboard .b-both_diff > .column,
  .a-billboard .b-both_diff > .g-column,
  .a-billboard .b-headlines > .column,
  .a-billboard .b-headlines > .g-column,
  .a-billboard .b-album > .column,
  .a-billboard .b-album > .g-column,
  .a-billboard .b-tab > .column,
  .a-billboard .b-tab > .g-column,
  .a-billboard .b-accordion > .column,
  .a-billboard .b-accordion > .g-column,
  .a-main .b-plain > .column,
  .a-main .b-plain > .g-column,
  .a-main .b-both_diff > .column,
  .a-main .b-both_diff > .g-column,
  .a-main .b-headlines > .column,
  .a-main .b-headlines > .g-column,
  .a-main .b-album > .column,
  .a-main .b-album > .g-column,
  .a-main .b-tab > .column,
  .a-main .b-tab > .g-column,
  .a-main .b-accordion > .column,
  .a-main .b-accordion > .g-column,
  .a-side-a .b-plain > .column,
  .a-side-a .b-plain > .g-column,
  .a-side-a .b-both_diff > .column,
  .a-side-a .b-both_diff > .g-column,
  .a-side-a .b-headlines > .column,
  .a-side-a .b-headlines > .g-column,
  .a-side-a .b-album > .column,
  .a-side-a .b-album > .g-column,
  .a-side-a .b-tab > .column,
  .a-side-a .b-tab > .g-column,
  .a-side-a .b-accordion > .column,
  .a-side-a .b-accordion > .g-column,
  .a-side-b .b-plain > .column,
  .a-side-b .b-plain > .g-column,
  .a-side-b .b-both_diff > .column,
  .a-side-b .b-both_diff > .g-column,
  .a-side-b .b-headlines > .column,
  .a-side-b .b-headlines > .g-column,
  .a-side-b .b-album > .column,
  .a-side-b .b-album > .g-column,
  .a-side-b .b-tab > .column,
  .a-side-b .b-tab > .g-column,
  .a-side-b .b-accordion > .column,
  .a-side-b .b-accordion > .g-column,
  .a-footer .b-plain > .column,
  .a-footer .b-plain > .g-column,
  .a-footer .b-both_diff > .column,
  .a-footer .b-both_diff > .g-column,
  .a-footer .b-headlines > .column,
  .a-footer .b-headlines > .g-column,
  .a-footer .b-album > .column,
  .a-footer .b-album > .g-column,
  .a-footer .b-tab > .column,
  .a-footer .b-tab > .g-column,
  .a-footer .b-accordion > .column,
  .a-footer .b-accordion > .g-column {
    margin: 0 auto;
    max-width: 96%;
  }

  #bk11820 > div {
    padding-left: 0;
    padding-top: 0;
    padding-right: 40px;
    padding-bottom: 0;
  }

  #bk11821 > div {
    padding: 15px;
  }

  #bk11839 > div {
    padding: 15px;
  }

  #bk11822 > div {
    padding: 15px;
  }

  #bk11826 > div {
    padding: 15px;
  }

  #bk11827 > div {
    padding: 15px;
  }

  #bk11828 > div {
    padding: 15px;
  }

  #bk11829 > div {
    padding: 15px;
  }

  #bk11830 > div {
    padding: 15px;
  }

  #bk11831 > div {
    padding: 15px;
  }

  #bk11834 > div {
    padding: 15px;
  }

  #bk11832 > div {
    padding: 15px;
  }

  #bk11835 > div {
    padding: 15px;
  }

  #bk11836 > div {
    padding: 15px;
  }

  #bk11838 > div {
    padding: 15px;
  }

  #bk11837 > div {
    padding: 15px;
  }

  #bk11833 > div {
    padding: 15px;
  }
}
