import { FormBorderColors } from '@/common/constants';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import React from 'react';

interface FormElementTitleProps {
  heading: string;
  description: string;
  headingClass?: string;
  descriptionClass?: string;
  display?: string;
  lineColor?: string;
}

const FormElementTitle: React.FC<FormElementTitleProps> = ({ heading, description, headingClass, descriptionClass, display, lineColor }) => {
  if (display === 'none') {
    return null;
  }

  return (
    <>
      <Box textAlign={(display as any) || 'left'}>
        <Typography sx={{ mb: 1, wordBreak: 'break-all' }} variant="h5" fontWeight="bold" className={headingClass}>
          {heading}
        </Typography>
        <div className={descriptionClass} dangerouslySetInnerHTML={{ __html: description }}></div>
      </Box>
      <Divider sx={{ mt: 2, borderColor: lineColor ?? FormBorderColors.DEFAULT }} />
    </>
  );
};

export default FormElementTitle;
