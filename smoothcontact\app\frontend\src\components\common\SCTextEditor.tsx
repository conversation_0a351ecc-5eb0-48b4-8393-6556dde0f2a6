import React, { FC, useRef } from 'react';
import { Editor } from '@tinymce/tinymce-react';

interface SCTextEditorProps {
  id: string;
  initialValue?: string;
  onContentChange: (content: string) => void;
}

const SCTextEditor: FC<SCTextEditorProps> = ({ id, initialValue, onContentChange }) => {
  const editorRef = useRef<any>(null);

  return (
    <Editor
      id={id}
      apiKey="gpl"
      tinymceScriptSrc="/tinymce/tinymce.min.js"
      value={initialValue}
      init={{
        branding: false,
        menubar: false,
        statusbar: false,
        height: 300,
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:0.75rem }',
        toolbar:
          'forecolor bold italic link | fontsize styles | redo undo underline strikethrough | alignleft aligncenter alignright alignjustify | numlist bullist outdent indent lineheight | removeformat',
        language: 'ja',
      }}
      onInit={(evt, editor) => (editorRef.current = editor)}
      onEditorChange={() => {
        onContentChange(editorRef.current.getContent());
      }}
    />
  );
};

export default SCTextEditor;
