import { Box, Checkbox, FormControl, FormControlLabel, FormGroup, FormLabel, Radio, RadioGroup } from '@mui/material';
import { FormikValues } from 'formik';
import { FC } from 'react';

interface FullNameFieldPropertyProps {
  form: FormikValues;
}

const FullNameFieldProperty: FC<FullNameFieldPropertyProps> = (props) => {
  const { form } = props;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
      <FormControl component="fieldset">
        <FormLabel component="legend">氏名設定</FormLabel>
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                value={true}
                {...form.register('isUseFurigana', { nameOfValueProps: 'checked' })}
                checked={form?.values?.isUseFurigana ?? false}
              />
            }
            label="フリガナを使う"
          />
          <FormGroup>
            <FormControlLabel
              sx={{ pl: 3 }}
              control={
                <Checkbox
                  disabled={!form?.values?.isUseFurigana}
                  value={true}
                  {...form.register('isAutoFill', { nameOfValueProps: 'checked' })}
                  checked={form?.values?.isAutoFill ?? false}
                />
              }
              label="自動入力"
            />
            <RadioGroup
              {...form.register('autoFillType')}
              name="autoFillType"
              value={form?.values?.autoFillType ?? (form?.values?.isAutoFill ? 'hiragana' : 'text')}
              sx={{ pl: 6 }}
            >
              <FormControlLabel
                disabled={!form?.values?.isAutoFill || !form?.values?.isUseFurigana}
                value="hiragana"
                control={<Radio value="hiragana" />}
                label="ひらがな"
              />
              <FormControlLabel
                disabled={!form?.values?.isAutoFill || !form?.values?.isUseFurigana}
                value="katakana"
                control={<Radio value="katakana" />}
                label="カタカナ"
              />
            </RadioGroup>
          </FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                value={true}
                {...form.register('isReduceFullName', { nameOfValueProps: 'checked' })}
                checked={form?.values?.isReduceFullName ?? false}
              />
            }
            label="入力項目を１つにする"
          />
        </FormGroup>
      </FormControl>
    </Box>
  );
};

export default FullNameFieldProperty;
