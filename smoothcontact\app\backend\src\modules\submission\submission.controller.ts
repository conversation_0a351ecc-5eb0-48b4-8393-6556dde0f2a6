import { Controller, Get, Param, Query, Request, Res, UseGuards } from '@nestjs/common';
import { Response } from 'express';
import * as moment from 'moment';

import { BaseController } from '@/core/controllers/api.controller';
import { AuthGuard } from '@/core/guard/auth.guard';

import { CsvService } from '../csv/csv.service';
import { GetFormSubmissionRequestDTO } from './dto/request.dto';
import { ListFormSubmissionResponseDto } from './dto/response.dto';
import { SubmissionService } from './submission.service';

@UseGuards(AuthGuard)
@Controller('api/form-submission')
export class FormSubmissionController extends BaseController {
  constructor(
    private readonly formSubmissionService: SubmissionService,
    private readonly csvService: CsvService,
  ) {
    super();
  }

  @Get('/:extId')
  async getSubmissionByExtId(@Request() request: Request, @Param('extId') extId: string, @Query() queryParams: GetFormSubmissionRequestDTO) {
    const result = await this.formSubmissionService.getSubmissionByExtId({ userId: request?.user?.id, extId, filter: queryParams });

    if (!result) {
      return this.failResponse({ message: 'フォームが見つかりません' });
    }

    return this.successResponse(
      {
        data: {
          items: result.data,
          pagination: { page: queryParams.page, perPage: queryParams.perPage, total: result.total },
        },
      },
      ListFormSubmissionResponseDto,
    );
  }

  @Get('/:extId/report')
  async getReportByExtId(@Request() request: Request, @Param('extId') extId: string) {
    const result = await this.formSubmissionService.getReportByExtId({ userId: request?.user?.id, extId });

    if (!result) {
      return this.failResponse({ message: 'フォームが見つかりません' });
    }

    return this.successResponse({
      data: result,
    });
  }

  @Get('/:extId/csv/download')
  async exportByExtId(@Request() request: Request, @Param('extId') extId: string, @Res() res: Response, @Query() queryParams: { isCombine: string }) {
    const data = await this.formSubmissionService.downloadSubmissionCsvByExtId({
      userId: request?.user?.id,
      extId,
      isCombine: queryParams?.isCombine?.toLowerCase() === 'true',
    });

    if (!data) {
      return this.failResponse({ message: 'フォームが見つかりません' });
    }

    const fileName = `submission_${moment().format('yyyy_MM_DD')}.csv`;
    this.csvService.downloadCsv(fileName, data, res);
  }

  @Get('/:extId/csv/statictis')
  async exportStatictisByExtId(@Request() request: Request, @Param('extId') extId: string, @Res() res: Response) {
    const data = await this.formSubmissionService.downloadSubmissionStatictisCsvByExtId({
      userId: request?.user?.id,
      extId,
    });

    const fileName = `submission_statictis_${moment().format('yyyy_MM_DD')}.csv`;
    this.csvService.downloadCsv(fileName, data, res);
  }

  @Get('/:extId/cross-tabulation')
  async getCrossTabulationByExtId(
    @Request() request: Request,
    @Param('extId') extId: string,
    @Query() queryParams: { targetTabulationId: string; crossTabulationId: string },
  ) {
    const result = await this.formSubmissionService.getCrossTabulationSelections({
      userId: request?.user?.id,
      extId,
      targetTabulationId: queryParams?.targetTabulationId,
      crossTabulationId: queryParams?.crossTabulationId,
    });

    if (!result) {
      return this.failResponse({ message: 'フォームが見つかりません' });
    }

    return this.successResponse({
      data: result,
    });
  }
}
