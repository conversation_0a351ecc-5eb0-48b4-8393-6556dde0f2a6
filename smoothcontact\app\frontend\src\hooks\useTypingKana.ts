import React, { useRef, useState } from 'react';
import { convertHiraganaToKatakana, removeNonKana } from '@/utils/helper';

interface TypingKana {
  enabled?: boolean;
  useKatakana?: boolean;
}
const useTypingKana = ({ enabled, useKatakana }: TypingKana) => {
  const kanaFreeze = useRef('');
  const [kana, setKana] = useState<string>('');

  const onCompositionStart = () => {
    kanaFreeze.current = kana;
  };

  const onCompositionUpdate = (e: any) => {
    if (!enabled) return;

    const event = e as React.CompositionEvent<HTMLInputElement>;
    const data = removeNonKana(event.data || '');
    const inputFieldValue = removeNonKana(e.target.value || '');
    const result = data.length > inputFieldValue.length ? data : inputFieldValue;
    if (result) {
      const value = useKatakana ? convertHiraganaToKatakana(result) : result;
      setKana(kanaFreeze.current + value);
    }
  };

  const onCompositionEnd = () => {
    kanaFreeze.current = kana;
  };

  const onKeyUp = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (!enabled) return;

    if (event.code === 'Space') {
      setKana((prev) => prev.trim() + '　');
    }
  };

  return { kana, setKana, onCompositionStart, onCompositionUpdate, onCompositionEnd, onKeyUp };
};

export default useTypingKana;
