import FocusInInputComponent from '@/components/FormBuilder/subcomponents/FormColorSetting/AnimationCustom/text/FocusInInputComponent';
import LabelTopInputComponent from '@/components/FormBuilder/subcomponents/FormColorSetting/AnimationCustom/text/LabelTopInputComponent';
import { FormItemValue } from '@/types/FormTemplateTypes';
import { FormInputAnimationTypes } from '@/utils/formBuilderUtils';
import { TextField } from '@mui/material';
import React, { ChangeEvent, FC, InputHTMLAttributes } from 'react';

interface InputFactoryProps {
  name: string;
  className?: string;
  inputAnimation: string;
  placeholder: string;
  value: FormItemValue;
  helperText?: string;
  error?: boolean;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  onChange?: (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onBlur?: (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onCompositionStart?: (event: React.CompositionEvent<any>) => void;
  onCompositionUpdate?: (event: React.CompositionEvent<any>) => void;
  onCompositionEnd?: (event: React.CompositionEvent<any>) => void;
  onKeyUp?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  borderColor?: string;
  backgroundColor?: string;
  borderRadius?: string;
  fontSize?: string;
  disabled?: boolean;
  marginTop?: string;
  inputRef?: React.RefObject<HTMLInputElement>;
  inputProps?: InputHTMLAttributes<HTMLInputElement>;
}

const InputFactory: FC<InputFactoryProps> = ({ inputAnimation, placeholder, value, ...rest }) => {
  const commonProps = {
    value,
    placeholder,
    ...Object.fromEntries(Object.entries(rest).filter(([, value]) => value != null)),
  } as any;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { borderColor, backgroundColor, borderRadius, borderRadiusUnit, marginTop, inputProps, ...newCommonProps } = commonProps;

  switch (inputAnimation) {
    case FormInputAnimationTypes.LABEL_TOP:
      return <LabelTopInputComponent {...commonProps} placeholder={placeholder} />;

    case FormInputAnimationTypes.LINE_COLOR:
      return (
        <TextField
          {...newCommonProps}
          label={placeholder}
          fullWidth
          variant="standard"
          sx={{
            '& .MuiInputBase-root': {
              background: 'transparent!important',
            },
          }}
        />
      );

    case FormInputAnimationTypes.FOCUS_IN:
      return <FocusInInputComponent {...commonProps} placeholder={placeholder} />;

    default:
      return <TextField {...newCommonProps} inputProps={{ ...inputProps }} placeholder={placeholder} fullWidth variant="outlined" size="small" />;
  }
};

export default InputFactory;
