import { Body, Controller, Get, HttpStatus, Post, Put, Request, UseGuards } from '@nestjs/common';

import { BaseController } from '@/core/controllers/api.controller';
import { AuthGuard } from '@/core/guard/auth.guard';

import { MfaVerifyRequestDTO } from './dto/request.dto';
import { MfaAuthenticationCodeResponseDTO, MfaVerifyResponseDTO } from './dto/response.dto';
import { MfaService } from './mfa.service';

@UseGuards(AuthGuard)
@Controller('api/mfa')
export class MfaController extends BaseController {
  constructor(private readonly mfaService: MfaService) {
    super();
  }

  @Get('')
  async index() {
    const result = await this.mfaService.getAuthenticationCode();

    if (!result) {
      return this.failResponse({ message: 'error_get_authentication_code' });
    }

    return this.successResponse({ data: result }, MfaAuthenticationCodeResponseDTO);
  }

  @Post('create')
  async create(@Request() request: Request, @Body() body: MfaVerifyRequestDTO) {
    const result = await this.mfaService.createMfaCode(body.secret, body.code, request?.user?.id);

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.BAD_REQUEST });
    }

    return this.successResponse({ data: result }, MfaVerifyResponseDTO);
  }

  @Put('disable')
  async disable(@Request() request: Request) {
    const result = await this.mfaService.disableMfaCode(request?.user?.id);

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.BAD_REQUEST });
    }

    return this.successResponse();
  }

  @Post('reissue')
  async reissue(@Request() request: Request) {
    const result = await this.mfaService.reissueBackupCode(request?.user?.id);

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.BAD_REQUEST });
    }

    return this.successResponse({ data: result });
  }

  @Post('verify')
  async verify(@Body() body: MfaVerifyRequestDTO) {
    const result = this.mfaService.verifyMfaCode(body.secret, body.code);

    if (!result) {
      return this.failResponse({ message: 'error_verify_mfa_token' });
    }

    return this.successResponse({ data: result });
  }
}
