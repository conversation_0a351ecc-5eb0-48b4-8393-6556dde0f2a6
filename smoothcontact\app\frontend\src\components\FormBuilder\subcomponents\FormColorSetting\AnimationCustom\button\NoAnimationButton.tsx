import React from 'react';
import { styled } from '@mui/system';

const Button = styled('button')(`
  width: 100%;
  height: 40px;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  color: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
  background-color: #3aaaff;

  &:hover {
    opacity: 0.8;
    background-color: #3aaaff;
  }
`);

const NoAnimationButton: React.FC<{ label: string }> = ({ label }) => {
  return <Button>{label}</Button>;
};

export default NoAnimationButton;
