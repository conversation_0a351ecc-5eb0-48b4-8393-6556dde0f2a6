import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { FormBuilderEntity } from '@/modules/form-builder/entities/form-builder.entity';

import { FormController } from './form.controller';
import { FormService } from './form.service';

@Module({
  imports: [TypeOrmModule.forFeature([FormBuilderEntity])],
  providers: [FormService],
  controllers: [FormController],
  exports: [FormService],
})
export class FormModule {}
