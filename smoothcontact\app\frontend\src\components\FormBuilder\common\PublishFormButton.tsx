import SCLoadingButton from '@/components/common/SCLoadingButton';
import SCModal from '@/components/common/SCModal';
import { useFormHandler } from '@/hooks/useFormHandler';
import { FormScheduleSettingRequestDTO } from '@/modules/formBuilder/edit/dto/request.dto';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormScheduleSetting, FormStatus } from '@/types/FormTemplateTypes';
import { ISO, isValidDate } from '@/utils/dateTime';
import { getRealStatus } from '@/utils/helper';
import { AccessTime } from '@mui/icons-material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import PublishIcon from '@mui/icons-material/Publish';
import { Divider, FormHelperText, LinearProgress, ListItemIcon, Stack, Typography } from '@mui/material';
import Button from '@mui/material/Button';
import Menu, { MenuProps } from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { alpha, styled } from '@mui/material/styles';
import { DateTimePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs from 'dayjs';
import * as React from 'react';
import { useEffect } from 'react';
import * as Yup from 'yup';

const StyledMenu = styled((props: MenuProps) => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: 'bottom',
      horizontal: 'right',
    }}
    transformOrigin={{
      vertical: 'top',
      horizontal: 'right',
    }}
    {...props}
  />
))(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    minWidth: 180,
    color: theme.palette.mode === 'light' ? 'rgb(55, 65, 81)' : theme.palette.grey[300],
    boxShadow:
      'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
    '& .MuiMenu-list': {
      padding: '4px 0',
    },
    '& .MuiMenuItem-root': {
      '& .MuiSvgIcon-root': {
        fontSize: 18,
        color: theme.palette.text.secondary,
        marginRight: theme.spacing(1.5),
      },
      '&:active': {
        backgroundColor: alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),
      },
    },
  },
}));

interface PublishFormButtonProps {
  text: string;
  loading?: boolean;
}

const COUNT_DOWN_SECONDS = 10;

const PublishFormButton = ({ text, loading = false }: PublishFormButtonProps) => {
  const { saveForm, formScheduleSetting, error, isFormChanged, selectedTemplate, setOpenModal, setSharingModal } = useFormBuilder();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [openScheduleModal, setOpenScheduleModal] = React.useState(false);
  const [openIntervalModal, setOpenIntervalModal] = React.useState(false);
  const [secondsLeft, setSecondsLeft] = React.useState(COUNT_DOWN_SECONDS);
  const open = Boolean(anchorEl);
  const currentStatus = React.useMemo(() => {
    return getRealStatus(
      selectedTemplate?.status,
      selectedTemplate?.releaseStartDate,
      selectedTemplate?.releaseEndDate,
      Number(selectedTemplate?.submissionCount ?? 0) >= Number(selectedTemplate?.formScheduleSetting?.maximumNumberFormsReceived ?? 9999999999)
    );
  }, [selectedTemplate]);

  const canReOpenForm = React.useMemo(() => {
    return currentStatus === FormStatus.CLOSED && dayjs(selectedTemplate?.releaseEndDate).isBefore(dayjs(selectedTemplate?.updatedAt));
  }, [selectedTemplate]);

  useEffect(() => {
    if (openIntervalModal && secondsLeft > 0) {
      const timer = setInterval(() => {
        setSecondsLeft((prevSeconds) => prevSeconds - 1);
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [openIntervalModal, secondsLeft]);

  useEffect(() => {
    if (secondsLeft === 0) {
      setOpenIntervalModal?.(false);
      setSecondsLeft(COUNT_DOWN_SECONDS);
      handlePublishNow?.();
    }
  }, [secondsLeft]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handlePublishNow = async () => {
    const newFormScheduleSetting = {
      ...formScheduleSetting,
      releaseStartDate: dayjs().utc().toISOString(),
      releaseEndDate: null as any,
    };

    const result = await saveForm(
      {
        status: FormStatus.PUBLISHED,
        formScheduleSetting: newFormScheduleSetting,
        releaseStartDate: dayjs().utc().toISOString(),
        releaseEndDate: null as any,
      },
      'フォームの公開が完了しました。'
    );

    if (result) {
      setOpenIntervalModal(false);
      setSharingModal(true);
    }
  };

  const handleSchedulePublish = async () => {
    const newFormScheduleSetting = {
      ...formScheduleSetting,
      releaseStartDate: dayjs(form?.values?.releaseStartDate),
      releaseEndDate: form?.values?.releaseEndDate ? dayjs(form?.values?.releaseEndDate) : null,
    };

    const result = await saveForm(
      {
        status: FormStatus.PUBLISHED,
        formScheduleSetting: newFormScheduleSetting,
        releaseStartDate: dayjs(form?.values?.releaseStartDate),
        releaseEndDate: form?.values?.releaseEndDate ? dayjs(form?.values?.releaseEndDate) : null,
      },
      `フォームの公開予約が完了しました${dayjs(form?.values?.releaseStartDate)?.format('YYYY/MM/DD HH:mm')}に公開予定です。`
    );

    if (result) {
      setOpenScheduleModal(false);
      setSharingModal(true);
    }
  };

  const cancelPublishConfirm = () => {
    setOpenModal({
      open: true,
      title: 'フォームを非公開にしますか？',
      subtitle: '公開後の再編集は集計結果に影響が出るため、推奨しておりません。また、ユーザーはURLにアクセスできなくなります。',
      onConfirm: async () => {
        await cancelPublish();
      },
      cancelText: 'キャンセル',
      confirmText: '非公開にする',
    });
  };

  const cancelPrePublishConfirm = () => {
    setOpenModal({
      open: true,
      title: '公開予約を取り消しますか？',
      subtitle: '',
      onConfirm: async () => {
        await cancelPublish();
      },
      cancelText: 'キャンセル',
      confirmText: 'OK',
    });
  };

  const finishPublishConfirm = () => {
    setOpenModal({
      open: true,
      title: 'フォームの公開を終了してもよろしいですか？',
      subtitle: 'ユーザーはURLにアクセスできますが、再度公開を開始するまでは投稿することはできません。',
      onConfirm: async () => {
        await finishPublish();
      },
      cancelText: 'キャンセル',
      confirmText: '公開を終了する',
    });

    handleClose();
  };

  const cancelPublish = async () => {
    const newFormScheduleSetting: FormScheduleSettingRequestDTO = {
      ...formScheduleSetting,
      releaseStartDate: null,
      releaseEndDate: null,
    };

    await saveForm(
      { status: FormStatus.DRAFT, releaseStartDate: null, releaseEndDate: null, formScheduleSetting: newFormScheduleSetting },
      'フォームを非公開にしました'
    );
  };

  const finishPublish = async () => {
    const newFormScheduleSetting: FormScheduleSettingRequestDTO = {
      ...formScheduleSetting,
      releaseEndDate: dayjs(new Date()).toDate(),
    };

    await saveForm({ releaseEndDate: dayjs(new Date()).toDate(), formScheduleSetting: newFormScheduleSetting }, 'フォームの公開を終了しました');
  };

  const validationSchema = Yup.object<FormScheduleSetting>({
    releaseStartDate: Yup.date()
      .typeError('開始日時が無効です。')
      .test('is-valid', '開始日時が無効です。', function (value) {
        return isValidDate(value);
      })
      .required('開始日時を入力してください。'),
    releaseEndDate: Yup.date()
      .typeError('終了日時が無効です。')
      .nullable()
      .test('is-valid', '終了日時が無効です。', function (value) {
        if (!value) return true;

        return isValidDate(value);
      })
      .test('is-later', '終了日が開始日より後になる', function (value) {
        if (!value) return true;

        const { releaseStartDate } = this.parent;

        if (!releaseStartDate || !value) return true;

        const start = new Date(releaseStartDate);
        const end = new Date(value);

        return end > start;
      }),
  });

  const form = useFormHandler<any>({
    initialValues: {
      releaseStartDate: selectedTemplate?.releaseStartDate ?? '',
      releaseEndDate: selectedTemplate?.releaseEndDate ?? '',
    },
    validationSchema,
    validateOnChange: true,
    validateOnBlur: false,
    onSubmit: () => {},
  });

  useEffect(() => {
    form.resetForm({
      values: { releaseStartDate: selectedTemplate?.releaseStartDate ?? '', releaseEndDate: selectedTemplate?.releaseEndDate ?? '' },
    });
  }, [selectedTemplate]);

  return (
    <>
      <SCLoadingButton
        disabled={error || !!isFormChanged}
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
        endIcon={<KeyboardArrowDownIcon />}
        loading={loading}
        color="secondary"
        className="btn-black"
        startIcon={<PublishIcon fontSize="small" />}
      >
        {text}
      </SCLoadingButton>

      {currentStatus === FormStatus.DRAFT && (
        <StyledMenu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem
            onClick={() => {
              setOpenScheduleModal(true);
              handleClose();
            }}
            disableRipple
          >
            <ListItemIcon>
              <AccessTime />
            </ListItemIcon>
            <Stack>
              <Typography>公開期間を設定</Typography>
              <FormHelperText>公開期間を設定してフォームを公開します</FormHelperText>
            </Stack>
          </MenuItem>
          <MenuItem
            onClick={() => {
              setOpenIntervalModal(true);
              handleClose();
            }}
            disableRipple
          >
            <ListItemIcon></ListItemIcon>
            <Stack>
              <Typography>今すぐ公開</Typography>
              <FormHelperText>今すぐフォームを公開します</FormHelperText>
            </Stack>
          </MenuItem>
        </StyledMenu>
      )}

      {currentStatus === FormStatus.PUBLISHED && (
        <StyledMenu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem disabled={true} disableRipple>
            <ListItemIcon></ListItemIcon>
            <Stack>
              <Typography>今すぐ公開</Typography>
              <FormHelperText>今すぐフォームを公開します</FormHelperText>
            </Stack>
          </MenuItem>
          <MenuItem
            onClick={() => {
              setOpenScheduleModal(true);
              handleClose();
            }}
            disableRipple
          >
            <ListItemIcon>
              <AccessTime />
            </ListItemIcon>
            <Stack>
              <Typography>フォームの公開期間を設定</Typography>
              <FormHelperText>フォームを公開する日時を選択します</FormHelperText>
            </Stack>
          </MenuItem>
          <MenuItem
            onClick={() => {
              finishPublishConfirm();
            }}
            disableRipple
          >
            <ListItemIcon></ListItemIcon>
            <Stack>
              <Typography>フォームの公開を終了</Typography>
              <FormHelperText>
                ユーザーはURLにアクセスできますが、再度公開
                <br />
                を開始するまでは投稿することはできません
              </FormHelperText>
            </Stack>
          </MenuItem>
          <MenuItem
            onClick={() => {
              cancelPublishConfirm();
              handleClose();
            }}
            disableRipple
          >
            <ListItemIcon></ListItemIcon>
            <Stack>
              <Typography>フォームを非公開</Typography>
              <FormHelperText>ユーザーはURLにアクセスできなくなります</FormHelperText>
            </Stack>
          </MenuItem>
        </StyledMenu>
      )}

      {currentStatus === FormStatus.PREPUBLISHED && (
        <StyledMenu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem disableRipple>
            <ListItemIcon>
              <AccessTime />
            </ListItemIcon>
            <Stack>
              <Typography>公開期間を設定</Typography>
              <FormHelperText>
                フォームのリンクを知っているユーザーのみ
                <br />
                アクセスできますが投稿はできません
                <br />
                {dayjs(form?.values?.releaseStartDate)?.format('YYYY/MM/DD HH:mm')}に公開予定です。
              </FormHelperText>
            </Stack>
          </MenuItem>
          <Divider />
          <MenuItem
            onClick={() => {
              cancelPrePublishConfirm();
              handleClose();
            }}
            disableRipple
          >
            <ListItemIcon></ListItemIcon>
            <Stack>
              <Typography>公開予約を取り消す</Typography>
            </Stack>
          </MenuItem>
          <MenuItem
            onClick={() => {
              setOpenScheduleModal(true);
              handleClose();
            }}
            disableRipple
          >
            <ListItemIcon></ListItemIcon>
            <Stack>
              <Typography>予約日時を変更</Typography>
            </Stack>
          </MenuItem>
          <MenuItem
            onClick={() => {
              setOpenIntervalModal(true);
              handleClose();
            }}
            disableRipple
          >
            <ListItemIcon></ListItemIcon>
            <Stack>
              <Typography>今すぐ公開</Typography>
              <FormHelperText>公開予約を取り消し今すぐフォームを公開します</FormHelperText>
            </Stack>
          </MenuItem>
        </StyledMenu>
      )}

      {currentStatus === FormStatus.CLOSED && (
        <StyledMenu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem
            disabled={!canReOpenForm}
            onClick={() => {
              setOpenIntervalModal(true);
              handleClose();
            }}
            disableRipple
          >
            <ListItemIcon></ListItemIcon>
            <Stack>
              <Typography>今すぐ公開</Typography>
              <FormHelperText>今すぐフォームを公開します</FormHelperText>
            </Stack>
          </MenuItem>
          <MenuItem
            onClick={() => {
              setOpenScheduleModal(true);
              handleClose();
            }}
            disableRipple
          >
            <ListItemIcon>
              <AccessTime />
            </ListItemIcon>
            <Stack>
              <Typography>フォームの公開期間を設定</Typography>
              <FormHelperText>フォームを公開する日時を選択します</FormHelperText>
            </Stack>
          </MenuItem>
          <MenuItem
            onClick={() => {
              cancelPublishConfirm();
              handleClose();
            }}
            disableRipple
          >
            <ListItemIcon></ListItemIcon>
            <Stack>
              <Typography>フォームを非公開</Typography>
              <FormHelperText>ユーザーはURLにアクセスできなくなります</FormHelperText>
            </Stack>
          </MenuItem>
        </StyledMenu>
      )}

      <SCModal
        title={'公開期間を設定'}
        width={550}
        isOpen={openScheduleModal}
        onClose={() => setOpenScheduleModal((pre) => !pre)}
        closeBtnLabel={'キャンセル'}
        primaryAction={
          <Button disabled={!form?.isValid || !form?.dirty} color="primary" onClick={handleSchedulePublish}>
            予約する
          </Button>
        }
      >
        <Stack direction="column" gap={2}>
          <Typography variant="body1" sx={{ pb: 1 }}>
            公開期間を設定してください。ユーザーはURLにアクセスできますが、公開開始までは投稿することはできません。
          </Typography>

          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Stack direction="row" alignItems={'baseline'} gap={2}>
              <DateTimePicker
                slotProps={{
                  textField: {
                    fullWidth: true,
                    placeholder: '公開開始日時',
                    variant: 'outlined',
                    error: !!form?.errors?.releaseStartDate,
                    helperText: form?.errors?.releaseStartDate ? String(form?.errors?.releaseStartDate) : '',
                  },
                }}
                timeSteps={{ minutes: 5 }}
                format={ISO.DATE_TIME}
                {...form.register('releaseStartDate')}
                value={!form?.values?.releaseStartDate ? null : dayjs(form?.values?.releaseStartDate)}
                onChange={(value) => {
                  form.setFieldValue('releaseStartDate', isValidDate(value) ? new Date(value.toISOString()) : '');
                }}
                closeOnSelect={false}
              />
              —
              <DateTimePicker
                slotProps={{
                  textField: {
                    fullWidth: true,
                    placeholder: '公開終了日時',
                    variant: 'outlined',
                    error: !!form?.errors?.releaseEndDate,
                    helperText: form?.errors?.releaseEndDate ? String(form?.errors?.releaseEndDate) : '',
                  },
                }}
                timeSteps={{ minutes: 5 }}
                format={ISO.DATE_TIME}
                {...form.register('releaseEndDate')}
                value={!form?.values?.releaseEndDate ? null : dayjs(form?.values?.releaseEndDate)}
                onChange={(value) => {
                  form.setFieldValue('releaseEndDate', isValidDate(value) ? value.utc().toISOString() : '');
                }}
                closeOnSelect={false}
              />
            </Stack>
          </LocalizationProvider>
        </Stack>
      </SCModal>

      <SCModal
        title={'フォームを公開しています…'}
        width={400}
        isOpen={openIntervalModal}
        onClose={() => {
          setSecondsLeft(COUNT_DOWN_SECONDS);
          setOpenIntervalModal((pre) => !pre);
        }}
        closeBtnLabel={'中止'}
        primaryAction={
          <Button color="primary" onClick={handlePublishNow}>
            確定する(
            {Math.floor(secondsLeft / 60)
              .toString()
              .padStart(2, '0')}
            :{(secondsLeft % 60).toString().padStart(2, '0')})
          </Button>
        }
      >
        <Stack direction="column" gap={4}>
          <LinearProgress variant="determinate" value={(1 - secondsLeft / COUNT_DOWN_SECONDS) * 100} />
        </Stack>
      </SCModal>
    </>
  );
};

export default PublishFormButton;
