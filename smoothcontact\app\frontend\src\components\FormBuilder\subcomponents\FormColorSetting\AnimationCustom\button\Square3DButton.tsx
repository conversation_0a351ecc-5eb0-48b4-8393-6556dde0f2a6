import React from 'react';
import { styled } from '@mui/system';

const Button = styled('button')(`
  width: 100%;
  height: 40px;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  color: #fff;
  transition: all 0.5s ease;
  cursor: pointer;
  position: relative;
  background-color: #3aaaff;
  box-shadow: 0 4px rgba(11, 123, 208, 0.8);
  border: 1px solid rgba(11, 123, 208, 0.8);

  &:hover {
    opacity: 0.8;
    top: 4px;
    box-shadow: none;
    border-color: rgba(8, 89, 150, 0.8);
  }
`);

const Square3DButton: React.FC<{ label: string }> = ({ label }) => {
  return <Button>{label}</Button>;
};

export default Square3DButton;
