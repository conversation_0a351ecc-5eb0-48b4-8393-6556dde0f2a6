import { forwardRef, Module } from '@nestjs/common';

import { AccountModule } from '../account/account.module';
import { FormBuilderModule } from '../form-builder/form-builder.module';
import { AidstartController } from './aidstart.controller';

@Module({
  imports: [forwardRef(() => AccountModule), forwardRef(() => FormBuilderModule)],
  controllers: [AidstartController],
  providers: [],
})
export class AidstartModule {}
