import { Expose, Transform, Type } from 'class-transformer';

import { FormType } from '@/modules/form-builder/common/common';
import { FormColorSettingResponse, FormElementsResponse } from '@/modules/form-builder/dto/dto.response';
import { PublishStatus } from '@/modules/form-builder/entities/form-builder.entity';

export class EmbedUploadTemporaryResponseDTO {
  @Expose()
  fileName: string;

  @Expose()
  key: string;

  @Expose()
  url: string;
}

export class EmbedUploadCompleteResponseDTO {
  @Expose()
  key: string;

  @Expose()
  url: string;

  @Expose()
  publicUrl: string;
}

export class EmbedFormGeneralSettingResponse {
  @Expose()
  oGPImage: string;

  @Expose()
  isDisplaySearchEngine: boolean;

  @Expose()
  isSettingPrivacyPolicy: boolean;

  @Expose()
  isDisplayTermsUse: boolean;

  @Expose()
  isCombineIntoOneCheckbox: boolean;

  @Expose()
  termsUse: string;

  @Expose()
  policyLink: string;

  @Expose()
  whitelistedDomain: string[];

  @Expose()
  isSettingReCAPTCHA: boolean;
}

export class EmbedFormMailSettingResponse {
  @Expose()
  screenAfterSendingType: 'display_message' | 'specified_url';

  @Expose()
  message: string;

  @Expose()
  specifiedUrl: string;

  @Expose()
  receiveEmailField: string;

  @Expose()
  htmlFieldDisplays?: string[];
}

export class EmbedFormScheduleSettingResponse {
  @Expose()
  displayTextBeforePublicForm: string;

  @Expose()
  displayTextAfterPublicForm: string;

  @Expose()
  displayTextHiddenForm: string;

  @Expose()
  hideHiddenText: boolean;

  @Expose()
  maximumNumberFormsReceived: number | null;
}

export class GetEmbedFormBuilderResponseDto {
  @Expose()
  id: number;

  @Expose()
  extId: string;

  @Expose()
  name: string;

  @Expose()
  status: PublishStatus;

  @Expose()
  @Transform(({ obj }) => (obj?.mode === FormType.HTML ? undefined : obj?.formColorSetting))
  @Type(() => FormColorSettingResponse)
  formColorSetting: FormColorSettingResponse;

  @Expose()
  @Transform(({ obj }) => (obj?.mode === FormType.HTML ? undefined : obj?.formElements))
  @Type(() => FormElementsResponse)
  formElements?: FormElementsResponse[];

  @Expose()
  @Type(() => EmbedFormGeneralSettingResponse)
  formGeneralSetting: EmbedFormGeneralSettingResponse;

  @Expose()
  @Type(() => EmbedFormMailSettingResponse)
  formMailSetting: EmbedFormMailSettingResponse;

  @Expose()
  @Type(() => EmbedFormScheduleSettingResponse)
  formScheduleSetting: EmbedFormScheduleSettingResponse;

  @Expose()
  releaseStartDate: Date;

  @Expose()
  releaseEndDate: Date;

  @Expose()
  submissionCount?: number;

  @Expose()
  @Transform(({ value }) => (value !== 'dev' ? 'manual' : 'dev'))
  mode?: string;
}
