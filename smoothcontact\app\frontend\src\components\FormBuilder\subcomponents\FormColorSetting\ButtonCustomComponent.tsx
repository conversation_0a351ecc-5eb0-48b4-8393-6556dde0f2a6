import SCColorPicker from '@/components/common/SCColorPicker';
import SCFontSelector from '@/components/common/SCFontSelector';
import { KeyboardArrowDown } from '@mui/icons-material';
import { FormControl, Grid, MenuItem, Select, Stack, TextField, Typography } from '@mui/material';
import { FC } from 'react';
import { CustomModeComponentProps } from './CustomModeComponent';

const ButtonCustomComponent: FC<CustomModeComponentProps> = (props) => {
  const { form, webFonts } = props;

  return (
    <>
      <Typography variant="body2">ボタン</Typography>
      <Stack direction="row" spacing={2}>
        <TextField
          label="文字サイズ"
          {...form.register('buttonSettings.fontSize')}
          value={form?.values?.buttonSettings?.fontSize}
          variant="outlined"
          type="number"
          error={!!form?.errors?.buttonSettings?.fontSize}
          helperText={form?.errors?.buttonSettings?.fontSize ? '整数を半角で入力してください' : ''}
        />
        <Select
          IconComponent={() => <KeyboardArrowDown />}
          {...form.register('buttonSettings.fontSizeUnit')}
          value={form?.values?.buttonSettings?.fontSizeUnit}
          displayEmpty
        >
          <MenuItem value="px">px</MenuItem>
          <MenuItem value={'rem'}>rem</MenuItem>
          <MenuItem value={'em'}>em</MenuItem>
          <MenuItem value={'%'}>%</MenuItem>
        </Select>
      </Stack>
      <Stack direction="row" spacing={2}>
        <TextField
          label="角丸"
          {...form.register('buttonSettings.borderRadius')}
          value={form?.values?.buttonSettings?.borderRadius}
          variant="outlined"
          type="number"
          error={!!form?.errors?.buttonSettings?.borderRadius}
          helperText={!!form?.errors?.buttonSettings?.borderRadius && '整数を半角で入力してください'}
        />
        <Select
          IconComponent={() => <KeyboardArrowDown />}
          {...form.register('buttonSettings.borderRadiusUnit')}
          value={form?.values?.buttonSettings?.borderRadiusUnit}
          displayEmpty
        >
          <MenuItem value={'px'}>px</MenuItem>
          <MenuItem value={'rem'}>rem</MenuItem>
        </Select>
      </Stack>

      <Grid container alignItems="center">
        <Grid item xs={4} container justifyContent="flex-end">
          <Typography variant="body2">文字色</Typography>
        </Grid>
        <Grid item xs={6} sx={{ pl: 2 }}>
          <SCColorPicker name={'buttonSettings.color'} color={form?.values?.buttonSettings?.color} form={form} />
        </Grid>
      </Grid>
      <Grid container alignItems="center">
        <Grid item xs={4} container justifyContent="flex-end">
          <Typography variant="body2">背景色</Typography>
        </Grid>
        <Grid item xs={6} sx={{ pl: 2 }}>
          <SCColorPicker name="buttonSettings.bgColor" color={form?.values?.buttonSettings?.bgColor} form={form} />
        </Grid>
      </Grid>
      <Grid container alignItems="center">
        <Grid item xs={4} container justifyContent="flex-end">
          <Typography variant="body2">枠線の色</Typography>
        </Grid>
        <Grid item xs={6} sx={{ pl: 2 }}>
          <SCColorPicker name="buttonSettings.borderColor" color={form?.values?.buttonSettings?.borderColor} form={form} />
        </Grid>
      </Grid>
      <FormControl fullWidth>
        <SCFontSelector
          name={'buttonSettings.fontFamily'}
          fontFamily={form?.values?.buttonSettings?.fontFamily ?? ''}
          fontName={form?.values?.buttonSettings?.fontName ?? form?.values?.buttonSettings?.fontFamily ?? ''}
          source={webFonts}
          onFontChange={(font) => {
            form?.setFieldValue('buttonSettings.fontFamily', `${font?.fontFamily}`);
            form?.setFieldValue('buttonSettings.fontName', `${font?.fontName}`);
          }}
        />
      </FormControl>
    </>
  );
};

export default ButtonCustomComponent;
