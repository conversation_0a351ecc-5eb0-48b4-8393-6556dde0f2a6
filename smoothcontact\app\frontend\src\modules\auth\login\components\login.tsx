import SCLoadingButton from '@/components/common/SCLoadingButton';
import { useFormHandler } from '@/hooks/useFormHandler';
import { checkFieldErrorHelper } from '@/utils/validate';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';

export interface Props {
  formHandler: ReturnType<typeof useFormHandler>;
  loading?: boolean;
}

function Login({ formHandler, loading }: Props) {
  return (
    <Container component="main">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          padding: '20px',
          minWidth: '400px',
        }}
      >
        <Typography component="h1" variant="h5">
          ログイン
        </Typography>
        <Box component="form" onSubmit={formHandler?.handleSubmit} noValidate sx={{ mt: 2, width: '100%' }}>
          <TextField
            fullWidth
            label="メールアドレス"
            placeholder="<EMAIL>"
            name="email"
            autoFocus
            size="medium"
            {...formHandler?.register('email')}
            value={formHandler?.values?.email}
            error={!!checkFieldErrorHelper(formHandler, 'email')}
            helperText={checkFieldErrorHelper(formHandler, 'email')}
            InputLabelProps={{ shrink: true }}
          />
          <TextField
            margin="normal"
            fullWidth
            name="pwd"
            label="パスワード"
            type="password"
            autoComplete="current-password"
            size="medium"
            {...formHandler?.register('pwd')}
            value={formHandler?.values?.pwd}
            error={!!checkFieldErrorHelper(formHandler, 'pwd')}
            helperText={
              checkFieldErrorHelper(formHandler, 'pwd')
                ? checkFieldErrorHelper(formHandler, 'pwd')
                : '※半角英数字6文字以上16文字以内で設定してください。 記号は「_ -」が使用できます '
            }
            InputLabelProps={{ shrink: true }}
          />
          <SCLoadingButton
            disabled={!formHandler?.isValid}
            loading={loading}
            className="btn-black"
            type="submit"
            fullWidth
            size="large"
            variant="contained"
            sx={{ mt: 3, mb: 2 }}
          >
            ログイン
          </SCLoadingButton>
        </Box>
      </Box>
    </Container>
  );
}

export default Login;
