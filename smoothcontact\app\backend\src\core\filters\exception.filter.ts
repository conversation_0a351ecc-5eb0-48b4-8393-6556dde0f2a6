import {
  ArgumentsHost,
  BadRequestException,
  Catch,
  HttpException as RootHttpException,
  HttpStatus,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';
import { ValidationError } from 'class-validator';
import { Response } from 'express';

import { buildFailResponse, errorFormatter } from '@/common/helper';
import { HttpException } from '@/types/httpException.type';
import { APIResponseBase } from '@/types/response.type';

import { logger } from '../logger/index.logger';

@Catch()
export class ExceptionFilter extends BaseExceptionFilter {
  doNotReport(): Array<any> {
    return [NotFoundException, UnauthorizedException];
  }

  catch(error: RootHttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const errorResponse = error?.getResponse?.();

    if (error instanceof BadRequestException) {
      const newError = error as BadRequestException;
      const data = newError.getResponse() as { message: ValidationError[] };
      let errorsValidations: { [key: string]: string } = {};

      if (Array.isArray(data.message)) {
        errorsValidations = errorFormatter(data.message);
      }

      return response.status(HttpStatus.BAD_REQUEST).send(
        buildFailResponse(
          new APIResponseBase({
            statusCode: HttpStatus.BAD_REQUEST,
            messageErrors: errorsValidations,
            message: errorsValidations ? Object.values(errorsValidations)?.[0] : '',
          }),
        ),
      );
    }

    if (!errorResponse && !(error instanceof HttpException)) {
      const newError = error as Error;

      if (!(newError as any).response) {
        logger.error(`StatusCode : ${HttpStatus.INTERNAL_SERVER_ERROR}, Message : ${newError.message}, Error: ${JSON.stringify(newError.stack)}`);

        return response.status(HttpStatus.INTERNAL_SERVER_ERROR).send(
          buildFailResponse(
            new APIResponseBase({
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
              message: process.env.NODE_ENV == 'development' ? error?.message : '',
            }),
          ),
        );
      }
    }

    const newError = error as HttpException<unknown>;

    const StatusCode = newError.statusCode || newError.getStatus() || HttpStatus.BAD_REQUEST;

    return response.status(StatusCode).send(
      buildFailResponse(
        new APIResponseBase({
          statusCode: StatusCode,
          ...newError,
        }),
      ),
    );
  }
}
