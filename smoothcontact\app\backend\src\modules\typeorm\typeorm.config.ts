import { config } from 'dotenv';
config();

import { DataSource, DataSourceOptions } from 'typeorm';

export const dataSourceOptions: DataSourceOptions = {
  type: process.env.DATABASE_TYPE as any,
  host: process.env.DATABASE_HOST,
  port: process.env.DATABASE_PORT as any,
  database: `${process.env.DATABASE_NAME}${process.env.TEST_MODE === '1' ? '_test' : ''}`,
  username: process.env.DATABASE_USER,
  password: process.env.DATABASE_PASSWORD,
  entities: ['dist/**/*.entity.{ts,js}'],
  migrations: ['dist/migrations/*.{ts,js}'],
  migrationsTableName: `${process.env.ENTITY_PREFIX}typeorm_migrations`,
  logger: process.env.NODE_ENV === 'production' ? 'simple-console' : 'advanced-console',
  logging: process.env.DATABASE_LOGGING === 'true',
  entityPrefix: process.env.ENTITY_PREFIX || '',
  migrationsRun: process.env.DATABASE_MIGRATIONS_RUN === 'true',
  timezone: 'Z',
};

const dataSource = new DataSource(dataSourceOptions);

// eslint-disable-next-line no-restricted-syntax
export default dataSource;
