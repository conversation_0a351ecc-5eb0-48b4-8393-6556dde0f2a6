import React, { FC, useState } from 'react';
import {
  Box,
  Grid,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TablePagination,
  TableRow,
  Typography,
  useTheme,
} from '@mui/material';
import { tableCellClasses } from '@mui/material/TableCell';
import SCSimpleCard from '@/components/common/SCSimpleCard';
import { KeyboardArrowLeft, KeyboardArrowRight } from '@mui/icons-material';
import { Link } from 'react-router-dom';
import styled from '@emotion/styled';

const StyledLink = styled(Link)`
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
`;

interface ReportOtherAnswerProps {
  otherData: string[];
  statistics: any;
  title: string;
  key?: number;
  controlName: string;
}

interface TablePaginationActionsProps {
  count: number;
  page: number;
  rowsPerPage: number;
  onPageChange: (event: React.MouseEvent<HTMLButtonElement>, newPage: number) => void;
}

const TablePaginationActions: FC<TablePaginationActionsProps> = ({ count, page, rowsPerPage, onPageChange }) => {
  const theme = useTheme();

  const handleBackButtonClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    onPageChange(event, page - 1);
  };

  const handleNextButtonClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    onPageChange(event, page + 1);
  };

  return (
    <Box sx={{ flexShrink: 0, ml: 2.5 }}>
      <IconButton onClick={handleBackButtonClick} disabled={page === 0} aria-label="previous page">
        {theme.direction === 'rtl' ? <KeyboardArrowRight /> : <KeyboardArrowLeft />}
      </IconButton>
      <IconButton onClick={handleNextButtonClick} disabled={page >= Math.ceil(count / rowsPerPage) - 1} aria-label="next page">
        {theme.direction === 'rtl' ? <KeyboardArrowLeft /> : <KeyboardArrowRight />}
      </IconButton>
    </Box>
  );
};

const ReportOtherAnswer: FC<ReportOtherAnswerProps> = ({ otherData, title, controlName, statistics }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const handleSelect = (rowId: number) => {
    setSelectedRow(rowId);
  };

  const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - otherData.length) : 0;

  const handleChangePage = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const renderAddressValue = (address: Record<string, any>) => {
    if (!address?.postalCode) {
      return '選択してください';
    }

    const { building = '', city = '', postalCode = '', prefecture = '', street = '', town = '' } = address;

    if (!postalCode && !prefecture && !city && !town && !street && !building) {
      return '';
    }

    return `〒${postalCode} ${prefecture}${city}${town}${street}${building}`;
  };

  const renderFullNameValue = (fullname: string | Record<string, any>): string => {
    if (typeof fullname === 'string') {
      return fullname;
    }

    const { name = '', pronunciation = '' } = fullname;

    let nameWithPronunciation = name;

    if (pronunciation) {
      nameWithPronunciation += ` (${pronunciation})`;
    }

    return nameWithPronunciation.trim();
  };

  const renderMultipleLineValue = (content: string) => {
    return (
      <>
        {content.split('\n').map((line, index) => (
          <React.Fragment key={index}>
            {line}
            <br />
          </React.Fragment>
        ))}
      </>
    );
  };

  const renderContent = (content: any, controlName: string) => {
    switch (controlName) {
      case 'address':
        return renderAddressValue(content as Record<string, any>);

      case 'fullname':
        return renderFullNameValue(content);

      case 'multiline-text-field':
        return renderMultipleLineValue(content as string);

      case 'file-upload':
        if (!content || typeof content !== 'string') {
          return '';
        }

        const fileName = statistics?.[content] ? statistics?.[content] : content.split('/').pop();
        const imageUrl = `${import.meta.env.VITE_AWS_S3_BUCKET_URL}/${content}`;

        return (
          <Grid container>
            <Grid item xs={10}>
              {fileName}
            </Grid>
            <Grid item xs={2} container justifyContent="flex-end">
              <StyledLink to={imageUrl} target="_blank">
                ダウンロード
              </StyledLink>
            </Grid>
          </Grid>
        );

      default:
        return content;
    }
  };

  return (
    <Grid item xs={12} sx={{ paddingTop: 4 }}>
      <SCSimpleCard>
        <Typography color="text.primary" variant="h6" sx={{ py: 2 }}>
          {title}
        </Typography>
        <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
          <Table
            sx={{
              minWidth: 500,
              [`& .${tableCellClasses.root}`]: {
                borderBottom: 'none',
                paddingTop: '5px',
                paddingBottom: '5px',
                paddingLeft: '10px',
              },
            }}
          >
            <TableBody>
              {(rowsPerPage > 0 ? otherData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage) : otherData).map(
                (content: any, index: number) => (
                  <TableRow key={index} selected={selectedRow === index} onClick={() => handleSelect(index)}>
                    <TableCell component="th" scope="row" style={{ width: 10, paddingLeft: '10px', paddingRight: '0' }}>
                      {page * rowsPerPage + index + 1}
                    </TableCell>
                    <TableCell component="th" scope="row">
                      {renderContent(content, controlName)}
                    </TableCell>
                  </TableRow>
                )
              )}
              {emptyRows > 0 && (
                <TableRow style={{ height: 53 * emptyRows }}>
                  <TableCell colSpan={6} />
                </TableRow>
              )}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TablePagination
                  rowsPerPageOptions={[5, 10, 25, { label: 'All', value: -1 }]}
                  colSpan={2}
                  count={otherData.length}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  slotProps={{
                    select: {
                      inputProps: {
                        'aria-label': 'rows per page',
                      },
                      native: true,
                    },
                  }}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  ActionsComponent={TablePaginationActions}
                />
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
      </SCSimpleCard>
    </Grid>
  );
};

export default ReportOtherAnswer;
