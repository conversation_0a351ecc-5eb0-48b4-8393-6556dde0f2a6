import { URL_REGEX } from '@/common/constants';
import { Checkbox, FormControlLabel, FormGroup, FormHelperText } from '@mui/material';
import React, { useEffect, useMemo, useState } from 'react';

interface FormPrivacyAndTermsProps {
  className?: string;
  isDisplayPrivacy?: boolean;
  isDisplayTerms?: boolean;
  isCombined?: boolean;
  error?: string;
  privacyUrl?: string;
  termsUrl?: string;
  onAccept?: (checked: boolean) => void;
}

const PrivacyLink: React.FC<{ url: string }> = ({ url }) => {
  return (
    <>
      <a href={url} target="_blank" rel="noreferrer">
        プライバシーポリシー
      </a>
      に同意する
    </>
  );
};

const TermsLink: React.FC<{ url: string }> = ({ url }) => {
  return (
    <>
      <a href={url} target="_blank" rel="noreferrer">
        利用規約
      </a>
      に同意する
    </>
  );
};

const PrivacyAndTermsLink: React.FC<{ privacy: string; terms: string }> = ({ privacy, terms }) => {
  const validPrivacy = privacy && URL_REGEX.test(privacy);
  const validTerm = terms && URL_REGEX.test(privacy);

  return (
    <>
      {validPrivacy && (
        <a href={terms} target="_blank" rel="noreferrer">
          プライバシーポリシー
        </a>
      )}
      {validPrivacy && validTerm && `と`}
      {validTerm && (
        <a href={privacy} target="_blank" rel="noreferrer">
          利用規約
        </a>
      )}
      に同意する
    </>
  );
};

const FormPrivacyAndTerms: React.FC<FormPrivacyAndTermsProps> = ({
  className,
  error,
  isDisplayPrivacy,
  privacyUrl,
  isDisplayTerms,
  termsUrl,
  isCombined,
  onAccept,
}) => {
  const [checked, setChecked] = useState({ privacy: null, terms: null });

  const handleChange = (type: 'privacy' | 'terms' | 'both') => (event: React.ChangeEvent<HTMLInputElement>) => {
    if (type === 'both') {
      setChecked({ privacy: event.target.checked, terms: event.target.checked });

      return;
    }

    setChecked((prev) => ({ ...prev, [type]: event.target.checked }));
  };

  const handleAccept = () => {
    if (!onAccept) return;

    if (isDisplayPrivacy && isDisplayTerms) {
      onAccept((checked.privacy ?? false) && (checked.terms ?? false));

      return;
    }

    if (isDisplayPrivacy) {
      onAccept(checked.privacy ?? false);

      return;
    }

    if (isDisplayTerms) {
      onAccept(checked.terms ?? false);

      return;
    }
  };

  useEffect(() => {
    if (checked.privacy !== null || checked.terms !== null) handleAccept();
  }, [checked.privacy, checked.terms]);

  const isValidPrivacyUrl = useMemo(() => {
    return isDisplayPrivacy && privacyUrl && URL_REGEX.test(privacyUrl);
  }, [isDisplayPrivacy, privacyUrl]);

  const isValidTermUrl = useMemo(() => {
    return isDisplayTerms && termsUrl && URL_REGEX.test(termsUrl);
  }, [isDisplayTerms, termsUrl]);

  const renderPrivacy = () => (
    <FormControlLabel
      value={checked.privacy ?? false}
      checked={checked.privacy ?? false}
      onChange={handleChange('privacy')}
      control={<Checkbox sx={{ py: 0 }} />}
      label={<PrivacyLink url={privacyUrl} />}
    />
  );

  const renderTerms = () => (
    <FormControlLabel
      value={checked.terms ?? false}
      checked={checked.terms ?? false}
      onChange={handleChange('terms')}
      control={<Checkbox sx={{ py: 0 }} />}
      label={<TermsLink url={termsUrl} />}
    />
  );

  const renderCombined = () => (
    <>
      <FormControlLabel
        control={
          <Checkbox
            checked={(checked.privacy ?? false) && (checked.terms ?? false)}
            value={(checked.privacy ?? false) && (checked.terms ?? false)}
            onChange={handleChange('both')}
          />
        }
        label={<PrivacyAndTermsLink privacy={privacyUrl || ''} terms={termsUrl || ''} />}
      />
      {error && <FormHelperText error>{error}</FormHelperText>}
    </>
  );

  const renderPrivacyAndTerms = () => (
    <>
      {isValidPrivacyUrl && renderPrivacy()}
      {isValidTermUrl && renderTerms()}
      {error && <FormHelperText error>{error}</FormHelperText>}
    </>
  );

  return (
    <>
      {(isValidPrivacyUrl || isValidTermUrl) && (
        <FormGroup sx={{ gap: 1 }} className={className}>
          {isCombined && isValidPrivacyUrl && isValidTermUrl ? renderCombined() : renderPrivacyAndTerms()}
        </FormGroup>
      )}
    </>
  );
};

export default FormPrivacyAndTerms;
