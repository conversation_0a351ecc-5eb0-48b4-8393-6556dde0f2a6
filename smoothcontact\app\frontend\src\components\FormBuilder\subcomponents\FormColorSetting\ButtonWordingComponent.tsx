import React, { FC } from 'react';
import { TextField } from '@mui/material';

interface ButtonWordingProps {
  name: string;
  label: string;
  value: string;
  helperText: string;
  onChange: (value: string) => void;
}

const ButtonWording: FC<ButtonWordingProps> = ({ name, label, value, onChange, helperText = '' }) => (
  <TextField
    id="button-wording-${label}"
    name={name}
    label={label}
    value={value}
    onChange={(event: React.ChangeEvent<HTMLInputElement>) => onChange(event.target.value)}
    helperText={helperText}
  />
);

export default ButtonWording;
