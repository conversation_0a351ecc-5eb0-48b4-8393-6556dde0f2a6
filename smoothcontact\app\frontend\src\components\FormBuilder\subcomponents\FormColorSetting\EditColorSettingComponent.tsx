import { use<PERSON><PERSON><PERSON>and<PERSON> } from '@/hooks/useFormHandler';
import { useWebFont } from '@/hooks/useWebFont';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormColorOptionMode, FormColorSetting } from '@/types/FormTemplateTypes';
import {
  FORM_COLOR_SETTING_INIT,
  FormTemplateModePresent,
  FormTemplateModePresentType,
  FormTemplateModePresets,
  ThemeColorSettingOptions,
} from '@/utils/formBuilderUtils';
import { FormControl, FormControlLabel, Radio, RadioGroup, Stack, Typography } from '@mui/material';
import Divider from '@mui/material/Divider';
import { ChangeEvent, FC, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import * as Yup from 'yup';
import { object } from 'yup';
import CustomModeComponent from './CustomModeComponent';
import TemplateModeComponent from './TemplateModeComponent';

interface EditColorSettingComponentProps {}

const EditColorSettingComponent: FC<EditColorSettingComponentProps> = () => {
  const { webFonts } = useWebFont();
  const { t } = useTranslation();
  const { formColorSetting, isFormChanged, editColorSetting, setError } = useFormBuilder();
  const validationSchema = useMemo(
    () =>
      object<FormColorSetting>({
        generalSettings: Yup.object({
          spacing: Yup.number().required('項目の間隔が必須です'),
          fontSize: Yup.number().required('文字サイズが必須です'),
        }),
        titleSettings: Yup.object({
          fontSize: Yup.number().required('文字サイズが必須です'),
        }),
        labelSettings: Yup.object({
          fontSize: Yup.number().required('文字サイズが必須です'),
        }),
        descriptionSettings: Yup.object({
          fontSize: Yup.number().required('文字サイズが必須です'),
        }),
        entryFormSettings: Yup.object({
          borderRadius: Yup.number().required('角丸が必須です'),
          fontSize: Yup.number().required('文字サイズが必須です'),
        }),
        buttonSettings: Yup.object({
          text: Yup.string().required('送信ボタンの文言が必須です'),
          fontSize: Yup.number().required('文字サイズが必須です'),
          borderRadius: Yup.number().required('角丸が必須です'),
        }),
      }),
    []
  );
  const form = useFormHandler<FormColorSetting>({
    initialValues: formColorSetting,
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: (e) => {
      console.log('submit', e);
    },
  });

  useEffect(() => {
    if (form.isValid && form.dirty) {
      editColorSetting(form.values);
    }

    setError(!form.isValid);
  }, [form.isValid, form.values]);

  useEffect(() => {
    if (!isFormChanged) {
      form.resetForm({
        values: formColorSetting,
      });
    }
  }, [isFormChanged]);

  const handleChangeOptionMode = (event: ChangeEvent<HTMLInputElement>) => {
    if (event?.target?.value === ThemeColorSettingOptions.TEMPLATE) {
      const formTemplateColor = getFormTemplateColor(formColorSetting?.templateModeColor as FormTemplateModePresentType);

      updateFormValues({
        ...formTemplateColor,
        templateModeColor: formColorSetting?.templateModeColor,
        optionMode: event?.target?.value as FormColorOptionMode,
      });
      editColorSetting(formTemplateColor);
    }

    form?.setFieldValue('optionMode', event?.target?.value);
  };

  const getFormTemplateColor = (selectedValue: FormTemplateModePresentType) => {
    const selectedTemplateColor: FormTemplateModePresent = FormTemplateModePresets[selectedValue ?? FormTemplateModePresentType.BASIC];
    const isBlackTemplate = selectedValue === FormTemplateModePresentType.BLACK;

    return {
      ...FORM_COLOR_SETTING_INIT,
      layoutMode: formColorSetting.layoutMode,
      bgColor: selectedTemplateColor?.bgColor,
      titleSettings: {
        ...FORM_COLOR_SETTING_INIT?.titleSettings,
        color: selectedTemplateColor?.buttonColor,
      },
      buttonSettings: {
        ...FORM_COLOR_SETTING_INIT?.buttonSettings,
        bgColor: selectedTemplateColor?.buttonColor,
        borderColor: selectedTemplateColor?.borderColor,
      },
      labelSettings: {
        ...FORM_COLOR_SETTING_INIT?.labelSettings,
        color: isBlackTemplate ? '#FFF' : (selectedTemplateColor?.color ?? '#0c0d0e'),
      },
      descriptionSettings: {
        ...FORM_COLOR_SETTING_INIT?.descriptionSettings,
        color: isBlackTemplate ? '#FFF' : (selectedTemplateColor?.color ?? '#0c0d0e'),
      },
      generalSettings: {
        ...FORM_COLOR_SETTING_INIT?.generalSettings,
        color: isBlackTemplate ? '#FFF' : (selectedTemplateColor?.color ?? '#0c0d0e'),
      },
      entryFormSettings: {
        ...FORM_COLOR_SETTING_INIT?.entryFormSettings,
        color: isBlackTemplate ? '#000' : (selectedTemplateColor?.color ?? '#0c0d0e'),
      },
      animationSettings: {
        ...FORM_COLOR_SETTING_INIT?.animationSettings,
      },
    };
  };

  const onChangeTemplateMode = (templateModeColor: FormTemplateModePresentType) => {
    const formTemplateColor = getFormTemplateColor(templateModeColor);

    updateFormValues({ ...formTemplateColor, templateModeColor });
    editColorSetting(formTemplateColor);
  };

  const updateFormValues = (formTemplateColor: FormColorSetting) => {
    form?.setFieldValue('bgColor', formTemplateColor?.bgColor);
    form?.setFieldValue('titleSettings', formTemplateColor?.titleSettings);
    form?.setFieldValue('buttonSettings', formTemplateColor?.buttonSettings);
    form?.setFieldValue('labelSettings', formTemplateColor?.labelSettings);
    form?.setFieldValue('descriptionSettings', formTemplateColor?.descriptionSettings);
    form?.setFieldValue('generalSettings', formTemplateColor?.generalSettings);
    form?.setFieldValue('entryFormSettings', formTemplateColor?.entryFormSettings);
    form?.setFieldValue('layoutMode', formTemplateColor?.layoutMode);
    form?.setFieldValue('templateModeColor', formTemplateColor?.templateModeColor);
    form?.setFieldValue('animationSettings', formTemplateColor?.animationSettings);
  };

  return (
    <>
      <Stack direction="column" spacing={2} sx={{ mb: 4 }}>
        <FormControl>
          <Typography variant="body2">{t('form_builder.color_setting.theme')}</Typography>
          <RadioGroup
            name="optionMode"
            value={form?.values?.optionMode ?? ThemeColorSettingOptions.STORE}
            {...form.register('optionMode')}
            onChange={handleChangeOptionMode}
          >
            <FormControlLabel
              sx={{ mb: -1 }}
              value={ThemeColorSettingOptions.TEMPLATE}
              control={<Radio value={ThemeColorSettingOptions.TEMPLATE} sx={{ paddingLeft: 2 }} />}
              label={t('form_builder.color_setting.theme_options.template_mode')}
            />
            <FormControlLabel
              sx={{ mb: -1 }}
              value={ThemeColorSettingOptions.CUSTOM}
              control={<Radio value={ThemeColorSettingOptions.CUSTOM} sx={{ paddingLeft: 2 }} />}
              label={t('form_builder.color_setting.theme_options.custom_mode')}
            />
          </RadioGroup>
        </FormControl>
        <Divider />
        <FormControl>
          <Typography variant="body2">{t('form_builder.color_setting.layout')}</Typography>
          <RadioGroup value={form?.values?.layoutMode ?? 'vertical'} {...form.register('layoutMode')}>
            <FormControlLabel
              sx={{ mb: -1 }}
              value="vertical"
              control={<Radio value="vertical" sx={{ paddingLeft: 2 }} />}
              label={t('form_builder.color_setting.layout_options.1column')}
            />
            <FormControlLabel
              sx={{ mb: -1 }}
              value="horizontal"
              control={<Radio value="horizontal" sx={{ paddingLeft: 2 }} />}
              label={t('form_builder.color_setting.layout_options.2column')}
            />
          </RadioGroup>
        </FormControl>
        <Divider />

        {form?.values?.optionMode === ThemeColorSettingOptions.TEMPLATE && (
          <TemplateModeComponent value={form?.values?.templateModeColor} onChangeTemplateMode={onChangeTemplateMode} />
        )}
        {form?.values?.optionMode === ThemeColorSettingOptions.CUSTOM && <CustomModeComponent form={form} webFonts={webFonts} />}
      </Stack>
    </>
  );
};

export default EditColorSettingComponent;
