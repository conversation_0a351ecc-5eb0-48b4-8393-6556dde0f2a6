import { WebFontItem, WebFontType } from '@/components/common/SCFontSelector';
import { getWebFontsRequestConfig } from '@/services/web-font.service';
import { useEffect, useState } from 'react';
import useAxios from './useAxios';
import { useAppSelector } from '@/store/hook';

export function useWebFont() {
  const [webFonts, setWebFonts] = useState<Record<WebFontType, WebFontItem[]>>(null);
  const { apiCaller } = useAxios();
  const { profile } = useAppSelector((state) => ({
    profile: state.app.profile,
  }));

  useEffect(() => {
    const getWebFonts = async () => {
      const result = await apiCaller(getWebFontsRequestConfig());

      if (result.success) {
        const fonts = result.data as Record<WebFontType, WebFontItem[]>;

        if (!!(profile?.callbackUrl && profile?.setting) || !!profile?.oemId) {
          delete fonts[WebFontType.SHOPIFY_FONT];
        }

        setWebFonts(result.data as Record<WebFontType, WebFontItem[]>);
      }
    };
    getWebFonts();
  }, []);

  return {
    webFonts,
  };
}
