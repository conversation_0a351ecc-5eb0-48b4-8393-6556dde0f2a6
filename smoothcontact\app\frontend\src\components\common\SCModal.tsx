import React, { FC } from 'react';
import { Box, IconButton, Stack, Typography, useMediaQuery, useTheme } from '@mui/material';
import Modal from '@mui/material/Modal';
import Button from '@mui/material/Button';
import CloseIcon from '@mui/icons-material/Close';

interface SCModalProps {
  title?: string;
  isOpen: boolean;
  onClose: () => void;
  onIconClose?: () => void;
  width?: number;
  primaryAction?: React.ReactNode;
  secondaryAction?: React.ReactNode;
  children?: React.ReactNode;
  closeBtnLabel?: string;
  secondaryActionColor?: 'inherit' | 'error' | 'primary' | 'secondary' | 'info' | 'success' | 'warning';
  enableIconClose?: boolean;
  enableBackDropClick?: boolean;
}

const SCModal: FC<SCModalProps> = ({
  title,
  isOpen,
  onClose,
  onIconClose,
  width = 500,
  primaryAction,
  secondaryAction,
  children,
  closeBtnLabel,
  secondaryActionColor = 'secondary',
  enableIconClose = false,
  enableBackDropClick = false,
}) => {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const modalWidth = isSmallScreen ? '100%' : width;

  return (
    <React.Fragment>
      <Modal
        open={isOpen}
        onClose={(event, reason) => {
          if (reason === 'backdropClick' && enableBackDropClick) return;

          onClose();
        }}
        sx={{ flexShrink: 0 }}
      >
        <Box
          width={modalWidth}
          p={1}
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            bgcolor: 'background.paper',
            boxShadow: 24,
            borderRadius: 1,
            p: 2,
          }}
        >
          <Stack direction="row" justifyContent={title ? 'space-between' : 'flex-end'} alignItems="center" gap={2} mb={2}>
            {title && (
              <Typography variant="h6" sx={{ fontSize: '17px', fontWeight: '700' }}>
                {title}
              </Typography>
            )}
            {enableIconClose && (
              <IconButton aria-label="delete" size="small" onClick={onIconClose}>
                <CloseIcon fontSize="inherit" />
              </IconButton>
            )}
          </Stack>
          <Box>{children}</Box>
          <Box sx={{ mt: 4 }} display="flex" justifyContent="end">
            <Stack direction="row" spacing={2}>
              {secondaryAction ? (
                secondaryAction
              ) : (
                <Button color={secondaryActionColor} onClick={onClose}>
                  {closeBtnLabel ?? '完了'}
                </Button>
              )}
              {primaryAction}
            </Stack>
          </Box>
        </Box>
      </Modal>
    </React.Fragment>
  );
};

export default SCModal;
