@charset "UTF-8";
html,
body,
div,
span,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
abbr,
address,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
samp,
small,
strong,
sub,
sup,
var,
b,
i,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  vertical-align: baseline;
  background: transparent;
}

fieldset,
img {
  border: 0;
}

address,
caption,
cite,
code,
dfn,
em,
strong,
th,
var {
  font-style: normal;
  font-weight: 400;
}

em,
strong {
  font-weight: 700;
}

ol,
ul {
  list-style: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

caption,
th {
  text-align: left;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

span.smode {
  display: inline-block;
  width: 100%;
}

a {
  text-decoration: none;
  color: #333;
  transition: opacity 0.2s ease;
}

a:hover {
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=40);
  opacity: 0.4;
  filter: alpha(opacity=40);
}

@media only screen and (max-width: 640px) {
  a:hover {
    filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

img,
video {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

*,
*:before,
*:after {
  box-sizing: border-box;
}

.clear {
  display: table;
  line-height: 0;
  content: '';
  clear: both;
}

@font-face {
  font-family: digitalstage;
  src: url(../font/digitalstage.eot?-nqk2uw);
  src:
    url(../font/digitalstage.eot?#iefix-nqk2uw) format('embedded-opentype'),
    url(../font/digitalstage.ttf?-nqk2uw) format('truetype'),
    url(../font/digitalstage.woff?-nqk2uw) format('woff'),
    url(../font/digitalstage.svg?-nqk2uw#digitalstage) format('svg');
}

[class^='icon-'],
[class*=' icon-'] {
  font-family: digitalstage;
  speak: none;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

[class^='bindicon-'],
[class*=' bindicon-'] {
  font-size: 1em;
  margin-right: 3px;
  margin-left: 3px;
}

.icon-close:before {
  content: '\e663';
}

.icon-left_arrow:before {
  content: '\e600';
}

.icon-right_arrow:before {
  content: '\e601';
}

html {
  font-size: 14px;
}

body {
  font-family:
    Helvetica Neue,
    Helvetica,
    YuGothic,
    Yu Gothic,
    'ヒラギノ角ゴ Pro W3',
    Hiragino Kaku Gothic Pro,
    'メイリオ',
    Meiryo,
    'ＭＳＰゴシック',
    sans-serif;
  color: #555;
  min-width: 100%;
  line-height: 1.9;
}

.site_frame {
  margin: 0 auto;
}

.site_frame:before,
.site_frame:after {
  display: table;
  line-height: 0;
  content: '';
}

.site_frame:after {
  clear: both;
}

@media only screen and (max-width: 640px) {
  .site_frame {
    width: 100%;
    margin: 0;
  }
}

.a-billboard .site_frame {
  position: relative;
}

.c-menu + .c-sp_navigation_btn {
  border-left-color: #555;
}

.c-menu .c-unlink {
  color: #999;
}

.c-menu.-menu_a {
  border-color: #999;
}

.c-menu.-menu_a li {
  border-color: #999;
}

.c-menu.-menu_a li a,
.c-menu.-menu_a li .c-unlink {
  border-color: #555;
  padding: 0 20px;
}

.c-menu.-menu_a li:first-child {
  border-color: #999;
}

.c-menu.-menu_a.-v {
  border-color: #999;
}

.c-menu.-menu_a.-v li a,
.c-menu.-menu_a.-v li .c-unlink,
.c-menu.-menu_a.-v li:first-child a,
.c-menu.-menu_a.-v li:first-child .c-unlink {
  border-color: #999;
}

@media only screen and (max-width: 768px) {
  .c-menu.-menu_a.-v {
    border-bottom: 1px solid #999;
  }

  .c-menu.-menu_a.-v li {
    border-bottom: 0 none;
  }

  .c-menu.-menu_a.-v li a,
  .c-menu.-menu_a.-v li .c-unlink {
    border-top: 1px solid #999;
  }
}

.c-menu.-menu_b li a {
  padding: 10px 20px;
  color: #333;
}

@media only screen and (max-width: 768px) {
  .c-menu.-menu_b li a {
    border-bottom: 0 none;
  }
}

.c-menu.-menu_b li .c-unlink {
  padding: 10px 20px;
}

@media only screen and (max-width: 768px) {
  .c-menu.-menu_b.-v {
    border-top: 0 none;
    border-bottom: 1px solid #999;
  }

  .c-menu.-menu_b.-v li {
    border-bottom: 0 none;
  }

  .c-menu.-menu_b.-v li a,
  .c-menu.-menu_b.-v li .c-unlink {
    border-bottom: 0 none;
    border-top: 1px solid #999;
  }
}

.c-menu.-menu_c {
  background: #dcdcdc;
  border-radius: 20px;
  padding: 0 20px;
  overflow: hidden;
}

.c-menu.-menu_c li {
  border-color: #fff;
}

.c-menu.-menu_c li a {
  border-bottom: none;
  background: #dcdcdc;
  padding: 5px 15px;
  line-height: 1.2;
  color: #333;
  display: block;
}

.c-menu.-menu_c li a:hover {
  background: #aaa;
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
  opacity: 1;
  filter: alpha(opacity=100);
}

.c-menu.-menu_c li .c-current {
  background: #aaa;
  color: #fff;
}

.c-menu.-menu_c li .c-unlink {
  padding: 5px 15px;
  line-height: 1.2;
}

@media only screen and (max-width: 768px) {
  .c-menu.-menu_c li {
    border-top: 1px solid #fff;
    border-bottom: 0 none;
  }

  .c-menu.-menu_c li:first-child {
    border: 0 none;
  }
}

.c-menu.-menu_c.-v {
  padding-left: 0;
  padding-right: 0;
  border-bottom: none;
}

@media only screen and (max-width: 768px) {
  .c-menu.-menu_c.-v li {
    border-top: 1px solid #fff;
    border-bottom: 0 none;
  }
}

.c-menu.-menu_d {
  background: #dcdcdc;
}

.c-menu.-menu_d li {
  border: 0 none;
}

.c-menu.-menu_d li a,
.c-menu.-menu_d li .c-unlink {
  padding: 10px 20px;
  background: #dcdcdc;
  border-bottom: 0 none;
}

.c-menu.-menu_d li a {
  color: #333;
}

.c-menu.-menu_d li:first-child {
  border: 0 none;
}

.c-menu.-menu_d a:hover {
  background: #fff;
  color: #000;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=40);
  opacity: 0.4;
  filter: alpha(opacity=40);
}

@media only screen and (max-width: 768px) {
  .c-menu.-menu_d a:hover {
    filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.c-menu.-menu_d .c-current {
  background: #fff;
  color: #000;
}

.c-menu.-menu_d.-v {
  background-image: none;
  background-color: transparent;
  -webkit-filter: none;
  filter: none;
  border-bottom: 0 none;
}

.c-menu.-menu_d.-v li {
  border-top: 0 none;
}

.c-menu.-menu_d.-v li a {
  border-bottom: 0 none;
}

@media only screen and (max-width: 768px) {
  .c-menu.-menu_d.-v li {
    border-bottom: 0 none;
  }
}

.c-menu.-menu_e {
  background: #d1d1d1;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff',endColorstr='#d1d1d1',GradientType=0);
  background: linear-gradient(to bottom, #fff 0, #ebe8eb 45%, #dedade 48%, #d1d1d1);
  border: 1px solid #d3d3d3;
}

.c-menu.-menu_e li {
  border: 0 none;
}

.c-menu.-menu_e li a {
  color: #333;
  border-bottom: 0 none;
}

.c-menu.-menu_e li a:hover {
  background: #fff;
  color: #000;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=65);
  opacity: 0.65;
  filter: alpha(opacity=65);
}

@media only screen and (max-width: 768px) {
  .c-menu.-menu_e li a:hover {
    filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.c-menu.-menu_e li a,
.c-menu.-menu_e li .c-unlink {
  padding: 8px 20px;
}

.c-menu.-menu_e li .c-current a,
.c-menu.-menu_e li .c-current:hover a {
  background: #dcdcdc;
  color: #333;
}

.c-menu.-menu_e li:first-child {
  border: 0 none;
}

@media only screen and (max-width: 768px) {
  .c-menu.-menu_e {
    background: none;
  }
}

.c-menu.-menu_e.-v {
  border: 0 none;
  background: transparent;
  -webkit-filter: none;
  filter: none;
}

.c-menu.-menu_e.-v li {
  border: none;
}

.c-menu.-menu_e.-v li a {
  border: none;
  background: #d1d1d1;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff',endColorstr='#d1d1d1',GradientType=0);
  background: -ms-linear-gradient(top, #fff 0, #ebe8eb 45%, #dedade 48%, #d1d1d1);
  background: -webkit-gradient(linear, left top, left bottom, from(#fff), color-stop(0.45, #ebe8eb), color-stop(0.48, #dedade), to(#d1d1d1));
}

.a-header,
.a-footer,
.a-billboard,
.a-site_contents,
.a-ghost_header {
  margin: 0 auto;
}

.a-header:before,
.a-header:after,
.a-footer:before,
.a-footer:after,
.a-billboard:before,
.a-billboard:after,
.a-site_contents:before,
.a-site_contents:after,
.a-ghost_header:before,
.a-ghost_header:after {
  display: table;
  line-height: 0;
  content: '';
}

.a-header:after,
.a-footer:after,
.a-billboard:after,
.a-site_contents:after,
.a-ghost_header:after {
  clear: both;
}

@media only screen and (max-width: 640px) {
  .a-header {
    padding: 0;
  }
}

@media only screen and (max-width: 768px) {
  .a-ghost_header {
    display: none;
  }
}

@media only screen and (max-width: 640px) {
  .a-main {
    float: none;
    width: 100%;
  }
}

.a-side-a {
  float: right;
}

@media only screen and (max-width: 640px) {
  .a-side-a {
    float: none;
    width: 100%;
  }
}

.a-footer {
  position: relative;
}

.l-2 .g-column > .a-main {
  float: left;
}

.l-2 .g-column > .a-side-a {
  float: right;
}

@media only screen and (max-width: 640px) {
  .l-2 .g-column .a-main,
  .l-2 .g-column .a-side-a {
    float: none;
    width: 100%;
  }
}

.l-3 .g-column > .a-main {
  float: right;
}

.l-3 .g-column > .a-side-a {
  float: left;
}

@media only screen and (max-width: 640px) {
  .l-3 .g-column .a-main,
  .l-3 .g-column .a-side-a {
    float: none;
    width: 100%;
  }
}

.l-4 .g-column > .a-main {
  float: right;
}

.l-4 .g-column > .a-side-a {
  float: left;
}

@media only screen and (max-width: 640px) {
  .l-4 .g-column .a-main,
  .l-4 .g-column .a-side-a {
    float: none;
    width: 100%;
  }
}

.l-5 .g-column > .a-main {
  float: left;
}

.l-5 .g-column > .a-side-a {
  float: left;
}

.l-5 .g-column > .a-side-b {
  float: right;
}

@media only screen and (max-width: 640px) {
  .l-5 .g-column .a-main,
  .l-5 .g-column .a-side-a,
  .l-5 .g-column .a-side-b {
    float: none;
    width: 100%;
  }
}

.l-6 .g-column > .a-main {
  float: left;
}

.l-6 .g-column > .a-side-a {
  float: right;
}

@media only screen and (max-width: 640px) {
  .l-6 .g-column .a-main,
  .l-6 .g-column .a-side-a {
    float: none;
    width: 100%;
  }
}

.l-7 .a-header,
.l-7 .a-billboard,
.l-7 .a-site_contents,
.l-7 .a-footer,
.l-7 .a-ghost_header {
  margin: 0;
}

.l-7 .g-column > .a-main {
  float: right;
}

.l-7 .g-column > .a-side-a {
  float: left;
}

@media only screen and (max-width: 640px) {
  .l-7 .g-column .a-main,
  .l-7 .g-column .a-side-a {
    float: none;
    width: 100%;
  }
}

.l-8 .a-header,
.l-8 .a-billboard,
.l-8 .a-site_contents,
.l-8 .a-footer,
.l-8 .a-ghost_header {
  margin: 0;
}

.l-8 .g-column > .a-main {
  float: left;
}

.l-8 .g-column > .a-side-a {
  float: right;
}

@media only screen and (max-width: 640px) {
  .l-8 .g-column .a-main,
  .l-8 .g-column .a-side-a {
    float: none;
    width: 100%;
  }
}

.l-fixed-side .site_frame > .g-column {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.l-fixed-side .site_frame > .g-column > .a-main {
  float: none;
  width: 100%;
}

.l-fixed-side .site_frame > .g-column > .a-side-a,
.l-fixed-side .site_frame > .g-column > .a-side-b {
  float: none;
  width: auto;
}

.l-fixed-side .site_frame > .g-column .a-main {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-box-ordinal-group: 3;
  -ms-flex-order: 2;
  order: 2;
}

.l-fixed-side .site_frame > .g-column .a-side-a {
  -webkit-box-ordinal-group: 2;
  -ms-flex-order: 1;
  order: 1;
}

.l-fixed-side .site_frame > .g-column .a-side-b {
  -webkit-box-ordinal-group: 4;
  -ms-flex-order: 3;
  order: 3;
}

@media only screen and (max-width: 640px) {
  .l-fixed-side .site_frame > .g-column {
    display: block;
  }

  .l-fixed-side .site_frame > .g-column .a-main {
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none;
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }

  .l-fixed-side .site_frame > .g-column .a-side-a {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }

  .l-fixed-side .site_frame > .g-column .a-side-b {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }
}

.l-fixed-side.l-2 .site_frame > .g-column .a-main,
.l-fixed-side.l-8 .site_frame > .g-column .a-main {
  -webkit-box-ordinal-group: 2;
  -ms-flex-order: 1;
  order: 1;
}

.l-fixed-side.l-2 .site_frame > .g-column .a-side-a,
.l-fixed-side.l-8 .site_frame > .g-column .a-side-a {
  -webkit-box-ordinal-group: 3;
  -ms-flex-order: 2;
  order: 2;
}

@media only screen and (max-width: 640px) {
  .l-fixed-side.l-2 .site_frame > .g-column,
  .l-fixed-side.l-8 .site_frame > .g-column {
    display: block;
  }

  .l-fixed-side.l-2 .site_frame > .g-column .a-main,
  .l-fixed-side.l-8 .site_frame > .g-column .a-main {
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none;
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }

  .l-fixed-side.l-2 .site_frame > .g-column .a-side-a,
  .l-fixed-side.l-8 .site_frame > .g-column .a-side-a {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }

  .l-fixed-side.l-2 .site_frame > .g-column .a-side-b,
  .l-fixed-side.l-8 .site_frame > .g-column .a-side-b {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0;
  }
}

.l-1 .bg-document,
.l-2 .bg-document,
.l-3 .bg-document,
.l-4 .bg-document,
.l-5 .bg-document,
.l-6 .bg-document {
  margin: 0 auto;
}

.display-none {
  display: none;
}

.-bg-tiling {
  background-repeat: repeat;
}

.-bg-left_top {
  background-position: left top;
  background-repeat: no-repeat;
}

.-bg-left_center {
  background-position: left center;
  background-repeat: no-repeat;
}

.-bg-left_bottom {
  background-position: left bottom;
  background-repeat: no-repeat;
}

.-bg-center_top {
  background-position: center top;
  background-repeat: no-repeat;
}

.-bg-center_center {
  background-position: center center;
  background-repeat: no-repeat;
}

.-bg-center_bottom {
  background-position: center bottom;
  background-repeat: no-repeat;
}

.-bg-right_top {
  background-position: right top;
  background-repeat: no-repeat;
}

.-bg-right_center {
  background-position: right center;
  background-repeat: no-repeat;
}

.-bg-right_bottom {
  background-position: right bottom;
  background-repeat: no-repeat;
}

.-bg-left_vrepeat {
  background-position: left top;
  background-repeat: repeat-y;
}

.-bg-center_vrepeat {
  background-position: center top;
  background-repeat: repeat-y;
}

.-bg-right_vrepeat {
  background-position: right top;
  background-repeat: repeat-y;
}

.-bg-top_hrepeat {
  background-position: left top;
  background-repeat: repeat-x;
}

.-bg-center_hrepeat {
  background-position: left center;
  background-repeat: repeat-x;
}

.-bg-bottom_hrepeat {
  background-position: left bottom;
  background-repeat: repeat-x;
}

.g-column:before,
.g-column:after {
  display: table;
  line-height: 0;
  content: '';
}

.g-column:after {
  clear: both;
}

.g-column > .column {
  float: left;
  position: relative;
}

.g-column > .column.-column1,
.g-column > .column:first-child {
  margin-left: 0;
}

.g-column > .column.-column-lasts,
.g-column > .column:last-child {
  margin-right: 0;
}

.g-column > .column.-column1.-column-lasts {
  float: none;
}

.g-column.-col1 > .column {
  float: none;
}

.g-column.-col2 > .column {
  width: 50%;
}

.g-column.-col3 > .column {
  width: 33.333%;
}

.g-column.-col4 > .column {
  width: 25%;
}

.g-column.-col5 > .column {
  width: 20%;
}

.g-column.-col6 > .column {
  width: 16.666%;
}

.g-column.-col7 > .column {
  width: 14.285%;
}

.g-column.-col8 > .column {
  width: 12.5%;
}

.g-column > .-col1 {
  width: 8.333%;
}

.g-column > .-col2 {
  width: 16.666%;
}

.g-column > .-col3 {
  width: 25%;
}

.g-column > .-col4 {
  width: 33.333%;
}

.g-column > .-col5 {
  width: 41.666%;
}

.g-column > .-col6 {
  width: 50%;
}

.g-column > .-col7 {
  width: 58.333%;
}

.g-column > .-col8 {
  width: 66.666%;
}

.g-column > .-col9 {
  width: 75%;
}

.g-column > .-col10 {
  width: 83.333%;
}

.g-column > .-col11 {
  width: 91.666%;
}

.g-column > .-col12 {
  margin-left: 0;
  width: 100%;
}

@media only screen and (max-width: 640px) {
  .g-column > .column {
    float: none;
    margin: 0;
  }

  .g-column > .column.-column1,
  .g-column > .column:first-child {
    margin-left: 0;
  }

  .g-column > .column.-column-lasts,
  .g-column > .column:last-child {
    margin-right: 0;
  }

  .g-column.-col2 > .column,
  .g-column.-col3 > .column,
  .g-column.-col4 > .column,
  .g-column.-col5 > .column,
  .g-column.-col6 > .column,
  .g-column.-col7 > .column,
  .g-column.-col8 > .column {
    width: 100%;
    margin: 0;
  }

  .g-column > .-col1,
  .g-column > .-col2,
  .g-column > .-col3,
  .g-column > .-col4,
  .g-column > .-col5,
  .g-column > .-col6,
  .g-column > .-col7,
  .g-column > .-col8,
  .g-column > .-col9,
  .g-column > .-col10,
  .g-column > .-col11,
  .g-column > .-col12 {
    width: 100%;
    margin: 0;
  }

  .g-column.-no_spacing.-col2 > .column,
  .g-column.-no_spacing.-col3 > .column,
  .g-column.-no_spacing.-col4 > .column,
  .g-column.-no_spacing.-col5 > .column,
  .g-column.-no_spacing.-col6 > .column,
  .g-column.-no_spacing.-col7 > .column,
  .g-column.-no_spacing.-col8 > .column {
    float: none;
    width: 100%;
  }

  .g-column.-sp-col2 > .column,
  .g-column.-sp-col2.-no_spacing > .column,
  .g-column.-sp-col3 > .column,
  .g-column.-sp-col3.-no_spacing > .column {
    float: left;
    margin: 0;
  }

  .g-column.-sp-col2 > .column,
  .g-column.-sp-col2.-no_spacing > .column {
    width: 50%;
  }

  .g-column.-sp-col3 > .column,
  .g-column.-sp-col3.-no_spacing > .column {
    width: 33.3%;
  }
}

.c-space_narrow .g-column > .column {
  margin-left: 1%;
  margin-right: 1%;
  margin-top: 2%;
}

.c-space_narrow .g-column > .column.-column1,
.c-space_narrow .g-column > .column:first-child {
  margin-left: 0;
}

.c-space_narrow .g-column > .column.-column-lasts,
.c-space_narrow .g-column > .column:last-child {
  margin-right: 0;
}

.c-space_narrow .g-column.-col2 > .column {
  width: 49%;
}

.c-space_narrow .g-column.-col2 > .column:nth-child(1),
.c-space_narrow .g-column.-col2 > .column:nth-child(2) {
  margin-top: 0;
}

.c-space_narrow .g-column.-col3 > .column {
  width: 32%;
}

.c-space_narrow .g-column.-col3 > .column:nth-child(1),
.c-space_narrow .g-column.-col3 > .column:nth-child(2),
.c-space_narrow .g-column.-col3 > .column:nth-child(3) {
  margin-top: 0;
}

.c-space_narrow .g-column.-col4 > .column {
  width: 23.5%;
}

.c-space_narrow .g-column.-col4 > .column:nth-child(1),
.c-space_narrow .g-column.-col4 > .column:nth-child(2),
.c-space_narrow .g-column.-col4 > .column:nth-child(3),
.c-space_narrow .g-column.-col4 > .column:nth-child(4) {
  margin-top: 0;
}

.c-space_narrow .g-column.-col5 > .column {
  width: 18.4%;
}

.c-space_narrow .g-column.-col5 > .column:nth-child(1),
.c-space_narrow .g-column.-col5 > .column:nth-child(2),
.c-space_narrow .g-column.-col5 > .column:nth-child(3),
.c-space_narrow .g-column.-col5 > .column:nth-child(4),
.c-space_narrow .g-column.-col5 > .column:nth-child(5) {
  margin-top: 0;
}

.c-space_narrow .g-column.-col6 > .column {
  width: 15%;
}

.c-space_narrow .g-column.-col6 > .column:nth-child(1),
.c-space_narrow .g-column.-col6 > .column:nth-child(2),
.c-space_narrow .g-column.-col6 > .column:nth-child(3),
.c-space_narrow .g-column.-col6 > .column:nth-child(4),
.c-space_narrow .g-column.-col6 > .column:nth-child(5),
.c-space_narrow .g-column.-col6 > .column:nth-child(6) {
  margin-top: 0;
}

.c-space_narrow .g-column.-col7 > .column {
  width: 12.571%;
}

.c-space_narrow .g-column.-col7 > .column:nth-child(1),
.c-space_narrow .g-column.-col7 > .column:nth-child(2),
.c-space_narrow .g-column.-col7 > .column:nth-child(3),
.c-space_narrow .g-column.-col7 > .column:nth-child(4),
.c-space_narrow .g-column.-col7 > .column:nth-child(5),
.c-space_narrow .g-column.-col7 > .column:nth-child(6),
.c-space_narrow .g-column.-col7 > .column:nth-child(7) {
  margin-top: 0;
}

.c-space_narrow .g-column.-col8 > .column {
  width: 10.75%;
}

.c-space_narrow .g-column.-col8 > .column:nth-child(1),
.c-space_narrow .g-column.-col8 > .column:nth-child(2),
.c-space_narrow .g-column.-col8 > .column:nth-child(3),
.c-space_narrow .g-column.-col8 > .column:nth-child(4),
.c-space_narrow .g-column.-col8 > .column:nth-child(5),
.c-space_narrow .g-column.-col8 > .column:nth-child(6),
.c-space_narrow .g-column.-col8 > .column:nth-child(7),
.c-space_narrow .g-column.-col8 > .column:nth-child(8) {
  margin-top: 0;
}

.c-space_narrow .g-column > .-col1 {
  width: 7.333%;
}

.c-space_narrow .g-column > .-col2 {
  width: 15.666%;
}

.c-space_narrow .g-column > .-col3 {
  width: 24%;
}

.c-space_narrow .g-column > .-col4 {
  width: 32.333%;
}

.c-space_narrow .g-column > .-col5 {
  width: 40.666%;
}

.c-space_narrow .g-column > .-col6 {
  width: 49%;
}

.c-space_narrow .g-column > .-col7 {
  width: 57.333%;
}

.c-space_narrow .g-column > .-col8 {
  width: 65.666%;
}

.c-space_narrow .g-column > .-col9 {
  width: 74%;
}

.c-space_narrow .g-column > .-col10 {
  width: 82.333%;
}

.c-space_narrow .g-column > .-col11 {
  width: 90.666%;
}

.c-space_narrow .g-column > .-col12 {
  margin-left: 0;
  width: 100%;
}

.c-space_narrow .g-column > .-col1:nth-child(1),
.c-space_narrow .g-column > .-col1:nth-child(2),
.c-space_narrow .g-column > .-col2:nth-child(1),
.c-space_narrow .g-column > .-col2:nth-child(2),
.c-space_narrow .g-column > .-col3:nth-child(1),
.c-space_narrow .g-column > .-col3:nth-child(2),
.c-space_narrow .g-column > .-col4:nth-child(1),
.c-space_narrow .g-column > .-col4:nth-child(2),
.c-space_narrow .g-column > .-col5:nth-child(1),
.c-space_narrow .g-column > .-col5:nth-child(2),
.c-space_narrow .g-column > .-col6:nth-child(1),
.c-space_narrow .g-column > .-col6:nth-child(2),
.c-space_narrow .g-column > .-col7:nth-child(1),
.c-space_narrow .g-column > .-col7:nth-child(2),
.c-space_narrow .g-column > .-col8:nth-child(1),
.c-space_narrow .g-column > .-col8:nth-child(2),
.c-space_narrow .g-column > .-col9:nth-child(1),
.c-space_narrow .g-column > .-col9:nth-child(2),
.c-space_narrow .g-column > .-col10:nth-child(1),
.c-space_narrow .g-column > .-col10:nth-child(2),
.c-space_narrow .g-column > .-col11:nth-child(1),
.c-space_narrow .g-column > .-col11:nth-child(2) {
  margin-top: 0;
}

.l-5 .c-space_narrow .g-column > .-col1 {
  width: 8%;
}

.l-5 .c-space_narrow .g-column > .-col2 {
  width: 16%;
}

.l-5 .c-space_narrow .g-column > .-col3 {
  width: 24%;
}

.l-5 .c-space_narrow .g-column > .-col4 {
  width: 32%;
}

.l-5 .c-space_narrow .g-column > .-col5 {
  width: 40%;
}

.l-5 .c-space_narrow .g-column > .-col6 {
  width: 48%;
}

.l-5 .c-space_narrow .g-column > .-col7 {
  width: 56%;
}

.l-5 .c-space_narrow .g-column > .-col8 {
  width: 64%;
}

.l-5 .c-space_narrow .g-column > .-col9 {
  width: 72%;
}

.l-5 .c-space_narrow .g-column > .-col10 {
  width: 80%;
}

.l-5 .c-space_narrow .g-column > .-col11 {
  width: 88%;
}

.l-5 .c-space_narrow .g-column > .-col12 {
  margin-left: 0;
  margin-right: 0;
  width: 100%;
}

.c-space_normal .g-column > .column {
  margin-left: 2%;
  margin-right: 2%;
  margin-top: 4%;
}

.c-space_normal .g-column > .column.-column1,
.c-space_normal .g-column > .column:first-child {
  margin-left: 0;
}

.c-space_normal .g-column > .column.-column-lasts,
.c-space_normal .g-column > .column:last-child {
  margin-right: 0;
}

.c-space_normal .g-column.-col2 > .column {
  width: 48%;
}

.c-space_normal .g-column.-col2 > .column:nth-child(1),
.c-space_normal .g-column.-col2 > .column:nth-child(2) {
  margin-top: 0;
}

.c-space_normal .g-column.-col3 > .column {
  width: 30.666%;
}

.c-space_normal .g-column.-col3 > .column:nth-child(1),
.c-space_normal .g-column.-col3 > .column:nth-child(2),
.c-space_normal .g-column.-col3 > .column:nth-child(3) {
  margin-top: 0;
}

.c-space_normal .g-column.-col4 > .column {
  width: 22%;
}

.c-space_normal .g-column.-col4 > .column:nth-child(1),
.c-space_normal .g-column.-col4 > .column:nth-child(2),
.c-space_normal .g-column.-col4 > .column:nth-child(3),
.c-space_normal .g-column.-col4 > .column:nth-child(4) {
  margin-top: 0;
}

.c-space_normal .g-column.-col5 > .column {
  width: 16.8%;
}

.c-space_normal .g-column.-col5 > .column:nth-child(1),
.c-space_normal .g-column.-col5 > .column:nth-child(2),
.c-space_normal .g-column.-col5 > .column:nth-child(3),
.c-space_normal .g-column.-col5 > .column:nth-child(4),
.c-space_normal .g-column.-col5 > .column:nth-child(5) {
  margin-top: 0;
}

.c-space_normal .g-column.-col6 > .column {
  width: 13.333%;
}

.c-space_normal .g-column.-col6 > .column:nth-child(1),
.c-space_normal .g-column.-col6 > .column:nth-child(2),
.c-space_normal .g-column.-col6 > .column:nth-child(3),
.c-space_normal .g-column.-col6 > .column:nth-child(4),
.c-space_normal .g-column.-col6 > .column:nth-child(5),
.c-space_normal .g-column.-col6 > .column:nth-child(6) {
  margin-top: 0;
}

.c-space_normal .g-column.-col7 > .column {
  width: 10.857%;
}

.c-space_normal .g-column.-col7 > .column:nth-child(1),
.c-space_normal .g-column.-col7 > .column:nth-child(2),
.c-space_normal .g-column.-col7 > .column:nth-child(3),
.c-space_normal .g-column.-col7 > .column:nth-child(4),
.c-space_normal .g-column.-col7 > .column:nth-child(5),
.c-space_normal .g-column.-col7 > .column:nth-child(6),
.c-space_normal .g-column.-col7 > .column:nth-child(7) {
  margin-top: 0;
}

.c-space_normal .g-column.-col8 > .column {
  width: 9%;
}

.c-space_normal .g-column.-col8 > .column:nth-child(1),
.c-space_normal .g-column.-col8 > .column:nth-child(2),
.c-space_normal .g-column.-col8 > .column:nth-child(3),
.c-space_normal .g-column.-col8 > .column:nth-child(4),
.c-space_normal .g-column.-col8 > .column:nth-child(5),
.c-space_normal .g-column.-col8 > .column:nth-child(6),
.c-space_normal .g-column.-col8 > .column:nth-child(7),
.c-space_normal .g-column.-col8 > .column:nth-child(8) {
  margin-top: 0;
}

.c-space_normal .g-column > .-col1 {
  width: 6.3%;
}

.c-space_normal .g-column > .-col2 {
  width: 14.6%;
}

.c-space_normal .g-column > .-col3 {
  width: 23%;
}

.c-space_normal .g-column > .-col4 {
  width: 31.3%;
}

.c-space_normal .g-column > .-col5 {
  width: 39.6%;
}

.c-space_normal .g-column > .-col6 {
  width: 48%;
}

.c-space_normal .g-column > .-col7 {
  width: 56.3%;
}

.c-space_normal .g-column > .-col8 {
  width: 64.6%;
}

.c-space_normal .g-column > .-col9 {
  width: 73%;
}

.c-space_normal .g-column > .-col10 {
  width: 81.3%;
}

.c-space_normal .g-column > .-col11 {
  width: 89.6%;
}

.c-space_normal .g-column > .-col12 {
  margin-left: 0;
  margin-right: 0;
  width: 100%;
}

.c-space_normal .g-column > .-col1:nth-child(1),
.c-space_normal .g-column > .-col1:nth-child(2),
.c-space_normal .g-column > .-col2:nth-child(1),
.c-space_normal .g-column > .-col2:nth-child(2),
.c-space_normal .g-column > .-col3:nth-child(1),
.c-space_normal .g-column > .-col3:nth-child(2),
.c-space_normal .g-column > .-col4:nth-child(1),
.c-space_normal .g-column > .-col4:nth-child(2),
.c-space_normal .g-column > .-col5:nth-child(1),
.c-space_normal .g-column > .-col5:nth-child(2),
.c-space_normal .g-column > .-col6:nth-child(1),
.c-space_normal .g-column > .-col6:nth-child(2),
.c-space_normal .g-column > .-col7:nth-child(1),
.c-space_normal .g-column > .-col7:nth-child(2),
.c-space_normal .g-column > .-col8:nth-child(1),
.c-space_normal .g-column > .-col8:nth-child(2),
.c-space_normal .g-column > .-col9:nth-child(1),
.c-space_normal .g-column > .-col9:nth-child(2),
.c-space_normal .g-column > .-col10:nth-child(1),
.c-space_normal .g-column > .-col10:nth-child(2),
.c-space_normal .g-column > .-col11:nth-child(1),
.c-space_normal .g-column > .-col11:nth-child(2) {
  margin-top: 0;
}

.l-5 .c-space_normal .g-column > .-col1 {
  width: 7.6%;
}

.l-5 .c-space_normal .g-column > .-col2 {
  width: 15.3%;
}

.l-5 .c-space_normal .g-column > .-col3 {
  width: 23%;
}

.l-5 .c-space_normal .g-column > .-col4 {
  width: 30.6%;
}

.l-5 .c-space_normal .g-column > .-col5 {
  width: 38.3%;
}

.l-5 .c-space_normal .g-column > .-col6 {
  width: 46%;
}

.l-5 .c-space_normal .g-column > .-col7 {
  width: 53.6%;
}

.l-5 .c-space_normal .g-column > .-col8 {
  width: 61.3%;
}

.l-5 .c-space_normal .g-column > .-col9 {
  width: 69%;
}

.l-5 .c-space_normal .g-column > .-col10 {
  width: 76.6%;
}

.l-5 .c-space_normal .g-column > .-col11 {
  width: 84.3%;
}

.l-5 .c-space_normal .g-column > .-col12 {
  margin-left: 0;
  margin-right: 0;
  width: 100%;
}

.c-space_wide .g-column > .column {
  margin-left: 3%;
  margin-right: 3%;
  margin-top: 6%;
}

.c-space_wide .g-column > .column.-column1,
.c-space_wide .g-column > .column:first-child {
  margin-left: 0;
}

.c-space_wide .g-column > .column.-column-lasts,
.c-space_wide .g-column > .column:last-child {
  margin-right: 0;
}

.c-space_wide .g-column.-col2 > .column {
  width: 47%;
}

.c-space_wide .g-column.-col2 > .column:nth-child(1),
.c-space_wide .g-column.-col2 > .column:nth-child(2) {
  margin-top: 0;
}

.c-space_wide .g-column.-col3 > .column {
  width: 29.333%;
}

.c-space_wide .g-column.-col3 > .column:nth-child(1),
.c-space_wide .g-column.-col3 > .column:nth-child(2),
.c-space_wide .g-column.-col3 > .column:nth-child(3) {
  margin-top: 0;
}

.c-space_wide .g-column.-col4 > .column {
  width: 20.5%;
}

.c-space_wide .g-column.-col4 > .column:nth-child(1),
.c-space_wide .g-column.-col4 > .column:nth-child(2),
.c-space_wide .g-column.-col4 > .column:nth-child(3),
.c-space_wide .g-column.-col4 > .column:nth-child(4) {
  margin-top: 0;
}

.c-space_wide .g-column.-col5 > .column {
  width: 15.2%;
}

.c-space_wide .g-column.-col5 > .column:nth-child(1),
.c-space_wide .g-column.-col5 > .column:nth-child(2),
.c-space_wide .g-column.-col5 > .column:nth-child(3),
.c-space_wide .g-column.-col5 > .column:nth-child(4),
.c-space_wide .g-column.-col5 > .column:nth-child(5) {
  margin-top: 0;
}

.c-space_wide .g-column.-col6 > .column {
  width: 11.666%;
}

.c-space_wide .g-column.-col6 > .column:nth-child(1),
.c-space_wide .g-column.-col6 > .column:nth-child(2),
.c-space_wide .g-column.-col6 > .column:nth-child(3),
.c-space_wide .g-column.-col6 > .column:nth-child(4),
.c-space_wide .g-column.-col6 > .column:nth-child(5),
.c-space_wide .g-column.-col6 > .column:nth-child(6) {
  margin-top: 0;
}

.c-space_wide .g-column.-col7 > .column {
  width: 9.142%;
}

.c-space_wide .g-column.-col7 > .column:nth-child(1),
.c-space_wide .g-column.-col7 > .column:nth-child(2),
.c-space_wide .g-column.-col7 > .column:nth-child(3),
.c-space_wide .g-column.-col7 > .column:nth-child(4),
.c-space_wide .g-column.-col7 > .column:nth-child(5),
.c-space_wide .g-column.-col7 > .column:nth-child(6),
.c-space_wide .g-column.-col7 > .column:nth-child(7) {
  margin-top: 0;
}

.c-space_wide .g-column.-col8 > .column {
  width: 7.25%;
}

.c-space_wide .g-column.-col8 > .column:nth-child(1),
.c-space_wide .g-column.-col8 > .column:nth-child(2),
.c-space_wide .g-column.-col8 > .column:nth-child(3),
.c-space_wide .g-column.-col8 > .column:nth-child(4),
.c-space_wide .g-column.-col8 > .column:nth-child(5),
.c-space_wide .g-column.-col8 > .column:nth-child(6),
.c-space_wide .g-column.-col8 > .column:nth-child(7),
.c-space_wide .g-column.-col8 > .column:nth-child(8) {
  margin-top: 0;
}

.c-space_wide .g-column > .-col1 {
  width: 5.333%;
}

.c-space_wide .g-column > .-col2 {
  width: 13.666%;
}

.c-space_wide .g-column > .-col3 {
  width: 22%;
}

.c-space_wide .g-column > .-col4 {
  width: 30.333%;
}

.c-space_wide .g-column > .-col5 {
  width: 38.666%;
}

.c-space_wide .g-column > .-col6 {
  width: 47%;
}

.c-space_wide .g-column > .-col7 {
  width: 55.333%;
}

.c-space_wide .g-column > .-col8 {
  width: 63.666%;
}

.c-space_wide .g-column > .-col9 {
  width: 72%;
}

.c-space_wide .g-column > .-col10 {
  width: 80.333%;
}

.c-space_wide .g-column > .-col11 {
  width: 88.666%;
}

.c-space_wide .g-column > .-col12 {
  margin-left: 0;
  margin-right: 0;
  width: 100%;
}

.c-space_wide .g-column > .-col1:nth-child(1),
.c-space_wide .g-column > .-col1:nth-child(2),
.c-space_wide .g-column > .-col2:nth-child(1),
.c-space_wide .g-column > .-col2:nth-child(2),
.c-space_wide .g-column > .-col3:nth-child(1),
.c-space_wide .g-column > .-col3:nth-child(2),
.c-space_wide .g-column > .-col4:nth-child(1),
.c-space_wide .g-column > .-col4:nth-child(2),
.c-space_wide .g-column > .-col5:nth-child(1),
.c-space_wide .g-column > .-col5:nth-child(2),
.c-space_wide .g-column > .-col6:nth-child(1),
.c-space_wide .g-column > .-col6:nth-child(2),
.c-space_wide .g-column > .-col7:nth-child(1),
.c-space_wide .g-column > .-col7:nth-child(2),
.c-space_wide .g-column > .-col8:nth-child(1),
.c-space_wide .g-column > .-col8:nth-child(2),
.c-space_wide .g-column > .-col9:nth-child(1),
.c-space_wide .g-column > .-col9:nth-child(2),
.c-space_wide .g-column > .-col10:nth-child(1),
.c-space_wide .g-column > .-col10:nth-child(2),
.c-space_wide .g-column > .-col11:nth-child(1),
.c-space_wide .g-column > .-col11:nth-child(2) {
  margin-top: 0;
}

.l-5 .c-space_wide .g-column > .-col1 {
  width: 7.333%;
}

.l-5 .c-space_wide .g-column > .-col2 {
  width: 14.666%;
}

.l-5 .c-space_wide .g-column > .-col3 {
  width: 22%;
}

.l-5 .c-space_wide .g-column > .-col4 {
  width: 29.333%;
}

.l-5 .c-space_wide .g-column > .-col5 {
  width: 36.666%;
}

.l-5 .c-space_wide .g-column > .-col6 {
  width: 44%;
}

.l-5 .c-space_wide .g-column > .-col7 {
  width: 51.333%;
}

.l-5 .c-space_wide .g-column > .-col8 {
  width: 58.666%;
}

.l-5 .c-space_wide .g-column > .-col9 {
  width: 66%;
}

.l-5 .c-space_wide .g-column > .-col10 {
  width: 73.333%;
}

.l-5 .c-space_wide .g-column > .-col11 {
  width: 80.666%;
}

.l-5 .c-space_wide .g-column > .-col12 {
  margin-left: 0;
  margin-right: 0;
  width: 100%;
}

@media only screen and (max-width: 640px) {
  .c-sp-space_init .g-column > .column {
    float: none;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
  }

  .c-sp-space_init .g-column.-col2 > .column,
  .c-sp-space_init .g-column.-col3 > .column,
  .c-sp-space_init .g-column.-col4 > .column,
  .c-sp-space_init .g-column.-col5 > .column,
  .c-sp-space_init .g-column.-col6 > .column,
  .c-sp-space_init .g-column.-col7 > .column,
  .c-sp-space_init .g-column.-col8 > .column {
    width: 100%;
  }

  .c-sp-space_init .g-column > .-col1,
  .c-sp-space_init .g-column > .-col2,
  .c-sp-space_init .g-column > .-col3,
  .c-sp-space_init .g-column > .-col4,
  .c-sp-space_init .g-column > .-col5,
  .c-sp-space_init .g-column > .-col6,
  .c-sp-space_init .g-column > .-col7,
  .c-sp-space_init .g-column > .-col8,
  .c-sp-space_init .g-column > .-col9,
  .c-sp-space_init .g-column > .-col10,
  .c-sp-space_init .g-column > .-col11,
  .c-sp-space_init .g-column > .-col12 {
    width: 100%;
  }

  .c-sp-space_init .g-column.-no_spacing.-col2 > .column,
  .c-sp-space_init .g-column.-no_spacing.-col3 > .column,
  .c-sp-space_init .g-column.-no_spacing.-col4 > .column,
  .c-sp-space_init .g-column.-no_spacing.-col5 > .column,
  .c-sp-space_init .g-column.-no_spacing.-col6 > .column,
  .c-sp-space_init .g-column.-no_spacing.-col7 > .column,
  .c-sp-space_init .g-column.-no_spacing.-col8 > .column {
    float: none;
    width: 100%;
  }

  .c-sp-space_init .g-column.-sp-col2 > .column,
  .c-sp-space_init .g-column.-sp-col2.-no_spacing > .column,
  .c-sp-space_init .g-column.-sp-col3 > .column,
  .c-sp-space_init .g-column.-sp-col3.-no_spacing > .column {
    float: left;
    margin-left: 0;
    margin-right: 0;
  }

  .c-sp-space_init .g-column.-sp-col2 > .column,
  .c-sp-space_init .g-column.-sp-col2.-no_spacing > .column {
    width: 50%;
  }

  .c-sp-space_init .g-column.-sp-col3 > .column,
  .c-sp-space_init .g-column.-sp-col3.-no_spacing > .column {
    width: 33.3%;
  }

  .l-5 .c-sp-space_init .g-column > .column {
    float: none;
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
  }

  .l-5 .c-sp-space_init .g-column.-col2 > .column,
  .l-5 .c-sp-space_init .g-column.-col3 > .column,
  .l-5 .c-sp-space_init .g-column.-col4 > .column,
  .l-5 .c-sp-space_init .g-column.-col5 > .column,
  .l-5 .c-sp-space_init .g-column.-col6 > .column,
  .l-5 .c-sp-space_init .g-column.-col7 > .column,
  .l-5 .c-sp-space_init .g-column.-col8 > .column {
    width: 100%;
  }

  .l-5 .c-sp-space_init .g-column > .-col1,
  .l-5 .c-sp-space_init .g-column > .-col2,
  .l-5 .c-sp-space_init .g-column > .-col3,
  .l-5 .c-sp-space_init .g-column > .-col4,
  .l-5 .c-sp-space_init .g-column > .-col5,
  .l-5 .c-sp-space_init .g-column > .-col6,
  .l-5 .c-sp-space_init .g-column > .-col7,
  .l-5 .c-sp-space_init .g-column > .-col8,
  .l-5 .c-sp-space_init .g-column > .-col9,
  .l-5 .c-sp-space_init .g-column > .-col10,
  .l-5 .c-sp-space_init .g-column > .-col11,
  .l-5 .c-sp-space_init .g-column > .-col12 {
    width: 100%;
  }

  .l-5 .c-sp-space_init .g-column.-no_spacing.-col2 > .column,
  .l-5 .c-sp-space_init .g-column.-no_spacing.-col3 > .column,
  .l-5 .c-sp-space_init .g-column.-no_spacing.-col4 > .column,
  .l-5 .c-sp-space_init .g-column.-no_spacing.-col5 > .column,
  .l-5 .c-sp-space_init .g-column.-no_spacing.-col6 > .column,
  .l-5 .c-sp-space_init .g-column.-no_spacing.-col7 > .column,
  .l-5 .c-sp-space_init .g-column.-no_spacing.-col8 > .column {
    float: none;
    width: 100%;
  }

  .l-5 .c-sp-space_init .g-column.-sp-col2 > .column,
  .l-5 .c-sp-space_init .g-column.-sp-col2.-no_spacing > .column,
  .l-5 .c-sp-space_init .g-column.-sp-col3 > .column,
  .l-5 .c-sp-space_init .g-column.-sp-col3.-no_spacing > .column {
    float: left;
    margin-left: 0;
    margin-right: 0;
  }

  .l-5 .c-sp-space_init .g-column.-sp-col2 > .column,
  .l-5 .c-sp-space_init .g-column.-sp-col2.-no_spacing > .column {
    width: 50%;
  }

  .l-5 .c-sp-space_init .g-column.-sp-col3 > .column,
  .l-5 .c-sp-space_init .g-column.-sp-col3.-no_spacing > .column {
    width: 33.3%;
  }

  .c-sp-space_narrow .g-column > .column {
    float: none;
    margin-left: 0;
    margin-right: 0;
    margin-top: 2%;
  }

  .c-sp-space_narrow .g-column.-col2 > .column,
  .c-sp-space_narrow .g-column.-col3 > .column,
  .c-sp-space_narrow .g-column.-col4 > .column,
  .c-sp-space_narrow .g-column.-col5 > .column,
  .c-sp-space_narrow .g-column.-col6 > .column,
  .c-sp-space_narrow .g-column.-col7 > .column,
  .c-sp-space_narrow .g-column.-col8 > .column {
    width: 100%;
  }

  .c-sp-space_narrow .g-column > .-col1,
  .c-sp-space_narrow .g-column > .-col2,
  .c-sp-space_narrow .g-column > .-col3,
  .c-sp-space_narrow .g-column > .-col4,
  .c-sp-space_narrow .g-column > .-col5,
  .c-sp-space_narrow .g-column > .-col6,
  .c-sp-space_narrow .g-column > .-col7,
  .c-sp-space_narrow .g-column > .-col8,
  .c-sp-space_narrow .g-column > .-col9,
  .c-sp-space_narrow .g-column > .-col10,
  .c-sp-space_narrow .g-column > .-col11,
  .c-sp-space_narrow .g-column > .-col12 {
    width: 100%;
  }

  .c-sp-space_narrow .g-column.-no_spacing.-col2 > .column,
  .c-sp-space_narrow .g-column.-no_spacing.-col3 > .column,
  .c-sp-space_narrow .g-column.-no_spacing.-col4 > .column,
  .c-sp-space_narrow .g-column.-no_spacing.-col5 > .column,
  .c-sp-space_narrow .g-column.-no_spacing.-col6 > .column,
  .c-sp-space_narrow .g-column.-no_spacing.-col7 > .column,
  .c-sp-space_narrow .g-column.-no_spacing.-col8 > .column {
    float: none;
    width: 100%;
  }

  .c-sp-space_narrow .g-column.-sp-col2 > .column,
  .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column,
  .c-sp-space_narrow .g-column.-sp-col3 > .column,
  .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column {
    float: left;
    margin-left: 1%;
    margin-right: 1%;
    margin-top: 2%;
  }

  .c-sp-space_narrow .g-column.-sp-col1 > .column:nth-child(n),
  .c-sp-space_narrow .g-column.-sp-col1.-no_spacing > .column:nth-child(n) {
    margin-top: 2%;
  }

  .c-sp-space_narrow .g-column.-sp-col2 > .column,
  .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column {
    width: 49%;
  }

  .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(2n + 1),
  .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(2n + 1) {
    margin-left: 0;
  }

  .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(2n),
  .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(2n) {
    margin-right: 0;
  }

  .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(1),
  .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(2),
  .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(1),
  .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(2) {
    margin-top: 0;
  }

  .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(3),
  .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(4),
  .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(5),
  .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(6),
  .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(7),
  .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(8),
  .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(3),
  .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(4),
  .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(5),
  .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(6),
  .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(7),
  .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(8) {
    margin-top: 2%;
  }

  .c-sp-space_narrow .g-column.-sp-col3 > .column,
  .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column {
    width: 32%;
  }

  .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(3n + 1),
  .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(3n + 1) {
    margin-left: 0;
  }

  .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(3n),
  .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(3n) {
    margin-right: 0;
  }

  .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(1),
  .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(2),
  .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(3),
  .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(1),
  .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(2),
  .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(3) {
    margin-top: 0;
  }

  .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(4),
  .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(5),
  .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(6),
  .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(7),
  .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(8),
  .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(4),
  .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(5),
  .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(6),
  .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(7),
  .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(8) {
    margin-top: 2%;
  }

  .l-5 .c-sp-space_narrow .g-column > .column {
    float: none;
    margin-left: 0;
    margin-right: 0;
    margin-top: 2%;
  }

  .l-5 .c-sp-space_narrow .g-column.-col2 > .column,
  .l-5 .c-sp-space_narrow .g-column.-col3 > .column,
  .l-5 .c-sp-space_narrow .g-column.-col4 > .column,
  .l-5 .c-sp-space_narrow .g-column.-col5 > .column,
  .l-5 .c-sp-space_narrow .g-column.-col6 > .column,
  .l-5 .c-sp-space_narrow .g-column.-col7 > .column,
  .l-5 .c-sp-space_narrow .g-column.-col8 > .column {
    width: 100%;
  }

  .l-5 .c-sp-space_narrow .g-column > .-col1,
  .l-5 .c-sp-space_narrow .g-column > .-col2,
  .l-5 .c-sp-space_narrow .g-column > .-col3,
  .l-5 .c-sp-space_narrow .g-column > .-col4,
  .l-5 .c-sp-space_narrow .g-column > .-col5,
  .l-5 .c-sp-space_narrow .g-column > .-col6,
  .l-5 .c-sp-space_narrow .g-column > .-col7,
  .l-5 .c-sp-space_narrow .g-column > .-col8,
  .l-5 .c-sp-space_narrow .g-column > .-col9,
  .l-5 .c-sp-space_narrow .g-column > .-col10,
  .l-5 .c-sp-space_narrow .g-column > .-col11,
  .l-5 .c-sp-space_narrow .g-column > .-col12 {
    width: 100%;
  }

  .l-5 .c-sp-space_narrow .g-column.-no_spacing.-col2 > .column,
  .l-5 .c-sp-space_narrow .g-column.-no_spacing.-col3 > .column,
  .l-5 .c-sp-space_narrow .g-column.-no_spacing.-col4 > .column,
  .l-5 .c-sp-space_narrow .g-column.-no_spacing.-col5 > .column,
  .l-5 .c-sp-space_narrow .g-column.-no_spacing.-col6 > .column,
  .l-5 .c-sp-space_narrow .g-column.-no_spacing.-col7 > .column,
  .l-5 .c-sp-space_narrow .g-column.-no_spacing.-col8 > .column {
    float: none;
    width: 100%;
  }

  .l-5 .c-sp-space_narrow .g-column.-sp-col2 > .column,
  .l-5 .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column,
  .l-5 .c-sp-space_narrow .g-column.-sp-col3 > .column,
  .l-5 .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column {
    float: left;
    margin-left: 1%;
    margin-right: 1%;
    margin-top: 2%;
  }

  .l-5 .c-sp-space_narrow .g-column.-sp-col1 > .column:nth-child(n),
  .l-5 .c-sp-space_narrow .g-column.-sp-col1.-no_spacing > .column:nth-child(n) {
    margin-top: 2%;
  }

  .l-5 .c-sp-space_narrow .g-column.-sp-col2 > .column,
  .l-5 .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column {
    width: 49%;
  }

  .l-5 .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(2n + 1),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(2n + 1) {
    margin-left: 0;
  }

  .l-5 .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(2n),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(2n) {
    margin-right: 0;
  }

  .l-5 .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(1),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(2),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(1),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(2) {
    margin-top: 0;
  }

  .l-5 .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(3),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(4),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(5),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(6),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(7),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2 > .column:nth-child(8),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(3),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(4),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(5),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(6),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(7),
  .l-5 .c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column:nth-child(8) {
    margin-top: 2%;
  }

  .l-5 .c-sp-space_narrow .g-column.-sp-col3 > .column,
  .l-5 .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column {
    width: 32%;
  }

  .l-5 .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(3n + 1),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(3n + 1) {
    margin-left: 0;
  }

  .l-5 .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(3n),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(3n) {
    margin-right: 0;
  }

  .l-5 .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(1),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(2),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(3),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(1),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(2),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(3) {
    margin-top: 0;
  }

  .l-5 .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(4),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(5),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(6),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(7),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3 > .column:nth-child(8),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(4),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(5),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(6),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(7),
  .l-5 .c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column:nth-child(8) {
    margin-top: 2%;
  }

  .c-sp-space_normal .g-column > .column {
    float: none;
    margin-left: 0;
    margin-right: 0;
    margin-top: 4%;
  }

  .c-sp-space_normal .g-column.-col2 > .column,
  .c-sp-space_normal .g-column.-col3 > .column,
  .c-sp-space_normal .g-column.-col4 > .column,
  .c-sp-space_normal .g-column.-col5 > .column,
  .c-sp-space_normal .g-column.-col6 > .column,
  .c-sp-space_normal .g-column.-col7 > .column,
  .c-sp-space_normal .g-column.-col8 > .column {
    width: 100%;
  }

  .c-sp-space_normal .g-column > .-col1,
  .c-sp-space_normal .g-column > .-col2,
  .c-sp-space_normal .g-column > .-col3,
  .c-sp-space_normal .g-column > .-col4,
  .c-sp-space_normal .g-column > .-col5,
  .c-sp-space_normal .g-column > .-col6,
  .c-sp-space_normal .g-column > .-col7,
  .c-sp-space_normal .g-column > .-col8,
  .c-sp-space_normal .g-column > .-col9,
  .c-sp-space_normal .g-column > .-col10,
  .c-sp-space_normal .g-column > .-col11,
  .c-sp-space_normal .g-column > .-col12 {
    width: 100%;
  }

  .c-sp-space_normal .g-column.-no_spacing.-col2 > .column,
  .c-sp-space_normal .g-column.-no_spacing.-col3 > .column,
  .c-sp-space_normal .g-column.-no_spacing.-col4 > .column,
  .c-sp-space_normal .g-column.-no_spacing.-col5 > .column,
  .c-sp-space_normal .g-column.-no_spacing.-col6 > .column,
  .c-sp-space_normal .g-column.-no_spacing.-col7 > .column,
  .c-sp-space_normal .g-column.-no_spacing.-col8 > .column {
    float: none;
    width: 100%;
  }

  .c-sp-space_normal .g-column.-sp-col2 > .column,
  .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column,
  .c-sp-space_normal .g-column.-sp-col3 > .column,
  .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column {
    float: left;
    margin-left: 2%;
    margin-right: 2%;
    margin-top: 4%;
  }

  .c-sp-space_normal .g-column.-sp-col1 > .column:nth-child(n),
  .c-sp-space_normal .g-column.-sp-col1.-no_spacing > .column:nth-child(n) {
    margin-top: 4%;
  }

  .c-sp-space_normal .g-column.-sp-col2 > .column,
  .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column {
    width: 48%;
  }

  .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(2n + 1),
  .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(2n + 1) {
    margin-left: 0;
  }

  .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(2n),
  .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(2n) {
    margin-right: 0;
  }

  .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(1),
  .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(2),
  .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(1),
  .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(2) {
    margin-top: 0;
  }

  .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(3),
  .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(4),
  .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(5),
  .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(6),
  .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(7),
  .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(8),
  .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(3),
  .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(4),
  .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(5),
  .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(6),
  .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(7),
  .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(8) {
    margin-top: 4%;
  }

  .c-sp-space_normal .g-column.-sp-col3 > .column,
  .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column {
    width: 30.666%;
  }

  .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(3n + 1),
  .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(3n + 1) {
    margin-left: 0;
  }

  .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(3n),
  .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(3n) {
    margin-right: 0;
  }

  .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(1),
  .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(2),
  .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(3),
  .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(1),
  .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(2),
  .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(3) {
    margin-top: 0;
  }

  .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(4),
  .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(5),
  .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(6),
  .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(7),
  .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(8),
  .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(4),
  .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(5),
  .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(6),
  .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(7),
  .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(8) {
    margin-top: 4%;
  }

  .l-5 .c-sp-space_normal .g-column > .column {
    float: none;
    margin-left: 0;
    margin-right: 0;
    margin-top: 4%;
  }

  .l-5 .c-sp-space_normal .g-column.-col2 > .column,
  .l-5 .c-sp-space_normal .g-column.-col3 > .column,
  .l-5 .c-sp-space_normal .g-column.-col4 > .column,
  .l-5 .c-sp-space_normal .g-column.-col5 > .column,
  .l-5 .c-sp-space_normal .g-column.-col6 > .column,
  .l-5 .c-sp-space_normal .g-column.-col7 > .column,
  .l-5 .c-sp-space_normal .g-column.-col8 > .column {
    width: 100%;
  }

  .l-5 .c-sp-space_normal .g-column > .-col1,
  .l-5 .c-sp-space_normal .g-column > .-col2,
  .l-5 .c-sp-space_normal .g-column > .-col3,
  .l-5 .c-sp-space_normal .g-column > .-col4,
  .l-5 .c-sp-space_normal .g-column > .-col5,
  .l-5 .c-sp-space_normal .g-column > .-col6,
  .l-5 .c-sp-space_normal .g-column > .-col7,
  .l-5 .c-sp-space_normal .g-column > .-col8,
  .l-5 .c-sp-space_normal .g-column > .-col9,
  .l-5 .c-sp-space_normal .g-column > .-col10,
  .l-5 .c-sp-space_normal .g-column > .-col11,
  .l-5 .c-sp-space_normal .g-column > .-col12 {
    width: 100%;
  }

  .l-5 .c-sp-space_normal .g-column.-no_spacing.-col2 > .column,
  .l-5 .c-sp-space_normal .g-column.-no_spacing.-col3 > .column,
  .l-5 .c-sp-space_normal .g-column.-no_spacing.-col4 > .column,
  .l-5 .c-sp-space_normal .g-column.-no_spacing.-col5 > .column,
  .l-5 .c-sp-space_normal .g-column.-no_spacing.-col6 > .column,
  .l-5 .c-sp-space_normal .g-column.-no_spacing.-col7 > .column,
  .l-5 .c-sp-space_normal .g-column.-no_spacing.-col8 > .column {
    float: none;
    width: 100%;
  }

  .l-5 .c-sp-space_normal .g-column.-sp-col2 > .column,
  .l-5 .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column,
  .l-5 .c-sp-space_normal .g-column.-sp-col3 > .column,
  .l-5 .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column {
    float: left;
    margin-left: 2%;
    margin-right: 2%;
    margin-top: 4%;
  }

  .l-5 .c-sp-space_normal .g-column.-sp-col1 > .column:nth-child(n),
  .l-5 .c-sp-space_normal .g-column.-sp-col1.-no_spacing > .column:nth-child(n) {
    margin-top: 4%;
  }

  .l-5 .c-sp-space_normal .g-column.-sp-col2 > .column,
  .l-5 .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column {
    width: 48%;
  }

  .l-5 .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(2n + 1),
  .l-5 .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(2n + 1) {
    margin-left: 0;
  }

  .l-5 .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(2n),
  .l-5 .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(2n) {
    margin-right: 0;
  }

  .l-5 .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(1),
  .l-5 .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(2),
  .l-5 .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(1),
  .l-5 .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(2) {
    margin-top: 0;
  }

  .l-5 .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(3),
  .l-5 .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(4),
  .l-5 .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(5),
  .l-5 .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(6),
  .l-5 .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(7),
  .l-5 .c-sp-space_normal .g-column.-sp-col2 > .column:nth-child(8),
  .l-5 .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(3),
  .l-5 .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(4),
  .l-5 .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(5),
  .l-5 .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(6),
  .l-5 .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(7),
  .l-5 .c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column:nth-child(8) {
    margin-top: 4%;
  }

  .l-5 .c-sp-space_normal .g-column.-sp-col3 > .column,
  .l-5 .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column {
    width: 30.666%;
  }

  .l-5 .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(3n + 1),
  .l-5 .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(3n + 1) {
    margin-left: 0;
  }

  .l-5 .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(3n),
  .l-5 .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(3n) {
    margin-right: 0;
  }

  .l-5 .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(1),
  .l-5 .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(2),
  .l-5 .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(3),
  .l-5 .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(1),
  .l-5 .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(2),
  .l-5 .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(3) {
    margin-top: 0;
  }

  .l-5 .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(4),
  .l-5 .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(5),
  .l-5 .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(6),
  .l-5 .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(7),
  .l-5 .c-sp-space_normal .g-column.-sp-col3 > .column:nth-child(8),
  .l-5 .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(4),
  .l-5 .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(5),
  .l-5 .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(6),
  .l-5 .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(7),
  .l-5 .c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column:nth-child(8) {
    margin-top: 4%;
  }

  .c-sp-space_wide .g-column > .column {
    float: none;
    margin-left: 0;
    margin-right: 0;
    margin-top: 6%;
  }

  .c-sp-space_wide .g-column.-col2 > .column,
  .c-sp-space_wide .g-column.-col3 > .column,
  .c-sp-space_wide .g-column.-col4 > .column,
  .c-sp-space_wide .g-column.-col5 > .column,
  .c-sp-space_wide .g-column.-col6 > .column,
  .c-sp-space_wide .g-column.-col7 > .column,
  .c-sp-space_wide .g-column.-col8 > .column {
    width: 100%;
  }

  .c-sp-space_wide .g-column > .-col1,
  .c-sp-space_wide .g-column > .-col2,
  .c-sp-space_wide .g-column > .-col3,
  .c-sp-space_wide .g-column > .-col4,
  .c-sp-space_wide .g-column > .-col5,
  .c-sp-space_wide .g-column > .-col6,
  .c-sp-space_wide .g-column > .-col7,
  .c-sp-space_wide .g-column > .-col8,
  .c-sp-space_wide .g-column > .-col9,
  .c-sp-space_wide .g-column > .-col10,
  .c-sp-space_wide .g-column > .-col11,
  .c-sp-space_wide .g-column > .-col12 {
    width: 100%;
  }

  .c-sp-space_wide .g-column.-no_spacing.-col2 > .column,
  .c-sp-space_wide .g-column.-no_spacing.-col3 > .column,
  .c-sp-space_wide .g-column.-no_spacing.-col4 > .column,
  .c-sp-space_wide .g-column.-no_spacing.-col5 > .column,
  .c-sp-space_wide .g-column.-no_spacing.-col6 > .column,
  .c-sp-space_wide .g-column.-no_spacing.-col7 > .column,
  .c-sp-space_wide .g-column.-no_spacing.-col8 > .column {
    float: none;
    width: 100%;
  }

  .c-sp-space_wide .g-column.-sp-col2 > .column,
  .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column,
  .c-sp-space_wide .g-column.-sp-col3 > .column,
  .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column {
    float: left;
    margin-left: 3%;
    margin-right: 3%;
    margin-top: 6%;
  }

  .c-sp-space_wide .g-column.-sp-col1 > .column:nth-child(n),
  .c-sp-space_wide .g-column.-sp-col1.-no_spacing > .column:nth-child(n) {
    margin-top: 6%;
  }

  .c-sp-space_wide .g-column.-sp-col2 > .column,
  .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column {
    width: 47%;
  }

  .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(2n + 1),
  .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(2n + 1) {
    margin-left: 0;
  }

  .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(2n),
  .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(2n) {
    margin-right: 0;
  }

  .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(1),
  .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(2),
  .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(1),
  .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(2) {
    margin-top: 0;
  }

  .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(3),
  .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(4),
  .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(5),
  .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(6),
  .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(7),
  .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(8),
  .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(3),
  .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(4),
  .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(5),
  .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(6),
  .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(7),
  .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(8) {
    margin-top: 6%;
  }

  .c-sp-space_wide .g-column.-sp-col3 > .column,
  .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column {
    width: 29.333%;
  }

  .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(3n + 1),
  .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(3n + 1) {
    margin-left: 0;
  }

  .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(3n),
  .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(3n) {
    margin-right: 0;
  }

  .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(1),
  .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(2),
  .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(3),
  .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(1),
  .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(2),
  .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(3) {
    margin-top: 0;
  }

  .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(4),
  .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(5),
  .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(6),
  .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(7),
  .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(8),
  .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(4),
  .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(5),
  .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(6),
  .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(7),
  .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(8) {
    margin-top: 6%;
  }

  .l-5 .c-sp-space_wide .g-column > .column {
    float: none;
    margin-left: 0;
    margin-right: 0;
    margin-top: 6%;
  }

  .l-5 .c-sp-space_wide .g-column.-col2 > .column,
  .l-5 .c-sp-space_wide .g-column.-col3 > .column,
  .l-5 .c-sp-space_wide .g-column.-col4 > .column,
  .l-5 .c-sp-space_wide .g-column.-col5 > .column,
  .l-5 .c-sp-space_wide .g-column.-col6 > .column,
  .l-5 .c-sp-space_wide .g-column.-col7 > .column,
  .l-5 .c-sp-space_wide .g-column.-col8 > .column {
    width: 100%;
  }

  .l-5 .c-sp-space_wide .g-column > .-col1,
  .l-5 .c-sp-space_wide .g-column > .-col2,
  .l-5 .c-sp-space_wide .g-column > .-col3,
  .l-5 .c-sp-space_wide .g-column > .-col4,
  .l-5 .c-sp-space_wide .g-column > .-col5,
  .l-5 .c-sp-space_wide .g-column > .-col6,
  .l-5 .c-sp-space_wide .g-column > .-col7,
  .l-5 .c-sp-space_wide .g-column > .-col8,
  .l-5 .c-sp-space_wide .g-column > .-col9,
  .l-5 .c-sp-space_wide .g-column > .-col10,
  .l-5 .c-sp-space_wide .g-column > .-col11,
  .l-5 .c-sp-space_wide .g-column > .-col12 {
    width: 100%;
  }

  .l-5 .c-sp-space_wide .g-column.-no_spacing.-col2 > .column,
  .l-5 .c-sp-space_wide .g-column.-no_spacing.-col3 > .column,
  .l-5 .c-sp-space_wide .g-column.-no_spacing.-col4 > .column,
  .l-5 .c-sp-space_wide .g-column.-no_spacing.-col5 > .column,
  .l-5 .c-sp-space_wide .g-column.-no_spacing.-col6 > .column,
  .l-5 .c-sp-space_wide .g-column.-no_spacing.-col7 > .column,
  .l-5 .c-sp-space_wide .g-column.-no_spacing.-col8 > .column {
    float: none;
    width: 100%;
  }

  .l-5 .c-sp-space_wide .g-column.-sp-col2 > .column,
  .l-5 .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column,
  .l-5 .c-sp-space_wide .g-column.-sp-col3 > .column,
  .l-5 .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column {
    float: left;
    margin-left: 3%;
    margin-right: 3%;
    margin-top: 6%;
  }

  .l-5 .c-sp-space_wide .g-column.-sp-col1 > .column:nth-child(n),
  .l-5 .c-sp-space_wide .g-column.-sp-col1.-no_spacing > .column:nth-child(n) {
    margin-top: 6%;
  }

  .l-5 .c-sp-space_wide .g-column.-sp-col2 > .column,
  .l-5 .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column {
    width: 47%;
  }

  .l-5 .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(2n + 1),
  .l-5 .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(2n + 1) {
    margin-left: 0;
  }

  .l-5 .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(2n),
  .l-5 .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(2n) {
    margin-right: 0;
  }

  .l-5 .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(1),
  .l-5 .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(2),
  .l-5 .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(1),
  .l-5 .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(2) {
    margin-top: 0;
  }

  .l-5 .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(3),
  .l-5 .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(4),
  .l-5 .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(5),
  .l-5 .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(6),
  .l-5 .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(7),
  .l-5 .c-sp-space_wide .g-column.-sp-col2 > .column:nth-child(8),
  .l-5 .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(3),
  .l-5 .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(4),
  .l-5 .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(5),
  .l-5 .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(6),
  .l-5 .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(7),
  .l-5 .c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column:nth-child(8) {
    margin-top: 6%;
  }

  .l-5 .c-sp-space_wide .g-column.-sp-col3 > .column,
  .l-5 .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column {
    width: 29.333%;
  }

  .l-5 .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(3n + 1),
  .l-5 .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(3n + 1) {
    margin-left: 0;
  }

  .l-5 .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(3n),
  .l-5 .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(3n) {
    margin-right: 0;
  }

  .l-5 .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(1),
  .l-5 .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(2),
  .l-5 .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(3),
  .l-5 .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(1),
  .l-5 .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(2),
  .l-5 .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(3) {
    margin-top: 0;
  }

  .l-5 .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(4),
  .l-5 .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(5),
  .l-5 .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(6),
  .l-5 .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(7),
  .l-5 .c-sp-space_wide .g-column.-sp-col3 > .column:nth-child(8),
  .l-5 .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(4),
  .l-5 .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(5),
  .l-5 .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(6),
  .l-5 .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(7),
  .l-5 .c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column:nth-child(8) {
    margin-top: 6%;
  }
}

.b-headlines .g-column.-col2 > .column {
  -ms-flex-preferred-size: 49.9%;
  flex-basis: 49.9%;
}

.b-headlines .g-column.-col3 > .column {
  -ms-flex-preferred-size: 33.2%;
  flex-basis: 33.2%;
}

.b-headlines .g-column.-col4 > .column {
  -ms-flex-preferred-size: 24.9%;
  flex-basis: 24.9%;
}

.b-headlines .g-column.-col5 > .column {
  -ms-flex-preferred-size: 19.9%;
  flex-basis: 19.9%;
}

.b-headlines.c-space_narrow .g-column.-col2 > .column {
  -ms-flex-preferred-size: 48.9%;
  flex-basis: 48.9%;
}

.b-headlines.c-space_narrow .g-column.-col3 > .column {
  -ms-flex-preferred-size: 31.9%;
  flex-basis: 31.9%;
}

.b-headlines.c-space_narrow .g-column.-col4 > .column {
  -ms-flex-preferred-size: 23.4%;
  flex-basis: 23.4%;
}

.b-headlines.c-space_narrow .g-column.-col5 > .column {
  -ms-flex-preferred-size: 18.3%;
  flex-basis: 18.3%;
}

.b-headlines.c-space_normal .g-column.-col2 > .column {
  -ms-flex-preferred-size: 47.9%;
  flex-basis: 47.9%;
}

.b-headlines.c-space_normal .g-column.-col3 > .column {
  -ms-flex-preferred-size: 30.5%;
  flex-basis: 30.5%;
}

.b-headlines.c-space_normal .g-column.-col4 > .column {
  -ms-flex-preferred-size: 21.9%;
  flex-basis: 21.9%;
}

.b-headlines.c-space_normal .g-column.-col5 > .column {
  -ms-flex-preferred-size: 16.7%;
  flex-basis: 16.7%;
}

.b-headlines.c-space_wide .g-column.-col2 > .column {
  -ms-flex-preferred-size: 46.9%;
  flex-basis: 46.9%;
}

.b-headlines.c-space_wide .g-column.-col3 > .column {
  -ms-flex-preferred-size: 29.233%;
  flex-basis: 29.233%;
}

.b-headlines.c-space_wide .g-column.-col4 > .column {
  -ms-flex-preferred-size: 20.4%;
  flex-basis: 20.4%;
}

.b-headlines.c-space_wide .g-column.-col5 > .column {
  -ms-flex-preferred-size: 15.1%;
  flex-basis: 15.1%;
}

@media only screen and (max-width: 640px) {
  .b-headlines.c-sp-space_init .g-column.-col2 > .column,
  .b-headlines.c-sp-space_init .g-column.-col3 > .column,
  .b-headlines.c-sp-space_init .g-column.-col4 > .column,
  .b-headlines.c-sp-space_init .g-column.-col5 > .column {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
  }

  .b-headlines.c-sp-space_init .g-column.-no_spacing.-col2 > .column,
  .b-headlines.c-sp-space_init .g-column.-no_spacing.-col3 > .column,
  .b-headlines.c-sp-space_init .g-column.-no_spacing.-col4 > .column,
  .b-headlines.c-sp-space_init .g-column.-no_spacing.-col5 > .column {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
  }

  .b-headlines.c-sp-space_init .g-column.-sp-col1 > .column,
  .b-headlines.c-sp-space_init .g-column.-sp-col1.-no_spacing > .column {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
  }

  .b-headlines.c-sp-space_init .g-column.-sp-col2 > .column,
  .b-headlines.c-sp-space_init .g-column.-sp-col2.-no_spacing > .column {
    -ms-flex-preferred-size: 49.9%;
    flex-basis: 49.9%;
  }

  .b-headlines.c-sp-space_init .g-column.-sp-col3 > .column,
  .b-headlines.c-sp-space_init .g-column.-sp-col3.-no_spacing > .column {
    -ms-flex-preferred-size: 33.2%;
    flex-basis: 33.2%;
  }

  .b-headlines.c-sp-space_narrow .g-column.-col2 > .column,
  .b-headlines.c-sp-space_narrow .g-column.-col3 > .column,
  .b-headlines.c-sp-space_narrow .g-column.-col4 > .column,
  .b-headlines.c-sp-space_narrow .g-column.-col5 > .column {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
  }

  .b-headlines.c-sp-space_narrow .g-column.-no_spacing.-col2 > .column,
  .b-headlines.c-sp-space_narrow .g-column.-no_spacing.-col3 > .column,
  .b-headlines.c-sp-space_narrow .g-column.-no_spacing.-col4 > .column,
  .b-headlines.c-sp-space_narrow .g-column.-no_spacing.-col5 > .column {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
  }

  .b-headlines.c-sp-space_narrow .g-column.-sp-col1 > .column,
  .b-headlines.c-sp-space_narrow .g-column.-sp-col1.-no_spacing > .column {
    -ms-flex-preferred-size: 98%;
    flex-basis: 98%;
  }

  .b-headlines.c-sp-space_narrow .g-column.-sp-col2 > .column,
  .b-headlines.c-sp-space_narrow .g-column.-sp-col2.-no_spacing > .column {
    -ms-flex-preferred-size: 48.9%;
    flex-basis: 48.9%;
  }

  .b-headlines.c-sp-space_narrow .g-column.-sp-col3 > .column,
  .b-headlines.c-sp-space_narrow .g-column.-sp-col3.-no_spacing > .column {
    -ms-flex-preferred-size: 31.9%;
    flex-basis: 31.9%;
  }

  .b-headlines.c-sp-space_normal .g-column.-col2 > .column,
  .b-headlines.c-sp-space_normal .g-column.-col3 > .column,
  .b-headlines.c-sp-space_normal .g-column.-col4 > .column,
  .b-headlines.c-sp-space_normal .g-column.-col5 > .column {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
  }

  .b-headlines.c-sp-space_normal .g-column.-no_spacing.-col2 > .column,
  .b-headlines.c-sp-space_normal .g-column.-no_spacing.-col3 > .column,
  .b-headlines.c-sp-space_normal .g-column.-no_spacing.-col4 > .column,
  .b-headlines.c-sp-space_normal .g-column.-no_spacing.-col5 > .column {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
  }

  .b-headlines.c-sp-space_normal .g-column.-sp-col1 > .column,
  .b-headlines.c-sp-space_normal .g-column.-sp-col1.-no_spacing > .column {
    -ms-flex-preferred-size: 97%;
    flex-basis: 97%;
  }

  .b-headlines.c-sp-space_normal .g-column.-sp-col2 > .column,
  .b-headlines.c-sp-space_normal .g-column.-sp-col2.-no_spacing > .column {
    -ms-flex-preferred-size: 47.9%;
    flex-basis: 47.9%;
  }

  .b-headlines.c-sp-space_normal .g-column.-sp-col3 > .column,
  .b-headlines.c-sp-space_normal .g-column.-sp-col3.-no_spacing > .column {
    -ms-flex-preferred-size: 30.566%;
    flex-basis: 30.566%;
  }

  .b-headlines.c-sp-space_wide .g-column.-col2 > .column,
  .b-headlines.c-sp-space_wide .g-column.-col3 > .column,
  .b-headlines.c-sp-space_wide .g-column.-col4 > .column,
  .b-headlines.c-sp-space_wide .g-column.-col5 > .column {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
  }

  .b-headlines.c-sp-space_wide .g-column.-no_spacing.-col2 > .column,
  .b-headlines.c-sp-space_wide .g-column.-no_spacing.-col3 > .column,
  .b-headlines.c-sp-space_wide .g-column.-no_spacing.-col4 > .column,
  .b-headlines.c-sp-space_wide .g-column.-no_spacing.-col5 > .column {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
  }

  .b-headlines.c-sp-space_wide .g-column.-sp-col1 > .column,
  .b-headlines.c-sp-space_wide .g-column.-sp-col1.-no_spacing > .column {
    -ms-flex-preferred-size: 96%;
    flex-basis: 96%;
  }

  .b-headlines.c-sp-space_wide .g-column.-sp-col2 > .column,
  .b-headlines.c-sp-space_wide .g-column.-sp-col2.-no_spacing > .column {
    -ms-flex-preferred-size: 46.9%;
    flex-basis: 46.9%;
  }

  .b-headlines.c-sp-space_wide .g-column.-sp-col3 > .column,
  .b-headlines.c-sp-space_wide .g-column.-sp-col3.-no_spacing > .column {
    -ms-flex-preferred-size: 29.233%;
    flex-basis: 29.233%;
  }
}

.b-plain:before,
.b-plain:after,
.b-float:before,
.b-float:after {
  display: table;
  line-height: 0;
  content: '';
}

.b-plain:after,
.b-float:after {
  clear: both;
}

.b-plain .g-column > .column,
.b-float .g-column > .column {
  min-height: 1px;
}

.b-both_diff:before,
.b-both_diff:after {
  display: table;
  line-height: 0;
  content: '';
}

.b-both_diff:after {
  clear: both;
}

.b-both_diff .column {
  float: left;
}

.b-both_diff .g-column > .column {
  min-height: 1px;
}

.b-both_diff.-left_large .column.-col4 {
  margin-right: 0;
}

.b-both_diff.-left_large .column.-col8 {
  margin-left: 0;
}

.b-both_diff.-right_large .column.-col4 {
  margin-left: 0;
}

.b-both_diff.-right_large .column.-col8 {
  margin-right: 0;
}

@media only screen and (max-width: 640px) {
  .b-both_diff.-left_large .column {
    float: left;
  }

  .b-both_diff.-left_large .column.-col8 {
    width: 70%;
  }

  .b-both_diff.-left_large .column.-col4 {
    width: 30%;
  }

  .b-both_diff.-left_large.c-sp-space_narrow .column {
    width: 69%;
  }

  .b-both_diff.-left_large.c-sp-space_narrow .column.-col8 {
    margin-right: 1%;
  }

  .b-both_diff.-left_large.c-sp-space_narrow .column.-col4 {
    width: 29%;
    margin-left: 1%;
  }

  .b-both_diff.-left_large.c-sp-space_normal .column {
    width: 68%;
  }

  .b-both_diff.-left_large.c-sp-space_normal .column.-col8 {
    margin-right: 2%;
  }

  .b-both_diff.-left_large.c-sp-space_normal .column.-col4 {
    width: 28%;
    margin-left: 2%;
  }

  .b-both_diff.-left_large.c-sp-space_wide .column {
    width: 67%;
  }

  .b-both_diff.-left_large.c-sp-space_wide .column.-col8 {
    margin-right: 3%;
  }

  .b-both_diff.-left_large.c-sp-space_wide .column.-col4 {
    width: 27%;
    margin-left: 3%;
  }

  .b-both_diff.-right_large .column {
    float: left;
  }

  .b-both_diff.-right_large .column.-col4 {
    width: 30%;
  }

  .b-both_diff.-right_large .column.-col8 {
    width: 70%;
  }

  .b-both_diff.-right_large.c-sp-space_narrow .column {
    width: 29%;
  }

  .b-both_diff.-right_large.c-sp-space_narrow .column.-col8 {
    width: 69%;
    margin-left: 1%;
  }

  .b-both_diff.-right_large.c-sp-space_narrow .column.-col4 {
    margin-right: 1%;
  }

  .b-both_diff.-right_large.c-sp-space_normal .column {
    width: 28%;
  }

  .b-both_diff.-right_large.c-sp-space_normal .column.-col8 {
    width: 68%;
    margin-left: 2%;
  }

  .b-both_diff.-right_large.c-sp-space_normal .column.-col4 {
    margin-right: 2%;
  }

  .b-both_diff.-right_large.c-sp-space_wide .column {
    width: 27%;
  }

  .b-both_diff.-right_large.c-sp-space_wide .column.-col8 {
    width: 67%;
    margin-left: 3%;
  }

  .b-both_diff.-right_large.c-sp-space_wide .column.-col4 {
    margin-right: 3%;
  }

  .b-both_diff.-sp-single_column.c-sp-space_init .column,
  .b-both_diff.-sp-single_column.c-sp-space_narrow .column,
  .b-both_diff.-sp-single_column.c-sp-space_normal .column,
  .b-both_diff.-sp-single_column.c-sp-space_wide .column {
    float: none;
  }

  .b-both_diff.-sp-single_column.c-sp-space_init .column.-col8,
  .b-both_diff.-sp-single_column.c-sp-space_narrow .column.-col8,
  .b-both_diff.-sp-single_column.c-sp-space_normal .column.-col8,
  .b-both_diff.-sp-single_column.c-sp-space_wide .column.-col8 {
    width: 100%;
  }

  .b-both_diff.-sp-single_column.c-sp-space_init .column.-col4,
  .b-both_diff.-sp-single_column.c-sp-space_narrow .column.-col4,
  .b-both_diff.-sp-single_column.c-sp-space_normal .column.-col4,
  .b-both_diff.-sp-single_column.c-sp-space_wide .column.-col4 {
    width: 100%;
  }

  .b-both_diff.-sp-single_column.c-sp-space_init .column.-col8,
  .b-both_diff.-sp-single_column.c-sp-space_init .column.-col4,
  .b-both_diff.-sp-single_column.c-sp-space_narrow .column.-col8,
  .b-both_diff.-sp-single_column.c-sp-space_narrow .column.-col4,
  .b-both_diff.-sp-single_column.c-sp-space_normal .column.-col8,
  .b-both_diff.-sp-single_column.c-sp-space_normal .column.-col4,
  .b-both_diff.-sp-single_column.c-sp-space_wide .column.-col8,
  .b-both_diff.-sp-single_column.c-sp-space_wide .column.-col4 {
    margin-right: 0;
    margin-left: 0;
  }
}

@media only screen and (max-width: 640px) {
  .l-5 .b-both_diff.-left_large .g-column > .column {
    float: left;
  }

  .l-5 .b-both_diff.-right_large .g-column > .column {
    float: left;
  }

  .l-5 .b-both_diff.-sp-single_column.c-sp-space_init .g-column > .column,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_narrow .g-column > .column,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_normal .g-column > .column,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_wide .g-column > .column {
    float: none;
  }

  .l-5 .b-both_diff.-sp-single_column.c-sp-space_init .g-column > .column.-col8,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_narrow .g-column > .column.-col8,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_normal .g-column > .column.-col8,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_wide .g-column > .column.-col8 {
    width: 100%;
  }

  .l-5 .b-both_diff.-sp-single_column.c-sp-space_init .g-column > .column.-col4,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_narrow .g-column > .column.-col4,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_normal .g-column > .column.-col4,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_wide .g-column > .column.-col4 {
    width: 100%;
  }

  .l-5 .b-both_diff.-sp-single_column.c-sp-space_init .g-column > .column.-col8,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_init .g-column > .column.-col4,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_narrow .g-column > .column.-col8,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_narrow .g-column > .column.-col4,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_normal .g-column > .column.-col8,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_normal .g-column > .column.-col4,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_wide .g-column > .column.-col8,
  .l-5 .b-both_diff.-sp-single_column.c-sp-space_wide .g-column > .column.-col4 {
    margin-right: 0;
    margin-left: 0;
  }
}

.b-album:before,
.b-album:after {
  display: table;
  line-height: 0;
  content: '';
}

.b-album:after {
  clear: both;
}

.b-album .column {
  text-align: center;
  width: 100%;
}

.b-album .column figure {
  display: inline-block;
}

.b-album .c-photo {
  font-size: 0;
  line-height: 0;
}

.b-album .c-photo_mouseover {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  font-size: 0;
  line-height: 0;
  overflow: hidden;
}

.b-album .c-photo_mouseover .js-photo_images {
  -webkit-transform: scale(1.1, 1.1);
  transform: scale(1.1, 1.1);
  transition: -webkit-transform 0.2s ease;
  transition: transform 0.2s ease;
}

.b-album .c-photo_mouseover:hover .js-photo_images {
  -webkit-transform: scale(1, 1);
  transform: scale(1, 1);
}

.b-album .c-photo_mouseover .c-mouseover_position {
  display: table;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  font-size: 0;
  line-height: 0;
}

.b-album .c-photo_mouseover .c-mouseover_position .js-photo_mouseover {
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
  opacity: 0;
  filter: alpha(opacity=0);
  display: table-cell;
  vertical-align: middle;
  width: 100%;
  height: 100%;
  *zoom: 1;
  filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0,startColorstr='#80000000',endColorstr='#80000000');
  background: transparent;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 14px;
  font-size: 1rem;
  line-height: 1.5;
  transition: all 0.2s ease;
  text-shadow: 0 0 1px #aaa;
}

:root .b-album .c-photo_mouseover .c-mouseover_position .js-photo_mouseover {
  -webkit-filter: none\0 / IE9;
  filter: none\0 / IE9;
}

.b-album .c-photo_mouseover .c-mouseover_position:hover .js-photo_mouseover {
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
  opacity: 1;
  filter: alpha(opacity=100);
}

@media only screen and (max-width: 640px) {
  .b-album .c-photo_mouseover .c-mouseover_position:hover .js-photo_mouseover {
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .b-album .c-photo_mouseover,
  .b-album .column figure {
    display: inline-block;
  }
}

.b-tab_navigation {
  font-size: 0;
}

.b-tab_navigation li {
  display: inline-block;
  margin-right: 1px;
  font-size: 14px;
  font-size: 1rem;
}

.b-tab_navigation li a {
  display: block;
  background-color: #ddd;
  color: inherit;
  padding: 1em;
  line-height: 1.5;
}

.b-tab_navigation li a:hover {
  background-color: #999;
  padding: 1em;
}

.b-tab_navigation li.-active a {
  background-color: #999;
  padding: 1em;
}

.b-tab_navigation.-right {
  text-align: right;
}

.b-tab_contents {
  position: relative;
  top: 0;
  left: 0;
  overflow: hidden;
  height: 100%;
  padding-top: 10px;
}

.b-tab_contents .column {
  display: none;
}

.b-tab_contents .column.-active {
  display: block;
}

.b-tab_contents .b-tab_outer-slidebox:before,
.b-tab_contents .b-tab_outer-slidebox:after {
  display: table;
  line-height: 0;
  content: '';
}

.b-tab_contents .b-tab_outer-slidebox:after {
  clear: both;
}

.b-tab:before,
.b-tab:after {
  display: table;
  line-height: 0;
  content: '';
}

.b-tab:after {
  clear: both;
}

.b-tab.-slider {
  text-align: center;
}

.b-tab.-slider .b-tab_contents {
  padding-top: 0;
  overflow: hidden;
  width: 100%;
  text-align: left;
}

.b-tab.-slider .b-tab_outer-slidebox {
  position: absolute;
  top: 0;
  left: 0;
}

.b-tab.-slider .g-column .column {
  float: left;
  display: block;
}

.b-tab.-slider .b-tab_navigation {
  display: inline-block;
}

.b-tab.-slider .b-tab_navigation li a [class^='bindicon-'],
.b-tab.-slider .b-tab_navigation [class*=' bindicon-'] {
  margin-right: 0;
}

.b-tab .g-column .column {
  float: none;
}

.-menu .b-tab_navigation {
  float: left;
  width: 30%;
  margin-right: 2%;
}

@media only screen and (max-width: 640px) {
  .-menu .b-tab_navigation {
    float: none;
    width: 100%;
    margin-right: 0;
  }
}

.-menu .b-tab_navigation li {
  display: block;
}

.-menu .b-tab_navigation.-right {
  float: right;
  margin-right: 0;
  margin-left: 2%;
}

@media only screen and (max-width: 640px) {
  .-menu .b-tab_navigation.-right {
    margin-left: 0;
  }
}

.-menu .b-tab_contents {
  float: left;
  width: 68%;
  padding-top: 0;
}

@media only screen and (max-width: 640px) {
  .-menu .b-tab_contents {
    float: none;
    width: 100%;
  }
}

.b-accordion:before,
.b-accordion:after {
  display: table;
  line-height: 0;
  content: '';
}

.b-accordion:after {
  clear: both;
}

.b-accordion_contents .column {
  display: none;
  background: #eee;
  padding: 1em;
}

.b-accordion_contents .g-column .column {
  float: none;
}

.b-accordion_navigation {
  border-top: 1px solid transparent;
}

.b-accordion_navigation a {
  display: block;
  background: #ddd;
  font-size: 110%;
  padding: 1em;
}

.b-accordion_navigation.-active a,
.b-accordion_navigation a:hover {
  background: #ccc;
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
  opacity: 1;
  filter: alpha(opacity=100);
}

.b-headlines:before,
.b-headlines:after {
  display: table;
  line-height: 0;
  content: '';
}

.b-headlines:after {
  clear: both;
}

.b-headlines > .g-column {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.b-headlines .column {
  width: 100%;
}

.b-headlines .column figure {
  text-align: center;
}

.b-float:before,
.b-float:after {
  display: table;
  line-height: 0;
  content: '';
}

.b-float:after {
  clear: both;
}

.b-float .g-column > .column,
.b-float .g-column.-sp-col1 > .column,
.b-float .g-column.-sp-col2 > .column,
.b-float .g-column.-sp-col3 > .column {
  float: none;
  margin-left: 0;
  margin-right: 0;
}

.c-blog-new {
  display: inline-block;
  padding: 0 0.5em;
  background-color: #dcdcdc;
}

.c-blog-category {
  display: inline-block;
  padding: 0 0.5em;
  border: 1px solid;
}

.c-blog-category + .c-blog-category {
  margin-left: 3px;
}

.c-menu {
  margin-bottom: 10px;
  font-size: 0;
}

.c-menu:before,
.c-menu:after {
  display: table;
  line-height: 0;
  content: '';
}

.c-menu:after {
  clear: both;
}

.c-menu li {
  font-size: 14px;
  font-size: 1rem;
  display: inline-block;
  border-right-width: 1px;
  border-right-style: solid;
}

.c-menu li:first-child {
  border-left-width: 1px;
  border-left-style: solid;
}

.c-menu li.c-sp-closer + li {
  border-left-width: 1px;
  border-left-style: solid;
}

@media only screen and (max-width: 768px) {
  .c-menu li.c-sp-closer + li {
    border-left: 0 none;
  }
}

.c-menu li a,
.c-menu li .c-unlink {
  display: block;
  padding: 4px 20px;
  text-decoration: none;
  transition: all 0.2s ease;
}

@media only screen and (max-width: 768px) {
  .c-menu li a,
  .c-menu li .c-unlink {
    padding: 10px 20px;
  }
}

@media only screen and (max-width: 768px) {
  .c-menu li {
    width: 100%;
    display: block;
    border: 0 none;
    border-bottom-width: 1px;
    border-bottom-style: solid;
  }

  .c-menu li:first-child {
    border-left: 0 none;
  }
}

.c-menu ul {
  margin-left: 20px;
}

.c-menu.-v {
  border-bottom-width: 1px;
  border-bottom-style: solid;
}

@media only screen and (max-width: 768px) {
  .c-menu.-v {
    border-bottom-width: 1px;
    border-bottom-style: solid;
  }
}

.c-menu.-v li {
  display: block;
  border-top-width: 1px;
  border-top-style: solid;
  border-left: 0 none;
  border-right: 0 none;
}

.c-menu.-v li.c-sp-closer {
  display: none;
}

.c-menu.-v li.c-sp-closer + li {
  border-left: 0 none;
}

@media only screen and (max-width: 768px) {
  .c-menu.-v li.c-sp-closer {
    display: block;
  }
}

@media only screen and (max-width: 768px) {
  .c-menu.-v li {
    border: 0 none;
    border-bottom-width: 1px;
    border-bottom-style: solid;
  }

  .c-menu.-v li:first-child {
    border-left: 0 none;
  }
}

.-menu_left {
  text-align: left;
}

.-menu_center {
  text-align: center;
}

.-menu_right {
  text-align: right;
}

.m-tab {
  display: table;
  width: 100%;
  table-layout: fixed;
}

.m-tab .c-menu {
  display: table-row;
}

.m-tab .c-menu li {
  display: table-cell;
  float: none;
  width: 100%;
}

@media only screen and (max-width: 768px) {
  .m-tab .c-menu li {
    border: 0 none;
    border-right-width: 1px;
    border-right-style: solid;
  }
}

@media only screen and (max-width: 768px) {
  .m-tab .c-menu li:first-child {
    border-left-width: 1px;
    border-left-style: solid;
  }
}

.m-tab .c-menu li a {
  text-align: center;
}

@media only screen and (max-width: 768px) {
  .m-tab .c-menu li .m-tab_text {
    display: none;
  }
}

.c-sp_navigation_btn {
  display: none;
  background: #fff;
  width: 48px;
  height: 48px;
  border: 1px solid #333;
  position: relative;
  top: 0;
  left: 0;
}

.c-sp_navigation_btn .c-sp-navigation_line1,
.c-sp_navigation_btn .c-sp-navigation_line2,
.c-sp_navigation_btn .c-sp-navigation_line3 {
  content: '';
  position: absolute;
  top: 11px;
  left: 7px;
  width: 32px;
  border-top: 4px solid #333;
}

.c-sp_navigation_btn .c-sp-navigation_line2 {
  top: 21px;
}

.c-sp_navigation_btn .c-sp-navigation_line3 {
  top: 31px;
}

@media only screen and (max-width: 768px) {
  .c-sp_navigation_btn {
    display: block;
  }
}

@media only screen and (max-width: 768px) {
  #js-globalNavigation {
    display: none;
    overflow: auto;
    background: #fff;
  }

  #js-globalNavigation.-js-menuSlide-right_side_sliders,
  #js-globalNavigation.-js-menuSlide-left_side_sliders,
  #js-globalNavigation.-js-menuSlide-up_sliders,
  #js-globalNavigation.-js-menuSlide-no_animation {
    display: block;
    position: fixed;
    top: 0;
    z-index: 9000;
    width: 100%;
  }

  #js-globalNavigation.-js-menuSlide-right_side_sliders {
    left: 100%;
  }

  #js-globalNavigation.-js-menuSlide-left_side_sliders {
    right: 100%;
  }

  #js-globalNavigation.-js-menuSlide-up_sliders,
  #js-globalNavigation.-js-menuSlide-no_animation {
    display: none;
    left: 0;
  }

  #spNavigationTrigger {
    z-index: 99;
  }

  #spNavigationTrigger.-js-menuPosition-left_top {
    float: left;
  }

  #spNavigationTrigger.-js-menuPosition-right_top {
    float: right;
  }

  #spNavigationTrigger.-js-menuPosition-left_top_fixed,
  #spNavigationTrigger.-js-menuPosition-right_top_fixed,
  #spNavigationTrigger.-js-menuPosition-top_fixed {
    position: fixed;
  }

  #spNavigationTrigger.-js-menuPosition-left_top_fixed {
    top: 30px;
    left: 0;
  }

  #spNavigationTrigger.-js-menuPosition-right_top_fixed {
    top: 30px;
    left: auto;
    right: 0;
  }

  #spNavigationTrigger.-js-menuPosition-top_fixed {
    top: 0;
    left: 50%;
    margin-left: -24px;
  }
}

@media only screen and (min-width: 641px) and (max-width: 768px) {
  #spNavigationTrigger.-js-menuPosition-relative_on_tablet {
    position: relative;
    top: initial;
    left: initial;
    right: initial;
    margin-left: initial;
    clear: both;
  }
}

.js-globalNavigationBaseBlock {
  position: relative;
  z-index: 11;
}

.c-padding_narrow > .g-column,
.c-padding_narrow > .column {
  padding: 10px;
}

.c-padding_normal > .g-column,
.c-padding_normal > .column {
  padding: 30px;
}

.c-padding_wide > .g-column,
.c-padding_wide > .column {
  padding: 60px;
}

.c-padding_highest > .g-column,
.c-padding_highest > .column {
  padding: 100px;
}

.c-padding_narrow > .column {
  width: auto;
}

.c-padding_normal > .column {
  width: auto;
}

.c-padding_wide > .column {
  width: auto;
}

.c-padding_highest > .column {
  width: auto;
}

@media only screen and (max-width: 640px) {
  .c-sp-padding_init > .g-column,
  .c-sp-padding_init > .column {
    padding: 0;
  }

  .c-sp-padding_narrow > .g-column,
  .c-sp-padding_narrow > .column {
    padding: 5px;
  }

  .c-sp-padding_normal > .g-column,
  .c-sp-padding_normal > .column {
    padding: 15px;
  }

  .c-sp-padding_wide > .g-column,
  .c-sp-padding_wide > .column {
    padding: 30px;
  }

  .c-sp-padding_highest > .g-column,
  .c-sp-padding_highest > .column {
    padding: 50px;
  }

  .c-sp-padding_narrow > .column {
    width: auto;
  }

  .c-sp-padding_normal > .column {
    width: auto;
  }

  .c-sp-padding_wide > .column {
    width: auto;
  }

  .c-sp-padding_highest > .column {
    width: auto;
  }
}

.c-left {
  text-align: left;
}

.c-right {
  text-align: right;
}

.c-center {
  text-align: center;
}

.c-fleft {
  float: left;
}

.c-fright {
  float: right;
}

.c-no_adjust {
  width: 100%;
}

.c-img {
  display: inline;
}

.c-images {
  font-size: 0;
  line-height: 0;
}

.c-images .c-img_comment {
  line-height: 1.5;
}

.c-img_comment {
  font-size: 14px;
  font-size: 1rem;
}

.c-fleft {
  float: left;
  padding: 0 10px 10px 0;
}

.c-fright {
  float: right;
  padding: 0 0 10px 10px;
}

.c-hr {
  margin: 10px 0;
  border: 0 none;
  border-top: 1px solid #999;
}

.c-menu .c-sp-closer {
  display: none;
}

@media only screen and (max-width: 768px) {
  .c-menu .c-sp-closer {
    display: block;
  }
}

.c-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  *zoom: 1;
  filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0,startColorstr='#80000000',endColorstr='#80000000');
  background: transparent;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99999;
}

:root .c-overlay {
  -webkit-filter: none\0 / IE9;
  filter: none\0 / IE9;
}

.c-overlay_outerBox {
  position: absolute;
  background: #fff;
  padding: 10px;
  box-shadow: black 0 0 5px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.c-overlay_commentBox {
  padding-top: 5px;
}

.c-overlay-next,
.c-overlay-prev,
.c-overlay-close_btn {
  position: absolute;
  top: 50%;
  left: -10px;
  margin-top: -10px;
  font-size: 40px;
  cursor: pointer;
}

.c-overlay-next {
  left: auto;
  right: -10px;
}

.c-overlay-close_btn {
  top: 20px;
  right: 10px;
  left: auto;
  width: 30px;
  height: 30px;
  line-height: 30px;
  font-size: 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.5);
}

.c-popup_iframe {
  display: block;
  width: 100%;
  height: 100%;
}

.c-sound_btn1,
.c-sound_btn2,
.c-sound_btn3,
.c-sound_btn4 {
  cursor: pointer;
  display: inline-block;
}

.c-sound_btn1 {
  background: url(../images/sound01.png) no-repeat 50% -30px;
  width: 60px;
  height: 30px;
}

.c-sound_btn1.on {
  background-position: 50% 0;
}

.c-sound_btn2 {
  background: url(../images/sound02.png) no-repeat 50% -23px;
  width: 60px;
  height: 23px;
}

.c-sound_btn2.on {
  background-position: 50% 0;
}

.c-sound_btn3 {
  background: url(../images/sound03.png) no-repeat 50% -29px;
  width: 100px;
  height: 29px;
}

.c-sound_btn3.on {
  background-position: 50% 0;
}

.c-sound_btn4 {
  background: url(../images/sound04.png) no-repeat 50% -11px;
  width: 102px;
  height: 11px;
}

.c-sound_btn4.on {
  background-position: 50% 0;
}

@media only screen and (max-width: 640px) {
  .c-sound_btn1,
  .c-sound_btn2,
  .c-sound_btn3,
  .c-sound_btn4 {
    display: none;
  }
}

.c-device_outer {
  background: rgba(0, 0, 0, 0.75);
  padding: 1em;
}

.c-device_changer {
  box-sizing: border-box;
  width: 100%;
  margin: 0;
  padding: 0.5em 1em;
  background: transparent;
  border: 1px solid #fff;
  border-radius: 3px;
  text-align: center;
  font-size: 14px;
  font-size: 1rem;
  font-family: inherit;
  line-height: 1.6;
  color: #fff;
  text-decoration: none;
  cursor: pointer;
}

.c-page_title {
  font-weight: 700;
  font-size: 32px;
  font-size: 30px;
  font-size: 2.2rem;
  line-height: 1.8;
}

.c-title {
  font-weight: 700;
  font-size: 26px;
  font-size: 1.9rem;
  line-height: 1.6;
}

.c-large_headline {
  font-weight: 700;
  font-size: 22px;
  font-size: 1.6rem;
  line-height: 1.6;
}

.c-small_headline {
  font-weight: 700;
  font-size: 18px;
  font-size: 1.3rem;
}

.c-lead {
  font-size: 15px;
  font-size: 1.1rem;
}

.c-blockquote:before,
.c-blockquote:after {
  content: '"';
  color: inherit;
}

.c-blockquote:before {
  margin-right: 5px;
}

.c-blockquote:after {
  margin-left: 5px;
}

.c-enclosure {
  border: 1px solid #555;
  padding: 1em;
}

.c-note {
  font-size: 10px;
  font-size: 9px;
  font-size: 0.7rem;
}

.c-credit {
  font-size: 10px;
  font-size: 9px;
  font-size: 0.7rem;
}

.c-html:before,
.c-html:after {
  display: table;
  line-height: 0;
  content: '';
}

.c-html:after {
  clear: both;
}

.c-list-no_mark ul,
.c-list-no_mark ol,
.c-list-sign ul,
.c-list-sign ol,
.c-list-numbers ul,
.c-list-numbers ol,
.c-list-alphabet ul,
.c-list-alphabet ol,
.c-list-alphabet_small ul,
.c-list-alphabet_small ol,
.c-list-greece ul,
.c-list-greece ol,
.c-list-greece_small ul,
.c-list-greece_small ol {
  margin-left: 15px;
}

.c-list-no_mark {
  list-style: none;
}

.c-list-no_mark ul,
.c-list-no_mark ol,
.c-list-no_mark > li {
  list-style: none;
}

.c-list-sign {
  list-style-type: disc;
  margin-left: 1.5em;
}

.c-list-sign ul,
.c-list-sign ol,
.c-list-sign > li {
  list-style: disc;
}

.c-list-numbers {
  list-style-type: decimal;
}

.c-list-numbers ul,
.c-list-numbers ol,
.c-list-numbers > li {
  list-style: decimal;
}

.c-list-alphabet {
  list-style-type: upper-latin;
}

.c-list-alphabet ul,
.c-list-alphabet ol,
.c-list-alphabet > li {
  list-style: upper-latin;
}

.c-list-alphabet_small {
  list-style-type: lower-latin;
}

.c-list-alphabet_small ul,
.c-list-alphabet_small ol,
.c-list-alphabet_small > li {
  list-style: lower-latin;
}

.c-list-greece {
  list-style-type: upper-roman;
}

.c-list-greece ul,
.c-list-greece ol,
.c-list-greece > li {
  list-style: upper-roman;
}

.c-list-greece_small {
  list-style-type: lower-roman;
}

.c-list-greece_small ul,
.c-list-greece_small ol,
.c-list-greece_small > li {
  list-style: lower-roman;
}

.c-list-numbers,
.c-list-alphabet,
.c-list-alphabet_small,
.c-list-greece,
.c-list-greece_small {
  margin-left: 1.5em;
}

.c-list-no_mark ul,
.c-list-numbers ol,
.c-list-alphabet ol,
.c-list-alphabet_small ol,
.c-list-greece ol,
.c-list-greece_small ol {
  margin-left: 1.5em;
}

.d-bold {
  font-weight: 700;
}

.d-italic {
  font-style: italic;
}

.d-underline {
  text-decoration: underline;
}

.d-negative_line {
  text-decoration: line-through;
}

.d-largest_font {
  font-size: 190%;
  line-height: 1.6;
}

.d-larger_font {
  font-size: 160%;
  line-height: 1.6;
}

.d-large_font {
  font-size: 130%;
}

.d-small_font {
  font-size: 85%;
}

.d-smallest_font {
  font-size: 70%;
}

.d-gothic {
  font-family:
    Helvetica Neue,
    Helvetica,
    YuGothic,
    Yu Gothic,
    'ヒラギノ角ゴ Pro W3',
    Hiragino Kaku Gothic Pro,
    'メイリオ',
    Meiryo,
    sans-serif;
}

.d-ming {
  font-family:
    YuMincho,
    Yu Mincho,
    '游明朝',
    'ヒラギノ明朝 ProN W6',
    HiraMinProN-W6,
    'HG明朝E',
    'ＭＳ Ｐ明朝',
    MS PMincho,
    'MS 明朝',
    serif;
}

.-bg_loading {
  background: url(../images/loading.gif) center center no-repeat;
  min-width: 32px;
  min-height: 32px;
}

.c-list_news {
  margin-bottom: 15px;
  border-top: 1px solid #999;
}

.c-list_news th,
.c-list_news td {
  border-bottom: 1px solid #999;
  padding: 1em;
}

.c-list_news > tr > th,
.c-list_news > tbody > tr > th {
  font-weight: 700;
  width: 25%;
}

@media only screen and (max-width: 640px) {
  .c-list_news > tr > th,
  .c-list_news > tbody > tr > th {
    display: block;
    width: 100%;
    float: left\9;
  }
}

.c-list_news > tr > td,
.c-list_news > tbody > tr > td {
  width: 75%;
}

@media only screen and (max-width: 640px) {
  .c-list_news > tr > td,
  .c-list_news > tbody > tr > td {
    display: block;
    padding-left: 0;
    width: 100%;
    float: left\9;
  }
}

.c-list_indent {
  margin-bottom: 15px;
}

.c-list_indent th,
.c-list_indent td {
  width: 100%;
  display: block;
}

.c-list_indent th {
  border-bottom: 1px solid #999;
}

.c-list_indent td {
  padding-left: 15px;
  padding-bottom: 15px;
}

:root .c-list_indent th,
:root .c-list_indent td {
  float: left;
}

.c-list_table {
  border: 1px solid #999;
  margin-bottom: 15px;
}

.c-list_table th,
.c-list_table td {
  padding: 7px 10px;
  border-top: 1px solid #999;
}

@media only screen and (max-width: 640px) {
  .c-list_table th,
  .c-list_table td {
    display: block;
    width: 100%;
  }
}

.c-list_table th {
  border-right: 1px solid #999;
}

@media only screen and (max-width: 640px) {
  .c-list_table th {
    border-right: 0 none;
    border-bottom: 1px solid #999;
  }
}

.c-list_table tr:first-child th,
.c-list_table tr:first-child td {
  border-top: 0 none;
}

.c-list_table > td,
.c-list_table > tbody > td {
  width: 82%;
}

@media only screen and (max-width: 640px) {
  .c-list_table > td,
  .c-list_table > tbody > td {
    border-top: 0 none;
    width: 100%;
  }
}

.c-list_news,
.c-list_indent,
.c-list_table {
  width: 100%;
}

.c-list_news .c-affix,
.c-list_indent .c-affix,
.c-list_table .c-affix {
  padding-left: 10px;
  font-size: 90%;
}

.c-breadcrumb li {
  display: inline-block;
  padding-right: 5px;
}

.c-breadcrumb li a {
  text-decoration: none;
}

.c-list_news-pager {
  margin-top: 10px;
  padding-bottom: 30px;
}

.c-site_logo a {
  border-bottom: 0 none;
}

.m-motion:before,
.m-motion:after {
  display: table;
  line-height: 0;
  content: '';
}

.m-motion:after {
  clear: both;
}

.m-motion.-f .c-sp-closer,
.m-motion.-g .c-sp-closer,
.m-motion.-h .c-sp-closer,
.m-motion.-i .c-sp-closer,
.m-motion.-j .c-sp-closer {
  display: none;
}

.m-motion.-g li ul,
.m-motion.-h li ul,
.m-motion.-i li ul,
.m-motion.-j li ul {
  display: none;
}

@media only screen and (max-width: 768px) {
  .m-motion .lavalamp-object {
    display: none;
  }
}

.m-motion a {
  color: #fff;
  border-bottom: 0 none;
}

.m-motion .c-unlink {
  color: #999;
}

.m-motion.-f {
  position: relative;
}

.m-motion.-f li {
  border: 0 none;
}

.m-motion.-f li a {
  text-align: center;
  background: transparent;
  color: #555;
}

.m-motion.-f > li {
  margin-left: 20px;
  margin-right: 20px;
  float: left;
  position: relative;
  z-index: 1;
}

@media only screen and (max-width: 768px) {
  .m-motion.-f > li {
    margin-left: 0;
    margin-right: 0;
  }
}

.m-motion.-f > li a {
  color: #333;
}

.m-motion.-f > li a:hover {
  background-color: #bbb;
  border-bottom: 1px solid #555;
  color: inherit;
}

@media only screen and (max-width: 768px) {
  .m-motion.-f > li a:hover {
    background-color: transparent;
    border-bottom: 0 none;
  }
}

.m-motion.-f > li > a + ul:before,
.m-motion.-f > li .c-unlink + ul:before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border: 5px solid transparent;
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-top-color: #999;
}

@media only screen and (max-width: 768px) {
  .m-motion.-f > li > a + ul:before,
  .m-motion.-f > li .c-unlink + ul:before {
    content: none;
  }
}

.m-motion.-f > li:hover {
  z-index: 2;
}

.m-motion.-f > li:hover > ul {
  height: auto;
}

.m-motion.-f > li:hover > ul li:first-child:before {
  display: block;
}

@media only screen and (max-width: 768px) {
  .m-motion.-f > li:hover > ul li:first-child:before {
    display: none;
  }
}

.m-motion.-f > li:hover > ul > li a,
.m-motion.-f > li:hover > ul > li .c-unlink {
  height: auto;
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
  opacity: 1;
  filter: alpha(opacity=100);
  padding: 10px 5px;
  border-bottom: 1px solid #fff;
}

@media only screen and (max-width: 768px) {
  .m-motion.-f > li:hover > ul > li a,
  .m-motion.-f > li:hover > ul > li .c-unlink {
    padding: 0;
    border-bottom: 0 none;
  }
}

.m-motion.-f > li:hover > a + ul > li:first-child:after,
.m-motion.-f > li:hover > .c-unlink + ul > li:first-child:after {
  border-top-color: #999;
}

.m-motion.-f > li > ul {
  width: 150px;
  position: absolute;
  left: 50%;
  margin-left: -75px;
  padding: 0;
  height: 0;
}

@media only screen and (max-width: 768px) {
  .m-motion.-f > li > ul {
    width: 100%;
    margin-left: 0;
    position: static;
    left: auto;
    height: auto;
  }
}

.m-motion.-f > li > ul:hover {
  height: auto;
}

.m-motion.-f > li > ul > li {
  display: block;
  float: none;
  margin-left: 0;
  padding: 0;
}

@media only screen and (max-width: 768px) {
  .m-motion.-f > li > ul > li {
    padding-left: 20px;
    border-bottom: 0 none;
    border-top: 1px solid #aaa;
  }
}

.m-motion.-f > li > ul > li a,
.m-motion.-f > li > ul > li .c-unlink {
  display: block;
  background-color: #ddd;
  border-bottom: 1px solid #eee;
  font-weight: 400;
  text-align: center;
  height: 0;
  padding: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  overflow: hidden;
  white-space: normal;
  word-wrap: break-word;
  transition-property: all;
  transition-duration: 0.2s;
  transition-timing-function: ease-in-out;
}

@media only screen and (max-width: 768px) {
  .m-motion.-f > li > ul > li a,
  .m-motion.-f > li > ul > li .c-unlink {
    height: auto;
    opacity: 1;
    filter: alpha(opacity=100);
    background-color: transparent;
    border-bottom: 0 none;
    text-align: left;
  }
}

.m-motion.-f > li > ul li:first-child {
  padding-top: 15px;
}

@media only screen and (max-width: 768px) {
  .m-motion.-f > li > ul li:first-child {
    padding-top: 0;
  }
}

.m-motion.-f > li > ul li:first-child:before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border: 7px solid transparent;
  border-bottom-color: #ddd;
  top: 2px;
  left: 50%;
  margin-left: -7px;
  display: none;
}

.m-motion.-f li ul li ul {
  display: none;
}

.m-motion.-g {
  background: #000;
  position: relative;
  padding: 15px;
  margin: 10px 0;
  overflow: hidden;
  background-color: transparent;
}

.m-motion.-g li {
  float: left;
  line-height: 30px;
  color: #555;
}

@media only screen and (max-width: 768px) {
  .m-motion.-g li {
    float: none;
    width: 100%;
    display: block;
  }
}

.m-motion.-g li a {
  background: transparent;
  position: relative;
  overflow: hidden;
  text-align: center;
  height: 30px;
  z-index: 10;
  letter-spacing: 1px;
  display: block;
  margin: auto 10px;
  color: #333;
  padding: 0 25px;
}

@media only screen and (max-width: 768px) {
  .m-motion.-g li a {
    width: 100%;
  }
}

.m-motion.-g li .c-unlink {
  height: 30px;
  letter-spacing: 1px;
  display: block;
  margin: auto 10px;
  padding: 0 25px;
  text-align: center;
}

.m-motion.-g .lavalamp-object {
  background-color: #dcdcdc;
  border: none;
}

.m-motion.-g.-blue {
  position: relative;
  z-index: 5;
  background-color: #4fbcd5;
  padding: 15px;
  margin: 10px 0;
  overflow: hidden;
  border-top: 1px solid #44a0b7;
  border-bottom: 1px solid #44a0b7;
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4gPHN2ZyB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PGxpbmVhckdyYWRpZW50IGlkPSJncmFkIiBncmFkaWVudFVuaXRzPSJvYmplY3RCb3VuZGluZ0JveCIgeDE9IjAuNSIgeTE9IjAuMCIgeDI9IjAuNSIgeTI9IjEuMCI+PHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzNiYjJjZiIvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzRmYmNkNSIvPjwvbGluZWFyR3JhZGllbnQ+PC9kZWZzPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZCkiIC8+PC9zdmc+IA==);
  background-size: 100%;
  background-image: linear-gradient(#3bb2cf, #4fbcd5);
}

.m-motion.-h {
  position: relative;
  z-index: 5;
}

.m-motion.-h li a {
  display: block;
  width: 150px;
  padding: 13px 15px;
  margin-bottom: 3px;
  border-radius: 3px;
  background-color: #bbb;
  color: #555;
}

@media only screen and (max-width: 768px) {
  .m-motion.-h li a {
    border-radius: 0;
    width: 100%;
  }
}

.m-motion.-h li a:hover {
  transition-property: all;
  transition-duration: 0.2s;
  transition-timing-function: ease-in;
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #ddd;
  width: 170px;
  padding-left: 40px;
}

@media only screen and (max-width: 768px) {
  .m-motion.-h li a:hover {
    width: 100%;
  }
}

.m-motion.-h li .c-unlink {
  display: block;
  width: 150px;
  padding: 13px 15px;
  margin-bottom: 3px;
}

.m-motion.-h.-blue li a {
  background: #127ba5;
}

.m-motion.-i {
  position: relative;
  z-index: 5;
}

.m-motion.-i li {
  margin-bottom: 3px;
}

.m-motion.-i li a {
  border: 1px solid #666;
  border-bottom-width: 3px;
  border-radius: 3px;
  background: #bbb;
  padding: 15px 20px;
  display: inline-block;
  color: #555;
  transition-property: all;
  transition-duration: 0.2s;
  transition-timing-function: ease-in;
}

@media only screen and (max-width: 768px) {
  .m-motion.-i li a {
    display: block;
    border-radius: 0;
  }
}

.m-motion.-i li a:hover {
  border-color: transparent;
  border-radius: 3px;
  padding-left: 30px;
  background-color: #bbb;
}

@media only screen and (max-width: 768px) {
  .m-motion.-i li a:hover {
    border-radius: 0;
    width: 100%;
  }
}

.m-motion.-i li .c-unlink {
  display: block;
  padding: 15px 20px;
}

.m-motion.-i.-blue li a {
  border: 1px solid #d8e5eb;
  border-bottom: 3px solid #96b9c8;
  background: #b4cdd7;
}

.m-motion.-i.-blue li a:hover {
  border: 1px solid #bde4f8;
  border-bottom: 3px solid #47afea;
  background-color: #81cef1;
}

.m-motion.-j li {
  float: left;
  padding-top: 0.6em;
}

.m-motion.-j li a {
  padding: 0 35px;
  color: #555;
}

.m-motion.-j li a:hover {
  background: transparent;
}

.m-motion.-j li .c-unlink {
  display: block;
  padding: 0 35px;
}

@media only screen and (max-width: 768px) {
  .m-motion.-j li {
    float: none;
    width: 100%;
    display: block;
  }
}

.m-motion.-j .lavalamp-object {
  border-top: 1px solid #999;
}

@media only screen and (max-width: 768px) {
  .m-motion.-f .c-sp-closer,
  .m-motion.-g .c-sp-closer,
  .m-motion.-h .c-sp-closer,
  .m-motion.-i .c-sp-closer,
  .m-motion.-j .c-sp-closer {
    display: block;
  }

  .m-motion.-f,
  .m-motion.-g,
  .m-motion.-h,
  .m-motion.-i,
  .m-motion.-j {
    padding: 0;
    margin: 0;
  }

  .m-motion.-f li,
  .m-motion.-g li,
  .m-motion.-h li,
  .m-motion.-i li,
  .m-motion.-j li {
    width: 100%;
    display: block;
    border: 0 none;
    border-bottom: 1px solid;
  }

  .m-motion.-f li:first-child,
  .m-motion.-g li:first-child,
  .m-motion.-h li:first-child,
  .m-motion.-i li:first-child,
  .m-motion.-j li:first-child {
    border-left: 0 none;
  }

  .m-motion.-f li a,
  .m-motion.-g li a,
  .m-motion.-h li a,
  .m-motion.-i li a,
  .m-motion.-j li a {
    color: #555;
  }

  .m-motion.-f li .c-unlink,
  .m-motion.-g li .c-unlink,
  .m-motion.-h li .c-unlink,
  .m-motion.-i li .c-unlink,
  .m-motion.-j li .c-unlink {
    color: #999;
  }
}

.js-slide {
  visibility: hidden;
  display: inline-block;
  overflow: hidden;
  position: relative;
  width: 100%;
}

.js-slide > span {
  display: none;
}

.js-slide:before {
  content: '';
  display: block;
}

.js-slide.s-slide-aspect_1:before {
  padding-top: 1%;
}

.js-slide.s-slide-aspect_2:before {
  padding-top: 2%;
}

.js-slide.s-slide-aspect_3:before {
  padding-top: 3%;
}

.js-slide.s-slide-aspect_4:before {
  padding-top: 4%;
}

.js-slide.s-slide-aspect_5:before {
  padding-top: 5%;
}

.js-slide.s-slide-aspect_6:before {
  padding-top: 6%;
}

.js-slide.s-slide-aspect_7:before {
  padding-top: 7%;
}

.js-slide.s-slide-aspect_8:before {
  padding-top: 8%;
}

.js-slide.s-slide-aspect_9:before {
  padding-top: 9%;
}

.js-slide.s-slide-aspect_10:before {
  padding-top: 10%;
}

.js-slide.s-slide-aspect_11:before {
  padding-top: 11%;
}

.js-slide.s-slide-aspect_12:before {
  padding-top: 12%;
}

.js-slide.s-slide-aspect_13:before {
  padding-top: 13%;
}

.js-slide.s-slide-aspect_14:before {
  padding-top: 14%;
}

.js-slide.s-slide-aspect_15:before {
  padding-top: 15%;
}

.js-slide.s-slide-aspect_16:before {
  padding-top: 16%;
}

.js-slide.s-slide-aspect_17:before {
  padding-top: 17%;
}

.js-slide.s-slide-aspect_18:before {
  padding-top: 18%;
}

.js-slide.s-slide-aspect_19:before {
  padding-top: 19%;
}

.js-slide.s-slide-aspect_20:before {
  padding-top: 20%;
}

.js-slide.s-slide-aspect_21:before {
  padding-top: 21%;
}

.js-slide.s-slide-aspect_22:before {
  padding-top: 22%;
}

.js-slide.s-slide-aspect_23:before {
  padding-top: 23%;
}

.js-slide.s-slide-aspect_24:before {
  padding-top: 24%;
}

.js-slide.s-slide-aspect_25:before {
  padding-top: 25%;
}

.js-slide.s-slide-aspect_26:before {
  padding-top: 26%;
}

.js-slide.s-slide-aspect_27:before {
  padding-top: 27%;
}

.js-slide.s-slide-aspect_28:before {
  padding-top: 28%;
}

.js-slide.s-slide-aspect_29:before {
  padding-top: 29%;
}

.js-slide.s-slide-aspect_30:before {
  padding-top: 30%;
}

.js-slide.s-slide-aspect_31:before {
  padding-top: 31%;
}

.js-slide.s-slide-aspect_32:before {
  padding-top: 32%;
}

.js-slide.s-slide-aspect_33:before {
  padding-top: 33%;
}

.js-slide.s-slide-aspect_34:before {
  padding-top: 34%;
}

.js-slide.s-slide-aspect_35:before {
  padding-top: 35%;
}

.js-slide.s-slide-aspect_36:before {
  padding-top: 36%;
}

.js-slide.s-slide-aspect_37:before {
  padding-top: 37%;
}

.js-slide.s-slide-aspect_38:before {
  padding-top: 38%;
}

.js-slide.s-slide-aspect_39:before {
  padding-top: 39%;
}

.js-slide.s-slide-aspect_40:before {
  padding-top: 40%;
}

.js-slide.s-slide-aspect_41:before {
  padding-top: 41%;
}

.js-slide.s-slide-aspect_42:before {
  padding-top: 42%;
}

.js-slide.s-slide-aspect_43:before {
  padding-top: 45%;
}

.js-slide.s-slide-aspect_44:before {
  padding-top: 44%;
}

.js-slide.s-slide-aspect_45:before {
  padding-top: 45%;
}

.js-slide.s-slide-aspect_46:before {
  padding-top: 46%;
}

.js-slide.s-slide-aspect_47:before {
  padding-top: 47%;
}

.js-slide.s-slide-aspect_48:before {
  padding-top: 48%;
}

.js-slide.s-slide-aspect_49:before {
  padding-top: 49%;
}

.js-slide.s-slide-aspect_50:before {
  padding-top: 50%;
}

.js-slide.s-slide-aspect_51:before {
  padding-top: 51%;
}

.js-slide.s-slide-aspect_52:before {
  padding-top: 52%;
}

.js-slide.s-slide-aspect_53:before {
  padding-top: 53%;
}

.js-slide.s-slide-aspect_54:before {
  padding-top: 54%;
}

.js-slide.s-slide-aspect_55:before {
  padding-top: 55%;
}

.js-slide.s-slide-aspect_56:before {
  padding-top: 56%;
}

.js-slide.s-slide-aspect_57:before {
  padding-top: 57%;
}

.js-slide.s-slide-aspect_58:before {
  padding-top: 58%;
}

.js-slide.s-slide-aspect_59:before {
  padding-top: 59%;
}

.js-slide.s-slide-aspect_60:before {
  padding-top: 60%;
}

.js-slide.s-slide-aspect_61:before {
  padding-top: 61%;
}

.js-slide.s-slide-aspect_62:before {
  padding-top: 62%;
}

.js-slide.s-slide-aspect_63:before {
  padding-top: 63%;
}

.js-slide.s-slide-aspect_64:before {
  padding-top: 64%;
}

.js-slide.s-slide-aspect_65:before {
  padding-top: 65%;
}

.js-slide.s-slide-aspect_66:before {
  padding-top: 66%;
}

.js-slide.s-slide-aspect_67:before {
  padding-top: 67%;
}

.js-slide.s-slide-aspect_68:before {
  padding-top: 68%;
}

.js-slide.s-slide-aspect_69:before {
  padding-top: 69%;
}

.js-slide.s-slide-aspect_70:before {
  padding-top: 70%;
}

.js-slide.s-slide-aspect_71:before {
  padding-top: 71%;
}

.js-slide.s-slide-aspect_72:before {
  padding-top: 72%;
}

.js-slide.s-slide-aspect_73:before {
  padding-top: 73%;
}

.js-slide.s-slide-aspect_74:before {
  padding-top: 74%;
}

.js-slide.s-slide-aspect_75:before {
  padding-top: 75%;
}

.js-slide.s-slide-aspect_76:before {
  padding-top: 76%;
}

.js-slide.s-slide-aspect_77:before {
  padding-top: 77%;
}

.js-slide.s-slide-aspect_78:before {
  padding-top: 78%;
}

.js-slide.s-slide-aspect_79:before {
  padding-top: 79%;
}

.js-slide.s-slide-aspect_80:before {
  padding-top: 80%;
}

.js-slide.s-slide-aspect_81:before {
  padding-top: 81%;
}

.js-slide.s-slide-aspect_82:before {
  padding-top: 82%;
}

.js-slide.s-slide-aspect_83:before {
  padding-top: 83%;
}

.js-slide.s-slide-aspect_84:before {
  padding-top: 84%;
}

.js-slide.s-slide-aspect_85:before {
  padding-top: 85%;
}

.js-slide.s-slide-aspect_86:before {
  padding-top: 86%;
}

.js-slide.s-slide-aspect_87:before {
  padding-top: 87%;
}

.js-slide.s-slide-aspect_88:before {
  padding-top: 88%;
}

.js-slide.s-slide-aspect_89:before {
  padding-top: 89%;
}

.js-slide.s-slide-aspect_90:before {
  padding-top: 90%;
}

.js-slide.s-slide-aspect_91:before {
  padding-top: 91%;
}

.js-slide.s-slide-aspect_92:before {
  padding-top: 92%;
}

.js-slide.s-slide-aspect_93:before {
  padding-top: 93%;
}

.js-slide.s-slide-aspect_94:before {
  padding-top: 94%;
}

.js-slide.s-slide-aspect_95:before {
  padding-top: 95%;
}

.js-slide.s-slide-aspect_96:before {
  padding-top: 96%;
}

.js-slide.s-slide-aspect_97:before {
  padding-top: 97%;
}

.js-slide.s-slide-aspect_98:before {
  padding-top: 98%;
}

.js-slide.s-slide-aspect_99:before {
  padding-top: 99%;
}

.js-slide.s-slide-aspect_100:before {
  padding-top: 100%;
}

.js-slide.s-slide-aspect_101:before {
  padding-top: 101%;
}

.js-slide.s-slide-aspect_102:before {
  padding-top: 102%;
}

.js-slide.s-slide-aspect_103:before {
  padding-top: 103%;
}

.js-slide.s-slide-aspect_104:before {
  padding-top: 104%;
}

.js-slide.s-slide-aspect_105:before {
  padding-top: 105%;
}

.js-slide.s-slide-aspect_106:before {
  padding-top: 106%;
}

.js-slide.s-slide-aspect_107:before {
  padding-top: 107%;
}

.js-slide.s-slide-aspect_108:before {
  padding-top: 108%;
}

.js-slide.s-slide-aspect_109:before {
  padding-top: 109%;
}

.js-slide.s-slide-aspect_110:before {
  padding-top: 110%;
}

.js-slide.s-slide-aspect_111:before {
  padding-top: 111%;
}

.js-slide.s-slide-aspect_112:before {
  padding-top: 112%;
}

.js-slide.s-slide-aspect_113:before {
  padding-top: 113%;
}

.js-slide.s-slide-aspect_114:before {
  padding-top: 114%;
}

.js-slide.s-slide-aspect_115:before {
  padding-top: 115%;
}

.js-slide.s-slide-aspect_116:before {
  padding-top: 116%;
}

.js-slide.s-slide-aspect_117:before {
  padding-top: 117%;
}

.js-slide.s-slide-aspect_118:before {
  padding-top: 118%;
}

.js-slide.s-slide-aspect_119:before {
  padding-top: 119%;
}

.js-slide.s-slide-aspect_120:before {
  padding-top: 120%;
}

.js-slide.s-slide-aspect_121:before {
  padding-top: 121%;
}

.js-slide.s-slide-aspect_122:before {
  padding-top: 122%;
}

.js-slide.s-slide-aspect_123:before {
  padding-top: 123%;
}

.js-slide.s-slide-aspect_124:before {
  padding-top: 124%;
}

.js-slide.s-slide-aspect_125:before {
  padding-top: 125%;
}

.js-slide.s-slide-aspect_126:before {
  padding-top: 126%;
}

.js-slide.s-slide-aspect_127:before {
  padding-top: 127%;
}

.js-slide.s-slide-aspect_128:before {
  padding-top: 128%;
}

.js-slide.s-slide-aspect_129:before {
  padding-top: 129%;
}

.js-slide.s-slide-aspect_130:before {
  padding-top: 130%;
}

.js-slide.s-slide-aspect_131:before {
  padding-top: 131%;
}

.js-slide.s-slide-aspect_132:before {
  padding-top: 132%;
}

.js-slide.s-slide-aspect_133:before {
  padding-top: 133%;
}

.js-slide.s-slide-aspect_134:before {
  padding-top: 134%;
}

.js-slide.s-slide-aspect_135:before {
  padding-top: 135%;
}

.js-slide.s-slide-aspect_136:before {
  padding-top: 136%;
}

.js-slide.s-slide-aspect_137:before {
  padding-top: 137%;
}

.js-slide.s-slide-aspect_138:before {
  padding-top: 138%;
}

.js-slide.s-slide-aspect_139:before {
  padding-top: 139%;
}

.js-slide.s-slide-aspect_140:before {
  padding-top: 140%;
}

.js-slide.s-slide-aspect_141:before {
  padding-top: 141%;
}

.js-slide.s-slide-aspect_142:before {
  padding-top: 142%;
}

.js-slide.s-slide-aspect_143:before {
  padding-top: 145%;
}

.js-slide.s-slide-aspect_144:before {
  padding-top: 144%;
}

.js-slide.s-slide-aspect_145:before {
  padding-top: 145%;
}

.js-slide.s-slide-aspect_146:before {
  padding-top: 146%;
}

.js-slide.s-slide-aspect_147:before {
  padding-top: 147%;
}

.js-slide.s-slide-aspect_148:before {
  padding-top: 148%;
}

.js-slide.s-slide-aspect_149:before {
  padding-top: 149%;
}

.js-slide.s-slide-aspect_150:before {
  padding-top: 150%;
}

.js-slide.s-slide-aspect_151:before {
  padding-top: 151%;
}

.js-slide.s-slide-aspect_152:before {
  padding-top: 152%;
}

.js-slide.s-slide-aspect_153:before {
  padding-top: 153%;
}

.js-slide.s-slide-aspect_154:before {
  padding-top: 154%;
}

.js-slide.s-slide-aspect_155:before {
  padding-top: 155%;
}

.js-slide.s-slide-aspect_156:before {
  padding-top: 156%;
}

.js-slide.s-slide-aspect_157:before {
  padding-top: 157%;
}

.js-slide.s-slide-aspect_158:before {
  padding-top: 158%;
}

.js-slide.s-slide-aspect_159:before {
  padding-top: 159%;
}

.js-slide.s-slide-aspect_160:before {
  padding-top: 160%;
}

.js-slide.s-slide-aspect_161:before {
  padding-top: 161%;
}

.js-slide.s-slide-aspect_162:before {
  padding-top: 162%;
}

.js-slide.s-slide-aspect_163:before {
  padding-top: 163%;
}

.js-slide.s-slide-aspect_164:before {
  padding-top: 164%;
}

.js-slide.s-slide-aspect_165:before {
  padding-top: 165%;
}

.js-slide.s-slide-aspect_166:before {
  padding-top: 166%;
}

.js-slide.s-slide-aspect_167:before {
  padding-top: 167%;
}

.js-slide.s-slide-aspect_168:before {
  padding-top: 168%;
}

.js-slide.s-slide-aspect_169:before {
  padding-top: 169%;
}

.js-slide.s-slide-aspect_170:before {
  padding-top: 170%;
}

.js-slide.s-slide-aspect_171:before {
  padding-top: 171%;
}

.js-slide.s-slide-aspect_172:before {
  padding-top: 172%;
}

.js-slide.s-slide-aspect_173:before {
  padding-top: 173%;
}

.js-slide.s-slide-aspect_174:before {
  padding-top: 174%;
}

.js-slide.s-slide-aspect_175:before {
  padding-top: 175%;
}

.js-slide.s-slide-aspect_176:before {
  padding-top: 176%;
}

.js-slide.s-slide-aspect_177:before {
  padding-top: 177%;
}

.js-slide.s-slide-aspect_178:before {
  padding-top: 178%;
}

.js-slide.s-slide-aspect_179:before {
  padding-top: 179%;
}

.js-slide.s-slide-aspect_180:before {
  padding-top: 180%;
}

.js-slide.s-slide-aspect_181:before {
  padding-top: 181%;
}

.js-slide.s-slide-aspect_182:before {
  padding-top: 182%;
}

.js-slide.s-slide-aspect_183:before {
  padding-top: 183%;
}

.js-slide.s-slide-aspect_184:before {
  padding-top: 184%;
}

.js-slide.s-slide-aspect_185:before {
  padding-top: 185%;
}

.js-slide.s-slide-aspect_186:before {
  padding-top: 186%;
}

.js-slide.s-slide-aspect_187:before {
  padding-top: 187%;
}

.js-slide.s-slide-aspect_188:before {
  padding-top: 188%;
}

.js-slide.s-slide-aspect_189:before {
  padding-top: 189%;
}

.js-slide.s-slide-aspect_190:before {
  padding-top: 190%;
}

.js-slide.s-slide-aspect_191:before {
  padding-top: 191%;
}

.js-slide.s-slide-aspect_192:before {
  padding-top: 192%;
}

.js-slide.s-slide-aspect_193:before {
  padding-top: 193%;
}

.js-slide.s-slide-aspect_194:before {
  padding-top: 194%;
}

.js-slide.s-slide-aspect_195:before {
  padding-top: 195%;
}

.js-slide.s-slide-aspect_196:before {
  padding-top: 196%;
}

.js-slide.s-slide-aspect_197:before {
  padding-top: 197%;
}

.js-slide.s-slide-aspect_198:before {
  padding-top: 198%;
}

.js-slide.s-slide-aspect_199:before {
  padding-top: 199%;
}

.js-slide.s-slide-aspect_200:before {
  padding-top: 200%;
}

.c-cart_number_input {
  width: 40px;
  background: #fff;
  border: 1px solid #ddd;
  font-family:
    Helvetica Neue,
    Helvetica,
    YuGothic,
    Yu Gothic,
    'ヒラギノ角ゴ Pro W3',
    Hiragino Kaku Gothic Pro,
    'メイリオ',
    Meiryo,
    'ＭＳＰゴシック',
    sans-serif;
}

.c-cart_table {
  width: 100%;
}

.c-cart_table th,
.c-cart_table td {
  font-family:
    Helvetica Neue,
    Helvetica,
    YuGothic,
    Yu Gothic,
    'ヒラギノ角ゴ Pro W3',
    Hiragino Kaku Gothic Pro,
    'メイリオ',
    Meiryo,
    'ＭＳＰゴシック',
    sans-serif;
}

.c-cart_table th {
  border-left: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  padding: 10px;
  text-align: center;
}

.c-cart_table th:first-child {
  border-left: 0 none;
}

.c-cart_table td {
  border-left: 1px solid #ddd;
  padding: 10px;
  text-align: center;
}

.c-cart_table td:first-child {
  border-left: 0 none;
}

.c-cart_table.c-list_indent th,
.c-cart_table.c-list_indent td {
  border: 0 none;
}

.c-cart_table.c-list_indent th {
  width: 40%;
}

.c-cart_table.c-list_indent td {
  width: 60%;
}

.c-cart_buying_area {
  background: #e5e5e5;
  padding: 15px;
  font-family:
    Helvetica Neue,
    Helvetica,
    YuGothic,
    Yu Gothic,
    'ヒラギノ角ゴ Pro W3',
    Hiragino Kaku Gothic Pro,
    'メイリオ',
    Meiryo,
    'ＭＳＰゴシック',
    sans-serif;
}

.c-cart_buying_area .c-list_table {
  width: 100%;
  border: 1px solid #ccc;
}

.c-cart_buying_area .c-list_table th,
.c-cart_buying_area .c-list_table td {
  font-family:
    Helvetica Neue,
    Helvetica,
    YuGothic,
    Yu Gothic,
    'ヒラギノ角ゴ Pro W3',
    Hiragino Kaku Gothic Pro,
    'メイリオ',
    Meiryo,
    'ＭＳＰゴシック',
    sans-serif;
}

.c-cart_buying_area .c-list_table th {
  width: 45%;
  padding: 7px;
  background: #f0f0f0;
  border-right: 1px solid #ccc;
  text-align: center;
}

.c-cart_buying_area .c-list_table td {
  width: 55%;
  padding: 7px;
  background: #fff;
}

.c-cart_submit_btn {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  font-family:
    Helvetica Neue,
    Helvetica,
    YuGothic,
    Yu Gothic,
    'ヒラギノ角ゴ Pro W3',
    Hiragino Kaku Gothic Pro,
    'メイリオ',
    Meiryo,
    'ＭＳＰゴシック',
    sans-serif;
}

.c-cart .c-large_headline {
  font-family:
    Helvetica Neue,
    Helvetica,
    YuGothic,
    Yu Gothic,
    'ヒラギノ角ゴ Pro W3',
    Hiragino Kaku Gothic Pro,
    'メイリオ',
    Meiryo,
    'ＭＳＰゴシック',
    sans-serif;
}

.c-cart .c-body {
  font-family:
    Helvetica Neue,
    Helvetica,
    YuGothic,
    Yu Gothic,
    'ヒラギノ角ゴ Pro W3',
    Hiragino Kaku Gothic Pro,
    'メイリオ',
    Meiryo,
    'ＭＳＰゴシック',
    sans-serif;
}

.c-cart .c-list_indent {
  width: 100%;
  margin-top: 10px;
}

.c-cart .c-list_indent th,
.c-cart .c-list_indent td {
  float: none;
  display: table-cell;
  font-family:
    Helvetica Neue,
    Helvetica,
    YuGothic,
    Yu Gothic,
    'ヒラギノ角ゴ Pro W3',
    Hiragino Kaku Gothic Pro,
    'メイリオ',
    Meiryo,
    'ＭＳＰゴシック',
    sans-serif;
}

.c-cart .c-list_indent th {
  width: 30%;
  padding: 1em;
}

.c-cart .c-list_indent td {
  width: 70%;
  padding: 1em 1em 1em 0;
  border-bottom: 1px solid #999;
}

.c-cart .c-list_indent .c-cart_table_unborder th,
.c-cart .c-list_indent .c-cart_table_unborder td {
  border-bottom: 0 none;
}

.c-cart .c-list_table th,
.c-cart .c-list_table td {
  display: table-cell;
  border-bottom: 1px solid #ccc;
  font-family:
    Helvetica Neue,
    Helvetica,
    YuGothic,
    Yu Gothic,
    'ヒラギノ角ゴ Pro W3',
    Hiragino Kaku Gothic Pro,
    'メイリオ',
    Meiryo,
    'ＭＳＰゴシック',
    sans-serif;
}

.c-cart .c-cart_thumbnail:before,
.c-cart .c-cart_thumbnail:after {
  display: table;
  line-height: 0;
  content: '';
}

.c-cart .c-cart_thumbnail:after {
  clear: both;
}

.c-cart .c-cart_thumbnail li {
  width: 20%;
  float: left;
  padding: 5px;
}

.c-cart.-h .c-cart_thumbnail li {
  width: 50%;
}

@media only screen and (max-width: 640px) {
  .c-cart.-h .c-cart_thumbnail li {
    width: 20%;
  }
}

.wp_list_pager,
.wp_post_pager {
  text-align: center;
}

.wp_list_pager .page-numbers {
  margin: 0 1px 0 0;
  padding: 1px 2px 1px 3px;
}

.wp_list_pager .prev {
  margin: 0 10px 0 0;
  padding: 1px 3px;
}

.wp_list_pager .next {
  margin: 0 0 0 10px;
  padding: 1px 3px;
}

.wp_post_pager span.nav-previous {
  margin: 0 7px 0 0;
}

.wp_post_pager span.nav-previous a {
  padding: 1px 3px;
}

.wp_post_pager span.nav-next {
  margin: 0 0 0 7px;
}

.wp_post_pager span.nav-next a {
  padding: 1px 3px;
}

.wp_comment_list blockquote {
  margin: 0;
}

.wp_comment_list blockquote:before,
.wp_comment_list blockquote:after {
  content: '"';
}

.wp_comment_list cite {
  font-style: italic;
}

.wp_comment_list em {
  font-style: italic;
  font-weight: 700;
}

.alignleft {
  float: left;
  margin: 5px 15px 5px 0;
}

.alignright {
  float: right;
  margin: 5px 0 5px 15px;
}

.wp_list_pager a.page-numbers {
  color: #333;
  font-size: 100%;
}

.wp_list_pager a.page-numbers:visited.page-numbers {
  color: #666;
  border-color: #999;
}

.wp_list_pager a.page-numbers:hover.page-numbers {
  color: #333;
}

.wp_list_pager .prev a {
  color: #333;
}

.wp_list_pager .prev a:visited {
  color: #666;
  border-color: #999;
}

.wp_list_pager .prev a:hover {
  color: #333;
}

.wp_list_pager .next a {
  color: #333;
}

.wp_list_pager .next a:visited {
  color: #666;
  border-color: #999;
}

.wp_list_pager .next a:hover {
  color: #333;
}

.wp_list_pager .current {
  color: #000;
}

.wp_post_pager .nav-previous a {
  color: #333;
}

.wp_post_pager .nav-previous a:visited {
  color: #666;
  border-color: #999;
}

.wp_post_pager .nav-previous a:hover {
  color: #333;
}

.wp_post_pager .nav-next a {
  color: #333;
}

.wp_post_pager .nav-next a:visited {
  color: #666;
  border-color: #999;
}

.wp_post_pager .nav-next a:hover {
  color: #333;
}

.wp_comment_list .wp_comment_name_style {
  font-weight: 700;
}

#respond #comment-title,
#respond #reply-title {
  margin-bottom: 10px;
}

#respond .comment-form-author input,
#respond .comment-form-email input,
#respond .comment-form-url input,
#respond .comment-form-comment textarea {
  font-size: 95%;
  font-family:
    'ヒラギノ角ゴ Pro W3',
    Hiragino Kaku Gothic Pro,
    Osaka,
    sans-serif;
}

#respond .comment-notes {
  font-size: 90%;
  padding-bottom: 22px;
}

#respond .form-submit {
  padding-top: 10px;
}

.wp_comment_list abbr,
.wp_comment_list acronym {
  color: #444;
}

.wp_comment_list code,
#respond .form-allowed-tags code {
  color: #777;
}

#respond .comment-form-author label,
#respond .comment-form-email label,
#respond .comment-form-url label,
#respond .comment-form-comment label {
  display: inline-block;
  width: 140px;
  vertical-align: top;
}

#respond .comment-form-author input,
#respond .comment-form-email input,
#respond .comment-form-url input,
#respond .comment-form-comment textarea {
  color: #444;
}

#respond .required {
  color: #d52525;
}

#respond .comment-form-author .required,
#respond .comment-form-email .required {
  display: inline-block;
  width: 10px;
  margin-left: -14px;
}

#respond .form-submit #submit {
  color: #333;
  background: #f5f5f5;
  border: 1px solid #ccc;
  padding: 3px 10px;
}

#respond .form-submit #submit:hover {
  color: #333;
  background: #e4e4e4;
}

#respond .form-submit #submit:active {
  background: #d4d4d4;
}

.wp_widgets ul {
  margin: 0;
}

.wp_widgets ul li {
  list-style: none;
  margin: 0;
}

.wp_widgets ul li h2 {
  font-size: 100%;
}

.wp_widgets ul li ul li {
  list-style: none;
  margin: 0;
}

.wp_widgets ul li form .screen-reader-text {
  margin: 0;
  padding: 0;
  display: none;
}

.wp_widgets ul li table {
  width: 100%;
  margin-top: 20px;
}

.wp_widgets ul li table caption {
  font-size: 110%;
  padding-bottom: 2px;
}

#wp-calendar th {
  font-weight: 700;
  text-align: center;
  padding-top: 4px;
  padding-bottom: 2px;
}

#wp-calendar td {
  text-align: center;
  padding-top: 4px;
  padding-bottom: 2px;
}

#wp-calendar td#today {
  font-weight: 700;
}

.wp_widgets ul li a {
  display: block;
}

.wp_widgets ul li table #prev {
  text-align: left;
}

.wp_widgets ul li table #next {
  text-align: right;
}

.wp_widgets ul li .tagcloud {
  padding: 10px 5px 0;
}

.wp_widgets ul li .tagcloud a,
.wp_widgets ul li .recentcomments a {
  padding: 0;
  background: none;
  display: inline;
}

.wp_widgets #calendar_wrap {
  padding: 0 4px;
}

.wp_widgets ul li {
  border-bottom: 0 solid #444;
  margin-bottom: 40px;
  padding-bottom: 0;
  color: #444;
}

.wp_widgets ul li ul {
  border-bottom: 0 none;
  margin-bottom: 0;
  padding-left: 18px;
}

.wp_widgets ul li ul li {
  border-bottom: 1px solid #444;
  margin-bottom: 0;
}

.wp_widgets ul li h2 {
  border-width: 1px 0;
  border-style: solid;
  border-color: #444;
  margin: 0;
  padding: 20px 0 4px 4px;
  background: none;
  color: #444;
}

.wp_widgets ul li a {
  text-decoration: none;
  border-bottom: 0 none;
  padding: 6px 4px 4px;
  color: #444;
}

.wp_widgets ul li a:hover {
  background: #e4e4e4;
}

.wp_widgets ul li form {
  border-width: 1px 0;
  border-style: solid;
  border-color: #444;
  margin: 0;
  padding: 14px 0 6px 4px;
  background: none;
  color: #444;
}

.wp_widgets ul li form #searchsubmit {
  color: #333;
  background: #f5f5f5;
  border: 1px solid #ccc;
}

.wp_widgets ul li form #searchsubmit:hover {
  color: #333;
  background: #e4e4e4;
}

.wp_widgets ul li form #searchsubmit:active {
  color: #333;
  background: #d4d4d4;
}

.wp_widgets ul li table td {
  color: #999;
}

.wp_widgets ul li table td a {
  color: #444;
  text-decoration: underline;
  padding: 0;
}

.wp_widgets ul li table td a:hover {
  color: #888;
  text-decoration: underline;
  background: none;
}

.wp_widgets ul li table td a:visited {
  color: #666;
  text-decoration: underline;
}

.wp_widgets ul li table #prev a {
  text-decoration: underline;
}

.wp_widgets ul li table #prev a:hover {
  text-decoration: none;
  background: none;
  color: #888;
}

.wp_widgets ul li table #prev a:visited {
  text-decoration: underline;
  background: none;
  color: #666;
}

.wp_widgets ul li table #next a {
  text-decoration: underline;
}

.wp_widgets ul li table #next a:hover {
  text-decoration: none;
  background: none;
  color: #888;
}

.wp_widgets ul li table #next a:visited {
  text-decoration: underline;
  background: none;
  color: #666;
}

.wp_widgets ul li .recentcomments {
  padding: 6px 4px 4px;
}

.wp_widgets ul li .tagcloud {
  padding: 10px 5px 0;
}

#wp_toplayout span.img span,
#wp_archivelayout span.img span,
#wp_singlelayout span.img span,
#wp_templatelayout span.img span,
#wp_searchlayout span.img span {
  padding: 0;
  margin: 0;
}

#wp_toplayout ul,
#wp_toplayout ol,
#wp_archivelayout ul,
#wp_archivelayout ol,
#wp_singlelayout ul,
#wp_singlelayout ol,
#wp_templatelayout ul,
#wp_templatelayout ol,
#wp_searchlayout ul,
#wp_searchlayout ol {
  margin: 0;
}

#wp_toplayout li,
#wp_archivelayout li,
#wp_singlelayout li,
#wp_templatelayout li,
#wp_searchlayout li {
  list-style: none;
  margin: 0;
}

.wp_widgets ul {
  padding: 0 0 20px;
}

.wp_widgets ul li a {
  background: none;
  padding: 0;
}

.wp_widgets ul li a:hover {
  background: none;
  padding: 0;
}

.wp_widgets ul li h2 {
  border: 0;
  margin: 0 0 18px;
  padding: 0 0 5px 15px;
  border-bottom: 1px solid #bab7b2;
  color: #7f7b75;
  font-size: 160%;
}

.wp_widgets ul li ul {
  padding: 0 15px;
}

.wp_widgets ul li ul li {
  border: none;
  margin-bottom: 5px;
}

.wp_widgets ul li ul li a {
  display: inline;
  background: none;
  border-bottom: 1px solid #7f7b75;
  padding: 0;
  color: #7f7b75;
}

.wp_widgets ul li ul li a:visited {
  border-bottom: 1px solid #a5a29d;
  color: #a5a29d;
}

.wp_widgets ul li ul li a:hover {
  border-bottom: 1px solid #a5a29d;
  background-color: transparent;
  color: #a5a29d;
}

.wp_widgets ul li.widget_search form {
  border: 0;
  margin: 0;
  padding: 0;
}

.wp_widgets ul li.widget_search form #searchsubmit {
  color: #fff;
  background: #bbb;
  border: none;
  padding: 1px 10px;
}

.wp_widgets ul li.widget_search form #searchsubmit:hover {
  color: #fff;
  background: #ddd;
  border: none;
}

.wp_widgets ul li.widget_search form #searchsubmit:active {
  background: #cecece;
  border: none;
}

.wp_widgets ul li table#wp-calendar td,
.wp_widgets ul li table#wp-calendar th {
  padding: 0;
  line-height: 1.6;
}

.wp_widgets ul li table caption,
.wp_widgets ul li .wp_widgets #wp-calendar th {
  color: #7f7b75;
}

.wp_widgets ul li table {
  table-layout: fixed;
}

.wp_widgets ul li table td {
  color: #bbb;
}

.wp_widgets ul li table td a {
  color: #7f7b75;
  text-decoration: underline;
  padding: 0;
}

.wp_widgets ul li table td a:visited {
  color: #a5a29d;
  text-decoration: underline;
}

.wp_widgets ul li table td a:hover {
  color: #a5a29d;
  text-decoration: underline;
  background: none;
}

.wp_widgets ul li table #prev a,
.wp_widgets ul li table #next a {
  padding-top: 10px;
  border-bottom: 0;
}

.wp_widgets ul li table #prev a {
  text-decoration: underline;
  margin-left: 5px;
}

.wp_widgets ul li table #prev a:visited {
  text-decoration: underline;
  background: none;
  color: #a5a29d;
}

.wp_widgets ul li table #prev a:hover {
  text-decoration: none;
  background: none;
  color: #a5a29d;
}

.wp_widgets ul li table #next a {
  text-decoration: underline;
  margin-right: 5px;
}

.wp_widgets ul li table #next a:visited {
  text-decoration: underline;
  background: none;
  color: #a5a29d;
}

.wp_widgets ul li table #next a:hover {
  text-decoration: none;
  background: none;
  color: #a5a29d;
}

.wp_widgets ul li .tagcloud {
  margin: 0;
  padding: 0 0 0 15px;
}

.wp_widgets ul li .tagcloud a {
  margin-right: 2px;
  display: inline;
  background: none;
  border-bottom: none;
  padding: 0;
  color: #7f7b75;
}

.wp_widgets ul li .tagcloud a:hover {
  border-bottom: 1px solid #a5a29d;
  background-color: transparent;
  color: #a5a29d;
}

.wp_widgets ul li .tagcloud a:visited {
  border-bottom: 1px solid #a5a29d;
  color: #a5a29d;
}

.wp_widgets ul .widget_calendar #calendar_wrap {
  padding: 0 15px;
}

.wp_widgets ul #recentcomments .recentcomments {
  padding: 0;
  color: #7f7b75;
}

.wp_articles .wp_content_style {
  padding-bottom: 15px;
}

.wp_articles .wp_content_style p {
  padding-bottom: 10px;
}

.wp_articles .wp_archive_img {
  width: 100%;
  text-align: center;
  border-width: 0;
  margin-bottom: 20px;
}

.wp_articles .wp_archive_img span.img span {
  padding: 0;
}

.wp_articles .wp_single_img {
  width: 100%;
  text-align: center;
}

.wp_articles .wp_single_img p {
  padding-bottom: 30px;
}

.wp_articles .wp_date_style p {
  padding-bottom: 5px;
  font-size: 90%;
}

.wp_articles .wp_archive_img {
  background-color: #7f7b75;
}

.wp_list_pager {
  text-align: center;
}

.wp_list_pager a.page-numbers {
  margin: 0 2px;
  padding: 1px 2px 1px 3px;
  color: #7f7b75;
  font-size: 100%;
}

.wp_list_pager a:visited.page-numbers {
  color: #a5a29d;
  border-color: #a5a29d;
}

.wp_list_pager a:hover.page-numbers {
  color: #a5a29d;
}

.wp_list_pager .current {
  color: #7f7b75;
}

.wp_list_pager .prev {
  margin: 0 10px 0 0;
  padding: 1px 3px;
}

.wp_list_pager .next {
  margin: 0 0 0 10px;
  padding: 1px 3px;
}

.wp_post_pager {
  text-align: center;
}

.wp_post_pager span.nav-previous {
  margin: 0 0 0 10px;
}

.wp_post_pager span.nav-previous a {
  padding: 1px 3px;
}

.wp_post_pager span.nav-next {
  margin: 0 10px 0 0;
}

.wp_post_pager span.nav-next a {
  padding: 1px 3px;
}

.wp_list_pager a.prev,
.wp_list_pager a.next,
.wp_post_pager .nav-previous a,
.wp_post_pager .nav-next a {
  color: #7f7b75;
}

.wp_list_pager a:visited.prev,
.wp_list_pager a:visited.next,
.wp_post_pager .nav-previous a:visited,
.wp_post_pager .nav-next a:visited {
  color: #a5a29d;
  border-color: #a5a29d;
}

.wp_list_pager a:hover.prev,
.wp_list_pager a:hover.next,
.wp_post_pager .nav-previous a:hover,
.wp_post_pager .nav-next a:hover {
  color: #a5a29d;
}

#respond .comment-notes {
  font-size: 90%;
  padding-bottom: 25px;
}

#comment-title,
#respond #reply-title {
  margin: 0 0 20px;
  padding: 0 0 5px;
  border-bottom: 1px solid #bab7b2;
  color: #7f7b75;
  font-size: 160%;
  line-height: 1.3;
  font-family:
    'ヒラギノ明朝 Pro W3',
    Hiragino Mincho Pro,
    'ＭＳ Ｐ明朝',
    MS PMincho,
    serif;
  font-weight: 700;
}

.wp_comment_list {
  color: #7f7b75;
}

.wp_comment_list abbr,
.wp_comment_list acronym {
  color: #666;
}

.wp_comment_list blockquote,
.wp_comment_list q {
  color: #7f7b75;
}

.wp_comment_list blockquote {
  padding: 14px 16px;
  margin-bottom: 15px;
  background-image: none;
  background-color: #fff;
}

.wp_comment_list code {
  color: #777;
}

.wp_comment_list .wp_comment_name_area {
  padding-bottom: 0;
}

.wp_comment_list .wp_comment_name_style {
  font-weight: 700;
}

#respond .form-allowed-tags code {
  color: #777;
}

#respond .comment-form-author label,
#respond .comment-form-email label,
#respond .comment-form-url label,
#respond .comment-form-comment label {
  display: inline-block;
  width: 140px;
  vertical-align: top;
  font-weight: 700;
  margin-bottom: 10px;
}

#respond .comment-form-author input,
#respond .comment-form-email input,
#respond .comment-form-url input,
#respond .comment-form-comment textarea {
  color: #7f7b75;
  margin-bottom: 10px;
}

#respond .required {
  color: #c17566;
}

#respond .comment-form-author .required,
#respond .comment-form-email .required {
  display: inline-block;
  width: 10px;
  margin-left: -14px;
}

#respond .form-submit {
  padding-top: 30px;
}

#respond .form-submit #submit {
  color: #fff;
  background: #bbb;
  border: none;
  padding: 10px 20px;
  font-weight: 700;
}

#respond .form-submit #submit:hover {
  color: #fff;
  background: #ddd;
  border: none;
}

#respond .form-submit #submit:active {
  background: #cecece;
  border: none;
}

.wp_search_block .wp_articles p {
  padding-bottom: 20px;
}

.init-block_animation {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  opacity: 0;
  filter: alpha(opacity=0);
}

@media only screen and (max-width: 640px) {
  .init-block_animation {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@media only screen and (max-width: 640px) {
  .init-sp-block_animation {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    opacity: 0;
    filter: alpha(opacity=0);
    z-index: 300;
  }
}

.init-block_animation.-infinite,
.init-sp-block_animation.-infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

.init-block_animation.-hinge,
.init-sp-block_animation.-hinge {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}

.init-block_animation.-flipOutX,
.init-block_animation.-flipOutY,
.init-block_animation.-bounceIn,
.init-block_animation.-bounceOut,
.init-sp-block_animation.-flipOutX,
.init-sp-block_animation.-flipOutY,
.init-sp-block_animation.-bounceIn,
.init-sp-block_animation.-bounceOut {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
}

@-webkit-keyframes shake {
  from,
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }

  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}

@keyframes shake {
  from,
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }

  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}

.-shake {
  -webkit-animation-name: shake;
  animation-name: shake;
}

@-webkit-keyframes bounceIn {
  from,
  20%,
  40%,
  60%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  0% {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }

  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }

  60% {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03);
  }

  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes bounceIn {
  from,
  20%,
  40%,
  60%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  0% {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }

  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }

  60% {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03);
  }

  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

.-bounceIn {
  -webkit-animation-name: bounceIn;
  animation-name: bounceIn;
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.-fadeIn {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

@-webkit-keyframes fadeInDown {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: none;
    transform: none;
  }
}

.-fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
}

@-webkit-keyframes fadeInLeft {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: none;
    transform: none;
  }
}

.-fadeInLeft {
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft;
}

@-webkit-keyframes fadeInRight {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: none;
    transform: none;
  }
}

.-fadeInRight {
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight;
}

@-webkit-keyframes fadeInUp {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: none;
    transform: none;
  }
}

.-fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}

.-bothSideIn {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

.-bothSideIn:first-child,
.-bothSideIn.-column1 {
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft;
}

.-bothSideIn.-column-lasts,
.-bothSideIn:last-child {
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight;
}

@-webkit-keyframes flipInX {
  from {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
    filter: alpha(opacity=0);
  }

  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }

  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes flipInX {
  from {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
    filter: alpha(opacity=0);
  }

  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }

  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }

  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.-flipInX {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipInX;
  animation-name: flipInX;
}

@-webkit-keyframes zoomIn {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
  }

  50% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
  }

  50% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.-zoomIn {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn;
}

@-webkit-keyframes rotateIn {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale3d(0.3, 0.3, 0.3) rotate(-60deg);
    transform: scale3d(0.3, 0.3, 0.3) rotate(-60deg);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: scale3d(1, 1, 1) rotate(0deg);
    transform: scale3d(1, 1, 1) rotate(0deg);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale3d(0.3, 0.3, 0.3) rotate(-60deg);
    transform: scale3d(0.3, 0.3, 0.3) rotate(-60deg);
  }

  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: scale3d(1, 1, 1) rotate(0deg);
    transform: scale3d(1, 1, 1) rotate(0deg);
  }
}

.-rotateIn {
  -webkit-animation-name: rotateIn;
  animation-name: rotateIn;
}

/*!
* animsition v4.0.2
* A simple and easy jQuery plugin for CSS animated page transitions.
* http://blivesta.github.io/animsition
* License : MIT
* Author : blivesta (http://blivesta.com/)
*/
.animsition,
.animsition-overlay {
  position: relative;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.animsition-overlay-slide {
  position: fixed;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: #ddd;
}

.animsition-loading,
.animsition-loading:after {
  width: 32px;
  height: 32px;
  position: fixed;
  top: 50%;
  left: 50%;
  margin-top: -16px;
  margin-left: -16px;
  border-radius: 50%;
  z-index: 100;
}

.animsition-loading {
  background-color: transparent;
  border-top: 5px solid rgba(0, 0, 0, 0.2);
  border-right: 5px solid rgba(0, 0, 0, 0.2);
  border-bottom: 5px solid rgba(0, 0, 0, 0.2);
  border-left: 5px solid #eee;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-duration: 0.8s;
  animation-duration: 0.8s;
  -webkit-animation-name: animsition-loading;
  animation-name: animsition-loading;
}

@-webkit-keyframes animsition-loading {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes animsition-loading {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-webkit-keyframes fade-in {
  0% {
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in {
  -webkit-animation-name: fade-in;
  animation-name: fade-in;
}

@-webkit-keyframes fade-out {
  0% {
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out {
  0% {
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out {
  -webkit-animation-name: fade-out;
  animation-name: fade-out;
}

@-webkit-keyframes fade-in-up {
  0% {
    -webkit-transform: translateY(500px);
    transform: translateY(500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in-up {
  0% {
    -webkit-transform: translateY(500px);
    transform: translateY(500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in-up {
  -webkit-animation-name: fade-in-up;
  animation-name: fade-in-up;
}

@-webkit-keyframes fade-out-up {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateY(-500px);
    transform: translateY(-500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out-up {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateY(-500px);
    transform: translateY(-500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out-up {
  -webkit-animation-name: fade-out-up;
  animation-name: fade-out-up;
}

@-webkit-keyframes fade-in-up-sm {
  0% {
    -webkit-transform: translateY(100px);
    transform: translateY(100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in-up-sm {
  0% {
    -webkit-transform: translateY(100px);
    transform: translateY(100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in-up-sm {
  -webkit-animation-name: fade-in-up-sm;
  animation-name: fade-in-up-sm;
}

@-webkit-keyframes fade-out-up-sm {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateY(-100px);
    transform: translateY(-100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out-up-sm {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateY(-100px);
    transform: translateY(-100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out-up-sm {
  -webkit-animation-name: fade-out-up-sm;
  animation-name: fade-out-up-sm;
}

@-webkit-keyframes fade-in-up-lg {
  0% {
    -webkit-transform: translateY(1000px);
    transform: translateY(1000px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in-up-lg {
  0% {
    -webkit-transform: translateY(1000px);
    transform: translateY(1000px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in-up-lg {
  -webkit-animation-name: fade-in-up-lg;
  animation-name: fade-in-up-lg;
}

@-webkit-keyframes fade-out-up-lg {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateY(-1000px);
    transform: translateY(-1000px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out-up-lg {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateY(-1000px);
    transform: translateY(-1000px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out-up-lg {
  -webkit-animation-name: fade-out-up-lg;
  animation-name: fade-out-up-lg;
}

@-webkit-keyframes fade-in-down {
  0% {
    -webkit-transform: translateY(-500px);
    transform: translateY(-500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in-down {
  0% {
    -webkit-transform: translateY(-500px);
    transform: translateY(-500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in-down {
  -webkit-animation-name: fade-in-down;
  animation-name: fade-in-down;
}

@-webkit-keyframes fade-out-down {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateY(500px);
    transform: translateY(500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out-down {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateY(500px);
    transform: translateY(500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out-down {
  -webkit-animation-name: fade-out-down;
  animation-name: fade-out-down;
}

@-webkit-keyframes fade-in-down-sm {
  0% {
    -webkit-transform: translateY(-100px);
    transform: translateY(-100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in-down-sm {
  0% {
    -webkit-transform: translateY(-100px);
    transform: translateY(-100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in-down-sm {
  -webkit-animation-name: fade-in-down-sm;
  animation-name: fade-in-down-sm;
}

@-webkit-keyframes fade-out-down-sm {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateY(100px);
    transform: translateY(100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out-down-sm {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateY(100px);
    transform: translateY(100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out-down-sm {
  -webkit-animation-name: fade-out-down-sm;
  animation-name: fade-out-down-sm;
}

@-webkit-keyframes fade-in-down-lg {
  0% {
    -webkit-transform: translateY(-1000px);
    transform: translateY(-1000px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in-down-lg {
  0% {
    -webkit-transform: translateY(-1000px);
    transform: translateY(-1000px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in-down-lg {
  -webkit-animation-name: fade-in-down;
  animation-name: fade-in-down;
}

@-webkit-keyframes fade-out-down-lg {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateY(1000px);
    transform: translateY(1000px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out-down-lg {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateY(1000px);
    transform: translateY(1000px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out-down-lg {
  -webkit-animation-name: fade-out-down-lg;
  animation-name: fade-out-down-lg;
}

@-webkit-keyframes fade-in-left {
  0% {
    -webkit-transform: translateX(-500px);
    transform: translateX(-500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in-left {
  0% {
    -webkit-transform: translateX(-500px);
    transform: translateX(-500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in-left {
  -webkit-animation-name: fade-in-left;
  animation-name: fade-in-left;
}

@-webkit-keyframes fade-out-left {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateX(-500px);
    transform: translateX(-500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out-left {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateX(-500px);
    transform: translateX(-500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out-left {
  -webkit-animation-name: fade-out-left;
  animation-name: fade-out-left;
}

@-webkit-keyframes fade-in-left-sm {
  0% {
    -webkit-transform: translateX(-100px);
    transform: translateX(-100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in-left-sm {
  0% {
    -webkit-transform: translateX(-100px);
    transform: translateX(-100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in-left-sm {
  -webkit-animation-name: fade-in-left-sm;
  animation-name: fade-in-left-sm;
}

@-webkit-keyframes fade-out-left-sm {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateX(-100px);
    transform: translateX(-100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out-left-sm {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateX(-100px);
    transform: translateX(-100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out-left-sm {
  -webkit-animation-name: fade-out-left-sm;
  animation-name: fade-out-left-sm;
}

@-webkit-keyframes fade-in-left-lg {
  0% {
    -webkit-transform: translateX(-1500px);
    transform: translateX(-1500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in-left-lg {
  0% {
    -webkit-transform: translateX(-1500px);
    transform: translateX(-1500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in-left-lg {
  -webkit-animation-name: fade-in-left-lg;
  animation-name: fade-in-left-lg;
}

@-webkit-keyframes fade-out-left-lg {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateX(-1500px);
    transform: translateX(-1500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out-left-lg {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateX(-1500px);
    transform: translateX(-1500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out-left-lg {
  -webkit-animation-name: fade-out-left-lg;
  animation-name: fade-out-left-lg;
}

@-webkit-keyframes fade-in-right {
  0% {
    -webkit-transform: translateX(500px);
    transform: translateX(500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in-right {
  0% {
    -webkit-transform: translateX(500px);
    transform: translateX(500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in-right {
  -webkit-animation-name: fade-in-right;
  animation-name: fade-in-right;
}

@-webkit-keyframes fade-out-right {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateX(500px);
    transform: translateX(500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out-right {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateX(500px);
    transform: translateX(500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out-right {
  -webkit-animation-name: fade-out-right;
  animation-name: fade-out-right;
}

@-webkit-keyframes fade-in-right-sm {
  0% {
    -webkit-transform: translateX(100px);
    transform: translateX(100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in-right-sm {
  0% {
    -webkit-transform: translateX(100px);
    transform: translateX(100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in-right-sm {
  -webkit-animation-name: fade-in-right-sm;
  animation-name: fade-in-right-sm;
}

@-webkit-keyframes fade-out-right-sm {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateX(100px);
    transform: translateX(100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out-right-sm {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateX(100px);
    transform: translateX(100px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out-right-sm {
  -webkit-animation-name: fade-out-right-sm;
  animation-name: fade-out-right-sm;
}

@-webkit-keyframes fade-in-right-lg {
  0% {
    -webkit-transform: translateX(1500px);
    transform: translateX(1500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes fade-in-right-lg {
  0% {
    -webkit-transform: translateX(1500px);
    transform: translateX(1500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.fade-in-right-lg {
  -webkit-animation-name: fade-in-right-lg;
  animation-name: fade-in-right-lg;
}

@-webkit-keyframes fade-out-right-lg {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateX(1500px);
    transform: translateX(1500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes fade-out-right-lg {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: translateX(1500px);
    transform: translateX(1500px);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.fade-out-right-lg {
  -webkit-animation-name: fade-out-right-lg;
  animation-name: fade-out-right-lg;
}

@-webkit-keyframes rotate-in {
  0% {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes rotate-in {
  0% {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.rotate-in {
  -webkit-animation-name: rotate-in;
  animation-name: rotate-in;
}

@-webkit-keyframes rotate-out {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes rotate-out {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.rotate-out {
  -webkit-animation-name: rotate-out;
  animation-name: rotate-out;
}

@-webkit-keyframes rotate-in-sm {
  0% {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes rotate-in-sm {
  0% {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.rotate-in-sm {
  -webkit-animation-name: rotate-in-sm;
  animation-name: rotate-in-sm;
}

@-webkit-keyframes rotate-out-sm {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes rotate-out-sm {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.rotate-out-sm {
  -webkit-animation-name: rotate-out-sm;
  animation-name: rotate-out-sm;
}

@-webkit-keyframes rotate-in-lg {
  0% {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes rotate-in-lg {
  0% {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.rotate-in-lg {
  -webkit-animation-name: rotate-in-lg;
  animation-name: rotate-in-lg;
}

@-webkit-keyframes rotate-out-lg {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes rotate-out-lg {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.rotate-out-lg {
  -webkit-animation-name: rotate-out-lg;
  animation-name: rotate-out-lg;
}

@-webkit-keyframes flip-in-x {
  0% {
    -webkit-transform: perspective(550px) rotateX(90deg);
    transform: perspective(550px) rotateX(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: perspective(550px) rotateX(0deg);
    transform: perspective(550px) rotateX(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes flip-in-x {
  0% {
    -webkit-transform: perspective(550px) rotateX(90deg);
    transform: perspective(550px) rotateX(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: perspective(550px) rotateX(0deg);
    transform: perspective(550px) rotateX(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.flip-in-x {
  -webkit-animation-name: flip-in-x;
  animation-name: flip-in-x;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flip-out-x {
  0% {
    -webkit-transform: perspective(550px) rotateX(0deg);
    transform: perspective(550px) rotateX(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: perspective(550px) rotateX(90deg);
    transform: perspective(550px) rotateX(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes flip-out-x {
  0% {
    -webkit-transform: perspective(550px) rotateX(0deg);
    transform: perspective(550px) rotateX(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: perspective(550px) rotateX(90deg);
    transform: perspective(550px) rotateX(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.flip-out-x {
  -webkit-animation-name: flip-out-x;
  animation-name: flip-out-x;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flip-in-x-nr {
  0% {
    -webkit-transform: perspective(100px) rotateX(90deg);
    transform: perspective(100px) rotateX(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: perspective(100px) rotateX(0deg);
    transform: perspective(100px) rotateX(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes flip-in-x-nr {
  0% {
    -webkit-transform: perspective(100px) rotateX(90deg);
    transform: perspective(100px) rotateX(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: perspective(100px) rotateX(0deg);
    transform: perspective(100px) rotateX(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.flip-in-x-nr {
  -webkit-animation-name: flip-in-x-nr;
  animation-name: flip-in-x-nr;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flip-out-x-nr {
  0% {
    -webkit-transform: perspective(100px) rotateX(0deg);
    transform: perspective(100px) rotateX(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: perspective(100px) rotateX(90deg);
    transform: perspective(100px) rotateX(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes flip-out-x-nr {
  0% {
    -webkit-transform: perspective(100px) rotateX(0deg);
    transform: perspective(100px) rotateX(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: perspective(100px) rotateX(90deg);
    transform: perspective(100px) rotateX(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.flip-out-x-nr {
  -webkit-animation-name: flip-out-x-nr;
  animation-name: flip-out-x-nr;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flip-in-x-fr {
  0% {
    -webkit-transform: perspective(1000px) rotateX(90deg);
    transform: perspective(1000px) rotateX(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: perspective(1000px) rotateX(0deg);
    transform: perspective(1000px) rotateX(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes flip-in-x-fr {
  0% {
    -webkit-transform: perspective(1000px) rotateX(90deg);
    transform: perspective(1000px) rotateX(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: perspective(1000px) rotateX(0deg);
    transform: perspective(1000px) rotateX(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.flip-in-x-fr {
  -webkit-animation-name: flip-in-x-fr;
  animation-name: flip-in-x-fr;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flip-out-x-fr {
  0% {
    -webkit-transform: perspective(1000px) rotateX(0deg);
    transform: perspective(1000px) rotateX(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: perspective(1000px) rotateX(90deg);
    transform: perspective(1000px) rotateX(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes flip-out-x-fr {
  0% {
    -webkit-transform: perspective(1000px) rotateX(0deg);
    transform: perspective(1000px) rotateX(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: perspective(1000px) rotateX(90deg);
    transform: perspective(1000px) rotateX(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.flip-out-x-fr {
  -webkit-animation-name: flip-out-x-fr;
  animation-name: flip-out-x-fr;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flip-in-y {
  0% {
    -webkit-transform: perspective(550px) rotateY(90deg);
    transform: perspective(550px) rotateY(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: perspective(550px) rotateY(0deg);
    transform: perspective(550px) rotateY(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes flip-in-y {
  0% {
    -webkit-transform: perspective(550px) rotateY(90deg);
    transform: perspective(550px) rotateY(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: perspective(550px) rotateY(0deg);
    transform: perspective(550px) rotateY(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.flip-in-y {
  -webkit-animation-name: flip-in-y;
  animation-name: flip-in-y;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flip-out-y {
  0% {
    -webkit-transform: perspective(550px) rotateY(0deg);
    transform: perspective(550px) rotateY(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: perspective(550px) rotateY(90deg);
    transform: perspective(550px) rotateY(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes flip-out-y {
  0% {
    -webkit-transform: perspective(550px) rotateY(0deg);
    transform: perspective(550px) rotateY(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: perspective(550px) rotateY(90deg);
    transform: perspective(550px) rotateY(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.flip-out-y {
  -webkit-animation-name: flip-out-y;
  animation-name: flip-out-y;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flip-in-y-nr {
  0% {
    -webkit-transform: perspective(100px) rotateY(90deg);
    transform: perspective(100px) rotateY(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: perspective(100px) rotateY(0deg);
    transform: perspective(100px) rotateY(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes flip-in-y-nr {
  0% {
    -webkit-transform: perspective(100px) rotateY(90deg);
    transform: perspective(100px) rotateY(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: perspective(100px) rotateY(0deg);
    transform: perspective(100px) rotateY(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.flip-in-y-nr {
  -webkit-animation-name: flip-in-y-nr;
  animation-name: flip-in-y-nr;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flip-out-y-nr {
  0% {
    -webkit-transform: perspective(100px) rotateY(0deg);
    transform: perspective(100px) rotateY(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: perspective(100px) rotateY(90deg);
    transform: perspective(100px) rotateY(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes flip-out-y-nr {
  0% {
    -webkit-transform: perspective(100px) rotateY(0deg);
    transform: perspective(100px) rotateY(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: perspective(100px) rotateY(90deg);
    transform: perspective(100px) rotateY(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.flip-out-y-nr {
  -webkit-animation-name: flip-out-y-nr;
  animation-name: flip-out-y-nr;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flip-in-y-fr {
  0% {
    -webkit-transform: perspective(1000px) rotateY(90deg);
    transform: perspective(1000px) rotateY(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: perspective(1000px) rotateY(0deg);
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes flip-in-y-fr {
  0% {
    -webkit-transform: perspective(1000px) rotateY(90deg);
    transform: perspective(1000px) rotateY(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    -webkit-transform: perspective(1000px) rotateY(0deg);
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.flip-in-y-fr {
  -webkit-animation-name: flip-in-y-fr;
  animation-name: flip-in-y-fr;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flip-out-y-fr {
  0% {
    -webkit-transform: perspective(1000px) rotateY(0deg);
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: perspective(1000px) rotateY(90deg);
    transform: perspective(1000px) rotateY(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes flip-out-y-fr {
  0% {
    -webkit-transform: perspective(1000px) rotateY(0deg);
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  100% {
    -webkit-transform: perspective(1000px) rotateY(90deg);
    transform: perspective(1000px) rotateY(90deg);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.flip-out-y-fr {
  -webkit-animation-name: flip-out-y-fr;
  animation-name: flip-out-y-fr;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes zoom-in {
  0% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes zoom-in {
  0% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.zoom-in {
  -webkit-animation-name: zoom-in;
  animation-name: zoom-in;
}

@-webkit-keyframes zoom-out {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  50% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes zoom-out {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  50% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.zoom-out {
  -webkit-animation-name: zoom-out;
  animation-name: zoom-out;
}

@-webkit-keyframes zoom-in-sm {
  0% {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes zoom-in-sm {
  0% {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.zoom-in-sm {
  -webkit-animation-name: zoom-in-sm;
  animation-name: zoom-in-sm;
}

@-webkit-keyframes zoom-out-sm {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  50% {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes zoom-out-sm {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  50% {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.zoom-out-sm {
  -webkit-animation-name: zoom-out-sm;
  animation-name: zoom-out-sm;
}

@-webkit-keyframes zoom-in-lg {
  0% {
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

@keyframes zoom-in-lg {
  0% {
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}

.zoom-in-lg {
  -webkit-animation-name: zoom-in-lg;
  animation-name: zoom-in-lg;
}

@-webkit-keyframes zoom-out-lg {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  50% {
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

@keyframes zoom-out-lg {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
    filter: alpha(opacity=100);
  }

  50% {
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
    opacity: 0;
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 0;
    filter: alpha(opacity=0);
  }
}

.zoom-out-lg {
  -webkit-animation-name: zoom-out-lg;
  animation-name: zoom-out-lg;
}

@-webkit-keyframes overlay-slide-in-top {
  0% {
    height: 100%;
  }

  100% {
    height: 0;
  }
}

@keyframes overlay-slide-in-top {
  0% {
    height: 100%;
  }

  100% {
    height: 0;
  }
}

.overlay-slide-in-top {
  top: 0;
  height: 0;
  -webkit-animation-name: overlay-slide-in-top;
  animation-name: overlay-slide-in-top;
}

@-webkit-keyframes overlay-slide-out-top {
  0% {
    height: 0;
  }

  100% {
    height: 100%;
  }
}

@keyframes overlay-slide-out-top {
  0% {
    height: 0;
  }

  100% {
    height: 100%;
  }
}

.overlay-slide-out-top {
  top: 0;
  height: 100%;
  -webkit-animation-name: overlay-slide-out-top;
  animation-name: overlay-slide-out-top;
}

@-webkit-keyframes overlay-slide-in-bottom {
  0% {
    height: 100%;
  }

  100% {
    height: 0;
  }
}

@keyframes overlay-slide-in-bottom {
  0% {
    height: 100%;
  }

  100% {
    height: 0;
  }
}

.overlay-slide-in-bottom {
  bottom: 0;
  height: 0;
  -webkit-animation-name: overlay-slide-in-bottom;
  animation-name: overlay-slide-in-bottom;
}

@-webkit-keyframes overlay-slide-out-bottom {
  0% {
    height: 0;
  }

  100% {
    height: 100%;
  }
}

@keyframes overlay-slide-out-bottom {
  0% {
    height: 0;
  }

  100% {
    height: 100%;
  }
}

.overlay-slide-out-bottom {
  bottom: 0;
  height: 100%;
  -webkit-animation-name: overlay-slide-out-bottom;
  animation-name: overlay-slide-out-bottom;
}

@-webkit-keyframes overlay-slide-in-left {
  0% {
    width: 100%;
  }

  100% {
    width: 0;
  }
}

@keyframes overlay-slide-in-left {
  0% {
    width: 100%;
  }

  100% {
    width: 0;
  }
}

.overlay-slide-in-left {
  width: 0;
  -webkit-animation-name: overlay-slide-in-left;
  animation-name: overlay-slide-in-left;
}

@-webkit-keyframes overlay-slide-out-left {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}

@keyframes overlay-slide-out-left {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}

.overlay-slide-out-left {
  left: 0;
  width: 100%;
  -webkit-animation-name: overlay-slide-out-left;
  animation-name: overlay-slide-out-left;
}

@-webkit-keyframes overlay-slide-in-right {
  0% {
    width: 100%;
  }

  100% {
    width: 0;
  }
}

@keyframes overlay-slide-in-right {
  0% {
    width: 100%;
  }

  100% {
    width: 0;
  }
}

.overlay-slide-in-right {
  right: 0;
  width: 0;
  -webkit-animation-name: overlay-slide-in-right;
  animation-name: overlay-slide-in-right;
}

@-webkit-keyframes overlay-slide-out-right {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}

@keyframes overlay-slide-out-right {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}

.overlay-slide-out-right {
  right: 0;
  width: 100%;
  -webkit-animation-name: overlay-slide-out-right;
  animation-name: overlay-slide-out-right;
}

.js-show {
  display: block !important;
}

.js-hide {
  display: none !important;
}

.js-sync:before,
.js-sync:after {
  display: block;
  line-height: 0;
  content: '';
}

.js-sync:after {
  clear: both;
}

.js-mouse_overs {
  position: relative;
  left: 0;
  top: 0;
  overflow: hidden;
  font-size: 0;
  line-height: 0;
}

.js-mouse_overs a:hover {
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled=false);
  opacity: 1;
  filter: alpha(opacity=100);
}

.js-mouse_overs img {
  position: relative;
  z-index: 1;
}

.js-mouse_overs .js-over_img {
  display: none;
}

.js-mouse_overs:hover img {
  display: none;
}

.js-mouse_overs:hover .js-over_img {
  display: inline;
}

@media only screen and (max-width: 640px) {
  .js-mouse_overs:hover img {
    display: inline;
  }

  .js-mouse_overs:hover .js-over_img {
    display: none;
  }
}

.js-mouse_overs .c-img_comment {
  line-height: normal;
}

.js-loading {
  background: url(../images/loading.gif) center center no-repeat;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 102;
}

.js-android_browser {
  border: 3px solid #f00;
  padding: 20px;
}

.js-android_browser a {
  color: #04c;
}

.js-no_scroll {
  overflow: hidden;
}

.js-mouse_pointer {
  cursor: pointer;
}

.is-pc-hide {
  display: none !important;
}

@media only screen and (max-width: 640px) {
  .is-pc-hide {
    display: block !important;
  }

  .is-sp-hide {
    display: none !important;
  }
}

.is-edit-show {
  display: block !important;
  position: relative;
}

.is-edit-show.is-pc-hide:before,
.is-edit-show.is-sp-hide:before {
  content: '';
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.3);
  background-repeat: no-repeat;
  background-position: center center;
}

.is-edit-show.is-pc-hide:before {
  background-image: url(hidden_pc.png);
}

.is-edit-show.is-sp-hide:before {
  background-image: url(hidden_sp.png);
}

.-follow-target {
  position: relative;
  z-index: 1;
}

.-follow-blocks {
  position: absolute;
  z-index: 2;
  overflow: auto;
  width: 100%;
  pointer-events: none;
}

.-js-block_sticky {
  position: fixed;
  top: 0;
  z-index: 170;
}

.js-ghost_mode {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 170;
  width: 100%;
  display: none;
  -webkit-animation-duration: 0.5s;
  animation-duration: 0.5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.js-ghost_mode.-fade-mode {
  display: block;
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

.-height100 {
  min-height: 100vh;
}

@media only screen and (max-width: 640px) {
  .-height100 {
    min-height: auto;
  }

  .-sp-height100 {
    min-height: 100vh;
  }
}

@media print {
  @page {
    size: A4;
    margin-top: 0.4cm;
    margin: 0.5cm;
  }

  p a,
  code,
  pre {
    word-wrap: break-word;
  }

  body {
    -webkit-print-color-adjust: exact;
  }

  .a-header {
    position: relative !important;
  }

  .a-billboard {
    padding-top: 0 !important;
  }

  .init-block_animation {
    opacity: 1;
    filter: alpha(opacity=100);
  }
}
