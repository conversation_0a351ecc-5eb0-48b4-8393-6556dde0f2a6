import { useLayoutEffect, useMemo, useState } from 'react';
import { makeStyles } from 'tss-react/mui';

const useStyles = makeStyles<{ transitionDuration: number }>()((theme, { transitionDuration }) => ({
  root: {
    transition: `opacity ${transitionDuration}ms, transform ${transitionDuration}ms`,
  },
  fadeEnter: {
    opacity: 0,
    transform: 'translateX(-20px)',
    '&.animate-active': {
      opacity: 1,
      transform: 'translateX(0)',
    },
  },
  fadeExit: {
    opacity: 1,
    transform: 'translateX(0)',
    '&.animate-active': {
      opacity: 0,
      transform: 'translateX(20px)',
    },
  },
}));

function useFadeAnimate<T>(transitionDuration: number = 150, value: T | any, animateId: number | string) {
  const [isActive, setIsActive] = useState(true);
  const [isEnter, setIsEnter] = useState(false);
  const [valueAnimated, setValueAnimated] = useState<T>(value);
  const { classes } = useStyles({ transitionDuration: transitionDuration / 2 });

  useLayoutEffect(() => {
    setIsEnter(false);

    const timer = setTimeout(() => {
      setIsEnter(true);
      setValueAnimated(value);
    }, transitionDuration);

    return () => {
      clearTimeout(timer);
    };
  }, [animateId]);

  useLayoutEffect(() => {
    setIsActive(!isEnter);

    const timer = setTimeout(() => {
      if (isEnter) {
        setIsActive(true);
      }
    }, transitionDuration);

    return () => {
      clearTimeout(timer);
    };
  }, [isEnter]);

  const animationClassFull = useMemo(() => {
    const animationClass = isEnter ? classes.fadeEnter : classes.fadeExit;
    const transitionClass = isActive ? 'animate-active' : '';

    return `${classes.root} ${animationClass} ${transitionClass}`;
  }, [isEnter, isActive]);

  return {
    classes: animationClassFull,
    isEnter,
    valueAnimated,
    setIsEnter,
    setIsActive,
  };
}

export default useFadeAnimate;
