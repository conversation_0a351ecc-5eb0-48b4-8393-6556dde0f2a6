import { Transform } from 'class-transformer';
import { IsIn } from 'class-validator';

import { IsCustomOptional } from '@/core/decorator/customOptional.decorator';
import { PaginationRequestDto } from '@/types/request.type';

export const enum FilterStatusOptions {
  ALL = 'ALL',
  NOT_YET_SUPPORT = 'NOT_YET_SUPPORT',
  WAITING_REPLY = 'WAITING_REPLY',
  DEVELOPMENT_SUPPORT_REQUIRED = 'DEVELOPMENT_SUPPORT_REQUIRED',
  COMPLETE = 'COMPLETE',
}

export class GetFormSubmissionRequestDTO extends PaginationRequestDto {
  @IsCustomOptional()
  @Transform(({ value }) => (value.map((v) => v.toLowerCase())?.includes('all') ? [] : value.map((v) => v.toUpperCase())))
  status?: FilterStatusOptions[] = [];

  @IsIn(['ASC', 'DESC'])
  @IsCustomOptional()
  @Transform(({ value }) => value.toUpperCase())
  order?: 'ASC' | 'DESC' = 'DESC';

  @IsIn(['id', 'name', 'status'])
  @IsCustomOptional()
  orderBy?: 'id' | 'name' | 'status';
}
