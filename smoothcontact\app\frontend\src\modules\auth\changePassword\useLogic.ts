import useAxios from '@/hooks/useAxios';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useToast } from '@/provider/toastProvider';
import { changePasswordRequestConfig } from '@/services/account.service';
import { getQueryParam } from '@/utils/helper';
import { ChangePasswordRequestDTO } from './dto/request.dto';
import { validationSchema } from './validator';

export type FormValue = {
  pwd: string;
  rePwd: string;
};

export default function useLogic() {
  const { toast } = useToast();
  const token = getQueryParam('token');
  const { apiCaller, loading } = useAxios();

  const changePassword = async (formData: FormValue) => {
    const dataBody = new ChangePasswordRequestDTO();

    dataBody.pwd = formData.pwd;
    dataBody.token = token;

    const result: any = await apiCaller(changePasswordRequestConfig(dataBody));

    if (result?.success) {
      toast({ isError: false, message: 'パスワードの変更が成功しました。' });
      formHandler.resetForm({ values: { pwd: '', rePwd: '' } });

      return;
    }

    if (result?.message) {
      toast({ isError: true, message: result.message });

      return;
    }

    formHandler.setErrors(result.messageErrors ?? {});
  };

  const formHandler = useFormHandler<FormValue>({
    initialValues: { pwd: '', rePwd: '' },
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: async (values) => {
      await changePassword(values);
    },
  });

  return { changePassword, loading, formHandler };
}
