import { Options, parseStringPromise } from 'xml2js';

export class XmlParseUtil {
  private static options: Options = {
    explicitArray: false,
  };

  public static async parse(xml: string, rootTag?: string): Promise<any> {
    try {
      const result = await parseStringPromise(xml, XmlParseUtil.options);

      return rootTag ? result?.[rootTag] : result;
    } catch (error) {
      throw new Error('XML parsing failed');
    }
  }
}
