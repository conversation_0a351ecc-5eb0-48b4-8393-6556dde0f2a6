import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { randomInt } from 'crypto';
import * as QRCode from 'qrcode';
import { Repository } from 'typeorm';

import { RootService } from '@/core/services/root.service';
import { OTP } from '@/libs/OTP';

import { AccountService } from '../account/account.service';
import { MfaBackupEntity } from './entities/mfa-backup.entity';

@Injectable()
export class MfaService extends RootService {
  @InjectRepository(MfaBackupEntity)
  private readonly mfaBackupRepository: Repository<MfaBackupEntity>;

  constructor(@Inject(forwardRef(() => AccountService)) private readonly accountService: AccountService) {
    super();
  }

  async getAuthenticationCode(name?: string) {
    const secret = OTP.generateSecret();
    const otp = new OTP(secret);
    const otpAuthUrl = otp.generateAuthUrl(name ?? 'user', 'Smooth Contact');
    const qrCode = await QRCode.toDataURL(otpAuthUrl as string);

    return {
      base32: otp.getSecret(),
      qrCode,
    };
  }

  verifyMfaCode(secret: string, token: string) {
    const isVerified = OTP.verifyCode(secret, token);

    if (!isVerified) {
      return this.response({ messageErrors: { code: 'OPTが無効となります' } });
    }

    return isVerified;
  }

  async verifyBackupCode(accountId: number, code: string) {
    const backupCode = await this.mfaBackupRepository.findOneBy({ accountId, backupCode: code });

    if (!backupCode) {
      return this.response({ messageErrors: { code: 'バックアップコードが無効となります。' } });
    }

    const deletedResult = await this.mfaBackupRepository.softDelete({ accountId, backupCode: code });

    if (!deletedResult) {
      return this.response({ messageErrors: { code: 'バックアップコードが無効となります。' } });
    }

    return true;
  }

  async createMfaCode(secret: string, token: string, accountId: number) {
    const isVerified = OTP.verifyCode(secret, token);

    if (!isVerified) {
      return this.response({ messageErrors: { code: 'OPTが無効となります' } });
    }

    const backupCodes = this.generateBackupCodes();

    if (backupCodes?.length) {
      const backupResult = await this.mfaBackupRepository.save(backupCodes.map((code) => ({ accountId, backupCode: code })));

      if (!backupResult) {
        return this.response({ messageErrors: { code: 'バックアップコードの作成に失敗しました。' } });
      }
    }

    const updateAccount = await this.accountService.updateMfaCommonKey({ accountId, secret });

    if (!updateAccount) {
      return this.response({ messageErrors: { code: 'MFAアカウントの更新に失敗しました。' } });
    }

    return { isVerified, backupCodes };
  }

  async disableMfaCode(accountId: number) {
    return await this.accountService.updateMfaCommonKey({ accountId, secret: null });
  }

  async reissueBackupCode(accountId: number) {
    const deletedResult = await this.mfaBackupRepository.softDelete({ accountId });

    if (!deletedResult) {
      return this.response({ message: 'バックアップコードの作成に失敗しました。' });
    }

    const backupCodes = this.generateBackupCodes();

    if (backupCodes?.length) {
      const backupResult = await this.mfaBackupRepository.save(backupCodes.map((code) => ({ accountId, backupCode: code })));

      if (!backupResult) {
        return this.response({ message: 'バックアップコードの作成に失敗しました。' });
      }
    }

    return backupCodes;
  }

  private generateBackupCodes(): string[] {
    const backupCodes = new Set<string>();
    while (backupCodes.size < 10) {
      const code = this.generateNumericCode(8);
      backupCodes.add(code);
    }

    return Array.from(backupCodes);
  }

  private generateNumericCode(length: number): string {
    let code = '';
    for (let i = 0; i < length; i++) {
      const digit = randomInt(0, 10);
      code += digit.toString();
    }

    return code;
  }
}
