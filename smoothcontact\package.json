{"name": "smooth_contact_app", "version": "1.0.0", "main": "app/index.js", "license": "UNLICENSED", "scripts": {"build-fe": "cd app/frontend && yarn build", "build-fe:embed": "cd app/frontend && yarn build:embed", "build-fe:html-embed": "cd app/frontend && yarn build:html-embed", "lint": "eslint app/frontend/src --max-warnings=0", "lint:fix": "eslint app/frontend/src --fix . --max-warnings=0", "ci": "npm ci && cd ./app/backend && npm ci && cd ../frontend && npm ci", "run-be": "cd ./app/backend && nodemon --exec nest start --ext js,ts --watch src/ --legacy-watch", "run-fe": "cd ./app/frontend && yarn dev", "dev": "npm-run-all --parallel run-be run-fe", "install-all": "yarn install && cd ./app/backend && yarn install && cd ../frontend && yarn install", "prepare": "husky install", "check-types": "npx tsc --noEmit --pretty"}, "dependencies": {"@nestjs/mapped-types": "*", "nodemon": "^3.1.0"}, "devDependencies": {"@commitlint/cli": "^19.2.2", "@commitlint/config-conventional": "^19.2.2", "@eslint/js": "^9.4.0", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "eslint": "^8.0.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-import-newlines": "^1.4.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-n": "^15.0.0 || ^16.0.0 ", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.0", "globals": "^15.4.0", "husky": "^8.0.0", "lint-staged": "^15.2.7", "npm-run-all": "^4.1.5", "prettier": "^3.3.2", "react": "18.2.0", "typescript": "*", "typescript-eslint": "^7.13.0"}, "author": "Weblife", "resolutions": {"@types/react": "17.0.30"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint app/frontend/src", "git add"]}}