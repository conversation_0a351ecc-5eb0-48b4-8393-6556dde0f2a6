import MuiAlert, { AlertProps } from '@mui/material/Alert';
import Snackbar from '@mui/material/Snackbar';
import React, { createContext, useContext, useState } from 'react';

interface ToastContextData {
  toast: ({ message, isError }: ToastOptions) => void;
}

export interface ToastOptions {
  isError?: boolean;
  message: string;
}

const ToastContext = createContext<ToastContextData | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }

  return context;
};

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [severity, setSeverity] = useState<AlertProps['severity']>('success');

  const toast = ({ message, isError }: ToastOptions) => {
    setMessage(message);
    setSeverity(isError ? 'error' : 'success');
    setOpen(true);
  };

  const handleCloseToast = () => {
    setOpen(false);
  };

  return (
    <ToastContext.Provider value={{ toast }}>
      {children}
      <Snackbar open={open} autoHideDuration={6000} onClose={handleCloseToast} anchorOrigin={{ vertical: 'top', horizontal: 'right' }}>
        <MuiAlert elevation={6} variant="filled" onClose={handleCloseToast} severity={severity}>
          {message}
        </MuiAlert>
      </Snackbar>
    </ToastContext.Provider>
  );
};
