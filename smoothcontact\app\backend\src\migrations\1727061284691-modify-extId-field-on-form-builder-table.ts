import { MigrationInterface, QueryRunner } from 'typeorm';

export class ModifyExtIdFieldOnFormBuilderTable1727061284691 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}form_builder`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE ${this.TABLE_NAME} MODIFY COLUMN ext_id varchar(64)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE ${this.TABLE_NAME} MODIFY COLUMN ext_id varchar(6)`);
  }
}
