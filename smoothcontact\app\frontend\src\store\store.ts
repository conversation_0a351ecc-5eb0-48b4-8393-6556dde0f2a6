import { combineReducers, configureStore } from '@reduxjs/toolkit';
import jsCookie from 'js-cookie';
import { PersistConfig, persistReducer, persistStore, WebStorage } from 'redux-persist';
import createWebStorage from 'redux-persist/lib/storage/createWebStorage';
import createSagaMiddleware from 'redux-saga';

import { rootSaga } from './sagas';
import { appSlice } from './slices/app';
import { authSlice } from './slices/authentication';
import { createNoopStorage } from '@/utils/reduxHelper';

const rootReducer = combineReducers({
  [authSlice.name]: authSlice.reducer,
  [appSlice.name]: appSlice.reducer,
});

export type RootState = ReturnType<typeof rootReducer>;

const createStorage = (storage: WebStorage) => {
  return {
    getItem: async (key: any) => {
      const data = jsCookie.get(key);
      if (data) {
        return data;
      }

      return storage.getItem(key);
    },
    removeItem: (key: any) => {
      return storage.removeItem(key);
    },
    setItem: (key: any, data: any) => {
      let newData = data;
      try {
        newData = JSON.parse(data) || {};

        if (newData.authentication) {
          const authenticationData = JSON.parse(newData.authentication) || {};

          if (typeof authenticationData.remember == 'boolean' && !authenticationData.remember) {
            storage.removeItem(key);

            return new Promise((resolve) => {
              resolve(jsCookie.set(key, data));
            });
          }
        }
      } catch (error) {
        console.error(error);
      }

      jsCookie.remove(key);

      return storage.setItem(key, data);
    },
  };
};

const persistConfig: PersistConfig<RootState> = {
  key: import.meta.env.VITE_APP_PERSIST_CONFIG,
  storage: typeof window !== 'undefined' ? createStorage(createWebStorage('local')) : createNoopStorage,
  whitelist: ['authentication'],
};

const sagaMiddleware = createSagaMiddleware();
const persistedReducer = persistReducer(persistConfig, rootReducer);

const store = configureStore({
  reducer: persistedReducer,
  middleware(getDefaultMiddlewares) {
    return getDefaultMiddlewares({ serializableCheck: false }).concat(sagaMiddleware);
  },
});

sagaMiddleware.run(rootSaga);

export { store };
export const persistor = persistStore(store);
export type AppDispatch = typeof store.dispatch;
(function () {
  if (typeof window !== 'undefined') {
    // (window as never).dispatchEventShowModalChange = () => {
    //   store.dispatch(appSlice.actions.showModalConfirmUnChanged());
    // };
  }
})();
