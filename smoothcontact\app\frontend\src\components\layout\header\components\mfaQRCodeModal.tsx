import { Course } from '@/common/constants';
import SCLoadingButton from '@/components/common/SCLoadingButton';
import SCModal from '@/components/common/SCModal';
import { useFormHandler } from '@/hooks/useFormHandler';
import { checkFieldErrorHelper } from '@/utils/validate';
import { Box, Button, Card, CardContent, Grid, TextField, Typography } from '@mui/material';

export interface MfaQRCodeModalProps {
  title: string;
  open: boolean;
  onClose: () => void;
  formHandler: ReturnType<typeof useFormHandler>;
  data: any;
  backupCodes: string[];
  downloadBackupCode: () => void;
}

export const MfaQRCodeModal = ({ title, open, onClose, formHandler, data, backupCodes, downloadBackupCode }: MfaQRCodeModalProps) => {
  return (
    <SCModal title={title ?? '二段階認証の設定'} width={550} isOpen={open} onClose={onClose} closeBtnLabel={'キャンセル'} enableBackDropClick={true}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          borderRadius: 2,
          padding: '20px',
          minWidth: '500px',
        }}
        gap={2}
      >
        <Typography sx={{ textAlign: 'center' }}>QRコードをAuthenticatorで 読み込んで下さい</Typography>
        {!!backupCodes?.length && (
          <Card sx={{ textAlign: 'center' }} variant="outlined">
            <CardContent>
              <Grid container spacing={2} mt={1}>
                {backupCodes?.map?.((code, index) => (
                  <Grid item xs={6} key={index}>
                    <Typography variant="body1">{code}</Typography>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        )}
        {!data?.isVerifiedMfa && data?.course === Course.ENTERPRISE && (
          <Box component="form" onSubmit={formHandler?.handleSubmit} noValidate sx={{ width: '100%' }} gap={2}>
            <Box m={2} textAlign={'center'}>
              <img src={data?.qrCode} alt="QR Code" className="w-full h-auto" />
            </Box>
            <Typography textAlign={'center'} mb={2} variant="body1" gutterBottom>
              {data?.base32}
            </Typography>
            <TextField
              fullWidth
              label="MFA Code"
              placeholder="123456"
              name="code"
              autoFocus
              {...formHandler?.register('code')}
              value={formHandler?.values?.code}
              error={!!checkFieldErrorHelper(formHandler, 'code')}
              helperText={checkFieldErrorHelper(formHandler, 'code')}
              InputLabelProps={{ shrink: true }}
            />
            <SCLoadingButton
              fullWidth
              className="btn-black"
              disabled={!formHandler?.dirty || !formHandler?.isValid}
              variant="contained"
              type="submit"
              color="primary"
              sx={{ mt: 1 }}
            >
              次へ
            </SCLoadingButton>
          </Box>
        )}
        {data?.isVerifiedMfa && data?.course === Course.ENTERPRISE && (
          <Box>
            <Typography>
              機器の紛失や機種変更により、Google
              Authenticatorを使用出来なくなった場合は、8桁のバックアップコードのいずれかを入力することでログイン出来ます
            </Typography>

            <Button variant="outlined" color="primary" onClick={downloadBackupCode} sx={{ mt: 2 }}>
              バックアップコードのダウンロード
            </Button>
          </Box>
        )}
      </Box>
    </SCModal>
  );
};
