import React, { FC, useState } from 'react';
import { Stack, Typography, Chip } from '@mui/material';
import { FormReportRangeDate } from '@/utils/formBuilderUtils';
import { getChartComponents } from './chartComponents';

interface ReportBarChartProps {
  chartData: {
    lastMonth: { [key: string]: number };
    lastSixMonths: { [key: string]: number };
    lastYear: { [key: string]: number };
    allTime: { [key: string]: number };
  };
}

const ReportBarChart: FC<ReportBarChartProps> = ({ chartData }) => {
  const [filterRangeDate, setFilterRangeDate] = useState('lastMonth');

  const handleFilterChange = (rangeDate: string) => {
    setFilterRangeDate(rangeDate);
  };

  const chartComponents = getChartComponents(chartData);

  return (
    <Stack sx={{ border: '1px solid #E6E6E6', borderRadius: '10px', padding: '10px' }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ padding: '5px 10px' }}>
        <Typography color="text.primary" variant="h6">
          回答数
        </Typography>
        <Stack direction="row" spacing={1} sx={{ border: '1px solid #E6E6E6', borderRadius: 5, padding: '5px 20px' }}>
          {Object.keys(FormReportRangeDate).map((key) => (
            <Chip
              key={key}
              label={FormReportRangeDate[key]}
              onClick={() => handleFilterChange(key)}
              color={filterRangeDate === key ? 'primary' : 'default'}
              sx={{
                backgroundColor: filterRangeDate === key ? '#272937' : 'white',
                color: filterRangeDate === key ? 'white' : '#272937',
                padding: '0 3px',
                height: '20px',
              }}
            />
          ))}
        </Stack>
      </Stack>
      {chartComponents[filterRangeDate]}
    </Stack>
  );
};

export default ReportBarChart;
