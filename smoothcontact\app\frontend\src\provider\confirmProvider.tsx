import { createContext, type ReactNode, useCallback, useMemo, useState, useContext } from 'react';

import SCModal from '@/components/common/SCModal';
import { Button, Typography } from '@mui/material';

export const ConfirmCtx = createContext<Nullable<ConfirmContext>>(null);

interface Props {
  children: ReactNode;
}

export interface ConfirmOptions {
  title?: string;
  subtitle?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  type?: 'success' | 'error' | 'warning' | 'info';
  width?: number;
}

export type Nullable<T> = T | null;

export interface ConfirmContext {
  show: (options: Nullable<ConfirmOptions>) => void;
}

export function ConfirmProvider({ children }: Props) {
  const [confirm, setConfirm] = useState<Nullable<ConfirmOptions>>(null);
  const [open, toggle] = useState(false);

  const show = useCallback(
    (confirmOptions: Nullable<ConfirmOptions>) => {
      setConfirm(confirmOptions);
      toggle(true);
    },
    [toggle]
  );

  const onConfirm = () => {
    confirm?.onConfirm?.();
    toggle(false);
  };

  const onCancel = () => {
    confirm?.onCancel?.();
    toggle(false);
  };

  const value = useMemo(() => ({ show }), [show]);

  return (
    <ConfirmCtx.Provider value={value}>
      <SCModal
        title={confirm?.title}
        width={confirm?.width ?? 400}
        isOpen={open}
        onClose={onCancel}
        closeBtnLabel={confirm?.cancelText}
        primaryAction={
          <Button color="primary" onClick={onConfirm}>
            {confirm?.confirmText}
          </Button>
        }
      >
        <Typography variant="body1">{confirm?.subtitle}</Typography>
      </SCModal>
      {children}
    </ConfirmCtx.Provider>
  );
}

export const useConfirm = () => {
  const context = useContext(ConfirmCtx);
  if (!context) {
    throw new Error('useConfirm must be used within a ConfirmProvider');
  }

  return context;
};
