import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

import { SubmissionFormValue } from '@/modules/submission/entities/form-submission.entity';
import { FormControlNames } from '@/types/controlName.type';

export type FormSubmissionData = {
  token?: string;
  formValues: SubmissionFormValue[];
};

export class UploadDto {
  @IsNotEmpty()
  date: string;
}

export class HtmlFormSubmissionData {
  @Transform(({ value }) => {
    const formValues = JSON.parse(value);

    const getControlName = (controlValue) => {
      switch (controlValue) {
        case 'textarea':
          return FormControlNames.INPUT_MULTILINE;

        case 'checkbox':
          return FormControlNames.CHECKLIST;

        case 'radio':
          return FormControlNames.RADIO;

        case 'select':

        case 'select-one':
          return FormControlNames.DROPDOWN;

        case 'date':
          return FormControlNames.DATE;

        case 'number':
          return FormControlNames.INPUT_TEXTFIELD;

        case 'file':
          return FormControlNames.FILE_UPLOAD;

        default:
          return FormControlNames.INPUT_TEXTFIELD;
      }
    };

    return formValues.map((formValue: SubmissionFormValue) => {
      return {
        ...formValue,
        controlName: getControlName(formValue.controlName),
      };
    });
  })
  formValues: SubmissionFormValue[];

  date?: Date;

  gRecaptchaResponse?: string;
}
