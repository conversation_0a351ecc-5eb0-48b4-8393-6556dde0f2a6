import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import ja from './languages/ja.json';
import { setLocale } from 'yup';

i18n.use(initReactI18next).init({
  fallbackLng: 'ja',
  debug: true,
  preload: false,
  load: 'languageOnly',
  interpolation: {
    escapeValue: false, // not needed for react as it escapes by default
  },
  resources: {
    ja,
  },
});

// Set the locale for Yup
// First use in FormElementEmail.tsx
setLocale({
  mixed: {
    default: 'validation.invalid',
    required: 'validation.required',
  },
  string: {
    email: 'validation.email',
  },
});

export default i18n;
