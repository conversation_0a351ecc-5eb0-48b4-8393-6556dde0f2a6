import React, { FC } from 'react';
import { Autocomplete, Stack, TextField } from '@mui/material';

interface SettingAutocompleteProps {
  label: string;
  value: string;
  inputValue: string;
  onChange: (value: string | null) => void;
  onInputChange: (inputValue: string) => void;
  options: string[];
}

const SettingAutocomplete: FC<SettingAutocompleteProps> = ({ label = 'フォント', value, inputValue, onChange, onInputChange, options }) => (
  <Stack direction="row" spacing={2}>
    <Autocomplete
      value={value}
      inputValue={inputValue}
      onChange={(event, newValue) => onChange(newValue)}
      onInputChange={(event, newInputValue) => onInputChange(newInputValue)}
      options={options}
      fullWidth
      renderInput={(params) => <TextField {...params} label={label} />}
    />
  </Stack>
);

export default SettingAutocomplete;
