import React from 'react';
import Box from '@mui/material/Box';
import { Container, CssBaseline } from '@mui/material';
import ResponsiveAppBar from '../header';

export const PublicLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <Box>
      <ResponsiveAppBar showAvatar={false} />
      <Container component="main">
        <CssBaseline />
        <Box
          sx={{
            marginTop: 4,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          {children}
        </Box>
      </Container>
    </Box>
  );
};
