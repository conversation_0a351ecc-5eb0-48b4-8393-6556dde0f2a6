import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { RootService } from '@/core/services/root.service';
import { FormBuilderEntity } from '@/modules/form-builder/entities/form-builder.entity';

@Injectable()
export class FormService extends RootService {
  @InjectRepository(FormBuilderEntity)
  private readonly formBuilderRepository: Repository<FormBuilderEntity>;

  async getFormBuilderById(id: number, userId?: number): Promise<FormBuilderEntity> {
    if (!id) {
      return null;
    }

    const condition = userId !== null ? { id, createdBy: userId } : { id };

    return this.formBuilderRepository.findOneBy(condition);
  }
}
