import React from 'react';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Stack, { StackProps } from '@mui/material/Stack';

interface ResponsiveStackProps extends StackProps {
  mobileDirection?: 'column' | 'row' | 'row-reverse' | 'column-reverse';
  forceDirection?: 'column' | 'row' | 'row-reverse' | 'column-reverse';
  forceAlignItems?: 'left' | 'center' | 'right';
}

const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  mobileDirection,
  forceDirection,
  forceAlignItems,
  alignItems = 'column',
  direction,
  ...props
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  if (forceDirection) {
    return <Stack direction={forceDirection} alignItems={forceAlignItems ?? (!isMobile && alignItems)} {...props} />;
  }

  return <Stack direction={isMobile ? mobileDirection : direction} alignItems={forceAlignItems ?? (!isMobile && alignItems)} {...props} />;
};

export default ResponsiveStack;
