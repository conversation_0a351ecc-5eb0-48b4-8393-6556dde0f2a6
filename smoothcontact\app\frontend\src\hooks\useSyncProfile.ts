import { getMeRequestConfig } from '@/services/account.service';
import { useAppDispatch, useAppSelector } from '@/store/hook';
import { appAction } from '@/store/slices/app';
import { useEffect } from 'react';
import useAxios from './useAxios';
import { getAppAccessToken } from '@/utils/helper';

export function useSyncProfile() {
  const dispatch = useAppDispatch();
  const { profile } = useAppSelector((state) => ({
    profile: state.app.profile,
  }));

  const { apiCaller } = useAxios();

  const accessToken = getAppAccessToken();
  const isAuthenticated = !!accessToken;

  useEffect(() => {
    const fetchProfile = async () => {
      const result = await apiCaller(getMeRequestConfig);

      if (result.success) {
        dispatch(appAction.setAppState({ profile: result.data, profileLoaded: true, profileFetchStatus: { forceGetProfile: false } }));
      }
    };
    if (isAuthenticated) fetchProfile();
  }, []);

  return {
    profile,
  };
}
