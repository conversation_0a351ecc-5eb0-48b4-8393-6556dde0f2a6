import React from 'react';
import { styled } from '@mui/system';
import { alpha } from '@mui/material/styles';
import InputBase from '@mui/material/InputBase';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';

interface FocusInInputProps {
  name?: string;
  className?: string;
  type?: string;
  placeholder?: string;
  error?: boolean;
  helperText?: string;
  value?: any;
  onChange?: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>;
  onBlur?: React.FocusEventHandler<HTMLTextAreaElement | HTMLInputElement>;
  multiline?: boolean;
  minRows?: number;
  label?: string;
  background?: string;
  borderColor?: string;
}

const FocusInInput = styled(InputBase)<{ background?: string; borderColor?: string }>(({ theme, background, borderColor }) => ({
  '& .MuiInputBase-input': {
    borderRadius: '4px',
    padding: '12px 12px',
    backgroundColor: background || 'transparent',
    border: `1px solid ${borderColor}`,
    '&:focus': {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 2px`,
      borderColor: 'transparent',
    },
  },
}));

const FocusInInputComponent: React.FC<FocusInInputProps> = ({
  className,
  error = false,
  helperText,
  multiline = false,
  minRows,
  value,
  name,
  type = 'text',
  placeholder,
  onChange,
  onBlur,
  background,
  borderColor,
}) => {
  return (
    <FormControl error={error} className={className} sx={{ width: '100%' }}>
      <FocusInInput
        name={name}
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        error={error}
        multiline={multiline}
        rows={minRows}
        background={background}
        borderColor={borderColor}
      />
      {error && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default FocusInInputComponent;
