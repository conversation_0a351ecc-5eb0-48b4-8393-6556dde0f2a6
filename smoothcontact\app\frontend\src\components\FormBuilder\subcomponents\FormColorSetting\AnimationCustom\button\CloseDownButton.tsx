import React from 'react';
import { styled } from '@mui/system';

const Button = styled('button')(`
  width: 100%;
  height: 40px;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  transition: all 0.8s ease;
  cursor: pointer;
  position: relative;
  background-color: #0794ff;
  color: white;

  &::before {
    content: '';
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background-color: #fff;
    opacity: 0;
    transition:
      height 0.3s ease,
      opacity 0.3s ease;
  }

  &:hover::before {
    height: 100%;
    opacity: 1;
  }

  &:hover {
    color: #1976d2;
    background-color: #fff;
    border: 1px solid #1976d2;
    opacity: 0.8;
  }

`);

const CloseDownButton: React.FC<{ label: string }> = ({ label }) => {
  return <Button>{label}</Button>;
};

export default CloseDownButton;
