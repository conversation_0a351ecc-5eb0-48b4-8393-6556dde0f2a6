class LoginRequestWithCallbackAndSettingDTO {
  cb?: string;
  s?: string;
}

export class LoginRequestDTO extends LoginRequestWithCallbackAndSettingDTO {
  email: string;
  pwd: string;
}

export class MfaVerifyRequestDTO extends LoginRequestWithCallbackAndSettingDTO {
  accountId: number;
  code: string;
}

export class BackupCodeVerifyRequestDTO extends LoginRequestWithCallbackAndSettingDTO {
  accountId: number;
  code: string;
}
