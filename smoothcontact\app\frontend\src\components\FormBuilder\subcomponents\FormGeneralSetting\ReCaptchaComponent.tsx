import { FC } from 'react';
import { FormikValues } from 'formik';
import { useTranslation } from 'react-i18next';
import { Box, TextField, Typography } from '@mui/material';
import SettingItem from './SettingItemComponent';
import SwitchStyled from '@/components/common/SCToggleSwitch';

interface ReCaptchaProps {
  form: FormikValues;
  isHtmlForm?: boolean;
}

const ReCaptcha: FC<ReCaptchaProps> = ({ form, isHtmlForm = false }) => {
  const { t } = useTranslation();

  const isEnable = form?.values?.isSettingReCAPTCHA ?? true;
  const checkboxLabel = isEnable ? t('form_builder.reCaptcha.checkbox_text_enable') : t('form_builder.reCaptcha.checkbox_text_disable');

  return (
    <>
      <SettingItem label={t('form_builder.reCaptcha.label')} isEnable={isEnable} />
      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'left', alignItems: 'center' }}>
        <SwitchStyled checked={isEnable} name="isSettingReCAPTCHA" {...form.register('isSettingReCAPTCHA')} />
        {isEnable && <Typography>{checkboxLabel}</Typography>}
      </Box>
      {isEnable && isHtmlForm && <TextField size="small" {...form.register('captchaKey')} />}
    </>
  );
};

export default ReCaptcha;
