import useAxios from '@/hooks/useAxios';
import { useF<PERSON><PERSON>and<PERSON> } from '@/hooks/useFormHandler';
import { useToast } from '@/provider/toastProvider';
import { createMfaCodeRequestConfig, disableMfaCodeRequestConfig, reissueBackupCodeRequestConfig } from '@/services/mfa.service';
import { useAppDispatch, useAppSelector } from '@/store/hook';
import { getAppAccessToken } from '@/utils/helper';
import { useTheme } from '@mui/material';
import React, { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { MfaVerifyRequestDTO } from './dto/request.dto';
import { validationSchema } from './validator';
import { HttpStatusCode } from 'axios';
import { appAction } from '@/store/slices/app';

export type FormValue = {
  code: string;
};

export default function useLogic() {
  const navigate = useNavigate();
  const { toast } = useToast();

  const { apiCaller } = useAxios();
  const dispatch = useAppDispatch();
  const [anchorElUser, setAnchorElUser] = React.useState<null | HTMLElement>(null);
  const [showMfaSetting, setShowMfaSetting] = React.useState(false);
  const [showMfaQRCode, setShowMfaQRCode] = React.useState(false);
  const [backupCodes, setBackupCodes] = React.useState([]);
  const { profile } = useAppSelector((state) => ({
    profile: state.app.profile,
  }));
  const accessToken = getAppAccessToken();
  const isAuthenticated = !!accessToken;

  const isFromPopup: boolean = useMemo(() => {
    return profile?.callbackUrl !== undefined && profile?.setting !== undefined;
  }, [profile]);

  const isFromOem: boolean = useMemo(() => {
    return profile?.oemId !== undefined;
  }, [profile]);

  const theme = useTheme();

  const handleOpenUserMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElUser(event.currentTarget);
  };

  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };

  const handleMfaSetting = () => {
    handleCloseUserMenu();
    setShowMfaSetting(true);
  };

  const redirectDashboard = () => {
    navigate('/form-builder');
    handleCloseUserMenu();
  };

  const redirectProfile = () => {
    navigate('/profile');
    handleCloseUserMenu();
  };

  const handleEnableMfa = () => {
    setShowMfaQRCode(true);
    setShowMfaSetting(false);
    handleCloseUserMenu();
  };

  const disableMfaSetting = async () => {
    const result: any = await apiCaller(disableMfaCodeRequestConfig());

    if (result?.success) {
      toast({ isError: false, message: 'MFAが無効になりました' });
      dispatch(appAction.setAppState({ profile: { ...profile, isVerifiedMfa: false } }));
      setBackupCodes([]);

      return;
    }
  };

  const createMfaCode = async (formData: FormValue) => {
    const dataBody = new MfaVerifyRequestDTO();
    dataBody.code = formData.code;
    dataBody.secret = profile.base32;

    const result: any = await apiCaller(createMfaCodeRequestConfig(dataBody));

    if (result?.success) {
      toast({ isError: false, message: 'MFAがチェックされました' });
      setBackupCodes(result?.data?.backupCodes);
      dispatch(appAction.setAppState({ profile: { ...profile, isVerifiedMfa: true } }));
      formHandler.resetForm({ values: { code: '' } });

      return;
    }

    if (result?.statusCode !== HttpStatusCode.BadRequest) {
      toast({ isError: true, message: result.message });
    }

    formHandler.setErrors(result.messageErrors ?? {});
  };

  const reissueBackupCode = async () => {
    const result: any = await apiCaller(reissueBackupCodeRequestConfig());

    if (result?.success) {
      toast({ isError: false, message: 'バックアップコードの再発行が成功しました。' });
      setBackupCodes(result?.data);
      setShowMfaSetting(false);
      setShowMfaQRCode(true);

      return;
    }

    if (result?.statusCode !== HttpStatusCode.BadRequest) {
      toast({ isError: true, message: result.message });
    }

    formHandler.setErrors(result.messageErrors ?? {});
  };

  const formHandler = useFormHandler<FormValue>({
    initialValues: { code: '' },
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: async (values) => {
      await createMfaCode(values);
    },
  });

  const downloadBackupCodes = () => {
    const textContent = backupCodes?.join('\n');

    const blob = new Blob([textContent], { type: 'text/plain' });

    const url = window.URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'backup_codes.txt';

    a.click();

    window.URL.revokeObjectURL(url);
  };

  return {
    anchorElUser,
    profile,
    theme,
    isFromPopup,
    isFromOem,
    isAuthenticated,
    showMfaQRCode,
    showMfaSetting,
    backupCodes,
    formHandler,
    handleOpenUserMenu,
    handleCloseUserMenu,
    redirectDashboard,
    redirectProfile,
    handleMfaSetting,
    setShowMfaSetting,
    setShowMfaQRCode,
    handleEnableMfa,
    reissueBackupCode,
    disableMfaSetting,
    downloadBackupCodes,
  };
}
