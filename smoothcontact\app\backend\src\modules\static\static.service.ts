import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as fs from 'fs';
import { Repository } from 'typeorm';

import { RootService } from '@/core/services/root.service';
import { FormBuilderEntity } from '@/modules/form-builder/entities/form-builder.entity';
import { stripHtmlTags } from '@/utils/helpers';

import { WebFontService } from '../web-font/web-font.service';

interface StaticRenderTemplateParams {
  title?: string;
  url?: string;
  image?: string;
  allowSearchEngine?: boolean;
  GATrackingId?: string;
  GoogleAdsGlobalTag?: string;
  GoogleAdsEventSnippet?: string;
  hasYahooAds?: boolean;
  YahooAdsEventSnippet?: string;
  pageData?: string;
  formFonts?: string[];
}

@Injectable()
export class StaticService extends RootService {
  @InjectRepository(FormBuilderEntity)
  private readonly formBuilderRepository: Repository<FormBuilderEntity>;

  constructor(private readonly webFontService: WebFontService) {
    super();
  }

  async getFormBuilderByExtId(extId: string): Promise<FormBuilderEntity> {
    return await this.formBuilderRepository.findOneBy({
      extId,
    });
  }

  // eslint-disable-next-line complexity
  async renderTemplateByHtmlFile(htmlFile: string, params: StaticRenderTemplateParams, hasSnippet: boolean = false): Promise<string> {
    const fileContent = fs.readFileSync(htmlFile, 'utf8');
    const headIndex = fileContent.indexOf('<!-- HEAD -->');

    if (headIndex !== -1) {
      const metaPreventRobots = params.allowSearchEngine ? '<meta name="robots" content="index">' : '<meta name="robots" content="noindex">';
      const googleTagId = this.getGoogleTagIdFromSnippet(params.GoogleAdsEventSnippet || '');
      const googleGlobalTag = this.renderGoogleGlobalTag(params.GATrackingId, googleTagId);
      const yahooGlobalTag = this.renderYahooGlobalTag(params.hasYahooAds);
      const googleFontTag = this.renderGoogleFontLink(params.formFonts);
      const japanFontFreeTag = await this.renderJapanFontFreeLink(params.formFonts);

      const metaTags = `
      <title>${params.title || ''}</title>
      ${metaPreventRobots}
      <meta name="title" content="${params.title || ''}">
      <meta name="description" content="${params.title || ''}">
      <!-- Open Graph / Facebook -->
      <meta property="og:type" content="website">
      <meta property="og:url" content="${params.url || ''}">
      <meta property="og:title" content="${params.title || ''}">
      <meta property="og:description" content="${params.title || ''}">
      <meta property="og:image" content="${params.image || ''}">
      <!-- Twitter -->
      <meta property="twitter:card" content="summary_large_image">
      <meta property="twitter:url" content="${params.url || ''}">
      <meta property="twitter:title" content="${params.title || ''}">
      <meta property="twitter:description" content="${params.title || ''}">
      <meta property="twitter:image" content="${params.image || ''}">
      ${googleGlobalTag}
      ${yahooGlobalTag}
      ${googleFontTag}
      ${japanFontFreeTag ?? ''}
      <script>
        window.pageData = ${params.pageData || '{}'};
      </script>
    `;

      const bodyScripts = hasSnippet
        ? `
        ${params.GoogleAdsEventSnippet || ''}
        ${params.YahooAdsEventSnippet || ''}
      `
        : '';

      return fileContent.replace('<!-- HEAD -->', metaTags).replace('<!-- BODY -->', bodyScripts);
    }

    return fileContent;
  }

  protected getGoogleTagIdFromSnippet(snippet: string): string {
    // Regular expression to match the Google Tag ID
    const regex = /([A-Z]{1,2}-\d+)/;
    const match = snippet.match(regex);
    if (match && match[1]) {
      return match[1];
    }

    return '';
  }

  protected renderGoogleGlobalTag(ga4?: string, adsId?: string): string {
    const trackingCode = stripHtmlTags(ga4 || adsId);
    if (!trackingCode) {
      return '';
    }

    return `
      <!-- Google tag (gtag.js) -->
      <script async src="https://www.googletagmanager.com/gtag/js?id=${trackingCode}"></script>
      <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
      
        gtag('config', '${trackingCode}');
      </script>
      ${ga4 && adsId ? `<script>gtag('config', '${stripHtmlTags(adsId)}');</script>` : ''}
    `;
  }

  protected renderYahooGlobalTag(enabled: boolean): string {
    if (!enabled) {
      return '';
    }

    return `
      <script async src="https://s.yimg.jp/images/listing/tool/cv/ytag.js"></script>
      <script>
      window.yjDataLayer = window.yjDataLayer || [];
      function ytag() { yjDataLayer.push(arguments); }
      ytag({"type":"ycl_cookie", "config":{"ycl_use_non_cookie_storage":true}});
      </script>
    `;
  }

  protected renderGoogleFontLink(fontFamily: string[]): string {
    if (!fontFamily || !fontFamily.length) {
      return '';
    }

    return `
      <link rel="preconnect" href="https://fonts.gstatic.com">
      <link href="https://fonts.googleapis.com/css2?family=${fontFamily.join('&family=')}&display=swap" rel="stylesheet">
    `;
  }

  protected async renderJapanFontFreeLink(fontFamily: string[]): Promise<string> {
    if (!fontFamily || !fontFamily.length) {
      return '';
    }

    const japanFontFree = await this.webFontService.getJapanFreeWebFonts(true);

    if (!japanFontFree?.JAPAN_FREE_FONT?.length) {
      return '';
    }

    const styleLinks = japanFontFree?.JAPAN_FREE_FONT?.filter((font) => fontFamily.includes(font.fontFamily))?.map((font) => font?.styleLink) ?? [];

    if (!styleLinks.length) {
      return '';
    }

    const styleLinkUnique = [...new Set(styleLinks)];

    return styleLinkUnique
      .map((style) => {
        return `<link rel="stylesheet" href="${style}">`;
      })
      .join('\n');
  }

  public extractFontFamilies(obj: Record<string, any>): string[] {
    const fontFamilies = new Set<string>();

    function traverse(object: Record<string, any>) {
      for (const key in object) {
        if (object[key] && typeof object[key] === 'object') {
          traverse(object[key]);
        } else if (key === 'fontFamily') {
          // Open Sans, sans-serif, remove the last part
          const fontFamily = object[key].split(',')[0];
          fontFamilies.add(fontFamily);
        }
      }
    }

    traverse(obj);

    return Array.from(fontFamilies);
  }
}
