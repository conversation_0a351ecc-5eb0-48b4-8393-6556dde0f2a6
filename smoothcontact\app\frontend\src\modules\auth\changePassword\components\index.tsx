import SCLoadingButton from '@/components/common/SCLoadingButton';
import { useFormHandler } from '@/hooks/useFormHandler';
import { checkFieldErrorHelper } from '@/utils/validate';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';

export interface Props {
  formHandler: ReturnType<typeof useFormHandler>;
  loading?: boolean;
}

function ChangePassword({ formHandler, loading }: Props) {
  return (
    <Container component="main">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          padding: '20px',
          minWidth: '400px',
        }}
      >
        <Typography component="h1" variant="h5">
          パスワードを再設定
        </Typography>
        <Typography sx={{ mt: 2 }}>新しいパスワードを入力し「再設定」をクリックしてください</Typography>
        <Box component="form" onSubmit={formHandler?.handleSubmit} noValidate sx={{ mt: 2, width: '100%' }}>
          <TextField
            size="small"
            margin="normal"
            name="pwd"
            fullWidth
            label="パスワード"
            type="password"
            {...formHandler?.register('pwd')}
            error={!!formHandler?.errors?.pwd}
            helperText={checkFieldErrorHelper(formHandler, 'pwd') ?? '※半角英数字6文字以上16文字以内で設定してください。 記号は「_ -」が使用できます'}
          />
          <TextField
            size="small"
            margin="normal"
            name="rePwd"
            fullWidth
            label="パスワード（確認用）"
            type="password"
            {...formHandler?.register('rePwd')}
            error={!!formHandler?.errors?.rePwd}
            helperText={checkFieldErrorHelper(formHandler, 'rePwd')}
          />
          <SCLoadingButton
            disabled={!formHandler?.isValid || !formHandler?.dirty}
            loading={loading}
            className="btn-black"
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 2, mb: 2 }}
          >
            再設定
          </SCLoadingButton>
        </Box>
      </Box>
    </Container>
  );
}

export default ChangePassword;
