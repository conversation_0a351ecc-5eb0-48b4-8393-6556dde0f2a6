import SCModal from '@/components/common/SCModal';
import { useFormHandler } from '@/hooks/useFormHandler';
import { ISO, isValidDate } from '@/utils/dateTime';
import { Button, Stack, Typography } from '@mui/material';
import { DateTimePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import * as Yup from 'yup';

export interface ScheduleModalProps {
  title?: string;
  open: boolean;
  onClose: () => void;
  onSubmit: (schedule: any) => void;
  item: any;
}

export const ScheduleModal = ({ title, open, onClose, onSubmit, item }: ScheduleModalProps) => {
  const validationSchema = Yup.object({
    releaseStartDate: Yup.date()
      .typeError('開始日時が無効です。')
      .test('is-valid', '開始日時が無効です。', function (value) {
        return isValidDate(value);
      })
      .required('開始日時を入力してください。'),
    releaseEndDate: Yup.date()
      .typeError('終了日時が無効です。')
      .nullable()
      .test('is-valid', '終了日時が無効です。', function (value) {
        if (!value) return true;

        return isValidDate(value);
      })
      .test('is-later', '終了日が開始日より後になる', function (value) {
        if (!value) return true;

        const { releaseStartDate } = this.parent;

        if (!releaseStartDate || !value) return true;

        const start = new Date(releaseStartDate);
        const end = new Date(value);

        return end > start;
      }),
  });

  const form = useFormHandler<any>({
    initialValues: {
      releaseStartDate: item?.releaseStartDate ?? '',
      releaseEndDate: item?.releaseEndDate ?? '',
    },
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: () => {},
  });

  return (
    <SCModal
      key={`schedule-modal-${item?.id}`}
      title={title ?? '公開期間を設定'}
      width={550}
      isOpen={open}
      onClose={onClose}
      closeBtnLabel={'キャンセル'}
      primaryAction={
        <Button disabled={!form?.isValid || !form?.dirty} color="primary" onClick={() => onSubmit(form.values)}>
          予約する
        </Button>
      }
    >
      <Stack component="form" onSubmit={form.handleSubmit} direction="column" gap={2}>
        <Typography variant="body1">公開期間を設定してください。ユーザーはURLにアクセスできますが、公開開始までは投稿することはできません</Typography>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <Stack direction="row" alignItems={'baseline'} gap={2}>
            <DateTimePicker
              slotProps={{
                textField: {
                  fullWidth: true,
                  placeholder: '公開開始日時',
                  variant: 'outlined',
                  error: !!form?.errors?.releaseStartDate,
                  helperText: form?.errors?.releaseStartDate ? String(form?.errors?.releaseStartDate) : '',
                },
              }}
              timeSteps={{ minutes: 5 }}
              format={ISO.DATE_TIME}
              {...form.register('releaseStartDate')}
              value={!form?.values?.releaseStartDate ? null : dayjs(form?.values?.releaseStartDate)}
              onChange={(value) => {
                form.setFieldValue('releaseStartDate', isValidDate(value) ? new Date(value.toISOString()) : '');
              }}
              closeOnSelect={false}
            />
            —
            <DateTimePicker
              slotProps={{
                textField: {
                  fullWidth: true,
                  placeholder: '公開終了日時',
                  variant: 'outlined',
                  error: !!form?.errors?.releaseEndDate,
                  helperText: form?.errors?.releaseEndDate ? String(form?.errors?.releaseEndDate) : '',
                },
              }}
              timeSteps={{ minutes: 5 }}
              format={ISO.DATE_TIME}
              {...form.register('releaseEndDate')}
              value={!form?.values?.releaseEndDate ? null : dayjs(form?.values?.releaseEndDate)}
              onChange={(value) => {
                form.setFieldValue('releaseEndDate', isValidDate(value) ? new Date(value.toISOString()) : '');
              }}
              closeOnSelect={false}
            />
          </Stack>
        </LocalizationProvider>
      </Stack>
    </SCModal>
  );
};
