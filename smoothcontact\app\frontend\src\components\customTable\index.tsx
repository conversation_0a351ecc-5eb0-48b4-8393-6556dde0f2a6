import { PaginationRes } from '@/common/dto/response';
import { OrderRequest } from '@/types/app';
import {
  Checkbox,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
  Typography,
} from '@mui/material';
import React from 'react';

export interface SCDataTableProps {
  columns: SCTableColumnProps[];
  data: any[];
  pagination: PaginationRes;
  order: OrderRequest;
  orderBy: string;
  showCheckbox?: boolean;
  handleChangePage: (event: unknown, newPage: number) => any;
  handleChangeRowsPerPage: (value: any) => any;
  handleSort: (event: React.MouseEvent<unknown>, property: string) => void;
  renderToolBar?: () => any;
  renderEmpty?: () => any;
}

export interface SCTableHeadProps {
  showCheckbox?: boolean;
  numSelected: number;
  onRequestSort: (event: React.MouseEvent<unknown>, property: string) => void;
  onSelectAllClick: (event: React.ChangeEvent<HTMLInputElement>) => void;
  order: OrderRequest;
  orderBy: string;
  rowCount: number;
  columns: SCTableColumnProps[];
}

export interface SCTableColumnProps {
  disablePadding?: boolean;
  sortable?: boolean;
  width?: number | string;
  id: string;
  title: string;
  className?: string;
  align?: 'left' | 'right' | 'center';
  render?: (value: any) => React.ReactNode;
}

function SCTableHead(props: SCTableHeadProps) {
  const { showCheckbox, onSelectAllClick, order, orderBy, numSelected, rowCount, onRequestSort, columns: headCells } = props;

  const createSortHandler = (property: string) => (event: React.MouseEvent<unknown>) => {
    onRequestSort(event, property);
  };

  return (
    <TableHead>
      <TableRow>
        {showCheckbox && (
          <TableCell padding="checkbox">
            <Checkbox
              color="primary"
              indeterminate={numSelected > 0 && numSelected < rowCount}
              checked={rowCount > 0 && numSelected === rowCount}
              onChange={onSelectAllClick}
              inputProps={{
                'aria-label': 'select all desserts',
              }}
            />
          </TableCell>
        )}
        {headCells?.map?.((headCell) => (
          <TableCell
            key={headCell.id}
            align={headCell?.align ? headCell?.align : 'left'}
            padding={headCell.disablePadding ? 'none' : 'normal'}
            sortDirection={orderBy === headCell.id ? 'asc' : 'desc'}
            width={headCell?.width ?? 'auto'}
          >
            {headCell?.sortable && (
              <TableSortLabel
                active={orderBy === headCell.id}
                direction={order === OrderRequest.ASC ? OrderRequest.ASC : OrderRequest.DESC}
                onClick={createSortHandler(headCell.id)}
              >
                {headCell?.title ?? ''}
              </TableSortLabel>
            )}
            {!headCell?.sortable && <Typography variant="body1">{headCell?.title ?? ''}</Typography>}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

const SCDataTable: React.FC<SCDataTableProps> = ({
  columns,
  data,
  pagination,
  order,
  orderBy,
  handleChangePage,
  handleChangeRowsPerPage,
  handleSort,
  renderToolBar,
  renderEmpty,
  showCheckbox,
}) => {
  const [selected, setSelected] = React.useState<readonly number[]>([]);

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = data?.map?.((n) => n.id);
      setSelected(newSelected);

      return;
    }

    setSelected([]);
  };

  const handleClick = (event: React.MouseEvent<unknown>, id: number) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected: readonly number[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(selected.slice(0, selectedIndex), selected.slice(selectedIndex + 1));
    }

    setSelected(newSelected);
  };

  const isSelected = (id: number) => selected.indexOf(id) !== -1;

  return (
    <Paper>
      <TableContainer>
        {renderToolBar?.()}
        <Table>
          <SCTableHead
            showCheckbox={showCheckbox}
            numSelected={selected.length}
            order={order}
            orderBy={orderBy}
            onSelectAllClick={handleSelectAllClick}
            onRequestSort={handleSort}
            rowCount={data?.length}
            columns={columns}
          />
          <TableBody>
            {data?.map?.((row, index) => {
              const isItemSelected = isSelected(Number(row.id));
              const labelId = `enhanced-table-checkbox-${index}`;

              return (
                <TableRow role="checkbox" aria-checked={isItemSelected} tabIndex={-1} key={row.id} selected={isItemSelected}>
                  {showCheckbox && (
                    <TableCell padding="checkbox">
                      <Checkbox
                        color="primary"
                        checked={isItemSelected}
                        onClick={(event) => handleClick(event, Number(row.id))}
                        inputProps={{
                          'aria-labelledby': labelId,
                        }}
                      />
                    </TableCell>
                  )}
                  {columns?.map?.((column) => {
                    return column.render ? (
                      <TableCell key={column.id}>{column.render(row)}</TableCell>
                    ) : (
                      <TableCell key={column.id}>{row[column.id]}</TableCell>
                    );
                  })}
                </TableRow>
              );
            })}
            {!data.length && (
              <TableRow>
                <TableCell colSpan={6}>
                  {renderEmpty ? (
                    renderEmpty()
                  ) : (
                    <Typography variant="body2" textAlign={'center'}>
                      {'データがありません'}
                    </Typography>
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 15, 20]}
          component="div"
          count={pagination.total}
          rowsPerPage={pagination.perPage}
          page={pagination.page - 1}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          showFirstButton
          showLastButton
        />
      </TableContainer>
    </Paper>
  );
};

export default SCDataTable;
