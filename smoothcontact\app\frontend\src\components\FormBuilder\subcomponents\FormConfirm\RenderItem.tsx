import { FormBorderColors } from '@/common/constants';
import SCSimpleCard from '@/components/common/SCSimpleCard';
import {
  FormElementChildrenType,
  FormItemValue,
  ItemAddressValue,
  ItemFullNameFullValue,
  ItemFullNameValue,
  ItemUploadValue,
} from '@/types/FormTemplateTypes';
import { FormControlDefaultValue, FormControlNames } from '@/utils/formBuilderUtils';
import { fileSizeFormatted, getBaseFileName, getPrefectureName, isImageFile } from '@/utils/helper';
import FilePresentOutlinedIcon from '@mui/icons-material/FilePresentOutlined';
import ImageIcon from '@mui/icons-material/Image';
import { Box, Stack } from '@mui/material';
import styled from '@mui/system/styled';
import React from 'react';

interface RenderItemProps {
  item: FormElementChildrenType;
  value?: FormItemValue;
  isPreview?: boolean;
}

const ItemLabel = styled(Box)`
  width: 100px;
  text-align: right;
`;

const RenderItem: React.FC<RenderItemProps> = ({ item, value, isPreview }) => {
  const itemValue = isPreview ? FormControlDefaultValue[item.controlName] : value;

  switch (item.controlName) {
    case FormControlNames.FULL_NAME:
      if (isPreview) {
        return FormControlDefaultValue[item.controlName];
      }

      if (item.isReduceFullName) {
        const itemValue = value;
        if (typeof itemValue === 'string') {
          return <>{itemValue}</>;
        } else {
          const fullName = value as ItemFullNameValue;

          return (
            <>
              <span>{fullName.name || FormControlDefaultValue[item.controlName]}</span>
              {fullName.pronunciation && <span> ({fullName.pronunciation})</span>}
            </>
          );
        }
      } else {
        const { firstName, lastName, lastNamePronunciation, firstNamePronunciation } = value as ItemFullNameFullValue;

        return (
          <Box display="flex">
            <Box width={150}>フルネーム</Box>
            <Stack direction="row" spacing={4}>
              <Box>
                <span>{lastName}</span>
                {lastNamePronunciation && <span> ({lastNamePronunciation})</span>}
              </Box>
              <Box>
                <span>{firstName}</span>
                {firstNamePronunciation && <span> ({firstNamePronunciation})</span>}
              </Box>
            </Stack>
          </Box>
        );
      }

    case FormControlNames.FILE_UPLOAD:
      const fileValue = value as ItemUploadValue;
      if (fileValue) {
        return (
          <Box>
            <SCSimpleCard sx={{ width: 300, mx: 'auto', p: 0, bgcolor: '#eee', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              {isImageFile(fileValue.type) ? (
                <img src={fileValue.url} alt="preview" style={{ width: '100%', height: '100%' }} />
              ) : (
                <Stack p={2} direction="column" alignItems="center" spacing={1}>
                  <FilePresentOutlinedIcon />
                  <span>
                    {getBaseFileName(fileValue.file)} ({fileSizeFormatted(fileValue.size)})
                  </span>
                </Stack>
              )}
            </SCSimpleCard>
          </Box>
        );
      }

      return (
        <Box>
          <SCSimpleCard
            sx={{ width: 300, height: 100, mx: 'auto', bgcolor: '#eee', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          >
            <ImageIcon sx={{ color: FormBorderColors.DEFAULT }} />
          </SCSimpleCard>
        </Box>
      );

    case FormControlNames.DROPDOWN:

    case FormControlNames.RADIO:
      const radioValue = isPreview ? item?.items?.[0]?.value : (value as string);

      return <>{radioValue}</>;

    case FormControlNames.CHECKLIST:
      const choiceAllValues = item?.items?.map?.((checkbox) => checkbox.value);
      const choiceValue = isPreview ? choiceAllValues : (value as string[]);

      return <>{choiceValue?.map?.((value) => <Box key={value}>{value}</Box>)}</>;

    case FormControlNames.ADDRESS:
      const address = itemValue as ItemAddressValue;

      return (
        <Box>
          <Stack spacing={1}>
            <Stack direction="row" alignItems="center" spacing={2}>
              <ItemLabel>郵便番号</ItemLabel>
              <Box width={100}>{address.postalCode}</Box>
            </Stack>
            <Stack direction="row" alignItems="center" spacing={2}>
              <ItemLabel>都道府県</ItemLabel>
              <Box width={100}>{getPrefectureName(address.prefecture)}</Box>
            </Stack>
            <Stack direction="row" alignItems="center" spacing={2}>
              <ItemLabel>市区町村</ItemLabel>
              <Box width={100}>{address.city}</Box>
            </Stack>
            <Stack direction="row" alignItems="center" spacing={2}>
              <ItemLabel>町名</ItemLabel>
              <Box width={100}>{address.town}</Box>
            </Stack>
            <Stack direction="row" alignItems="center" spacing={2}>
              <ItemLabel>番地等</ItemLabel>
              <Box width={100}>{address.street}</Box>
            </Stack>
            <Stack direction="row" alignItems="center" spacing={2}>
              <ItemLabel>建物名</ItemLabel>
              <Box width={100}>{address.building}</Box>
            </Stack>
          </Stack>
        </Box>
      );

    case FormControlNames.DATE:
      const dateDefaultValue = FormControlDefaultValue[item.controlName][item.dataType] || '-';
      const dateTimeValue = isPreview ? dateDefaultValue : itemValue;

      return <>{dateTimeValue}</>;

    case FormControlNames.INPUT_MULTILINE:
      return (
        <>
          <pre style={{ fontFamily: 'inherit' }}>
            <div dangerouslySetInnerHTML={{ __html: itemValue }}></div>
          </pre>
        </>
      );

    default:
      return <>{itemValue}</>;
  }
};

export default RenderItem;
