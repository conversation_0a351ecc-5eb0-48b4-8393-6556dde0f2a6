import { Box, Button, Container, CssBaseline, Typography } from '@mui/material';
import { Stack } from '@mui/system';

export const NotFoundModule: React.FC = () => {
  return (
    <Container component="main" maxWidth="xs">
      <CssBaseline />
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
        gap={1}
      >
        <Typography variant="h1" sx={{ fontWeight: 'bold', fontSize: 60 }} gutterBottom>
          404
        </Typography>
        <Stack textAlign={'center'}>
          <Typography variant="h2" gutterBottom>
            ページが見つかりません
          </Typography>
          <Typography variant="body1" gutterBottom>
            お探しのページが見つかりませんでした。
          </Typography>
        </Stack>
        <Button href="/form-builder" variant="contained" color="secondary">
          ホームへ戻る
        </Button>
      </Box>
    </Container>
  );
};
