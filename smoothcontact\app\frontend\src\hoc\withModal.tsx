import { IModalCommon, ModalCommonRef, SCModalCommon } from '@/components/common/SCModalCommon';
import { useRef } from 'react';

export type SetOpenModal = (newModalCommon: IModalCommon) => void;

export type HocModalProps = {
  setOpenModal: SetOpenModal;
};

type Difference<T, TExclude> = Pick<T, Exclude<keyof T, keyof TExclude>>;

export function withModal<T>(Component: React.FC<T>, customModalProps?: IModalCommon): React.FC<Difference<T, HocModalProps>> {
  const ModalHOC = (props: T & HocModalProps) => {
    const modalRef = useRef<ModalCommonRef>(null);

    const setOpenModal: SetOpenModal = (newModalCommon = {}) => {
      modalRef.current?.openModal?.(newModalCommon);
    };

    return (
      <>
        <SCModalCommon {...customModalProps} ref={modalRef} />
        <Component {...props} setOpenModal={setOpenModal} />
      </>
    );
  };

  return ModalHOC;
}
