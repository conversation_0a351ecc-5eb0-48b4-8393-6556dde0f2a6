import { Box, Container, CssBaseline } from '@mui/material';
import React from 'react';
import ResponsiveAppBar from '../header';

export const AuthLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <Box>
      <ResponsiveAppBar showAvatar={false} />
      <Container component="main" maxWidth="xs">
        <CssBaseline />
        <Box
          sx={{
            marginTop: 8,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          {children}
        </Box>
      </Container>
    </Box>
  );
};
