import SCLoadingButton from '@/components/common/SCLoadingButton';
import { useFormHandler } from '@/hooks/useFormHandler';
import { checkFieldErrorHelper } from '@/utils/validate';
import { ArrowForward } from '@mui/icons-material';
import { Checkbox, FormControlLabel, FormHelperText } from '@mui/material';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import CssBaseline from '@mui/material/CssBaseline';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { Stack } from '@mui/system';
import { Link } from 'react-router-dom';

export interface Props {
  loading?: boolean;
  registerSuccess: boolean;
  formHandler: ReturnType<typeof useFormHandler>;
}

function Register({ loading, registerSuccess, formHandler }: Props) {
  if (registerSuccess) {
    return (
      <Container component="main" sx={{ width: '460px' }}>
        <CssBaseline />
        <Box
          sx={{
            marginTop: 8,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            border: '1px solid #e0e0e0',
            padding: 2,
            minWidth: '400px',
          }}
        >
          <Typography component="h6" variant="h6" fontWeight={700}>
            確認メールを送信しました
          </Typography>
          <Stack direction="column" justifyContent="flex-start" alignItems="flex-start" gap={3} sx={{ mt: 4 }}>
            <Typography>
              <strong>{formHandler?.values?.email ?? '******'}</strong>
              に確認メールを送信しました。メールをご確認いただき、メールに記載されたURLをクリックして完了させてください。
            </Typography>
            <Typography>メールが届かない場合は受信設定をご確認のうえ、再送信してください。</Typography>
            <SCLoadingButton className="btn-black" loading={loading} type="submit" fullWidth variant="contained">
              再送信する
            </SCLoadingButton>
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Box>
                <Link to="login">
                  <Stack direction={'row'} alignItems={'center'} justifyContent={'flex-start'}>
                    <Typography>ログイン</Typography>
                    <ArrowForward />
                  </Stack>
                </Link>
              </Box>
            </Stack>
          </Stack>
        </Box>
      </Container>
    );
  }

  return (
    <Container component="main" sx={{ width: '460px' }}>
      <CssBaseline />
      <Box
        sx={{
          marginTop: 8,
          padding: '30px 16px',
          border: '1px solid #e0e0e0',
          minWidth: '400px',
        }}
      >
        <Typography component="h6" variant="h6" sx={{ textAlign: 'center' }}>
          新規登録
        </Typography>
        <Typography sx={{ mt: 2 }}>
          すでにアカウントをお持ちですか？
          <Link to="login" style={{ textDecoration: 'none' }}>
            ログイン
          </Link>
        </Typography>
        <Box component="form" onSubmit={formHandler?.handleSubmit} noValidate mt={3} sx={{ width: '100%' }}>
          <Stack direction={'row'} gap={2}>
            <TextField
              fullWidth
              name="lastName"
              label=""
              autoFocus
              {...formHandler?.register('lastName')}
              error={!!formHandler?.errors?.lastName}
              helperText={checkFieldErrorHelper(formHandler, 'lastName')}
            />
            <TextField
              fullWidth
              name="firstName"
              label="お名前"
              autoFocus
              {...formHandler?.register('firstName')}
              error={!!formHandler?.errors?.firstName}
              helperText={checkFieldErrorHelper(formHandler, 'firstName')}
            />
          </Stack>
          <TextField
            margin="normal"
            fullWidth
            label="メールアドレス"
            name="email"
            autoComplete="email"
            {...formHandler?.register('email')}
            error={!!formHandler?.errors?.email}
            helperText={checkFieldErrorHelper(formHandler, 'email')}
          />
          <TextField
            margin="normal"
            name="pwd"
            fullWidth
            label="パスワード"
            type="password"
            {...formHandler?.register('pwd')}
            error={!!formHandler?.errors?.pwd}
            helperText={
              checkFieldErrorHelper(formHandler, 'pwd') ?? '※半角英数字6文字以上16文字以内で設定してください。 記号は「_ -」が使用できます '
            }
          />
          <TextField
            margin="normal"
            name="rePwd"
            fullWidth
            label="パスワード（確認用）"
            type="password"
            {...formHandler?.register('rePwd')}
            error={!!formHandler?.errors?.rePwd}
            helperText={checkFieldErrorHelper(formHandler, 'rePwd')}
          />
          <Box sx={{ pl: 2 }}>
            <FormControlLabel
              control={
                <Checkbox
                  color="primary"
                  {...formHandler?.register('privacyPolicy', { nameOfValueProps: 'checked' })}
                  checked={formHandler?.values?.privacyPolicy}
                />
              }
              label={
                <Box>
                  <Link to="https://smoothcontact.jp/terms/index.html" target="_blank" rel="noopener" style={{ textDecoration: 'none' }}>
                    利用規約
                  </Link>
                  に同意する
                </Box>
              }
            />
            <FormHelperText sx={{ mt: -1, color: '#d32f2f' }}>{checkFieldErrorHelper(formHandler, 'privacyPolicy')}</FormHelperText>
            <FormControlLabel
              control={
                <Checkbox
                  color="primary"
                  {...formHandler?.register('termsOfUse', { nameOfValueProps: 'checked' })}
                  checked={formHandler?.values?.termsOfUse}
                />
              }
              label={
                <Box>
                  <Link to="https://web-life.co.jp/policy/" target="_blank" rel="noreferrer" style={{ textDecoration: 'none' }}>
                    プライバシーポリシー
                  </Link>
                  に同意する
                </Box>
              }
            />
            <FormHelperText sx={{ mt: -1, color: '#d32f2f' }}>{checkFieldErrorHelper(formHandler, 'termsOfUse')}</FormHelperText>
          </Box>
          <SCLoadingButton
            sx={{ mt: 2 }}
            loading={loading}
            disabled={!formHandler?.isValid || !formHandler?.values?.privacyPolicy || !formHandler?.values?.termsOfUse}
            type="submit"
            fullWidth
            variant="contained"
          >
            登録する
          </SCLoadingButton>
        </Box>
      </Box>
    </Container>
  );
}

export default Register;
