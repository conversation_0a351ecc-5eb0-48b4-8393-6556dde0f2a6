import useAxios from '@/hooks/useAxios';
import { useToast } from '@/provider/toastProvider';
import { shopifyLoginRequestConfig } from '@/services/account.service';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { setAppAuthTokens } from '@/utils/helper';

export default function useLogic() {
  const { toast } = useToast();
  const { apiCaller, loading } = useAxios();
  const navigate = useNavigate();

  const login = async (formData: any) => {
    const result: any = await apiCaller(shopifyLoginRequestConfig(formData));

    if (!result?.success) {
      navigate('/login');

      return;
    }

    setAppAuthTokens(result.data.accessToken, result.data.refreshToken);
    toast({ isError: false, message: 'ログインに成功しました' });
    navigate('/form-builder');
  };

  useEffect(() => {
    const url = new URL(window.location.href);

    if (url.searchParams.get('accessToken')) {
      login({ accessToken: url.searchParams.get('accessToken') });
    }
  }, []);

  return { login, loading };
}
