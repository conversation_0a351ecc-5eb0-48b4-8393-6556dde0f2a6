import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddTourCompletedFieldOnAccountTable1718869275357 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}account`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'tour_completed',
        type: 'tinyint',
        isNullable: false,
        default: 0,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'tour_completed',
        type: 'tinyint',
        isNullable: false,
      }),
    ]);
  }
}
