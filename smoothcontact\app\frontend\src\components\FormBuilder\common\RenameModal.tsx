import SCButton from '@/components/common/SCButton';
import SCModal from '@/components/common/SCModal';
import { FormControl, TextField } from '@mui/material';
import { Box } from '@mui/system';
import { useState } from 'react';

interface RenameModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRename: (newName: string) => void;
  currentName: string;
}

const RenameModal: React.FC<RenameModalProps> = ({ isOpen, onClose, onRename, currentName }) => {
  const [newName, setNewName] = useState(currentName);

  const handleRename = () => {
    onRename(newName);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <SCModal
      title={'フォーム名の変更'}
      width={400}
      isOpen={isOpen}
      closeBtnLabel={'キャンセル'}
      primaryAction={
        <SCButton color="primary" onClick={handleRename}>
          名前を変更
        </SCButton>
      }
      onClose={onClose}
    >
      <Box>
        <FormControl fullWidth>
          <TextField required value={newName} onChange={(e) => setNewName(e.target.value)} placeholder="新しい名前を入力" fullWidth />
        </FormControl>
      </Box>
    </SCModal>
  );
};

export default RenameModal;
