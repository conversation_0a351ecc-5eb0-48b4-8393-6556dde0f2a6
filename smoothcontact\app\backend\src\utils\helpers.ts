import * as crypto from 'crypto';
import * as CryptoJS from 'crypto-js';
import * as moment from 'moment';

import { prefectures } from '@/common/constant';
import { CheckHmacResult } from '@/modules/account/dto/response.dto';

export const deepClean = (o: any) => {
  for (const k in o) {
    if (!o[k] || typeof o[k] !== 'object') {
      continue;
    }

    deepClean(o[k]);
    if (Object.keys(o[k]).length === 0) {
      delete o[k];
    }
  }

  return o;
};

// Generate hash from input string
// With new system, we use salt to hash password. useSalt = true and outputFormat = 'hex'
// With old system, we hash password without salt, but we append 'salt' to password before hashing.
// useSalt = false and outputFormat = 'base64'
export const generateHash = (input: string, salt: string = '', outputFormat: 'hex' | 'base64' = 'hex', useSalt: boolean = true): string | null => {
  try {
    const hash = useSalt ? crypto.createHmac('sha256', salt) : crypto.createHash('sha256');
    hash.update(useSalt ? input : input + salt);

    return hash.digest(outputFormat);
  } catch (error) {
    return null;
  }
};

export const hashString = (stringOrig: string, salt = ''): string => {
  return generateHash(stringOrig, salt, 'hex', true);
};

export const adaptiveVerifyPassword = (password: string, salt: string, storedHash: string): boolean => {
  const isLegacyHash = /[A-Z]/.test(storedHash);
  const hashedPassword = isLegacyHash ? generateHash(password, salt, 'base64', false) : hashString(password, salt);

  return hashedPassword !== null && hashedPassword === storedHash;
};

export const checkHmac = (paramString: string): CheckHmacResult => {
  const urlSearchParam = new URLSearchParams(paramString);
  const hmac = urlSearchParam.get('hmac');

  urlSearchParam.delete('hmac');
  const result: CheckHmacResult = {
    host: urlSearchParam.get('host'),
    session: urlSearchParam.get('session'),
    shop: urlSearchParam.get('shop'),
    timestamp: parseInt(urlSearchParam.get('timestamp'), 10) * 1000,
  };
  const params = Array.from(urlSearchParam.entries());
  params.sort();

  const newParamString = params.map((param) => `${param[0]}=${param[1]}`).join('&');

  const checkHmac = CryptoJS.HmacSHA256(newParamString, process.env.SHOPIFY_JWT_SECRET).toString(CryptoJS.enc.Hex);

  if (hmac === checkHmac) {
    return result;
  } else {
    return null;
  }
};

export const createURL = (host: string, path: string): string => {
  return host + (path.startsWith('/') ? path : '/' + path);
};

export const getS3Url = (key: string): string => {
  return createURL(process.env.AWS_S3_BUCKET_URL, key);
};

export function isValidDate(d: unknown) {
  if (d === null || d === undefined) {
    return false;
  }

  const date = new Date(d as string | number | Date);

  return !isNaN(date.getTime());
}

export function formatDate(date: string | Date, template: string = 'YYYY-MM-DD') {
  if (!date) {
    return '';
  }

  const timeStamp = new Date(date);

  if (!isValidDate(date)) {
    return '';
  }

  return moment(timeStamp).format(template);
}

export function stripHtmlTags(html: string) {
  return html.replace(/<[^>]*>?/gm, '');
}

export const getPrefectureName = (prefectureCode: string) => {
  return prefectures.filter((prefecture) => prefecture.iso === prefectureCode)[0]?.prefecture_kanji || '';
};

export const SAKDecode = (encedText: string, ivText?: string) => {
  const KEY = process.env.SAK_KEY;
  const iv = Buffer.from(process.env.SAK_IV);
  const encedIv = ivText ? Buffer.from(ivText) : iv;
  const decipher = crypto.createDecipheriv('aes-128-cbc', KEY, encedIv);
  let decrypted = decipher.update(encedText, 'base64', 'hex');
  const wk = decipher.final('hex');
  if (wk.length > 0) {
    decrypted += wk;
  }

  return Buffer.from(decrypted, 'hex').toString('ascii');
};

export const SAKEncode = (plainText: string, ivText?: string) => {
  const KEY = process.env.SAK_KEY;
  const iv = Buffer.from(process.env.SAK_IV);
  const encedIv = ivText ? Buffer.from(ivText) : iv;
  const cipher = crypto.createCipheriv('aes-128-cbc', KEY, encedIv);
  let encrypted = cipher.update(plainText, 'ascii', 'hex');
  encrypted += cipher.final('hex');

  return Buffer.from(encrypted, 'hex').toString('base64');
};

export const generateID = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};
