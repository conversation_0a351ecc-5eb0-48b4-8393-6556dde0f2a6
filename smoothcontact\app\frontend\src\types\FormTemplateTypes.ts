import { FormControlNames, FormTemplateModePresentType } from '@/utils/formBuilderUtils';
import { SvgIconComponent } from '@mui/icons-material';

export enum FormType {
  MANUAL = 'manual',
  HTML = 'dev',
}

export enum SideBarType {
  ELEMENT = 'ELEMENT',
  GENERAL = 'GENERAL',
  COLOR = 'COLOR',
  MAIL = 'MAIL',
  SCHEDULE = 'SCHEDULE',
  EMBED_APP = 'EMBED_APP',
  HELP = 'HELP',
}

export const SideBarLabel: { [key in SideBarType]: string } = {
  [SideBarType.ELEMENT]: 'エディター',
  [SideBarType.GENERAL]: 'その他設定',
  [SideBarType.COLOR]: 'デザイン',
  [SideBarType.MAIL]: '回答の送信',
  [SideBarType.SCHEDULE]: '公開設定',
  [SideBarType.EMBED_APP]: 'アプリ埋め込みと外部連携',
  [SideBarType.HELP]: 'ヘルプ',
};

export enum FormStatus {
  DRAFT = 0,
  PUBLISHED = 1,
  PREPUBLISHED = 2,
  CLOSED = 3,
}

export const NewFormMode = {
  MANUAL: 'manual',
  DEV: 'dev',
};

export const FormStatusLabel: { [key in FormStatus]: string } = {
  [FormStatus.DRAFT]: '非公開',
  [FormStatus.PUBLISHED]: '公開中',
  [FormStatus.PREPUBLISHED]: '公開予約',
  [FormStatus.CLOSED]: '公開終了',
};

export interface TemplateType {
  id?: string;
  extId?: string;
  name?: string;
  status: FormStatus;
  formElements: FormElement[];
  publishHistory: FormHistoryType[];
  formGeneralSetting: GeneralSetting;
  formColorSetting: FormColorSetting;
  formMailSetting: FormMailSetting;
  formScheduleSetting: FormScheduleSetting;
  formEmbedAppSetting: FormEmbedAppSetting;
  createdAt?: Date;
  updatedAt?: Date;
  createdBy?: number;
  creator?: string;
  releaseStartDate?: Date;
  releaseEndDate?: Date;
  memo?: string;
  submissionCount?: number;
  mode?: (typeof NewFormMode)[keyof typeof NewFormMode];
}

export interface FormElement {
  container: FormContainerType;
  children: FormElementChildrenType[];
}

export interface FormGeneralSetting {
  spacing: number;
  spacingUnit: string;
  fontSize: number;
  fontSizeUnit: string;
  color: string;
  borderColor: string;
  fontFamily: string;
  fontName?: string;
}

export interface FormTitleSetting {
  fontSize: number;
  fontSizeUnit: string;
  color: string;
  fontFamily: string;
  fontName?: string;
}

export interface FormLabelsSetting {
  fontSize: number;
  fontSizeUnit: string;
  color: string;
  fontFamily: string;
  fontName?: string;
}

export interface FormDescriptionSetting {
  fontSize: number;
  fontSizeUnit: string;
  color: string;
  fontFamily: string;
  fontName?: string;
}

export interface FormEntrySetting {
  fontSize: number;
  fontSizeUnit: string;
  borderRadius: number;
  borderRadiusUnit: string;
  color: string;
  bgColor: string;
  borderColor: string;
}

export interface FormChoiceSetting {
  color: string;
}

export interface FormButtonsSetting {
  text?: string;
  confirmText: string;
  submitText: string;
  fontSize: number;
  fontSizeUnit: string;
  borderRadius: number;
  borderRadiusUnit: string;
  color: string;
  bgColor: string;
  borderColor: string;
  fontFamily: string;
  fontName?: string;
}

export interface FormAnimationSetting {
  textEntryArea: string;
  itemButton: string;
}

export type FormColorOptionMode = 'custom_mode' | 'template_mode';
export type FormColorLayoutMode = 'vertical' | 'horizontal';
export type FormTemplateModeColor = 'White' | 'Black' | 'Blue' | 'Green' | 'Navy' | 'Purple' | 'Red' | 'Yellow' | 'Basic';

export interface FormColorSetting {
  optionMode: FormColorOptionMode;
  layoutMode: FormColorLayoutMode;
  bgColor: string;
  generalSettings: FormGeneralSetting;
  titleSettings: FormTitleSetting;
  labelSettings: FormLabelsSetting;
  descriptionSettings: FormDescriptionSetting;
  entryFormSettings: FormEntrySetting;
  buttonSettings: FormButtonsSetting;
  animationSettings: FormAnimationSetting;
  templateModeColor?: FormTemplateModePresentType;
  choiceSettings: FormChoiceSetting;
}

export interface HtmlFieldDisplay {
  fieldName: string;
  displayText?: string;
  displayOrder?: number;
}

export interface FormMailSetting {
  emailAddress: string[];
  screenAfterSendingType: 'display_message' | 'specified_url';
  message: string;
  specifiedUrl: string;
  isAutoReply: boolean;
  autoReplyEmailAddress: string;
  autoReplySenderName: string;
  autoReplySubject: string;
  isTextMail: boolean;
  textMailBody: string;
  isHtmlMail: boolean;
  isVerifyMailDomain: boolean;
  mailDomain: string;
  txtRecord: string;
  receiveEmailField?: string;
  useCustomSMTP: boolean;
  smtpHost: string;
  smtpPort: number;
  smtpUsername: string;
  smtpPassword: string;
  smtpFromEmail: string;
  htmlFieldDisplays?: string[];
}

export interface FormScheduleSetting {
  displayTextBeforePublicForm: string;
  displayTextAfterPublicForm: string;
  displayTextHiddenForm: string;
  hideHiddenText: boolean;
  maximumNumberFormsReceived: number | null;
}

export interface FormEmbedAppSetting {
  isEnableGA4Setting: boolean;
  gA4TrackingID: string;
  isEnableGoogleAdsSetting: boolean;
  // TODO: REMOVE IT (Reason ID-7409)
  globalSiteTag?: string;
  eventSnippet: string;
  isLinkageYahoo: boolean;
  conversionMeasurementTags: string;
}

export interface GeneralSetting {
  oGPImage: string;
  linkageDomain: string;
  receivedDataSaveFlag?: boolean;
  isDisplaySearchEngine: boolean;
  isSettingReCAPTCHA: boolean;
  isSettingPrivacyPolicy: boolean;
  isDisplayTermsUse: boolean;
  isCombineIntoOneCheckbox: boolean;
  isDisplaySignUp: boolean;
  isDisplaySignUpSample: boolean;
  termsUse: string;
  policyLink: string;
  contactPerson: string[];
  whitelistedDomain: string[];
  captchaKey: string;
}

export interface FormHistoryType {
  lastPublishedAt: number;
  formElements: FormElement[];
}

export interface FormButtonType {
  controlName: FormControlNames;
  id: string;
  confirmText: string;
  submitText: string;
  itemType: string;
}

export interface FormContainerType {
  controlName: FormControlNames;
  displayText: string;
  itemType: string;
  heading: string;
  description: string;
  id: string;
  desktopWidth?: number;
  required?: boolean;
  display?: 'none' | 'left' | 'center' | 'right';
  icon?: SvgIconComponent;
}

export interface FormElementChildrenType {
  controlName: FormControlNames;
  displayText: string;
  description: string;
  isBottomDescription?: boolean;
  labelName: string;
  itemType: string;
  required?: boolean;
  items?: FormElementChildrenItemsType[];
  category: string;
  index?: number;
  id?: string;
  parentId?: string;
  condition?: string[];
  level?: number;
  containerId?: string;
  placeholder?: string;
  fullNamePlaceholder?: {
    full: string;
    fullPronunciation: string;
    first: string;
    last: string;
    firstPronunciation: string;
    lastPronunciation: string;
  };
  rows?: number;
  dataType?: string;
  position?: number;
  characterType?: 'text' | 'number' | 'alphanumeric';
  min?: number;
  max?: number;
  showMinuteStep?: boolean;
  dateLimit?: 'none' | 'future' | 'past';
  minuteStep?: number;
  isUseFurigana?: boolean;
  isAutoFill?: boolean;
  autoFillType?: 'hiragana' | 'katakana';
  isReduceFullName?: boolean;
  verifyEmail?: boolean;
  verifyEmailPlaceholder?: string;
  oneFieldPostcode?: boolean;
  displayPostCodeLink?: boolean;
  displayOtherOption?: boolean;
  displayMode?: 'vertical' | 'horizontal';
  icon?: SvgIconComponent;
  requiredEmailConfirm?: boolean;
  limitedAgeRequired?: boolean;
  limitedAge?: number;
  switchContacts?: {
    id: string;
    contact: {
      email: string;
      sender: string;
      title: string;
      content: string;
    };
    enabled?: boolean;
  }[];
}

export interface FormElementChildrenItemsType {
  id?: string;
  value: string;
  label: string;
}

export type ItemStringValue = string;
export type ItemBooleanValue = boolean;
export type ItemNumberValue = number;
export type ItemChoiceValue = string[];
export type ItemAddressValue = {
  postalCode: string;
  prefecture: string;
  city: string;
  town: string;
  street: string;
  building: string;
};
export type ItemUploadValue = {
  file: string;
  size: number;
  url: string;
  type: string;
};
export type ItemFullNameValue = {
  name: string;
  pronunciation: string;
};

export type ItemFullNameFullValue = {
  firstName: string;
  firstNamePronunciation: string;
  lastName: string;
  lastNamePronunciation: string;
};

export type FormItemValue<T = string> =
  | T
  | ItemStringValue
  | ItemBooleanValue
  | ItemNumberValue
  | ItemAddressValue
  | ItemChoiceValue
  | ItemUploadValue
  | ItemFullNameValue
  | ItemFullNameFullValue;

export type FormItemValueDetailItem = {
  value: string;
  isOther: boolean;
};

export type FormItemValueDetail = FormItemValueDetailItem | FormItemValueDetailItem[] | null;

export type SubmissionFormValue = {
  id: string;
  controlName: FormControlNames;
  labelName: string;
  value: FormItemValue;
  valueDetail: FormItemValueDetail;
};

export type FormSubmissionData = {
  token?: string;
  formValues: SubmissionFormValue[];
};
