import { FC } from 'react';
import { FormikValues } from 'formik';
import { Grid, Typography } from '@mui/material';
import SCColorPicker from '@/components/common/SCColorPicker';

interface CustomModeComponentProps {
  form: FormikValues;
}

const PreviewColorComponent: FC<CustomModeComponentProps> = ({ form }) => {
  return (
    <Grid container alignItems="center">
      <Grid item xs={4} container justifyContent="flex-end">
        <Typography variant="body2">プレビュー用背景色</Typography>
      </Grid>
      <Grid item xs={6} sx={{ pl: 2 }}>
        <SCColorPicker name="bgColor" color={form?.values?.bgColor} form={form} />
      </Grid>
    </Grid>
  );
};

export default PreviewColorComponent;
