import { forwardRef, HttpStatus, Inject, Injectable, Scope } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import * as moment from 'moment';
import * as process from 'process';
import { Brackets, Repository } from 'typeorm';

import { AppException } from '@/common/exceptions/app.exception';
import { RootService } from '@/core/services/root.service';
import { AWSS3 } from '@/libs/AWSS3';
import { FormBuilderEntity, PublishStatus } from '@/modules/form-builder/entities/form-builder.entity';
import { FormBuilderService } from '@/modules/form-builder/form-builder.service';
import { MailService } from '@/modules/mail/mail.service';
import { FormSubmissionEntity, SubmissionFormValue, SubmissionStatus } from '@/modules/submission/entities/form-submission.entity';
import { FormControlNames } from '@/types/controlName.type';
import { formatDate, getPrefectureName } from '@/utils/helpers';
import { StringUtil } from '@/utils/string.util';

import { CsvService } from '../csv/csv.service';
import { HtmlFormSubmissionData } from '../embed/dto/dto.request';
import { FormElementChildrenType, FormType } from '../form-builder/common/common';
import { UploadService } from '../upload/upload.service';
import { FilterStatusOptions, GetFormSubmissionRequestDTO } from './dto/request.dto';
import { logger } from '@/core/logger/index.logger';

@Injectable({ scope: Scope.REQUEST })
export class SubmissionService extends RootService {
  @InjectRepository(FormSubmissionEntity)
  private readonly formSubmissionRepository: Repository<FormSubmissionEntity>;
  private formBuilder: FormBuilderEntity | null = null;

  constructor(
    @Inject(forwardRef(() => FormBuilderService))
    protected formBuilderService: FormBuilderService,
    protected mailService: MailService,
    protected configService: ConfigService,
    private awsS3: AWSS3,
    private readonly csvService: CsvService,
    private readonly uploadService: UploadService,
  ) {
    super();
  }

  setFormBuilderData(formBuilder: FormBuilderEntity) {
    this.formBuilder = formBuilder as FormBuilderEntity;

    return this;
  }

  async create({
    formValues,
    mode,
    receivedDataSaveFlag = true,
  }: {
    formValues: SubmissionFormValue[];
    mode?: FormType;
    receivedDataSaveFlag?: boolean;
  }) {
    this.ensureFormBuilder();
    const formSubmission = new FormSubmissionEntity();
    formSubmission.formExtId = this.formBuilder.extId;
    formSubmission.status = SubmissionStatus.NOT_YET_SUPPORT;
    formSubmission.mode = mode ?? FormType.MANUAL;

    formSubmission.formValues = formValues.map((item) => {
      switch (item.controlName) {
        case FormControlNames.FILE_UPLOAD:
          return {
            ...item,
            value: item.value.file ?? '',
          };

        default:
          return item;
      }
    });

    if (receivedDataSaveFlag) {
      const submission = await this.formSubmissionRepository.save(formSubmission);

      return submission;
    }

    return formSubmission;
  }

  async confirmAndMoveFiles(submission: FormSubmissionEntity) {
    const uploadItems = submission.formValues.filter((item) => {
      return item.controlName === FormControlNames.FILE_UPLOAD && item.value;
    });
    if (!uploadItems.length) {
      return;
    }

    for (const item of uploadItems) {
      await this.awsS3.confirmAndMoveFile(item.value);
    }
  }

  async sendEmails(submission: FormSubmissionEntity) {
    await this.sendSubmissionEmail(submission);
    await this.sendAutoReplyEmail(submission);
  }

  async sendSubmissionEmail(submission: FormSubmissionEntity) {
    this.ensureFormBuilder();
    const { formMailSetting, mode } = this.formBuilder;

    if (!formMailSetting.emailAddress) {
      return;
    }

    const subject = `${this.formBuilder.name}に投稿がありました。`;
    const html = this.buildMailMessageHtml(submission.formValues, true, this.formBuilder?.formElements?.[0]?.children);
    const text = html.replace(/<[^>]*>?/gm, '\n');

    if (formMailSetting.useCustomSMTP) {
      this.mailService.addTransporter('custom', {
        host: formMailSetting.smtpHost || '',
        port: formMailSetting.smtpPort || 25,
        secure: true,
        auth: {
          user: formMailSetting.smtpUsername || '',
          pass: formMailSetting.smtpPassword || '',
        },
        from: {
          name: formMailSetting.autoReplySenderName || '',
          address: formMailSetting.smtpFromEmail || '',
        },
      });
    }

    const replyMails: Array<string> =
      mode !== FormType.HTML ? submission.formValues.filter((item) => item.controlName === FormControlNames.EMAIL)?.map((item) => item.value) : [];

    await this.mailService.sendMail({
      transporterName: formMailSetting.useCustomSMTP ? 'custom' : 'default',
      to: formMailSetting.emailAddress,
      replyTo: replyMails,
      subject,
      text,
      html,
    });
  }

  async sendAutoReplyEmail(submission: FormSubmissionEntity) {
    // TODO: Not work for HTML form
    this.ensureFormBuilder();
    const { formMailSetting, mode } = this.formBuilder;

    const mailRecipient =
      mode !== FormType.HTML ? submission.formValues.filter((item) => item.controlName === FormControlNames.EMAIL)?.map((item) => item.value) : [];

    if (!formMailSetting?.isAutoReply || !formMailSetting?.autoReplyEmailAddress || !mailRecipient?.length) {
      return;
    }

    const mailFromAddress = formMailSetting.useCustomSMTP ? formMailSetting.smtpFromEmail : this.configService.get('SYSTEM_SENDER');
    const submissionHtml = this.buildMailMessageHtml(submission.formValues, true, this.formBuilder?.formElements?.[0]?.children, true);
    await this.mailService.sendMail({
      transporterName: formMailSetting.useCustomSMTP ? 'custom' : 'default',
      to: mailRecipient,
      replyTo: formMailSetting.autoReplyEmailAddress,
      inReplyTo: formMailSetting.autoReplyEmailAddress,
      sender: {
        name: formMailSetting.autoReplySenderName,
        address: mailFromAddress,
      },
      from: {
        name: formMailSetting.autoReplySenderName,
        address: mailFromAddress,
      },
      subject: formMailSetting?.autoReplySubject || `${this.formBuilder.name}自動返信`,
      text: formMailSetting.textMailBody,
      html: formMailSetting.textMailBody.replace(/(?:\r\n|\r|\n)/g, '<br>') + submissionHtml,
    });
  }

  async verifyCaptcha(token: string, key: string = ''): Promise<void> {
    const secretKey = key || process.env.GOOGLE_CAPTCHA_SECRET_KEY;
    const response = await axios.post(`https://www.google.com/recaptcha/api/siteverify?secret=${secretKey}&response=${token}`);

    if (!response.data.success) {
      throw new AppException('Invalid reCAPTCHA token');
    }
  }

  async verifyFormState() {
    this.ensureFormBuilder();
    const { formScheduleSetting } = this.formBuilder;
    const currentDate = new Date();
    const releaseStartDate = new Date(this.formBuilder?.releaseStartDate);
    const releaseEndDate = new Date(this.formBuilder?.releaseEndDate);
    const totalSubmission = await this.countSubmissionByFormExtId(this.formBuilder.extId);

    if (formScheduleSetting.maximumNumberFormsReceived && totalSubmission >= formScheduleSetting.maximumNumberFormsReceived) {
      const newData = {
        ...formScheduleSetting,
        releaseEndDate: new Date(),
      };
      await this.formBuilderService.updateScheduleSetting(this.formBuilder.id, newData);
      this.response({ message: 'このフォームは最大受信数に達しました。たくさんの投稿ありがとうございました。' });
    }

    if (this.formBuilder?.releaseStartDate && currentDate < releaseStartDate) {
      this.response({ message: this.formBuilder.formScheduleSetting?.displayTextBeforePublicForm });
    }

    if (this.formBuilder?.releaseEndDate && currentDate > releaseEndDate) {
      this.response({ message: this.formBuilder.formScheduleSetting?.displayTextAfterPublicForm });
    }
  }

  async countSubmissionByFormExtId(formExtId: string) {
    return await this.formSubmissionRepository.countBy({ formExtId });
  }

  protected ensureFormBuilder() {
    if (!this.formBuilder) {
      throw new Error('Form Builder not set');
    }
  }

  protected isShowFormElement(formElements: FormElementChildrenType[], element: FormElementChildrenType, formValues: SubmissionFormValue[]) {
    if (!element?.parentId || element?.level === 0) {
      return true;
    }

    const parent = formElements.find((child) => child?.id?.toString() === element?.parentId?.toString());

    if (!parent) {
      return true;
    }

    switch (parent.controlName) {
      case FormControlNames.CHECKLIST:

      case FormControlNames.RADIO:

      case FormControlNames.DROPDOWN:
        const parentValue = formValues?.filter((item) => item?.id === parent?.id)?.[0]?.value;
        const checklist = (Array.isArray(parentValue) ? parentValue : [parentValue]) || [];

        return Array.isArray(element?.condition)
          ? element?.condition?.every?.((cond) => checklist.includes?.(cond))
          : checklist?.includes?.(element?.condition);

      default:
        const value = formElements?.[parent.id] as string;

        return element?.condition?.includes?.(value);
    }
  }

  buildMailMessageHtml(formValues: SubmissionFormValue[], isHtml: boolean = false, formElements: FormElementChildrenType[], onlyAutoReply = false) {
    let html = onlyAutoReply ? '' : '<p>本メールは、フォーム投稿時の受信通知を「有効」にされているユーザー様へお送りしております。</p>';

    html += formValues
      // eslint-disable-next-line complexity
      .map((value) => {
        const element = formElements.find((child) => child?.id?.toString() === value?.id?.toString());

        if (!this.isShowFormElement(formElements, element, formValues)) {
          return '';
        }

        switch (value.controlName) {
          case FormControlNames.ADDRESS:
            const addressValue = value.value
              ? `<div style="margin-left: 15px">
  <p>郵便番号: ${value.value.postalCode}</p>
  <p>都道府県: ${getPrefectureName(value.value.prefecture)}</p>
  <p>市区町村: ${value.value.city}</p>
  <p>町名: ${value.value.town}</p>
  <p>番地等: ${value.value.street}</p>
  <p>建物名: ${value.value.building}</p>
</div>
`
              : '';

            return `<p>[ ${value.labelName} ]</p> ${addressValue}`;

          case FormControlNames.FILE_UPLOAD:
            if (onlyAutoReply) {
              const onlyFilename = value.value.split('/').pop();

              return `<p>[ ${value.labelName} ] ${onlyFilename}</p>`;
            }

            const valueEncoded = StringUtil.encodeFilename(value.value);
            const url = value.value ? `${process.env.APP_URL}/attachments/viewer?path=${valueEncoded}` : '';

            return `<p>[ ${value.labelName} ] <a href="${url}" target="_blank">${url}</a></p>`;

          case FormControlNames.FULL_NAME:
            switch (typeof value.value) {
              case 'object':
                const {
                  name = '',
                  pronunciation = '',
                  lastName = '',
                  firstName = '',
                  lastNamePronunciation = '',
                  firstNamePronunciation = '',
                } = value.value || {};

                const fullNameDisplay = name ? `${name || ''}` : `${lastName || ''} ${firstName || ''}`.trim();
                if (!fullNameDisplay) {
                  return `<p>[ ${value.labelName} ] ${value.value}</p>`;
                }

                const pronunciationDisplay = pronunciation
                  ? ` (${pronunciation || ''})`
                  : !!firstNamePronunciation || !!lastNamePronunciation
                    ? `(${lastNamePronunciation || ''} ${firstNamePronunciation || ''})`
                    : '';

                return `<p>[ ${value.labelName} ] ${fullNameDisplay.trim()} ${pronunciationDisplay.trim()}</p>`;

              default:
                return `<p>[ ${value.labelName} ] ${value.value}</p>`;
            }

          case FormControlNames.INPUT_MULTILINE:
            return `<p>[ ${value.labelName} ]</p><p>${StringUtil.convertNewlinesToHtmlBreaks(value.value)}</p>`;

          case FormControlNames.EMAIL:
            return `<p>[ ${value.labelName} ] <a href="mailto:${value.value}">${value.value}</a></p>`;
        }

        return `<p>[ ${value.labelName} ] ${value.value}</p>`;
      })
      .join('');
    if (!isHtml) {
      return html.replace(/<[^>]*>?/gm, '\n');
    }

    return html;
  }

  async getSubmissionByExtId({ extId, userId, filter }: { extId: string; userId: number; filter: GetFormSubmissionRequestDTO }) {
    const formBuilder = await this.formBuilderService.getFormBuilder(userId, extId);

    if (!formBuilder) {
      return this.response({ message: 'フォームが見つかりません' });
    }

    const queryBuilder = this.formSubmissionRepository
      .createQueryBuilder('formSubmission')
      .where('formSubmission.formExtId = :extId', { extId })
      .leftJoinAndMapOne('formSubmission.formBuilder', FormBuilderEntity, 'formBuilder', 'formBuilder.extId = formSubmission.formExtId')
      .select([
        'formSubmission.id',
        'formSubmission.formExtId',
        'formSubmission.status',
        'formSubmission.formValues',
        'formSubmission.updatedAt',
        'formBuilder.formElements',
        'formBuilder.formGeneralSetting',
        'formBuilder.formMailSetting',
      ])
      .take(filter.perPage)
      .skip(filter.getSkip());

    if (filter.status.length > 0) {
      queryBuilder.andWhere(
        new Brackets((query) => {
          filter.status.forEach((element) => {
            switch (element) {
              case FilterStatusOptions.WAITING_REPLY:
                query.where('formSubmission.status = :statusWaitingReply', { statusWaitingReply: SubmissionStatus.WAITING_REPLY });
                break;

              case FilterStatusOptions.NOT_YET_SUPPORT:
                query.orWhere('formSubmission.status = :statusNotYetSupport', { statusNotYetSupport: SubmissionStatus.NOT_YET_SUPPORT });
                break;

              case FilterStatusOptions.DEVELOPMENT_SUPPORT_REQUIRED:
                query.orWhere('formSubmission.status = :statusDevelopmentSupport', {
                  statusDevelopmentSupport: SubmissionStatus.DEVELOPMENT_SUPPORT_REQUIRED,
                });
                break;

              case FilterStatusOptions.COMPLETE:
                query.orWhere('formSubmission.status = :statusComplete', { statusComplete: SubmissionStatus.COMPLETE });
                break;

              default:
            }
          });
        }),
      );
    }

    const [result, total] = await queryBuilder.getManyAndCount();

    return { data: result, total };
  }

  // eslint-disable-next-line max-lines-per-function
  async getReportByExtId({ extId, userId }: { extId: string; userId: number }) {
    const formBuilder = await this.formBuilderService.getFormBuilder(userId, extId);

    if (!formBuilder) {
      return this.response({ message: 'フォームが見つかりません' });
    }

    const queryBuilder = this.formSubmissionRepository
      .createQueryBuilder('formSubmission')
      .where('formSubmission.formExtId = :extId', { extId })
      .andWhere('formSubmission.mode = :mode', { mode: formBuilder?.mode ?? FormType.MANUAL })
      .orderBy('formSubmission.createdAt', 'DESC')
      .select(['formSubmission.id', 'formSubmission.formExtId', 'formSubmission.status', 'formSubmission.formValues', 'formSubmission.createdAt']);

    const [result, total] = await queryBuilder.getManyAndCount();

    const now = new Date();
    const nowUTC = new Date()?.toISOString()?.slice(0, 10);
    const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 6)?.toISOString()?.slice(0, 10);
    const thirtyDaysAgo = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 30)?.toISOString()?.slice(0, 10);

    const lastMonth = result.reduce((acc, cur) => {
      const createdAt = new Date(cur.createdAt)?.toISOString()?.slice(0, 10);

      if (createdAt >= thirtyDaysAgo && createdAt <= nowUTC) {
        const day = createdAt; // Get the day in YYYY-MM-DD format
        acc[day] = (acc[day] || 0) + 1;
      }

      return acc;
    }, {});

    // Count by month for the last 6 months
    const lastSixMonths = result.reduce((acc, cur) => {
      const createdAt = new Date(cur.createdAt)?.toISOString()?.slice(0, 10);
      if (createdAt >= sixMonthsAgo) {
        const month = createdAt?.slice(0, 7); // Get the month in YYYY-MM format
        acc[month] = (acc[month] || 0) + 1;
      }

      return acc;
    }, {});

    // Count by month for the last 12 months
    const twelveMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 11, 1)?.toISOString()?.slice(0, 10);
    const lastYear = result.reduce((acc, cur) => {
      const createdAt = new Date(cur.createdAt)?.toISOString()?.slice(0, 10);
      if (createdAt >= twelveMonthsAgo && createdAt <= nowUTC) {
        const month = createdAt?.slice(0, 7); // Get the month in YYYY-MM format
        acc[month] = (acc[month] || 0) + 1;
      }

      return acc;
    }, {});

    const twelveYearsAgo = new Date(now.getFullYear() - 11, 0, 1)?.toISOString()?.slice(0, 10);
    const allTime = result.reduce((acc, cur) => {
      const createdAt = new Date(cur.createdAt)?.toISOString()?.slice(0, 10);
      if (createdAt >= twelveYearsAgo && createdAt <= nowUTC) {
        const year = createdAt.slice(0, 4); // Get the year
        acc[year] = (acc[year] || 0) + 1;
      }

      return acc;
    }, {});

    const elements = result.reduce((acc, cur) => {
      // eslint-disable-next-line complexity
      cur.formValues.forEach((item: any) => {
        const key = item?.id ? `${item.id}` : `${item.labelName}`;
        if (!acc[key]) {
          acc[key] = {
            controlName: item.controlName,
            labelName: item.labelName,
            statistics: {},
            list: [],
          };
        }

        if ([FormControlNames.CHECKLIST, FormControlNames.RADIO, FormControlNames.DROPDOWN].includes(item.controlName)) {
          formBuilder?.formElements?.[0]?.children
            ?.find((child) => child?.id === item.id)
            ?.items?.map((child) => child.value)
            ?.forEach((value) => {
              acc[key].statistics[value] = acc[key].statistics[value] || 0;
            });
        }

        if (item.controlName === FormControlNames.CHECKLIST) {
          item?.valueDetail?.forEach?.((detail) => {
            if (!detail.isOther) {
              acc[key].statistics[detail.value] = (acc[key].statistics[detail.value] || 0) + 1;
            }

            if (detail.isOther) {
              acc[key].list.push(detail.value);
            }
          });

          formBuilder?.mode === FormType.HTML && (acc[key].statistics[item.value] = (acc[key].statistics[item.value] || 0) + 1);
        } else if (item.controlName === FormControlNames.RADIO) {
          if (!item?.valueDetail?.isOther) {
            acc[key].statistics[item?.value] = (acc[key].statistics[item?.value] || 0) + 1;
          }

          if (item?.valueDetail?.isOther) {
            acc[key].list.push(item?.valueDetail.value);
          }

          formBuilder?.mode === FormType.HTML && (acc[key].statistics[item.value] = (acc[key].statistics[item.value] || 0) + 1);
        } else if (item.controlName === FormControlNames.DROPDOWN) {
          item.value && (acc[key].statistics[item.value] = (acc[key].statistics[item.value] || 0) + 1);
        } else if (item.controlName === FormControlNames.FULL_NAME) {
          const { name, pronunciation, lastName, firstName, lastNamePronunciation, firstNamePronunciation } = item.value;
          const fullNameDisplay = name || `${lastName || ''} ${firstName || ''}`.trim();
          if (!fullNameDisplay) {
            acc[key].statistics[item.value] = (acc[key].statistics[item.value] || 0) + 1;
            acc[key].list.push(item?.value);

            return;
          }

          const pronunciationDisplay = pronunciation
            ? ` (${pronunciation || ''})`
            : !!lastNamePronunciation || !!firstNamePronunciation
              ? `(${lastNamePronunciation || ''} ${firstNamePronunciation || ''})`
              : '';

          acc[key].statistics[fullNameDisplay.trim() + pronunciationDisplay.trim()] =
            (acc[key].statistics[fullNameDisplay.trim() + pronunciationDisplay.trim()] || 0) + 1;
          acc[key].list.push(fullNameDisplay.trim() + pronunciationDisplay.trim());
        } else if (item.controlName === FormControlNames.FILE_UPLOAD) {
          acc[key].statistics[item?.value] = JSON.parse(item?.valueDetail)?.filename ?? '';
          acc[key].list.push(item?.value);
        } else {
          acc[key].statistics[item?.value] = (acc[key].statistics[item.value] || 0) + 1;
          acc[key].list.push(item?.value);
        }
      });

      return acc;
    }, {});

    return {
      form: {
        name: formBuilder.name,
        extId: formBuilder.extId,
        totalSubmission: total,
      },
      general: {
        lastMonth,
        lastSixMonths,
        lastYear,
        allTime,
      },
      elements,
    };
  }

  getManualFormCsv(result: FormSubmissionEntity[], formBuilder: FormBuilderEntity, isCombine: boolean) {
    const headerData: { id?: string; labelName: string; controlName: string }[] = [];
    const csvData = [];

    formBuilder?.mode !== FormType.HTML &&
      formBuilder?.formElements?.[0]?.children?.map((element) => {
        if (!headerData.some((header) => header.id === element.id)) {
          headerData.push({ id: element.id, labelName: element.labelName, controlName: element.controlName });
        }
      });

    formBuilder?.mode !== FormType.HTML &&
      result?.map?.((submission) => {
        const rowData = [];
        rowData.push(moment(submission?.createdAt).format('YYYY.MM.DD HH:mm:ss'));

        // eslint-disable-next-line complexity
        headerData.map((header) => {
          const item = submission.formValues.find((item) => item?.id == header.id);

          if (!item) {
            if (header.controlName === FormControlNames.ADDRESS) {
              if (!isCombine) {
                ['郵便番号', '都道府県', '市区町村', '町名', '番地等', '建物名'].map(() => rowData.push(''));
              } else {
                rowData.push('住所');
              }
            }

            return;
          }

          if (header.controlName === FormControlNames.ADDRESS) {
            const addressOrder = ['postalCode', 'prefecture', 'city', 'town', 'street', 'building'];
            const orderedValues = addressOrder.map((key) => item.value[key] || ''); // Default to empty string if key doesn't exist

            if (!isCombine) {
              orderedValues.forEach((value) => rowData.push(this.formatCsvValue(value)));
            } else {
              let formattedAddress = `〒${orderedValues[0]}`; // Start with postalCode formatted with "〒"

              for (let i = 1; i < orderedValues.length; i++) {
                if (orderedValues[i]) {
                  formattedAddress += orderedValues[i];
                }
              }
              rowData.push(formattedAddress);
            }

            return;
          }

          if (header.controlName === FormControlNames.CHECKLIST) {
            rowData.push(Object.values(item.value).join(', '));

            return;
          }

          if (header.controlName === FormControlNames.FULL_NAME) {
            const {
              name = '',
              pronunciation = '',
              lastName = '',
              firstName = '',
              lastNamePronunciation = '',
              firstNamePronunciation = '',
            } = item.value || {};

            // Build the full name display
            const fullNameDisplay = name || `${lastName} ${firstName}`.trim();

            // If full name is empty, push the entire item value and return early
            if (!fullNameDisplay) {
              rowData.push(item.value);

              return;
            }

            // Determine pronunciation display
            let pronunciationDisplay = '';
            if (pronunciation) {
              pronunciationDisplay = `(${pronunciation})`;
            } else if (lastNamePronunciation || firstNamePronunciation) {
              pronunciationDisplay = `(${lastNamePronunciation} ${firstNamePronunciation})`.trim();
            }

            // Combine full name with pronunciation and push to rowData
            rowData.push(`${fullNameDisplay}${pronunciationDisplay}`);

            return;
          }

          rowData.push(item.value);
        });

        csvData.push(rowData);
      });

    return { headerData, csvData };
  }

  formatCsvValue = (value: string | number): string => {
    if (typeof value === 'string' && value.match(/^\d{1,2}-\d{1,2}-\d{1,2}$/)) {
      return `'${value}`;
    }

    return `${value}`; // Ensure all values are wrapped in quotes
  };

  getHtmlFormCsv(result: FormSubmissionEntity[]) {
    const headerData: { id?: string; labelName: string; controlName: string }[] = [];
    const csvData = [];

    result?.map?.((submission) => {
      submission.formValues.map((item) => {
        const hasHeader = headerData.find((header) => header.labelName === item.labelName);
        if (!hasHeader) {
          headerData.push({ labelName: item.labelName, controlName: item.controlName });
        }
      });
    });

    result?.map?.((submission) => {
      const rowData = [];
      rowData.push(moment(submission?.createdAt).format('YYYY.MM.DD HH:mm:ss'));

      headerData.map((header) => {
        const item = submission.formValues.find((item) => {
          return item?.labelName === header.labelName;
        });
        rowData.push(item.value);
      });

      csvData.push(rowData);
    });

    return { headerData, csvData };
  }

  async downloadSubmissionCsvByExtId({ extId, userId, isCombine = true }: { extId: string; userId: number; isCombine?: boolean }) {
    const formBuilder = await this.formBuilderService.getFormBuilder(userId, extId);

    if (!formBuilder) {
      return this.response({ message: 'フォームが見つかりません' });
    }

    const queryBuilder = this.formSubmissionRepository
      .createQueryBuilder('formSubmission')
      .where('formSubmission.formExtId = :extId', { extId })
      .andWhere('formSubmission.mode = :mode', { mode: formBuilder?.mode ?? FormType.MANUAL })
      .select(['formSubmission.id', 'formSubmission.formExtId', 'formSubmission.status', 'formSubmission.formValues', 'formSubmission.createdAt']);

    const result = await queryBuilder.getMany();

    const { headerData, csvData } =
      formBuilder?.mode === FormType.HTML ? this.getHtmlFormCsv(result) : this.getManualFormCsv(result, formBuilder, isCombine);

    return await this.csvService.transformToCsv(
      headerData.reduce(
        (acc, item) => {
          if (!isCombine) {
            if (item.controlName === FormControlNames.ADDRESS) {
              return acc.concat(['郵便番号', '都道府県', '市区町村', '町名', '番地等', '建物名']);
            }
          }

          return acc.concat(item.labelName);
        },
        ['日時'],
      ),
      csvData,
    );
  }

  getStatictisManualFormCsv(result: FormSubmissionEntity[], formBuilder: FormBuilderEntity) {
    const headerData: { id: string; labelName: string; controlName: string }[] = [];

    // Collect elements that match the required control types
    formBuilder?.formElements?.[0]?.children?.forEach((element) => {
      if (
        [FormControlNames.CHECKLIST, FormControlNames.CHECKBOX, FormControlNames.RADIO, FormControlNames.DROPDOWN].includes(element.controlName) &&
        !headerData.some((header) => header.id === element.id)
      ) {
        headerData.push({
          id: element.id,
          labelName: element.labelName,
          controlName: element.controlName,
        });
      }
    });

    return result.reduce((acc, cur) => {
      cur.formValues.forEach((item: any) => {
        const existedElement = headerData.find((header) => header.id === item.id);
        if (!existedElement) {
          return;
        }

        const key = `${item.id}`;
        if (!acc[key]) {
          acc[key] = {
            controlName: item.controlName,
            labelName: item.labelName,
            statistics: {},
            total: 0,
          };
        }

        if (item.controlName === FormControlNames.CHECKLIST) {
          let selectionCount = 0; // Count selections per submission
          item?.valueDetail?.forEach?.((detail) => {
            const optionKey = detail.isOther ? 'その他' : detail.value;
            acc[key].statistics[optionKey] = (acc[key].statistics[optionKey] || 0) + 1;
            selectionCount++;
          });
          acc[key].total += selectionCount; // Total is sum of all checked items
        } else if (item.controlName === FormControlNames.RADIO) {
          const optionKey = item?.valueDetail?.isOther ? 'その他' : item?.value;
          acc[key].statistics[optionKey] = (acc[key].statistics[optionKey] || 0) + 1;
          acc[key].total += 1; // One selection per submission
        } else {
          const optionKey = item.value === '' ? 'Not Selected' : item.value;
          acc[key].statistics[optionKey] = (acc[key].statistics[optionKey] || 0) + 1;
          acc[key].total += 1; // One selection per submission
        }
      });

      return acc;
    }, {});
  }

  getStatictisHtmlFormCsv(result: FormSubmissionEntity[]) {
    const headerData: { id?: string; labelName: string; controlName: string }[] = [];

    // Extract headers efficiently
    result.forEach((submission) => {
      submission.formValues.forEach((item) => {
        if (
          !headerData.some((header) => header.labelName === item.labelName) &&
          [FormControlNames.CHECKLIST, FormControlNames.CHECKBOX, FormControlNames.RADIO, FormControlNames.DROPDOWN].includes(item.controlName)
        ) {
          headerData.push({ labelName: item.labelName, controlName: item.controlName });
        }
      });
    });

    return result.reduce((acc, cur) => {
      cur.formValues.forEach((item: any) => {
        const existedElement = headerData.find((header) => header.labelName === item.labelName);
        if (!existedElement) {
          return;
        }

        const key = item.labelName;
        if (!acc[key]) {
          acc[key] = {
            controlName: item.controlName,
            labelName: item.labelName,
            statistics: {},
            total: 0, // Corrected total calculation
          };
        }

        if (item.controlName === FormControlNames.CHECKLIST) {
          let selectionCount = 0;
          item?.valueDetail?.forEach?.((detail) => {
            const optionKey = detail.isOther ? 'その他' : detail.value;
            acc[key].statistics[optionKey] = (acc[key].statistics[optionKey] || 0) + 1;
            selectionCount++;
          });
          acc[key].total += selectionCount; // Add the number of checked items
        } else if (item.controlName === FormControlNames.RADIO) {
          const optionKey = item?.valueDetail?.isOther ? 'その他' : item.value;
          acc[key].statistics[optionKey] = (acc[key].statistics[optionKey] || 0) + 1;
          acc[key].total += 1; // Only one selection per submission
        } else {
          const optionKey = item.value === '' ? 'Not Selected' : item.value;
          acc[key].statistics[optionKey] = (acc[key].statistics[optionKey] || 0) + 1;
          acc[key].total += 1; // One selection per submission
        }
      });

      return acc;
    }, {});
  }

  // eslint-disable-next-line max-lines-per-function
  async downloadSubmissionStatictisCsvByExtId({ extId, userId }: { extId: string; userId: number; isCombine?: boolean }) {
    const formBuilder = await this.formBuilderService.getFormBuilder(userId, extId);

    if (!formBuilder) {
      return this.response({ message: 'フォームが見つかりません' });
    }

    const queryBuilder = this.formSubmissionRepository
      .createQueryBuilder('formSubmission')
      .where('formSubmission.formExtId = :extId', { extId })
      .andWhere('formSubmission.mode = :mode', { mode: formBuilder?.mode ?? FormType.MANUAL })
      .select(['formSubmission.id', 'formSubmission.formExtId', 'formSubmission.status', 'formSubmission.formValues', 'formSubmission.createdAt']);

    const result = await queryBuilder.getMany();
    const elements = formBuilder?.mode === FormType.HTML ? this.getStatictisHtmlFormCsv(result) : this.getStatictisManualFormCsv(result, formBuilder);
    const csvData = Object.keys(elements).map((element) => {
      const row = [];
      row.push(elements[element].labelName); // Add label name as the first column

      if (elements[element].statistics && elements[element].total) {
        Object.keys(elements[element].statistics).forEach((key) => {
          const count = Number(elements[element].statistics[key]);
          const total = Number(elements[element].total);
          const percentage = total > 0 ? Math.round((count * 100) / total) : 0;

          row.push(key); // Option name
          row.push(`${percentage}%`); // Rounded percentage
        });
      }

      return row;
    });

    // Determine the max row length to normalize CSV structure
    const maxLength = Math.max(...csvData.map((row) => row.length));

    // Normalize row lengths by filling missing columns with empty strings
    const normalizedData = csvData.map((row) => row.concat(Array(maxLength - row.length).fill('')));

    // Generate the CSV file
    return await this.csvService.transformToCsv(
      Array.from({ length: maxLength }).map(() => ''), // Header row (empty for now)
      normalizedData,
      false,
    );
  }

  async rawSubmission(extId: string, files: Array<Express.Multer.File>, body: HtmlFormSubmissionData) {
    try {
      const { formValues, date, gRecaptchaResponse } = body;

      if (files && files.length > 0) {
        for (const file of files) {
          const originalName = file.originalname.split('.').slice(0, -1).join('.');
          const newFileName = Buffer.from(`${originalName}-${date ?? formatDate(new Date(), 'YYYY.MM.DD_HH.mm.ss')}`, 'latin1').toString('utf8');
          const upload = await this.uploadService.uploadFile(extId, file, newFileName);

          formValues.push({
            labelName: Buffer.from(file.fieldname, 'latin1').toString('utf8'),
            value: {
              file: upload?.key,
              size: file.size,
              url: upload.url,
              type: file.mimetype,
            },
            controlName: FormControlNames.FILE_UPLOAD,
          });
        }
      }

      const formBuilder = await this.formBuilderService.getFormBuilderByExtId(extId);

      if (!formBuilder || formBuilder?.status !== PublishStatus.PUBLISHED || formBuilder?.mode !== FormType.HTML) {
        return this.response({
          message: 'フォームが見つかりませんでした。フォームURLが誤っているか、フォームが非公開または削除された可能性があります。',
        });
      }

      this.setFormBuilderData(formBuilder as FormBuilderEntity);
      if (formBuilder.formGeneralSetting?.isSettingReCAPTCHA) {
        await this.verifyCaptcha(gRecaptchaResponse, formBuilder.formGeneralSetting?.captchaKey);
      }

      await this.verifyFormState();
      const submission = await this.create({ formValues, mode: FormType.HTML });
      await this.confirmAndMoveFiles(submission);
      this.setFormBuilderData(formBuilder as FormBuilderEntity).sendEmails(submission);

      return submission;
    } catch (e) {
      return this.response({
        statusCode: HttpStatus.BAD_REQUEST,
        message: e.message,
        data: e.message,
      });
    }
  }

  async deleteByFormExtId(extId: string) {
    try {
      await this.formSubmissionRepository.delete({ formExtId: extId });
    } catch (error) {
      logger.error('deleteByFormExtId', error);
    }
  }

  async getCrossTabulationSelections({
    extId,
    userId,
    targetTabulationId,
    crossTabulationId,
  }: {
    extId: string;
    userId: number;
    targetTabulationId: string;
    crossTabulationId: string;
  }) {
    try {
      const formBuilder = await this.formBuilderService.getFormBuilder(userId, extId);

      if (!formBuilder) {
        return this.response({ message: 'フォームが見つかりません' });
      }

      // Extract target and cross tabulation options
      const { targetTabulationSelectionOptions, crossTabulationSelectionOptions } = this.extractTabulationOptions(
        formBuilder.formElements,
        targetTabulationId,
        crossTabulationId,
      );

      // Fetch form submissions
      const formSubmissions = await this.getFormSubmissions(extId);

      // Count the occurrences of target and cross tabulation values
      const result = this.countTabulationValues(formSubmissions, targetTabulationSelectionOptions, crossTabulationSelectionOptions);

      return result;
    } catch (error) {
      return this.response({ message: 'データ取得中にエラーが発生しました。' });
    }
  }

  // Function to extract the tabulation selection options
  private extractTabulationOptions(formElements: any[], targetTabulationId: string, crossTabulationId: string) {
    return {
      targetTabulationSelectionOptions: this.getItemsValuesByChildId(formElements, targetTabulationId),
      crossTabulationSelectionOptions: this.getItemsValuesByChildId(formElements, crossTabulationId),
    };
  }

  // Function to get form submissions
  private async getFormSubmissions(extId: string) {
    return this.formSubmissionRepository
      .createQueryBuilder('formSubmission')
      .where('formSubmission.formExtId = :extId', { extId })
      .select(['formSubmission.formValues'])
      .getMany();
  }

  // Function to count the occurrences of target and cross tabulation values in the form submissions
  private countTabulationValues(formSubmissions: any[], targetTabulationSelectionOptions: string[], crossTabulationSelectionOptions: string[]) {
    const result: Record<string, Record<string, number>> = {};

    // Initialize the result structure with target values and cross values set to 0
    targetTabulationSelectionOptions.forEach((targetValue) => {
      result[targetValue] = {};
      crossTabulationSelectionOptions.forEach((crossValue) => {
        result[targetValue][crossValue] = 0;
      });
    });

    // Process form submissions and count target and cross values
    formSubmissions.forEach((submission) => {
      const formValues = submission.formValues;

      // Track the target and cross values that have been selected in this submission
      const selectedTargetValues: Set<string> = new Set();
      const selectedCrossValues: Set<string> = new Set();

      formValues.forEach((item) => {
        if (Array.isArray(item.value)) {
          // If value is an array, loop through the array and match each item
          item.value.forEach((arrayItem) => {
            if (arrayItem && targetTabulationSelectionOptions.includes(arrayItem)) {
              selectedTargetValues.add(arrayItem); // Add to target values if matching
            }

            if (arrayItem && crossTabulationSelectionOptions.includes(arrayItem)) {
              selectedCrossValues.add(arrayItem); // Add to cross values if matching
            }
          });
        } else {
          // If value is a single item, match it directly
          if (item.value && targetTabulationSelectionOptions.includes(item.value)) {
            selectedTargetValues.add(item.value); // Add to target values if matching
          }

          if (item.value && crossTabulationSelectionOptions.includes(item.value)) {
            selectedCrossValues.add(item.value); // Add to cross values if matching
          }
        }
      });

      // Increment the count for each combination of target and cross values found in this submission
      selectedTargetValues.forEach((targetValue) => {
        selectedCrossValues.forEach((crossValue) => {
          result[targetValue][crossValue] += 1;
        });
      });
    });

    return result;
  }

  // Function to get item values by child ID
  private getItemsValuesByChildId(data: any[], targetId: string): string[] {
    const result: string[] = [];

    // Loop through the data and extract items for the matching child ID
    data.forEach((item) => {
      item.children.forEach((child: any) => {
        if (child.id === targetId) {
          child.items.forEach((item: any) => result.push(item.value));
        }
      });
    });

    return result;
  }
}
