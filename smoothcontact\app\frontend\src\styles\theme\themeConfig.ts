import { createTheme } from '@mui/material/styles';
import paletteOptions from './color';

declare module '@mui/material/Button' {
  interface ButtonPropsColorOverrides {
    'grey[50]': true;
    'primary.dark': true;
  }
}

const theme = createTheme({
  palette: {
    primary: {
      main: paletteOptions.primary.main,
      dark: paletteOptions.primary.dark,
      light: paletteOptions.primary.light,
    },
    secondary: {
      main: paletteOptions.secondary.main,
      dark: paletteOptions.secondary.dark,
      light: paletteOptions.secondary.light,
    },
    background: {
      default: paletteOptions.background.default,
    },
    text: {
      primary: paletteOptions.text.primary,
      secondary: paletteOptions.text.secondary,
    },
    grey: {
      50: paletteOptions.grey[50],
      100: paletteOptions.grey[100],
      200: paletteOptions.grey[200],
      300: paletteOptions.grey[300],
      400: paletteOptions.grey[400],
    },
  },
  typography: {
    fontFamily: ['Noto Sans JP', 'sans-serif'].join(','),
    fontSize: 12,
    h1: {
      fontSize: '1.75rem',
      fontStyle: 'normal',
      fontWeight: 300,
      letterSpacing: '-1.5px',
    },
    h2: {
      fontSize: '1.5rem',
      fontStyle: 'normal',
      fontWeight: 300,
      letterSpacing: '-0.5px',
    },
    h3: {
      fontSize: '1.25rem',
      fontWeight: 400,
      fontStyle: 'normal',
    },
    h4: {
      fontSize: '1.2rem',
      fontStyle: 'normal',
      fontWeight: 400,
      lineHeight: '123.5%',
      letterSpacing: '0.25px',
    },
    body1: {
      fontSize: 12,
    },
    body2: {
      fontSize: '0.98em',
    },
    button: {
      fontSize: 12,
    },
  },
  components: {
    MuiInputBase: {
      styleOverrides: {
        input: {
          fontSize: '0.75rem',
        },
      },
    },
    MuiTextField: {
      defaultProps: {
        size: 'medium',
      },
    },
    MuiFormHelperText: {
      styleOverrides: {
        root: {
          marginLeft: '0px',
        },
      },
    },
  },
});

export default theme;
