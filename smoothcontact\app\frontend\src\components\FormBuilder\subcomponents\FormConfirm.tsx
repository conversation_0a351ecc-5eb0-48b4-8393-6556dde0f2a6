import RenderItem from '@/components/FormBuilder/subcomponents/FormConfirm/RenderItem';
import FormElementTitle from '@/components/FormBuilder/subcomponents/FormElementTitle';
import useFormStyles from '@/hooks/useFormStyle';
import { FormColorSetting, FormElement } from '@/types/FormTemplateTypes';
import { Box, Container, FormGroup, Stack, Typography } from '@mui/material';
import Divider from '@mui/material/Divider';
import React, { FC, useState } from 'react';

interface FormConfirmProps {
  formLayoutComponents: FormElement[];
  formColorSetting: FormColorSetting;
}

const FormConfirm: FC<FormConfirmProps> = ({ formColorSetting, formLayoutComponents }) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [componentIndex, _] = useState(0);
  const component = formLayoutComponents[componentIndex];

  const { classes } = useFormStyles({ colorSetting: formColorSetting });

  return (
    <Container
      sx={{ width: '100%', maxWidth: '1200px', marginRight: 'auto', marginLeft: 'auto', paddingLeft: '0!important', paddingRight: '0!important' }}
    >
      <Stack spacing={2}>
        <Box>
          <FormElementTitle
            heading={component.container.heading}
            description={component.container.description}
            headingClass={classes.title}
            descriptionClass={classes.general}
            display={component.container.display}
            lineColor={formColorSetting?.generalSettings?.borderColor}
          />
        </Box>
        <Stack direction="column" spacing={2}>
          {component?.children?.map?.((child, index) => {
            return (
              <React.Fragment key={child.id}>
                <FormGroup className={classes.formGroup}>
                  <Stack mb={0}>
                    <Typography fontWeight="bold" className={classes.label} sx={{ wordBreak: 'break-all' }}>
                      {child.labelName}
                      {!!child.required && <span style={{ color: 'red' }}>*</span>}
                    </Typography>
                    <Box className={classes.general} sx={{ mt: 1 }}>
                      <RenderItem isPreview={true} item={child} />
                    </Box>
                  </Stack>
                </FormGroup>
                {index !== component.children.length - 1 && <Divider color={formColorSetting?.generalSettings?.color} />}
              </React.Fragment>
            );
          })}
        </Stack>
        <Stack spacing={2} direction="row">
          <Box flexGrow={1}>
            <button type="button" className={classes.button}>
              戻る
            </button>
          </Box>
          <Box flexGrow={1}>
            <button type="submit" className={classes.button}>
              {formColorSetting?.buttonSettings?.submitText || '送信'}
            </button>
          </Box>
        </Stack>
      </Stack>
    </Container>
  );
};

export default FormConfirm;
