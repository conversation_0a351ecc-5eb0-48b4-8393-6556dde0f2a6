import { Grid, TextField } from '@mui/material';
import React, { useState } from 'react';

interface SCBirthdayProps {
  sx?: React.CSSProperties;
}

interface Birthday {
  day: string;
  month: string;
  year: string;
}

const SCBirthday: React.FC<SCBirthdayProps> = (props) => {
  const [birthday, setBirthday] = useState<Birthday>({
    day: '',
    month: '',
    year: '',
  });

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>, field: keyof Birthday) => {
    setBirthday({ ...birthday, [field]: event.target.value });
  };

  return (
    <Grid container spacing={2} alignItems="center">
      <Grid item>
        <TextField
          sx={{ ...props.sx, width: '100px' }}
          type="text"
          label="Year"
          variant="outlined"
          value={birthday.year}
          onChange={(e) => handleInputChange(e as React.ChangeEvent<HTMLInputElement>, 'year')}
        />
      </Grid>
      <Grid item>Year</Grid>
      <Grid item>
        <TextField
          sx={{ ...props.sx, width: '100px' }}
          type="text"
          label="Month"
          variant="outlined"
          value={birthday.month}
          onChange={(e) => handleInputChange(e as React.ChangeEvent<HTMLInputElement>, 'month')}
        />
      </Grid>
      <Grid item>Month</Grid>
      <Grid item>
        <TextField
          sx={{ ...props.sx, width: '100px' }}
          type="text"
          label="Day"
          variant="outlined"
          value={birthday.day}
          onChange={(e) => handleInputChange(e as React.ChangeEvent<HTMLInputElement>, 'day')}
        />
      </Grid>
      <Grid item>Day</Grid>
    </Grid>
  );
};

export default SCBirthday;
