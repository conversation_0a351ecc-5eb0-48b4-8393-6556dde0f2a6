import { use<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/hooks/useFormHandler';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { Box, Checkbox, FormControlLabel, FormGroup, FormLabel, TextField, Typography, Zoom } from '@mui/material';
import { Stack } from '@mui/system';
import { FC, useEffect } from 'react';
import * as Yup from 'yup';

type FormValues = {
  id: string;
  contact: {
    email: string;
    sender: string;
    title: string;
    content: string;
    isAutoReply?: boolean;
    autoReplyEmailAddress?: string;
    autoReplySenderName?: string;
    autoReplySubject?: string;
    autoReplyBody?: string;
  };
  enabled?: boolean;
};

const EditSwitchContactComponent: FC = () => {
  const { selectedSwitchContact, setError, editControlProperties } = useFormBuilder();

  const renderSwitchContact = () => {
    return Object.keys(form?.values || {}).map((key: string) => {
      const switchContact = form?.values[key as keyof FormValues] as any;
      const selectedItem = selectedSwitchContact?.items?.[key];

      return (
        <Box key={key}>
          <FormControlLabel
            control={
              <Checkbox
                value={true}
                {...form.register(`${key}.enabled`, { nameOfValueProps: 'checked' })}
                checked={!!switchContact?.enabled}
                onChange={(e: any) => {
                  form?.setFieldValue(`${key}.enabled`, e?.target?.checked ?? false);
                }}
              />
            }
            label={selectedItem?.label}
          />
          <Box display={switchContact?.enabled ? 'block' : 'none'}>
            <Zoom in={switchContact?.enabled}>
              <Stack spacing={1}>
                <Typography variant="body2">受信したいメールアドレス</Typography>
                <TextField
                  placeholder="<EMAIL>"
                  size="small"
                  name={`${key}.contact.email`}
                  value={switchContact?.contact?.email ?? ''}
                  helperText={(form?.errors?.[key as keyof FormValues] as any)?.contact?.email?.toString()}
                  error={!!(form?.errors?.[key as keyof FormValues] as any)?.contact?.email}
                  onChange={form.handleChange}
                />
                <Typography variant="body2">送信者名</Typography>
                <TextField
                  placeholder="送信者名"
                  size="small"
                  name={`${key}.contact.sender`}
                  value={switchContact?.contact?.sender ?? ''}
                  helperText={(form?.errors?.[key as keyof FormValues] as any)?.contact?.sender?.toString()}
                  error={!!(form?.errors?.[key as keyof FormValues] as any)?.contact?.sender}
                  onChange={form.handleChange}
                />
                <Typography variant="body2">件名</Typography>
                <TextField
                  placeholder="件名"
                  size="small"
                  name={`${key}.contact.title`}
                  value={switchContact?.contact?.title ?? ''}
                  helperText={(form?.errors?.[key as keyof FormValues] as any)?.contact?.title?.toString()}
                  error={!!(form?.errors?.[key as keyof FormValues] as any)?.contact?.title}
                  onChange={form.handleChange}
                />
                <Typography variant="body2">メール本文</Typography>
                <TextField
                  placeholder="メール本文"
                  multiline
                  rows={4}
                  size="small"
                  name={`${key}.contact.content`}
                  value={switchContact?.contact?.content ?? ''}
                  helperText={(form?.errors?.[key as keyof FormValues] as any)?.contact?.content?.toString()}
                  error={!!(form?.errors?.[key as keyof FormValues] as any)?.contact?.content}
                  onChange={form.handleChange}
                />
                <FormGroup sx={{ pl: 2 }}>
                  <FormLabel component="legend">自動返信メール</FormLabel>
                  <FormControlLabel
                    control={
                      <Checkbox
                        value={true}
                        {...form.register(`${key}.contact.isAutoReply`, { nameOfValueProps: 'checked' })}
                        checked={switchContact?.contact?.isAutoReply && switchContact?.enabled}
                      />
                    }
                    label="お客さまに自動返信メールを送る"
                  ></FormControlLabel>
                  {!!switchContact?.contact?.isAutoReply && !!switchContact?.enabled && (
                    <Stack direction="column" spacing={2}>
                      <TextField
                        size="small"
                        placeholder="<EMAIL>"
                        error={!!(form?.errors?.[key as keyof FormValues] as any)?.contact?.autoReplyEmailAddress}
                        helperText={(form?.errors?.[key as keyof FormValues] as any)?.contact?.autoReplyEmailAddress}
                        {...form.register(`${key}.contact.autoReplyEmailAddress`)}
                        value={switchContact?.contact?.autoReplyEmailAddress ?? ''}
                      />
                      <TextField
                        size="small"
                        placeholder={'送信者名'}
                        error={!!(form?.errors?.[key as keyof FormValues] as any)?.contact?.autoReplySenderName}
                        helperText={(form?.errors?.[key as keyof FormValues] as any)?.contact?.autoReplySenderName}
                        {...form.register(`${key}.contact.autoReplySenderName`)}
                        value={switchContact?.contact?.autoReplySenderName ?? ''}
                      />
                      <TextField
                        size="small"
                        placeholder={'件名'}
                        error={!!(form?.errors?.[key as keyof FormValues] as any)?.contact?.autoReplySubject}
                        helperText={(form?.errors?.[key as keyof FormValues] as any)?.contact?.autoReplySubject}
                        {...form.register(`${key}.contact.autoReplySubject`)}
                        value={switchContact?.contact?.autoReplySubject ?? ''}
                      />
                      <TextField
                        size="small"
                        placeholder="メール本文"
                        multiline
                        rows={8}
                        error={!!(form?.errors?.[key as keyof FormValues] as any)?.contact?.autoReplyBody}
                        helperText={(form?.errors?.[key as keyof FormValues] as any)?.contact?.autoReplyBody}
                        {...form.register(`${key}.contact.autoReplyBody`)}
                        value={switchContact?.contact?.autoReplyBody ?? ''}
                      />
                    </Stack>
                  )}
                </FormGroup>
              </Stack>
            </Zoom>
          </Box>
        </Box>
      );
    });
  };

  const validationSchema = Yup.array().of(
    Yup.object().shape({
      id: Yup.string().required('ID is required'),
      enabled: Yup.boolean(),
      contact: Yup.object()
        .optional()
        .when(['enabled'], {
          is: (enabled: boolean) => !!enabled,
          then: (schema) =>
            schema.shape({
              email: Yup.string().email('メールアドレスの形式ではありません。').required('メールアドレスを入力してください。'),
              sender: Yup.string().required('受信したいメールアドレスを入力してください。'),
              title: Yup.string().required('メール件名を入力してください。'),
              content: Yup.string().required('メール本文を入力してください。'),
              autoReplyEmailAddress: Yup.string().when('isAutoReply', {
                is: true,
                then: (schema) => schema.email('メールアドレスの形式ではありません。').required('メールアドレスを入力してください。'),
              }),
              autoReplySenderName: Yup.string().when('isAutoReply', {
                is: true,
                then: (schema) => schema.required('受信したいメールアドレスを入力してください。'),
              }),
              autoReplySubject: Yup.string().when('isAutoReply', {
                is: true,
                then: (schema) => schema.required('メール件名を入力してください。'),
              }),
              autoReplyBody: Yup.string().when('isAutoReply', {
                is: true,
                then: (schema) => schema.required('メール本文を入力してください。'),
              }),
            }),
        }),
    })
  );

  const initialValues: FormValues = selectedSwitchContact?.items?.map?.((item: any) => {
    const selectedItem = selectedSwitchContact?.switchContacts?.filter?.((contact: any) => contact?.id === item?.value)?.[0];

    return {
      id: item?.value,
      contact: {
        email: selectedItem?.contact?.email ?? '',
        sender: selectedItem?.contact?.sender ?? '',
        title: selectedItem?.contact?.title ?? '',
        content: selectedItem?.contact?.content ?? '',
        isAutoReply: selectedItem?.contact?.isAutoReply ?? false,
        autoReplyEmailAddress: selectedItem?.contact?.autoReplyEmailAddress ?? '',
        autoReplySenderName: selectedItem?.contact?.autoReplySenderName ?? '',
        autoReplySubject: selectedItem?.contact?.autoReplySubject ?? '',
        autoReplyBody: selectedItem?.contact?.autoReplyBody ?? ``,
      },
      enabled: selectedItem?.enabled ?? false,
    };
  });

  const form = useFormHandler<FormValues>({
    initialValues: initialValues,
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: (e) => {
      console.log('submit', e);
    },
  });

  useEffect(() => {
    form.resetForm({
      values: initialValues,
    });
  }, [selectedSwitchContact?.items]);

  useEffect(() => {
    if (form.isValid) {
      editControlProperties({ ...selectedSwitchContact, switchContacts: form.values });
    }

    setError?.(!form.isValid);
  }, [form.isValid, form.values]);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={1}>
      <Typography variant="body2">対象の選択肢</Typography>
      {renderSwitchContact()}
    </Box>
  );
};

export default EditSwitchContactComponent;
