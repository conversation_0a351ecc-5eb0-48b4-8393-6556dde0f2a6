import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { SvgIconComponent } from '@mui/icons-material';
import AddIcon from '@mui/icons-material/Add';
import { Box, SvgIcon } from '@mui/material';
import { styled } from '@mui/material/styles';

interface FormItemButtonProps {
  text?: string;
  icon: SvgIconComponent;
  onClick?: (item?: any) => void;
  isStartIcon?: boolean;
  size?: 'small' | 'medium' | 'large';
  isDragging?: boolean;
  allowDrag?: boolean;
}

const ButtonStyled = styled(Box, { shouldForwardProp: (prop) => prop !== 'isDragging' && prop !== 'isEnabled' && prop !== 'allowDrag' })<{
  isDragging?: boolean;
  isEnabled?: boolean;
  allowDrag?: boolean;
}>(({ theme, isDragging, isEnabled, allowDrag }) => {
  return {
    color: theme.palette.text.secondary,
    borderColor: theme.palette.divider,
    backgroundColor: theme.palette.common.white,
    borderRadius: '32px',
    borderWidth: '1px',
    fontWeight: 'bold',
    '&:hover': {
      color: theme.palette.primary.dark,
      borderColor: isEnabled ? theme.palette.primary.dark : theme.palette.divider,
      cursor: allowDrag ? 'grab' : 'pointer',
      borderStyle: allowDrag ? 'dashed' : 'solid',
    },
    '&:focus': {
      outline: 'none',
    },
    borderStyle: isDragging ? 'dashed' : 'solid',
    cursor: allowDrag ? 'grab' : 'pointer',
    padding: '4px 10px 4px 8px',
    opacity: isEnabled ? 1 : 0.5,
  };
});

const AddIconStyled = styled(AddIcon)(({ theme }) => {
  return {
    border: '1px solid',
    borderColor: theme.palette.divider,
    borderRadius: '50%',
    width: '14px',
    height: '14px',
  };
});

const IconStyled = styled(SvgIcon)(({ theme }) => ({
  color: theme.palette.primary.dark,
  width: '16px',
  height: '16px',
}));

const FormItemButton = ({ text, icon: Icon, onClick, isStartIcon = true, isDragging = false, allowDrag = true }: FormItemButtonProps) => {
  const { error } = useFormBuilder();

  return (
    <ButtonStyled
      isEnabled={!error}
      sx={{ cursor: !allowDrag ? 'grab' : 'pointer' }}
      onClick={(e) => {
        if (!allowDrag) onClick(e);
      }}
      isDragging={isDragging}
      gap={0.5}
      display={'flex'}
      flexDirection={'row'}
      alignItems={'center'}
      allowDrag={allowDrag}
    >
      {!!isStartIcon && <AddIconStyled sx={{ cursor: !error ? 'pointer' : 'not-allowed' }} />}
      <Box gap={0.5} display={'flex'} flexDirection={'row'} alignItems={'center'} pt={0.5} pb={0.5}>
        <IconStyled as={Icon} />
        {text ?? 'Button'}
      </Box>
    </ButtonStyled>
  );
};

export default FormItemButton;
