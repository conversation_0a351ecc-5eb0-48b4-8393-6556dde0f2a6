import React, { useEffect, useRef, useState } from 'react';
import { Card<PERSON><PERSON>, Card, styled } from '@mui/material';

const StyledCard = styled(Card)`
  box-shadow: none !important; // remove shadow
  border-radius: 10px; // border radius 10px
  background-color: #ffffff; // white background
  border: 1px solid ${(props: any) => props.theme.palette.grey[100]}; // border grey 200
  padding: 1rem; // padding 1rem
`;

interface SCSimpleCardFitHeightProps extends CardProps {
  offsetBottom?: number;
  onContentHeightChange?: (height: number) => void;
}

const SCSimpleCardFitHeight: React.FC<SCSimpleCardFitHeightProps> = ({ offsetBottom = 10, onContentHeightChange = () => {}, ...props }) => {
  const ref = useRef<any>(null);
  const [height, setHeight] = useState<string | undefined>(undefined);

  useEffect(() => {
    const handleResize = () => {
      if (ref.current) {
        const offsetTop = ref.current.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;
        setHeight(`${windowHeight - offsetTop - offsetBottom}px`);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [offsetBottom]);

  useEffect(() => {
    if (onContentHeightChange) {
      onContentHeightChange(parseInt(height || '0'));
    }
  }, [height, props]);

  return (
    <StyledCard ref={ref} {...props} style={{ ...props.style, height }}>
      {props.children}
    </StyledCard>
  );
};

export default SCSimpleCardFitHeight;
