import { Course } from '@/common/constants';
import SCLoadingButton from '@/components/common/SCLoadingButton';
import { useFormHandler } from '@/hooks/useFormHandler';
import { checkFieldErrorHelper } from '@/utils/validate';
import { Box, Button, Card, CardContent, Container, Grid, Stack, TextField, Typography } from '@mui/material';

export interface Props {
  formHandler: ReturnType<typeof useFormHandler>;
  data: any;
  loading?: boolean;
  backupCodes: string[];
  cancel: () => void;
  reissueBackupCode: () => void;
}

function MfaSetting({ formHandler, data, loading, cancel, reissueBackupCode, backupCodes }: Props) {
  if (!data) {
    return null;
  }

  return (
    <Container component="main" maxWidth="xs">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          padding: '20px',
          minWidth: '500px',
        }}
      >
        <Typography component="h1" variant="h5">
          二段階認証の設定
        </Typography>

        <Typography sx={{ mt: 2, textAlign: 'center' }}>QRコードをAuthenticatorで 読み込んで下さい</Typography>

        {!!backupCodes?.length && (
          <Card sx={{ margin: 1, textAlign: 'center' }} variant="outlined">
            <CardContent>
              <Typography variant="body2" gutterBottom>
                これらのバックアップコードを安全な場所に保管してください。デバイスを紛失した際に使用できます。
              </Typography>
              <Grid container spacing={2} mt={1}>
                {backupCodes?.map?.((code, index) => (
                  <Grid item xs={6} key={index}>
                    <Typography variant="body1">{code}</Typography>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        )}
        {!data?.isVerifiedMfa && data?.course === Course.ENTERPRISE && (
          <Box component="form" onSubmit={formHandler?.handleSubmit} noValidate sx={{ width: '100%' }} gap={2}>
            <Box m={2} textAlign={'center'}>
              <img src={data?.qrCode} alt="QR Code" className="w-full h-auto" />
            </Box>
            <Typography textAlign={'center'} mb={2} variant="body1" gutterBottom>
              {data?.base32}
            </Typography>
            <TextField
              fullWidth
              label="MFA Code"
              placeholder="123456"
              name="code"
              autoFocus
              {...formHandler?.register('code')}
              value={formHandler?.values?.code}
              error={!!checkFieldErrorHelper(formHandler, 'code')}
              helperText={checkFieldErrorHelper(formHandler, 'code')}
              InputLabelProps={{ shrink: true }}
            />
            <Stack
              direction="row"
              spacing={2}
              sx={{
                justifyContent: 'center',
                alignItems: 'center',
              }}
              mt={1}
            >
              <Button variant="outlined" color="secondary" onClick={cancel}>
                キャンセル
              </Button>
              <SCLoadingButton
                className="btn-black"
                disabled={!formHandler?.dirty || !formHandler?.isValid}
                loading={loading}
                variant="contained"
                type="submit"
                color="primary"
                sx={{ mt: 3, mb: 2 }}
              >
                次へ
              </SCLoadingButton>
            </Stack>
          </Box>
        )}
        {data?.isVerifiedMfa && data?.course === Course.ENTERPRISE && (
          <Stack
            direction="row"
            spacing={2}
            sx={{
              justifyContent: 'center',
              alignItems: 'center',
            }}
            mt={2}
          >
            <Button variant="outlined" color="secondary" onClick={cancel}>
              キャンセル
            </Button>
            <Button variant="outlined" color="primary" onClick={reissueBackupCode}>
              バッグアップコードの再発行
            </Button>
            <SCLoadingButton disabled={true} className="btn-black" variant="contained" type="submit" color="primary" sx={{ mt: 3, mb: 2 }}>
              次へ
            </SCLoadingButton>
          </Stack>
        )}
      </Box>
    </Container>
  );
}

export default MfaSetting;
