import { Inject, Injectable, Request, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { RootService } from '@/core/services/root.service';
import { AWSS3 } from '@/libs/AWSS3';
import { FormBuilderEntity } from '@/modules/form-builder/entities/form-builder.entity';
import { logger } from '@/core/logger/index.logger';

@Injectable({ scope: Scope.REQUEST })
export class AttachmentService extends RootService {
  constructor(
    @Inject(REQUEST) private readonly request: Request,
    @InjectRepository(FormBuilderEntity)
    private readonly formBuilderRepository: Repository<FormBuilderEntity>,
    private readonly awsS3: AWSS3,
  ) {
    super();
  }

  async verifyFormBuilderOwnership(formExtId: string) {
    if (!formExtId) {
      throw new Error('Form not found');
    }

    const userId = this.request?.user?.id || null;
    const count = await this.formBuilderRepository.countBy({ extId: formExtId, createdBy: userId });
    if (count === 0) {
      throw new Error('Form not found');
    }
  }

  async getSignedUrl(path: string) {
    const formExtId = path.split('/')[0];
    try {
      await this.verifyFormBuilderOwnership(formExtId);

      return this.awsS3.getSignedUrl(path);
    } catch (e) {
      logger.error('getSignedUrl error', e);

      return null;
    }
  }
}
