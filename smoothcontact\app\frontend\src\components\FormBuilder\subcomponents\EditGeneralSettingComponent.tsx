import { Course, DOMAIN_REGEX } from '@/common/constants';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { GeneralSetting } from '@/types/FormTemplateTypes';
import { Box, Checkbox, FormControlLabel, Stack } from '@mui/material';
import Divider from '@mui/material/Divider';
import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import * as Yup from 'yup';
import OGPComponent from './FormGeneralSetting/OGPComponent';
import PrivacyPolicy from './FormGeneralSetting/PrivacyPolicyComponent';
import ReCaptcha from './FormGeneralSetting/ReCaptchaComponent';
import SearchEngine from './FormGeneralSetting/SearchEngineComponent';
import TermsUse from './FormGeneralSetting/TermsUseComponent';
import WhitelistedDomain from './FormGeneralSetting/WhitelistedDomain';
import SettingItemComponent from './FormGeneralSetting/SettingItemComponent';
import { useAppSelector } from '@/store/hook';

interface EditGeneralSettingComponentProps {}

const EditGeneralSettingComponent: FC<EditGeneralSettingComponentProps> = () => {
  const { t } = useTranslation();
  const { profile } = useAppSelector((state) => ({
    profile: state.app.profile,
  }));
  const { selectedTemplate, editGeneralSetting, setError, isFormChanged, isHtmlForm } = useFormBuilder();
  const [isResetTrigger, setIsResetTrigger] = useState(false);

  const validationSchema = Yup.object<GeneralSetting>({
    isDisplayTermsUse: Yup.boolean().optional(),
    termsUse: Yup.string().when('isDisplayTermsUse', {
      is: (isDisplayTermsUse: boolean) => isDisplayTermsUse,
      then: (schema) => schema.url('有効なURLを入力してください').required('{field}を入力してください。'),
    }),
    isSettingPrivacyPolicy: Yup.boolean().optional(),
    policyLink: Yup.string().when('isSettingPrivacyPolicy', {
      is: (isSettingPrivacyPolicy: boolean) => isSettingPrivacyPolicy,
      then: (schema) => schema.url('有効なURLを入力してください').required('{field}を入力してください。'),
    }),
    whitelistedDomain: Yup.array().optional().of(Yup.string().trim().matches(DOMAIN_REGEX, '正しいドメインを入力して下さい')),
  });

  const form = useFormHandler<GeneralSetting>({
    initialValues: selectedTemplate?.formGeneralSetting,
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: (e) => {
      console.log('submit', e);
    },
  });

  useEffect(() => {
    editGeneralSetting(form.values);
    setIsResetTrigger(false);

    setError(!form.isValid);
  }, [form.isValid, form.values]);

  useEffect(() => {
    if (!isFormChanged) {
      form.resetForm({
        values: selectedTemplate?.formGeneralSetting,
      });
      setIsResetTrigger(true);
    }
  }, [isFormChanged]);

  return (
    <Stack direction="column" spacing={2}>
      <WhitelistedDomain form={form} />
      <Divider />
      {!isHtmlForm && profile?.course === Course.ENTERPRISE && (
        <Box>
          <SettingItemComponent label="投稿内容の保存設定" isEnable={!!form?.values?.receivedDataSaveFlag} />
          <FormControlLabel
            control={
              <Checkbox checked={form?.values?.receivedDataSaveFlag} {...form.register('receivedDataSaveFlag', { nameOfValueProps: 'checked' })} />
            }
            label="データベースに保存する"
          />
          <Divider />
        </Box>
      )}
      {!isHtmlForm && <OGPComponent form={form} isResetTrigger={isResetTrigger} />}
      {!isHtmlForm && <Divider />}
      {!isHtmlForm && <SearchEngine form={form} />}
      {!isHtmlForm && <Divider />}
      <ReCaptcha form={form} isHtmlForm={isHtmlForm} />
      <Divider />
      {!isHtmlForm && <PrivacyPolicy form={form} />}
      {!isHtmlForm && <TermsUse form={form} />}
      {!isHtmlForm && (
        <FormControlLabel
          control={
            <Checkbox
              checked={form?.values?.isCombineIntoOneCheckbox}
              name="isCombineIntoOneCheckbox"
              disabled={!(form?.values?.isDisplayTermsUse && form?.values?.isSettingPrivacyPolicy)}
              {...form.register('isCombineIntoOneCheckbox', { nameOfValueProps: 'checked' })}
            />
          }
          label={t('form_builder.is_combine_into_one_checkbox')}
        />
      )}
    </Stack>
  );
};

export default EditGeneralSettingComponent;
