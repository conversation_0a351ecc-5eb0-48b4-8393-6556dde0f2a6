import SCModal from '@/components/common/SCModal';
import SCSimpleCard from '@/components/common/SCSimpleCard';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormContainerType, FormElementChildrenType } from '@/types/FormTemplateTypes';
import AddIcon from '@mui/icons-material/Add';
import { Box, Button, Divider, Stack, Typography } from '@mui/material';
import { FunctionComponent, useState } from 'react';
import { FormItemTypes } from '../../../utils/formBuilderUtils';
import ControlViewComponent from './ControlViewComponent';

interface DropContainerComponentProps {
  accept: string;
  name?: string;
  index?: number;
  layout?: FormElementChildrenType | FormContainerType;
  childrenComponents?: FormElementChildrenType[];
}

const DropContainerComponent: FunctionComponent<DropContainerComponentProps> = ({ accept, layout, childrenComponents }) => {
  const { selectedControl, formColorSetting, selectControl, deleteControl } = useFormBuilder();
  const [displayConfirmDialog, setDisplayConfirmDialog] = useState(false);
  const [displayCanNotDeleteElementDialog, setDisplayCanNotDeleteElementDialog] = useState(false);

  const [containerId, setContainerId] = useState<string>('');
  const [selectedItemId, setSelectedItemId] = useState<string>('');

  const borderColor = '#92939A';
  const borderBase = '2px dotted';
  let border;

  if (selectedControl && selectedControl?.id === layout?.id) {
    border = borderBase + ' ' + borderColor;
  }

  const handleDeleteControl: React.MouseEventHandler<HTMLSpanElement> = (event) => {
    deleteControl(selectedItemId, containerId);
    if (event.stopPropagation) event.stopPropagation();

    setDisplayConfirmDialog(false);
  };

  const handleDeleteElement = (controlName: string, elementId: string, containerId?: string): void => {
    setSelectedItemId(elementId);
    setContainerId(containerId);

    setDisplayConfirmDialog(true);
  };

  return (
    <Box>
      {accept === FormItemTypes.CONTROL ? (
        <Stack
          direction="column"
          justifyContent="space-around"
          alignItems="stretch"
          gap={`${formColorSetting?.generalSettings?.spacing}${formColorSetting?.generalSettings?.spacingUnit}`}
        >
          <SCSimpleCard
            onClick={() => {
              if (selectControl) {
                selectControl(layout);
              }
            }}
            sx={{ cursor: 'pointer', border }}
          >
            <Typography fontSize={'18px'} sx={{ wordBreak: 'break-all' }}>
              {(layout as FormContainerType)?.heading}
            </Typography>
            <div dangerouslySetInnerHTML={{ __html: (layout as FormContainerType)?.description }}></div>
          </SCSimpleCard>
          <Divider />
          <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', minHeight: '10vh', position: 'relative' }}>
            {childrenComponents?.length === 0 ? (
              <Stack direction="column" justifyContent="center" alignItems="center">
                <AddIcon fontSize="large" />
              </Stack>
            ) : (
              <Stack direction="column" justifyContent="space-around" alignItems="stretch" gap={2}>
                {childrenComponents?.map?.((component, index) => {
                  return (
                    <ControlViewComponent
                      key={component?.id}
                      item={component}
                      containerId={layout?.id as string}
                      index={index}
                      handleDeleteElement={handleDeleteElement}
                    />
                  );
                })}
                {displayConfirmDialog && (
                  <SCModal
                    title={'削除しますか?'}
                    width={400}
                    isOpen={displayConfirmDialog}
                    onClose={() => setDisplayConfirmDialog(false)}
                    closeBtnLabel={'キャンセル'}
                    primaryAction={
                      <Button color="primary" onClick={handleDeleteControl}>
                        {'項目を削除'}
                      </Button>
                    }
                  />
                )}
              </Stack>
            )}
          </Box>
        </Stack>
      ) : null}
      {displayCanNotDeleteElementDialog && (
        <SCModal
          title={'この項目は削除できません'}
          width={400}
          isOpen={displayCanNotDeleteElementDialog}
          onClose={() => setDisplayCanNotDeleteElementDialog(false)}
        >
          <Box>
            <Typography variant="body2">このメールアドレスの項目は、自動返信メールの送信先として設定されています。</Typography>
            <Typography variant="body2">自動返信を無効にするか、送信先を変更してください。</Typography>
          </Box>
        </SCModal>
      )}
    </Box>
  );
};

export default DropContainerComponent;
