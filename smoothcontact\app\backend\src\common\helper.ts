import { HttpStatus, ValidationError } from '@nestjs/common';

import { APIResponseBase } from '@/types/response.type';

export const buildSuccessResponse = <T>(dataResponse: APIResponseBase<T>): any => {
  const dataResponseCus: APIResponseBase<T> = {
    success: true,
    statusCode: dataResponse?.statusCode ?? HttpStatus.OK,
    messageCode: dataResponse?.messageCode,
    messageErrors: dataResponse?.messageErrors,
    data: dataResponse?.data,
  };

  if (dataResponse instanceof APIResponseBase) {
    dataResponseCus.data = dataResponse?.data ?? null;
  } else {
    dataResponseCus.data = dataResponse ?? null;
  }

  return dataResponseCus;
};

export const buildFailResponse = <T>(dataResponse: APIResponseBase<T>): any => {
  const dataResponseCus: APIResponseBase<T> = {
    success: false,
    messageCode: dataResponse?.messageCode,
    message: dataResponse?.message ?? '',
    statusCode: dataResponse?.statusCode ?? HttpStatus.BAD_REQUEST,
    messageErrors: dataResponse?.messageErrors,
    data: dataResponse?.data ?? null,
  };

  return dataResponseCus;
};

export function assignDataToInstance<T>(data: T, instance: T) {
  const keys = Object.keys(data ?? {});
  keys.forEach((key) => {
    instance[key] = data[key];
  });
}

export const assignDataToPureInstance = <T>(data: unknown, instance: T): T => {
  if (!data || typeof data !== 'object') {
    return instance;
  }

  const keys = Object.keys(data ?? {});
  const classProperties = Object.getOwnPropertyNames(instance);
  keys.forEach((key: string) => {
    if (classProperties.includes(key)) {
      instance[key] = data[key];
    }
  });

  return instance;
};

export function errorFormatter(errors: ValidationError[]): { [key: string]: string } {
  const errorsValidations: { [key: string]: string } = {};

  function recurseErrors(errors: ValidationError[], parentPath: string = ''): void {
    errors.forEach((error) => {
      const propertyPath = parentPath ? `${parentPath}.${error.property}` : error.property;

      if (error.constraints) {
        errorsValidations[propertyPath] = Object.values(error.constraints)[0];
      }

      if (error.children && error.children.length > 0) {
        recurseErrors(error.children, propertyPath);
      }
    });
  }

  recurseErrors(errors);

  return errorsValidations;
}
