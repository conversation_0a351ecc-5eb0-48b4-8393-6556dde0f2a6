import React, { useEffect, useState } from 'react';
import useAxios from '@/hooks/useAxios';
import { makeStyles } from 'tss-react/mui';
import SCDrawer from '../common/SCDrawer';
import { useToast } from '@/provider/toastProvider';
import { TemplateType } from '@/types/FormTemplateTypes';
import FormDetailHeader from './FormDetailHeaderComponent';
import { Button, TextField, Typography } from '@mui/material';
import FormSettingsWithoutBorder from './FormSettingsWithoutBorder';
import FormSettingWithBottomBorder from './FormSettingWithBottomBorder';
import { updateFormBuilderMemoRequestConfig } from '@/services/form-builder.service';
import { useFormBuilderList } from '@/modules/formBuilder/list/store/FormBuilderListProvider';

interface FormDetailDrawerProps {
  open: boolean;
  onClose: () => void;
  item: (TemplateType & { heading?: string }) | null;
}

const useStyles = makeStyles()(() => ({
  heading: {
    marginTop: '16px',
    marginBottom: '8px',
    marginLeft: '8px',
  },
  subHeading: {
    marginBottom: '8px',
    marginTop: '16px',
    fontWeight: 'bold',
  },
}));

const FormDetailDrawer: React.FC<FormDetailDrawerProps> = ({ open, onClose, item }) => {
  const [memo, setMemo] = useState('');
  const { getAllForms, filter } = useFormBuilderList();
  const { classes } = useStyles();
  const { apiCaller } = useAxios();
  const { toast } = useToast();

  useEffect(() => {
    if (item) {
      setMemo(item.memo || '');
    }
  }, [item]);

  const updateMemo = (newMemo: string) => {
    setMemo(newMemo);
  };

  const saveMemo = async () => {
    if (!item) return;

    try {
      const data = { memo, id: item.id };
      const result = await apiCaller(updateFormBuilderMemoRequestConfig(data));

      if (result?.success) {
        toast({ message: 'メモが正常に更新されました' });
        getAllForms(filter);
      }
    } catch (error) {
      console.error(error);
      toast({ message: 'メモの更新に失敗しました' });
    }
  };

  return (
    <SCDrawer position="right" isOpen={open} onClose={onClose} width={700}>
      <FormDetailHeader
        status={item?.status}
        releaseStartDate={item?.releaseStartDate}
        releaseEndDate={item?.releaseEndDate}
        isLimitedSubmission={Number(item?.submissionCount ?? 0) >= Number(item?.formScheduleSetting?.maximumNumberFormsReceived ?? 9999999999)}
      />
      <Typography variant="h5" className={classes.heading}>
        {item?.name}
      </Typography>
      <FormSettingWithBottomBorder item={item} />
      <FormSettingsWithoutBorder item={item} />
      <Typography variant="h6" className={classes.subHeading}>
        メモ
      </Typography>
      <TextField multiline rows={4} fullWidth value={memo} onChange={(e) => updateMemo(e.target.value)} />
      <Button onClick={saveMemo} variant="outlined" color="secondary" sx={{ mt: 2 }}>
        保存
      </Button>
    </SCDrawer>
  );
};

export default FormDetailDrawer;
