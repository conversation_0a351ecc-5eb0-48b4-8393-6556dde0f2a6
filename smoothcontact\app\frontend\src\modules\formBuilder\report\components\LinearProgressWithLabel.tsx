import React, { FC } from 'react';
import { Box } from '@mui/system';
import { LinearProgress, LinearProgressProps, Typography } from '@mui/material';

interface LinearProgressWithLabelProps extends LinearProgressProps {
  label: string;
  value: number;
  percentage: number;
}

const LinearProgressWithLabel: FC<LinearProgressWithLabelProps> = (props) => {
  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', paddingBottom: 0.5, py: 2 }}>
        <Box sx={{ minWidth: 35 }}>
          <Typography color="text.primary" variant="h6">
            {props.label}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mr: 1 }}>
          <Typography sx={{ pr: 2 }} color="text.primary">
            {props.value}
          </Typography>
          <Typography fontWeight="bold" color="text.primary" variant="h6">{`${props.percentage.toFixed(1)}%`}</Typography>
        </Box>
      </Box>
      <Box>
        <LinearProgress variant="determinate" value={props.percentage} sx={{ height: '17px', borderRadius: 5, color: '#24CBD4' }} />
      </Box>
    </Box>
  );
};

export default LinearProgressWithLabel;
