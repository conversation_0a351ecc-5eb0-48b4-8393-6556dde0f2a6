import { MigrationInterface, QueryRunner, TableIndex } from 'typeorm';

export class AddIndexForModeFormBuilderTable1735117837662 implements MigrationInterface {
  TABLE_NAME = `${process.env.ENTITY_PREFIX || ''}form_builder`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createIndex(
      this.TABLE_NAME,
      new TableIndex({
        name: 'idx_form_builder_mode',
        columnNames: ['mode'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropIndex(this.TABLE_NAME, 'idx_form_builder_mode');
  }
}
