import { Box, Checkbox, FormControl, FormControlLabel, FormHelperText, FormLabel, Radio, RadioGroup, TextField, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { FormikValues } from 'formik';
import { FC } from 'react';

interface DateFieldPropertyProps {
  form: FormikValues;
}

const DateFieldProperty: FC<DateFieldPropertyProps> = ({ form }) => {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
      <Typography variant="body2">日時設定</Typography>
      <FormControl>
        <FormLabel>入力タイプ</FormLabel>
        <RadioGroup defaultValue={'date'} value={form?.values?.dataType ?? 'date'} name="dataType" {...form.register('dataType')}>
          <FormControlLabel value="date" control={<Radio value="date" />} label="日付" />
          <FormControlLabel value="time" control={<Radio value="time" />} label="時刻" />
          <FormControlLabel value="dateTime" control={<Radio value="dateTime" />} label="日付・時刻" />
        </RadioGroup>
      </FormControl>
      <FormControl>
        <FormLabel>入力制限</FormLabel>
        <RadioGroup defaultValue={'none'} value={form?.values?.dateLimit ?? 'none'} name="dateLimit" {...form.register('dateLimit')}>
          <FormControlLabel value="none" control={<Radio value="none" />} label="設定なし" />
          <FormControlLabel value="future" control={<Radio value="future" />} label="未来の日時のみ" />
          <FormControlLabel value="past" control={<Radio value="past" />} label="過去の日時のみ" />
        </RadioGroup>
      </FormControl>
      <FormControl>
        <FormLabel>分の設定</FormLabel>
        <FormControlLabel
          control={
            <Checkbox
              value={true}
              name="showMinuteStep"
              {...form.register('showMinuteStep', { nameOfValueProps: 'checked' })}
              checked={form?.values?.showMinuteStep ?? false}
            />
          }
          label="分の間隔を設定する"
        />
        <Stack direction="row" spacing={2} alignItems={'baseline'} sx={{ pl: 3 }}>
          <Typography variant="body2">設定間隔</Typography>
          <TextField
            inputProps={{ min: 1, max: 59 }}
            type="number"
            name="minuteStep"
            sx={{ maxWidth: '80px' }}
            variant="outlined"
            size="small"
            {...form.register('minuteStep')}
            value={form?.values?.minuteStep ?? 1}
            disabled={!form?.values?.showMinuteStep}
            onKeyDown={(e) => {
              if (e.key === 'e' || e.key === 'E' || e.key === '-' || e.key === '+') {
                e.preventDefault();
              }
            }}
            error={!!form?.errors?.minuteStep}
          />
          <Typography variant="body2">分</Typography>
        </Stack>
        {!!form?.errors?.minuteStep && <FormHelperText error>分の設定間隔を指定します。</FormHelperText>}
      </FormControl>
    </Box>
  );
};

export default DateFieldProperty;
