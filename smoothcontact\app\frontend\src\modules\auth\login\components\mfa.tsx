import SCLoadingButton from '@/components/common/SCLoadingButton';
import { useFormHandler } from '@/hooks/useFormHandler';
import { checkFieldErrorHelper } from '@/utils/validate';
import { Link, Stack } from '@mui/material';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';

export interface Props {
  formHandler: ReturnType<typeof useFormHandler>;
  loading?: boolean;
  backToLogin: () => void;
  verifyMode: 'otp' | 'backup';
  switchVerifyMode: () => void;
}

function MfaComponent({ formHandler, loading, backToLogin, verifyMode, switchVerifyMode }: Props) {
  return (
    <Container component="main">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          padding: '20px',
          minWidth: '400px',
        }}
      >
        <Typography component="h1" variant="h5">
          ログイン
        </Typography>

        <Box component="form" onSubmit={formHandler?.handleSubmit} noValidate sx={{ mt: 1, width: '100%' }}>
          <TextField
            fullWidth
            label={verifyMode === 'otp' ? '認証コード' : 'バックアップコード'}
            placeholder={verifyMode === 'otp' ? '123456' : '12345678'}
            name="code"
            autoFocus
            {...formHandler?.register('code')}
            value={formHandler?.values?.code}
            error={!!checkFieldErrorHelper(formHandler, 'code')}
            helperText={checkFieldErrorHelper(formHandler, 'code')}
            InputLabelProps={{ shrink: true }}
          />
          <SCLoadingButton
            sx={{ mt: 2, mb: 2 }}
            disabled={!formHandler?.isValid}
            loading={loading}
            className="btn-black"
            type="submit"
            fullWidth
            variant="contained"
          >
            認証する
          </SCLoadingButton>
          <Stack direction={'column'} alignItems={'flex-end'} gap={1}>
            <Link component={'button'} type="button" underline="none" onClick={switchVerifyMode} variant="body2">
              {verifyMode === 'otp' && <Typography>バックアップコードを入力する</Typography>}
              {verifyMode === 'backup' && <Typography>認証コードを入力する</Typography>}
            </Link>
            <Link component={'button'} type="button" underline="none" onClick={backToLogin} variant="body2">
              <Typography>Eメール、パスワード入力に戻る</Typography>
            </Link>
          </Stack>
        </Box>
      </Box>
    </Container>
  );
}

export default MfaComponent;
