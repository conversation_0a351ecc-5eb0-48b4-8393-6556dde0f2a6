import { Box, FormControlLabel, FormHelperText, Radio, RadioGroup, Typography } from '@mui/material';
import React, { useEffect } from 'react';
import InputFactory from './FormColorSetting/AnimationCustom/text/InputFactoryComponent';

interface FormElementRadioProps {
  classContainer?: string;
  classInput?: string;
  value: string;
  items: { label: string; value: string }[];
  name?: string;
  error: boolean;
  helperText?: string;
  isVertical?: boolean;
  displayOtherOption?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  entryFormSetting?: any;
  inputAnimation?: string;
}

const FormElementRadio: React.FC<FormElementRadioProps> = ({
  classContainer,
  classInput,
  value,
  items,
  name,
  error,
  helperText,
  isVertical,
  displayOtherOption,
  onChange,
  entryFormSetting,
  inputAnimation,
}) => {
  const [otherValue, setOtherValue] = React.useState<string>('');
  const [otherChecked, setOtherChecked] = React.useState<boolean>(false);

  const itemsMap = new Map<string, string>(
    items?.map?.((i) => {
      return [i.value, i.label];
    })
  );

  const handleOptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOtherValue('');
    setOtherChecked(false);
    onChange(e);
  };

  const handleOtherValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOtherValue(itemsMap.has(e.target.value) ? '' : e.target.value);
    onChange(e);
  };

  const handleOtherOptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOtherChecked(e.target.checked);
    !e.target.checked && setOtherValue('');
  };

  useEffect(() => {
    if (value && !itemsMap.has(value)) {
      setOtherValue(value);
      setOtherChecked(true);
    }
  }, []);

  return (
    <>
      <RadioGroup name={name} onChange={onChange}>
        <Box
          display={isVertical ? 'flex' : 'block'}
          sx={{
            flexDirection: isVertical ? 'column' : 'row',
          }}
        >
          {items?.map?.((i) => (
            <FormControlLabel
              className={classContainer}
              key={i.value}
              control={<Radio />}
              label={i.label}
              value={i.value}
              checked={value === i.value}
              onChange={handleOptionChange}
            />
          ))}
        </Box>
        <Box>
          {displayOtherOption && (
            <FormControlLabel
              className={classContainer}
              sx={{ display: 'flex' }}
              key="other"
              control={<Radio value={otherValue} checked={otherChecked} onChange={handleOtherOptionChange} />}
              label={
                <Box display="flex" alignItems="center">
                  <Typography variant="body1">その他：</Typography>
                  <div style={{ paddingLeft: '10px' }}>
                    <InputFactory
                      name={name}
                      placeholder=""
                      className={classInput}
                      borderColor={entryFormSetting?.borderColor}
                      backgroundColor={entryFormSetting?.bgColor}
                      borderRadius={entryFormSetting?.borderRadius}
                      fontSize={entryFormSetting?.fontSize}
                      onChange={handleOtherValueChange}
                      value={otherValue}
                      disabled={!otherChecked}
                      inputAnimation={inputAnimation}
                      marginTop="0"
                    />
                  </div>
                </Box>
              }
              value={otherValue}
            />
          )}
        </Box>
      </RadioGroup>
      {helperText && <FormHelperText error={error}>{helperText}</FormHelperText>}
    </>
  );
};

export default FormElementRadio;
