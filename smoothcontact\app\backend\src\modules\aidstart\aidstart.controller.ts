import { Controller, Get, Query } from '@nestjs/common';

import { AUTO_UPDATE, DELETE_FORM } from '@/utils/digitalStage.util';

import { AccountService } from '../account/account.service';
import { AidstartFormDTO } from '../account/dto/request.dto';
import { FormBuilderService } from '../form-builder/form-builder.service';

@Controller('api/aidstart')
export class AidstartController {
  constructor(
    private readonly accountService: AccountService,
    private readonly formBuilderService: FormBuilderService,
  ) {}

  @Get('')
  async index(@Query() queryParams: AidstartFormDTO) {
    // Validate query parameters
    if (!queryParams || Object.keys(queryParams).length === 0) {
      return this.createErrorResponse();
    }

    // Authenticate account
    const account = await this.authenticateAccount(queryParams);
    if (!account || !account.id) {
      return this.createErrorResponse();
    }

    // auto delete
    if (queryParams.action && queryParams.action === AUTO_UPDATE) {
      return this.deleteForms(queryParams.autoData);
    }

    // auto create
    const formResult = await this.createForm(account.id);
    if (!formResult) {
      return this.createErrorResponse();
    }

    const formUrl = formResult[0];
    const formId = formResult[1];

    // Return success response
    return this.createSuccessResponse(formUrl, formId);
  }

  private deleteForms(autoData: string = '') {
    const parsedAutoData = JSON.parse(autoData);
    if (parsedAutoData && parsedAutoData.hasOwnProperty(DELETE_FORM) && parsedAutoData[DELETE_FORM].length > 0) {
      this.formBuilderService.deleteSCForm(parsedAutoData[DELETE_FORM]);
    }

    return this.createSuccessResponse();
  }

  private async createForm(accountId: number) {
    // Create the form
    const form = await this.createFormLogic(accountId);
    if (!form) {
      return this.createErrorResponse();
    }

    // Generate the form URL
    const formUrl = this.generateFormUrl(form);

    return [formUrl, form.id];
  }

  private authenticateAccount(queryParams: AidstartFormDTO): Promise<any | null> {
    return this.accountService.loginViaAidStart(queryParams);
  }

  private createFormLogic(accountId: number): Promise<any> {
    const formData = this.formBuilderService.getContactTemplateForm();

    return this.formBuilderService.create(accountId, formData);
  }

  private generateFormUrl(form: any): string {
    return `${process.env.APP_URL}/front/output/${form.extId}`;
  }

  private createSuccessResponse(url: string = '', formId: number = 0) {
    return {
      result: 'success',
      url: url || '',
      formid: formId || 0,
    };
  }

  // Helper function to return the error response
  private createErrorResponse() {
    return {
      result: 'error',
      url: '',
      formid: '',
    };
  }
}
