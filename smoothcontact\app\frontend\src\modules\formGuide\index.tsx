import React, { useRef } from 'react';
import SCCodeBlock from '@/components/common/SCCodeBlock';
import { Grid, Typography, List, ListItem, ListItemText, Paper, Button } from '@mui/material';

interface GuideContentProps {
  id: string;
  title: string;
  description?: string;
  codeBlock?: string;
  note?: string;
  children?: GuideContentProps[];
}

const sections: GuideContentProps[] = [
  {
    id: 'premise',
    title: 'Premise',
    description: `SmoothContact makes implementation easier by using a library called smoothcontact.js.<br><br> When creating a code-based form, you can use smoothcontact.js to implement it, or you can implement validation yourself without using it. <br><br>This page provides information that will be useful for your implementation, but if there is anything not included, please implement it yourself.`,
  },
  {
    id: 'custom_smtp',
    title: 'Configure Simple Mail Transfer Protocol (SMTP) with custom domain name',
    description: `Choose to set the recipient's email address domain to your company domain or service domain.`,
    children: [
      {
        id: 'custom_smtp_step1',
        title: 'Enable Use Custom SMTP',
        description: `Access the form editing screen → Click on "Mail Settings" (回答の送信) → Enable "Use Custom SMTP" (カスタム SMTP を使います) in the "Email Address Domain Settings" (メールアドレスのドメイン設定) section and follow setup instructions`,
      },
      {
        id: 'custom_smtp_step2',
        title: 'Configure the SMTP server connection',
        description: `1. SMTP Server name(SMTP ホスト)<br>
&nbsp;&nbsp;&nbsp;&nbsp;Enter the address of the SMTP server you want to use (e.g., smtp.example.com).<br><br>
2. SMTP Server Port (SMTP ポート)<br>
&nbsp;&nbsp;&nbsp;&nbsp;Port 25 (Standard): Typically used for non-secure connections (not recommended due to security risks).<br>
&nbsp;&nbsp;&nbsp;&nbsp;Port 465 (SSL): Used for secure SSL/TLS connections.<br>
&nbsp;&nbsp;&nbsp;&nbsp;Port 587 (TLS): Preferred for secure communication<br><br>
3. SMTP Server Username (SMTP ユーザー名)<br>
&nbsp;&nbsp;&nbsp;&nbsp;Enter the username associated with your SMTP server account. This is usually your email address.<br><br>

5. SMTP Server Password (SMTP パスワード)<br>
&nbsp;&nbsp;&nbsp;&nbsp;Enter the password for your SMTP account.<br><br>

6. Sender Email Address (From メールアドレス)<br>
&nbsp;&nbsp;&nbsp;&nbsp;Enter the email address that will appear as the sender in outgoing emails.<br>`,
      },
      {
        id: 'custom_smtp_step3',
        title: 'Save Configure Settings',
        description: `After completing the configuration, click "Save" (保存) to save the settings.`,
      },
    ],
  },
  {
    id: 'validation',
    title: 'Validation',
    children: [
      {
        id: 'required_fields',
        title: 'Required Fields',
        codeBlock: `<input type="text" name="お名前" data-sc-required>`,
        description: `Specify the data-sc-required attribute on the tag.<br><br>If specified, the input field will be set as required.<br><br>Empty text fields and unselected radio buttons, check boxes, and select boxes will cause an error.`,
      },
      {
        id: 'email_address_field',
        title: 'Email Address Field',
        description: `Specify the data-sc-type="email" attribute in the tag.<br><br>If specified, email address validation will be set in the input field.<br><br>If any characters other than those in the email address are entered, an error will occur.`,
        codeBlock: `<input name="メールアドレス" type="text" data-sc-type="email">
<div data-sc-show-if-error="メールアドレス">メールアドレスを正しく入力してください</div>`,
        children: [
          {
            id: 're_enter_field',
            title: 'Re-enter Field',
            description: `Specify the data-sc-type="email_confirmation" attribute in the tag.<br><br>Also, specify the value of the name attribute specified in the tag of the email address you want to match in data-sc-confirmed-name.<br><br>If the email address entered in the email address field does not match the email address entered in the re-enter field, an error will occur.`,
            codeBlock: `<input name="確認用メールアドレス" data-sc-type="email_confirmation" data-sc-confirmed-name="メールアドレス">
<div data-sc-show-if-error="確認用メールアドレス">メールアドレスが一致しません</div>`,
          },
        ],
      },
      {
        id: 'phone_number_field',
        title: 'Phone Number Field',
        description: `Specify the data-sc-type="tel" attribute in the tag.<br><br>If specified, phone number validation will be set on the input field.<br><br>If any characters other than the phone number are entered, an error will occur.`,
        codeBlock: `<input type="text" name="電話番号" data-sc-type="tel">`,
        children: [
          {
            id: 'specify_max_digits',
            title: 'Specify the maximum number of digits to enter for phone numbers',
            description: `Specify the data-sc-chars-max attribute in the tag (you must also set data-sc-type="tel").<br><br>If specified, you can specify the maximum number of digits for text input.<br><br>*The minimum number of digits is preset by sc and cannot be specified dynamically.`,
            codeBlock: `<input type="text" class="form-control" id="tel" name="電話番号"
data-sc-type="tel"
data-sc-chars-max="10">`,
          },
        ],
      },
      {
        id: 'numeric_fields',
        title: 'Numeric fields',
        description: `If you only want to allow numbers between 0 and 100, specify the following attributes in the tag: data-sc-type="number" max="100" min="0".<br><br>If a number outside the specified range is entered, an error will occur.`,
        codeBlock: `<input type="number" data-sc-type="number" max="100" min="0" name="個数" >`,
      },

      {
        id: 'text_field',
        title: 'Text Field',
        description: '',
        codeBlock: '',
        children: [
          {
            id: 'specify_max_characters',
            title: 'Specify the maximum number of characters',
            description: `Specify the data-sc-chars-max attribute in the tag (you must also specify data-sc-type="text").<br><br>If specified, a limit is set to the number of characters that can be entered into the text area, and an error message will be displayed if that number is exceeded.`,
            codeBlock: `<!-- マークアップ例 1行テキスト-->
<input type="text" class="form-control" id="name" name="お名前"
data-sc-type="text"
data-sc-chars-max="10">

<!-- マークアップ例 テキストエリア-->
<textarea class="form-control" id="body" name="お問い合わせ"
data-sc-type="text"
data-sc-chars-max="20">
</textarea>`,
          },
          {
            id: 'specify_min_characters',
            title: 'Minimum character limit',
            description: `Specify the data-sc-chars-min attribute in the tag (you must also specify data-sc-type="text").<br><br>If specified, a minimum number of characters will be set for the text area, and an error will be displayed if the number is exceeded.`,
            codeBlock: `<!-- マークアップ例 1行テキスト-->
<input type="text" class="form-control" id="name" name="お名前"
data-sc-type="text"
data-sc-chars-min="7">

<!-- マークアップ例 テキストエリア-->
<textarea class="form-control" id="body" name="お問い合わせ"
data-sc-type="text"
data-sc-chars-min="2">
</textarea>`,
          },
        ],
      },
    ],
  },
  {
    id: 'useful_features',
    title: 'Useful features',
    children: [
      {
        id: 'multiple_selection_items',
        title: 'Multiple Selection Items',
        description: `By adding [] after "name=XXX", both sets of data will be sent if multiple items are selected.<br><br>Please note that if you do not add [] after "name=XXX", only one piece of data will be sent even if multiple items are selected.`,
        codeBlock: `好きなもの
<label><input type="checkbox" name="好きなもの[]" value="A" >A</label>
<label><input type="checkbox" name="好きなもの[]" value="B" >B</label>
<label><input type="checkbox" name="好きなもの[]" value="C" >C</label>`,
      },
      {
        id: 'date_selection',
        title: 'Date Selection',
        description: `Used to create a date input field. Specifically, it creates an item for entering three values: "year," "month," and "day."`,
        codeBlock: `<input type="date" name="date">`,
      },
      {
        id: 'address_completion_by_postal_code',
        title: 'Address completion by postal code',
        description: `Add the data-sc-type="postal_code" attribute to the tag.<br><br>Once you enter your zip code, the address will be updated in the corresponding field.<br><br>The address field is specified with data-sc-type="region" or data-sc-type="locality" or data-sc-type="street-address".<br><br>Each region corresponds to a prefecture, locality to a city, town, or village, and street-address to a town name.<br><br>If there is no corresponding town name, this will be left blank.<br><br>You can also support multiple address fields by filling in separate values for data-sc-type.`,
        codeBlock: `<!-- マークアップ例1 (住所を1つにまとめる場合) -->
<div style="display: flex; padding-bottom: 15px">
  <label>住所*</label>
  <div data-sc-hide-if-confirm>
    <input type="text" name="郵便番号" data-sc-type="postal_code" data-sc-required />
    <select name="県" data-sc-type="prefecture"></select>
    <input type="text" name="地域" data-sc-type="region" />
  </div>
  <div data-sc-show-if-confirm>
    <span data-sc-confirm-value="郵便番号"></span>
    <span data-sc-confirm-value="県"></span>
    <span data-sc-confirm-value="地域"></span>
  </div>
  <div style="color: red" data-sc-show-if-error="郵便番号">郵便番号 is invalid</div>
</div>`,
        children: [
          {
            id: 'prefecture_drop_down_settings',
            title: 'Prefecture drop-down settings',
            description: `Specify data-sc-type="prefecture" in the Select tag.<br><br>When you use this function, the <option> tag listing the prefectures is automatically set, eliminating the need to write the <option> tag.<br><br>By using this setting in conjunction with data-sc-type="region", you can also enable auto-complete by postal code.`,
            codeBlock: `<input type="text" name="郵便番号" data-sc-type="postal-code">
<select name="都道府県" data-sc-type="prefecture"></select>`,
          },
        ],
      },
      {
        id: 'file_upload_field',
        title: 'File Upload Field',
        description: `You can set up a file upload item by doing the following two things.<br><br>Adding enctype="multipart/form-data" to the < form> tag<br><br>Adding an < input type="file"> tag`,
        codeBlock: `<form class="sc-html-embed" data-sc-form="FORM_ID">
  <input type="file" name="添付ファイル">
  <button type="submit">送信</button>
</form>`,
        note: `*Please note that if method="post" is not specified correctly, the file will not be attached properly.`,
      },
      {
        id: 'button_label_text',
        title: 'Button label (text)',
        children: [
          {
            id: 'change_the_text_being_sent',
            title: 'Change the text being sent',
            description: `Specify the data-sc-submitting-text attribute on the button tag.<br><br>If specified, the button label will be changed during form submission.<br><br>This will prevent duplicate submissions and improve usability.`,
            codeBlock: `<button type="submit" class="btn btn-primary" data-sc-submitting-text="送信中...">送信</button>`,
          },
          {
            id: 'change_the_text_when_an_error_occurs',
            title: 'Change the text when an error occurs',
            description: `Specify the data-sc-error-text attribute on the button tag.<br><br>The button label will change if there are errors in the form.<br><br>Since errors can be detected before users hit the submit button, usability is improved and user abandonment is reduced.`,
            codeBlock: `<button type="submit" class="btn btn-primary" data-sc-error-text="未入力の項目があります">送信</button>`,
          },
          {
            id: 'change_the_text_when_there_are_blank_or_incorrect_fields_in_the_form',
            title: 'Change the wording when there are blank or incorrect fields in the form',
            description: `Specify the data-sc-not-input-error-text attribute and the data-sc-invalid-input-error-text attribute in the tag. (You must specify both attributes to use them.)<br><br>If specified, the label of the submit button will be changed if any required fields in the form are not filled in or if the input in the form is incorrect (validation check error).<br><br>This improves usability by allowing you to check whether any input errors have occurred before submitting the form.`,
            codeBlock: `<button type="submit"
class="sf-btn sf-submit-btn sf-btn-block sf-btn-lg"
data-sc-invalid-input-error-text="正しく入力されていない項目があります"
data-sc-not-input-error-text="未入力の項目があります">
送信</button>`,
          },
        ],
      },
      {
        id: 'thankyou_text',
        title: 'Thank You Text',
        description: `Specify the data-sc-show-thank-you-message attribute in the tag. After submit form successfully, the text will be displayed.`,
        codeBlock: `<div data-sc-show-thank-you-message></div>`,
      },
      {
        id: 'show_form_unavailable_text',
        title: 'Form Unavailable Text',
        description: `Specify the data-sc-show-if-form-unavailable attribute in the tag. If form is unavailable, the text will be displayed.`,
        codeBlock: `<div data-sc-show-if-form-unavailable></div>`,
      },
      {
        id: 'show_submit_form_error_text',
        title: 'Submit Form Error Text',
        description: `Specify the data-sc-show-api-error attribute in the tag. If submit form is error, the text will be displayed.`,
        codeBlock: `<div data-sc-show-api-error></div>`,
      },
      {
        id: 'validate_error_text',
        title: 'Validate Error text',
        description: `Specify the data-sc-show-if-error=<FieldName> attribute in the tag.<br><br>If specified, text will be displayed when an error occurs in the field specified in FieldName.<br><br>In the example below, if you leave the "Name" field empty, the text "Please enter your name correctly" will be displayed.`,
        codeBlock: `<input type="text" name="お名前" data-sc-required>
<div data-sc-show-if-error="お名前">お名前を正しく入力してください</div>`,
      },
      {
        id: 'successful_input_sign',
        title: 'Successful input sign',
        description: `Specify the data-sc-show-if-success=<FieldName> attribute in the tag.<br><br>If specified, text or image will be displayed if there are no errors in the field specified in FieldName.<br><br>In the example below, when you enter a string into the "Name" field, the image "ok.png" will be displayed.`,
        codeBlock: `<input type="text" name="お名前" data-sc-required>
<img src="ok.png" data-sc-class-if-success="お名前" />`,
      },
      {
        id: 'confirmation_screen',
        title: 'Confirmation screen',
        children: [
          {
            id: 'enable_prompt_mode',
            title: 'Enable prompt mode',
            description: `Specify the data-sc-confirm attribute in the Form tag.<br><br>If specified, a confirmation screen will be displayed before the form is submitted.<br><br>Normally, when a form is submitted, the data is sent directly without going through a confirmation screen, but if you set up a confirmation screen, you can check the items the user has entered before submitting the form.<br><br>When using a confirmation screen, it is necessary to install a "Back" button with data-sc-back-button set in addition to the normal submit button.`,
            codeBlock: `<form class="sc-html-embed" data-sc-form="FORM_ID" data-sc-confirm>
...
<a href="javascript:void(0)" class="btn btn-default" data-sc-back-button>戻る</a>
<button type="submit" class="btn btn-primary">確認</button>
</form>`,
          },
          {
            id: 'display_form_input_on_confirmation_screen',
            title: 'Display form input on confirmation screen',
            description: `If specified, the value of the target field will be displayed on the confirmation screen.<br><br>By using it in conjunction with data-sc-show-if-confirm, you can layout the display when in confirmation screen mode.`,
            codeBlock: `<input type="text" name="お名前">
<div class="col-xs-9" data-sc-show-if-confirm>
<span data-sc-confirm-value="お名前"></span>
</div>`,
          },
          {
            id: 'hide_elements_on_confirmation_screen',
            title: 'Hide elements on confirmation screen',
            description: `Specify the data-sc-hide-if-confirm attribute on the tag.<br><br>If specified, hides the element only on confirmation screens.`,
            codeBlock: `<form class="sc-html-embed" data-sc-form="FORM_ID" data-sc-confirm>
<div data-sc-hide-if-confirm>
  <input type="text" value="" name="フルネーム" data-sc-type="full_name" data-sc-required />
</div>
...
</form>`,
          },
        ],
      },
      {
        id: 'hide_form_unavailable',
        title: 'Hide form unavailable',
        description: `Specify the data-sc-hide-if-form-unavailable attribute on the tag.<br>If specified, form will be hide if unavailable.`,
        codeBlock: `<form
      class="sc-html-embed"
      data-sc-form="FORM_ID"
      data-sc-hide-if-form-unavailable
      data-sc-confirm
    >
<div data-sc-hide-if-confirm>
<input type="text" value="" name="フルネーム" data-sc-type="full_name" data-sc-required />
</div>
...
</form>`,
      },
      {
        id: 'confirm_form_entry',
        title: 'Confirm form entry',
        description: `Specify the sc-system-confirm class in the Body tag. <br/><br/>Specify the data-sc-confirmed attribute in the Form tag.`,
        codeBlock: `<!-- マークアップ例 -->
<body class="sc-system-confirm">
    <form class="sc-html-embed sc-system-show" data-sc-form="FORM_ID" data-sc-confirm data-sc-confirmed>
...
</form>`,
      },
      {
        id: 'save_form_entry',
        title: 'Save form entry',
        description: `Specify the data-sc-saving="true" attribute in the Form tag.<br><br>If specified, the information entered in the form will be temporarily saved, so that it can be used even if you accidentally close the browser.<br><br>When you reopen the form, the information you entered will be filled in.<br><br>The saved information will be deleted once you submit the form.`,
        codeBlock: `<!-- マークアップ例 -->
<body class="sc-system-submit">
  <form class="sc-html-embed" data-sc-form="FORM_ID">
  ...
    <button class="button" type="submit" disabled>提出済み</button>
  ...
</body>`,
      },
      {
        id: 'button_confirm_text',
        title: 'Button Confirm Text',
        description: `Specify the data-sc-confirm-text attribute in the submit button.<br>Submit button will display the text specified in the attribute after confirmed.`,
        codeBlock: `<!-- マークアップ例 -->
<body class="sc-system-confirm">
  <form class="sc-html-embed" data-sc-form="FORM_ID">
  ...
    <button
      class="button"
      type="submit"
      data-sc-error-text="Some required fields are missing."
      data-sc-confirm-text="確認する"
      data-sc-submitted-text="提出済み"
    >Submit</button>
  ...
</body>`,
      },
      {
        id: 'button_submitted_text',
        title: 'Button Submitted Text',
        description: `Specify the data-sc-submitted-text attribute in the submit button.<br>Submit button will display the text specified in the attribute after submitted.`,
        codeBlock: `<!-- マークアップ例 -->
<body class="sc-system-confirm">
  <form class="sc-html-embed" data-sc-form="FORM_ID">
  ...
    <button
      class="button"
      type="submit"
      data-sc-error-text="Some required fields are missing."
      data-sc-confirm-text="確認する"
      data-sc-submitted-text="提出済み"
    >Submit</button>
  ...
</body>`,
      },
      {
        id: 'button_back',
        title: 'Button Back',
        description: `Specify the data-sc-back-button attribute in the button.<br>Allow back to init form after confirmed.`,
        codeBlock: `<!-- マークアップ例 -->
<body class="sc-system-confirm">
  <form class="sc-html-embed" data-sc-form="FORM_ID">
    <button class="button" type="button" data-sc-back-button>戻る</button>
    ...
</body>`,
      },
    ],
  },
  {
    id: 'spam_prevention',
    title: 'Spam prevention',
    children: [
      {
        id: 'setting_up_recaptcha',
        title: 'Setting up reCAPTCHA',
        description: `1. reCAPTCHA for visiting the following sites<br>&nbsp;&nbsp; <a target="_blank" rel="noopener noreferrer" class="notion-link" href="https://www.google.com/recaptcha/admin#list">: Easy on Humans, Hard on Bots</a><br><br>2. Fill in the necessary fields and press the "Send" button.<br>*If you select "Invisible reCAPTCHA badge", reCAPTCHA will not be displayed on the form.<br><br>3. Copy the secret key that is displayed<br><br>4. On the sc form settings screen, check "Use Google Recaptcha" and enter the secret key copied in 3.<br><br>5. Add the following tag to the HTML of the embed page:<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add it just before the closing head tag<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Add the tag in the form tag where you want to display reCAPTCHA<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Please specify the site key you copied in step 3.`,
        children: [
          {
            id: 'enable_button_when_recaptcha_is_checked',
            title: 'Enable the button when reCAPTCHA is checked',
            description: `*If you select hidden reCAPTCHA, you cannot set this.<br><br>*Please make sure to complete all the previous settings before implementing this.<br><br>1. Specify the data-callback attribute in the tag. The value should be a function that will enable the button when reCAPTCHA is checked.<br><br>2. Specify the data-expired-callback attribute in the tag. The value should be a function that will disable the button when the reCAPTCHA expires.<br><br>3. Specify the disabled attribute for the submit button.<br><br>4. In the Script tag, prepare a function to enable the submit button and a function to disable the submit button.`,
            codeBlock: `<!-- マークアップ例 -->
<form
  class="sc-html-embed"
  data-sc-form="FORM_ID"
  data-sc-recaptcha-site-key="CAPTCHA_KEY"
  data-sc-confirm
>

<button type="submit" data-sc-submitting-text="送信中" disabled>送信</button>
...
</form>`,
          },
        ],
      },
    ],
  },
];

// Render section list with clickable items
const renderSectionList = (sections: GuideContentProps[], handleScrollTo: (id: string) => void) => (
  <List
    sx={{ width: '100%', maxWidth: 360, bgcolor: 'background.paper', paddingTop: 0, paddingBottom: 0 }}
    component="nav"
    aria-labelledby="nested-list-subheader"
  >
    {sections.map((section) => (
      <React.Fragment key={section.id}>
        <ListItem onClick={() => handleScrollTo(section.id)} sx={{ paddingTop: 0, paddingBottom: 0 }}>
          <ListItemText
            primary={section.title}
            sx={{
              pt: 0,
              pb: 0,
              whiteSpace: 'break-spaces',
              textUnderlineOffset: '0.2em',
              textDecoration: 'underline',
              color: 'rgba(55, 53, 47, 0.6)',
              cursor: 'pointer',
            }}
          />
        </ListItem>
        {section.children && (
          <List component="div" disablePadding sx={{ pl: 2 }}>
            {renderSectionList(section.children, handleScrollTo)}
          </List>
        )}
      </React.Fragment>
    ))}
  </List>
);

// Render content for each section
const renderContent = (section: GuideContentProps, sectionRefs: React.MutableRefObject<{ [key: string]: HTMLElement | null }>) => (
  <div key={section.id} style={{ paddingBottom: '40px' }}>
    <Button
      variant="contained"
      fullWidth
      sx={{ color: 'white', fontSize: '2em', mb: 2, pointerEvents: 'none', backgroundColor: '#42B5D1' }}
      id={section.id}
      ref={(el) => (sectionRefs.current[section.id] = el)}
    >
      {section.title}
    </Button>
    <Typography
      variant="body1"
      sx={{ pl: '16px', color: '#66667F', fontSize: '16px' }}
      dangerouslySetInnerHTML={{ __html: section.description || '' }}
    />
    {section.codeBlock && <SCCodeBlock codeString={section.codeBlock} language="javascript" />}
    {section.children &&
      section.children.map((child) => (
        <div key={child.id}>
          <Typography
            variant="h5"
            sx={{ padding: '8px', fontSize: '1.5em', color: '#66667F', fontWeight: '600' }}
            id={child.id}
            ref={(el) => (sectionRefs.current[child.id] = el)}
          >
            {child.title}
          </Typography>
          <Typography
            variant="body1"
            sx={{ pl: '16px', color: '#66667F', fontSize: '13px' }}
            dangerouslySetInnerHTML={{ __html: child.description || '' }}
          />
          {child.codeBlock && <SCCodeBlock codeString={child.codeBlock} language="javascript" />}
          {child.children &&
            child.children.map((grandChild) => (
              <div key={grandChild.id}>
                <Typography
                  variant="h6"
                  sx={{ padding: '8px', fontSize: '14px', color: '#66667F', fontWeight: '600' }}
                  id={grandChild.id}
                  ref={(el) => (sectionRefs.current[grandChild.id] = el)}
                >
                  {grandChild.title}
                </Typography>
                <Typography
                  variant="body1"
                  sx={{ pl: '16px', color: '#66667F', fontSize: '13px' }}
                  dangerouslySetInnerHTML={{ __html: grandChild.description || '' }}
                />
                {grandChild.codeBlock && <SCCodeBlock codeString={grandChild.codeBlock} language="javascript" />}
              </div>
            ))}
        </div>
      ))}
  </div>
);

// Main component for Form Guide Module
const FormGuideModule: React.FC = () => {
  // Refs to section elements
  const sectionRefs = useRef<{ [key: string]: HTMLElement | null }>({});

  // Function to handle click and scroll to section
  const handleScrollTo = (sectionId: string) => {
    const sectionElement = sectionRefs.current[sectionId];
    if (sectionElement) {
      sectionElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <Grid container>
      <Grid item xs={3}>
        <Paper sx={{ position: 'sticky', top: '0' }}>
          <Typography variant="h3" sx={{ pl: 2, pt: 2, color: '#66667F' }}>
            ■ Table of Contents
          </Typography>
          {renderSectionList(sections, handleScrollTo)}
        </Paper>
      </Grid>
      <Grid item xs={9}>
        <Paper style={{ padding: '16px', overflowY: 'auto' }}>{sections.map((section) => renderContent(section, sectionRefs))}</Paper>
      </Grid>
    </Grid>
  );
};

export default FormGuideModule;
