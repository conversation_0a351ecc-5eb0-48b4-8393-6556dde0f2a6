import { useEffect } from 'react';
import { useNavigate, useLocation, createSearchParams } from 'react-router-dom';
import useAxios from '@/hooks/useAxios';
import { LoginViaOemResponseDTO } from '@/modules/auth/loginViaOem/dto/response.dto';
import { useLogout } from '@/hooks/useLogout';
import { useToast } from '@/provider/toastProvider';
import { BindUpSettingDto } from '@/types/dto/bindup.dto';

const useLogic = () => {
  const logout = useLogout();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { apiCaller, loading } = useAxios<LoginViaOemResponseDTO>();

  const handleLoginViaOem = async () => {
    const queryParams = new URLSearchParams(location.search);
    const sak = queryParams.get('sak');
    const cb = queryParams.get('cb');
    const s = queryParams.get('s');
    if (!sak) {
      console.error('Invalid query parameters');
      toast({ isError: true, message: 'Login failed' });
      navigate('/login');

      return;
    }

    try {
      const result = await apiCaller({
        method: 'POST',
        url: '/api/account/login-via-oem',
        data: { sak, cb, s },
      });
      if (!result?.data || !result.success) {
        logout.handleLogout();
        toast({ isError: true, message: 'Login failed' });
        const urlQuery = createSearchParams({ fromOem: 'true', cb, s });
        navigate({
          pathname: '/login',
          search: urlQuery.toString(),
        });

        return;
      }

      const settingParsed: BindUpSettingDto = JSON.parse(s || '{}');
      // Fix ID-9024: Login from OEM must a session
      sessionStorage.setItem('accessToken', result?.data?.accessToken);

      if (settingParsed?.formid) {
        navigate(`/form-builder/${settingParsed.formid}/edit-proxy?fromOem=true`);

        return;
      }

      navigate('/form-builder');
    } catch (error) {
      toast({ isError: true, message: 'Login via OEM failed' });
    }
  };

  useEffect(() => {
    handleLoginViaOem();
  }, []);

  return { loading };
};

export default useLogic;
