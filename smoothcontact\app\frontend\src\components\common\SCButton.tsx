import Button from '@mui/material/Button';
import styled from '@mui/system/styled';

const SCButton = styled(Button)(({ theme }) => {
  return {
    '&:focus': {
      outline: 'none',
    },
    '&.btn-grey': {
      transition: `all 0.3s ease`,
      color: theme.palette.secondary.main,
      backgroundColor: theme.palette.grey[100],
      '&.MuiButton-outlined': {
        color: theme.palette.secondary.main,
        borderColor: theme.palette.grey[100],
        backgroundColor: theme.palette.common.white,
      },
      '&:hover': {
        opacity: 0.6,
      },
      '&:disabled': {
        color: theme.palette.grey[200],
        backgroundColor: theme.palette.grey[100],
      },
    },
  };
});

export default SCButton;
