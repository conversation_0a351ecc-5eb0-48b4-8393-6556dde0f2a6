import { forwardRef, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { HttpStatusCode } from 'axios';
import * as jwt from 'jsonwebtoken';
import * as moment from 'moment';
import { In, IsNull, LessThan, Or, Repository } from 'typeorm';
import { DOMParser } from 'xmldom';

import { MailConst, MessageCode } from '@/common/constant';
import { RootService } from '@/core/services/root.service';
import { AWSS3 } from '@/libs/AWSS3';
import { BindUpSettingDto } from '@/types/bindup.dto';
import { IDataSign } from '@/types/dataSign.type';
import { CHANGE_DS_ID, CourseValue, DigitalStageUtil, LoginType } from '@/utils/digitalStage.util';
import { adaptiveVerifyPassword, generateHash, hashString, SAKDecode } from '@/utils/helpers';

import { FormBuilderService } from '../form-builder/form-builder.service';
import { MailService } from '../mail/mail.service';
import { MfaService } from '../mfa/mfa.service';
import { UploadService } from '../upload/upload.service';
import {
  AidstartFormDTO,
  BackupCodeLoginRequestDTO,
  ChangePasswordRequestDTO,
  LoginRequestDTO,
  MfaLoginRequestDTO,
  RefreshTokenRequestDTO,
  ResetPasswordRequestDTO,
  ShopifyAuthReq,
  ShopifyRegisterRequestDTO,
} from './dto/request.dto';
import { LoginResponseDTO, ResetPasswordResponseDTO, ShopifyAuthResponseDTO } from './dto/response.dto';
import { AccountEntity, Course } from './entities/account.entity';
import { logger } from '@/core/logger/index.logger';

@Injectable()
export class AccountService extends RootService {
  @InjectRepository(AccountEntity)
  public readonly accountRepository: Repository<AccountEntity>;

  constructor(
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
    private readonly uploadService: UploadService,
    private readonly mailService: MailService,
    @Inject(forwardRef(() => FormBuilderService))
    private readonly formService: FormBuilderService,
    private readonly mfaService: MfaService,
    private readonly awsS3: AWSS3,
  ) {
    super();
  }

  async getMe(id: number | string) {
    if (!id) {
      return this.response({ statusCode: HttpStatus.UNAUTHORIZED, message: 'アカウントが存在していません' });
    }

    const profile: AccountEntity = await this.accountRepository.findOneBy({
      id: Number(id),
    });

    if (!profile) {
      return this.response({ statusCode: HttpStatus.UNAUTHORIZED, message: 'アカウントが存在していません' });
    }

    let mfaInfo = {};
    if (profile?.course === Course.ENTERPRISE && !profile?.mfaCommonKey) {
      mfaInfo = await this.mfaService.getAuthenticationCode(profile?.username ?? profile?.name);
    }

    return {
      id: profile.id,
      email: profile.email,
      name: profile.name,
      shop: profile.shop,
      avatar: profile.avatar ?? '',
      tourCompleted: profile.tourCompleted,
      course: profile?.course ?? Course.FREE,
      expirationDate: profile?.expirationDate,
      isVerifiedMfa: profile?.mfaCommonKey ? true : undefined,
      ...mfaInfo,
    };
  }

  async getById(id: number) {
    const profile: AccountEntity = await this.accountRepository.findOneBy({
      id,
    });

    if (!profile) {
      return this.response({ statusCode: HttpStatus.UNAUTHORIZED, message: 'アカウントが存在していません' });
    }

    return {
      id: profile.id,
      email: profile.email,
      name: profile.name,
      shop: profile.shop,
      avatar: profile.avatar ?? '',
      tourCompleted: profile.tourCompleted,
    };
  }

  async register(data: AccountEntity): Promise<boolean> {
    const account: AccountEntity = await this.accountRepository.findOneBy({
      email: data.email,
    });

    if (account) {
      return this.response({ messageErrors: { email: 'メールアドレスはすでに存在しています。' } });
    }

    const hashedPassword = hashString(data.pwd, this.configService.get('CRYPTO_SALT'));
    await this.accountRepository.save({ ...data, name: data?.shop, pwd: hashedPassword });

    return true;
  }

  async hasMfaByEmail(email: string): Promise<boolean> {
    const account = await this.accountRepository.findOneBy({
      email,
    });

    return !!account?.mfaCommonKey;
  }

  // eslint-disable-next-line max-lines-per-function
  async emailAndPasswordLogin(data: LoginRequestDTO): Promise<LoginResponseDTO> {
    let hasSerialCode: boolean = false;
    let account: AccountEntity = await this.accountRepository.findOneBy({
      email: data.email,
      loginType: LoginType.SC_ACCOUNT,
    });

    if (!account) {
      // デジタルステージID確認
      const digitalStageInfo = await DigitalStageUtil.getDigitalStageInfo(data.email, data.pwd);

      if (!digitalStageInfo?.userId) {
        return this.response({ messageErrors: { email: 'メールアドレスが存在しません。' } });
      }

      hasSerialCode = await DigitalStageUtil.getWlsCldCourse(digitalStageInfo?.userId);

      if (hasSerialCode) {
        const userName = `${digitalStageInfo?.name?.sei ?? ''}${digitalStageInfo?.name?.mei ?? ''}`;

        account = await this.createOrUpdateDigitalAccount({
          email: data?.email,
          pwd: generateHash(data.pwd, this.configService.get('CRYPTO_SALT'), 'base64', false),
          userKey: digitalStageInfo?.userId,
          userName: userName || data?.email,
          loginType: LoginType.DEGITALSTAGEID,
        });
      } else {
        // SmoothContact認証・情報チェック
        const scMap = await DigitalStageUtil.registScLink(data.email);

        if (!Object.keys(scMap).length || !scMap?.AVAILABLE) {
          return this.response({ messageErrors: { email: 'メールアドレスが存在しません。' } });
        }

        account = await this.createOrUpdateDigitalAccount({
          email: data?.email,
          pwd: generateHash(data.pwd, this.configService.get('CRYPTO_SALT'), 'base64', false),
          userKey: digitalStageInfo?.userId,
          loginType: LoginType.DEGITALSTAGEID,
          userName: scMap?.USER_NAME || data?.email,
          expirationDate: scMap?.EXPIRATION_DATE ?? null,
        });
      }
    } else {
      // SC_ACCOUNTのログインの場合、パスワードを確認する
      const isValidPassword = adaptiveVerifyPassword(data.pwd, this.configService.get('CRYPTO_SALT'), account.pwd);

      if (!isValidPassword) {
        return this.response({ messageErrors: { pwd: 'パスワードが無効になる' } });
      }
    }

    if (!account?.id) {
      return this.response({ messageErrors: { email: 'メールアドレスが存在しません。' } });
    }

    // 最終ログイン日及びユーザー情報を更新
    account = await this.updLastLoginDate(account, hasSerialCode);
    const hasMfa = await this.hasMfaByEmail(data?.email);

    if (hasMfa) {
      return { account: { id: account.id, mfaEnabled: hasMfa } };
    }

    const payload: any = {
      id: account.id,
      email: account.email,
      name: account.name,
      callbackUrl: data?.cb ?? undefined,
      setting: data?.s ?? undefined,
    };

    const accessToken = await this.jwtService.signAsync(payload, { expiresIn: this.configService.get('JWT_AUTH_IN') });
    const refreshToken = await this.jwtService.signAsync(payload, { expiresIn: this.configService.get('JWT_REFRESH_IN') });

    return {
      accessToken,
      refreshToken,
    };
  }

  async updLastLoginDate(account: AccountEntity | null, hasCourse: boolean = false) {
    if (account) {
      // Retrieve course information from WebLife API
      if (hasCourse) {
        // TODO: Handle enterprise course
        // As of 2022/03, users with enterprise settings in SmoothContact are not updated
        if (!DigitalStageUtil.isAboveEnterpriseCourse(account.course)) {
          account.course = Course.PRO;
        }

        account.expirationDate = null;
      }

      // Update latest login info
      await this.accountRepository.update(account.id, {
        expirationDate: account?.expirationDate,
        course: account?.course,
        lastLoginDate: new Date(),
      });
    }

    return account;
  }

  async verifyMfa(data: MfaLoginRequestDTO) {
    const userId = data.accountId;
    const code = data.code;
    const account = await this.accountRepository.findOneBy({ id: userId });
    const verified = this.mfaService.verifyMfaCode(account?.mfaCommonKey, code);

    if (!verified) {
      return this.response({ messageErrors: { code: 'OPTが無効となります' } });
    }

    const payload: any = {
      id: account.id,
      email: account.email,
      name: account.name,
      callbackUrl: data.cb || undefined,
      setting: data.s || undefined,
    };

    const accessToken = await this.jwtService.signAsync(payload, { expiresIn: this.configService.get('JWT_AUTH_IN') });
    const refreshToken = await this.jwtService.signAsync(payload, { expiresIn: this.configService.get('JWT_REFRESH_IN') });

    return {
      accessToken,
      refreshToken,
    };
  }

  async verifyBackupCode(data: BackupCodeLoginRequestDTO) {
    const userId = data.accountId;
    const code = data.code;
    const account = await this.accountRepository.findOneBy({ id: userId });
    const verified = await this.mfaService.verifyBackupCode(userId, code);

    if (!verified) {
      return this.response({ messageErrors: { code: 'OPTが無効となります' } });
    }

    const payload: any = {
      id: account.id,
      email: account.email,
      name: account.name,
      callbackUrl: data.cb || undefined,
      setting: data.s || undefined,
    };

    const accessToken = await this.jwtService.signAsync(payload, { expiresIn: this.configService.get('JWT_AUTH_IN') });
    const refreshToken = await this.jwtService.signAsync(payload, { expiresIn: this.configService.get('JWT_REFRESH_IN') });

    return {
      accessToken,
      refreshToken,
    };
  }

  async shopifyRegister(data: ShopifyRegisterRequestDTO): Promise<any> {
    let account = await this.accountRepository.findOneBy({
      shop: data.shop,
    });

    if (!account) {
      const hashedPassword = hashString(data.shop, this.configService.get('CRYPTO_SALT'));
      account = await this.accountRepository.save({ ...data, pwd: hashedPassword, loginType: LoginType.SHOPIFY });

      this.mailService.sendMail({
        transporterName: 'default',
        to: data.email,
        subject: MailConst.register.title,
        template: MailConst.register.template,
        context: {
          name: data.shop,
        },
      });
    }

    return account;
  }

  async shopifyAuth(data: ShopifyAuthReq) {
    const token = data.accessToken;

    try {
      const decodedToken: ShopifyAuthResponseDTO = jwt.verify(token, this.configService.get('SHOPIFY_JWT_SECRET')) as ShopifyAuthResponseDTO;

      let account = await this.accountRepository.findOneBy({
        shop: decodedToken.shop,
        loginType: LoginType.SHOPIFY,
      });

      if (!account) {
        account = await this.shopifyRegister({ shop: decodedToken.shop, email: decodedToken.email, name: decodedToken.shop });
      }

      const payload: any = {
        id: account.id,
        email: account.email,
        name: account.name,
        shop: account.shop,
      };

      const accessToken = await this.jwtService.signAsync(payload, { expiresIn: this.configService.get('JWT_AUTH_IN') });
      const refreshToken = await this.jwtService.signAsync(payload, { expiresIn: this.configService.get('JWT_REFRESH_IN') });

      return {
        accessToken,
        refreshToken,
      };
    } catch (err) {
      return this.response({ message: err?.message, messageCode: MessageCode.UNAVAILABLE_ACCESS_TIME, statusCode: HttpStatusCode.Unauthorized });
    }
  }

  async refreshToken(refreshTokenDto: RefreshTokenRequestDTO): Promise<LoginResponseDTO> {
    let payload: IDataSign | undefined = undefined;
    try {
      payload = await this.jwtService.verifyAsync<IDataSign>(refreshTokenDto.refreshToken);
    } catch (error) {
      logger.error('refreshToken error', error);
    }

    if (!payload) {
      return this.response({ statusCode: HttpStatus.UNAUTHORIZED, message: 'アカウントが存在していません' });
    }

    const account = await this.accountRepository.findOneBy({
      id: payload.id,
    });

    if (!account) {
      return this.response({ statusCode: HttpStatus.UNAUTHORIZED, message: 'アカウントが存在していません' });
    }

    const payloadSigned: IDataSign = {
      id: account.id,
      email: account.email,
      name: account.name,
    };

    const accessToken = await this.jwtService.signAsync(payloadSigned, { expiresIn: this.configService.get('JWT_AUTH_IN') });
    const refreshToken = await this.jwtService.signAsync(payloadSigned, { expiresIn: this.configService.get('JWT_REFRESH_IN') });

    return {
      accessToken,
      refreshToken,
    };
  }

  async removeAccount(userId: number) {
    const account: AccountEntity = await this.accountRepository.findOneBy({
      id: userId,
    });

    if (!account) {
      return this.response({ message: 'アカウントが存在していません' });
    }

    return await this.accountRepository.softDelete(account.id);
  }

  async updateProfile({ id, name, avatar, clearAvatar }: { id: number; name: string; avatar: Express.Multer.File; clearAvatar: string }) {
    const account: AccountEntity = await this.accountRepository.findOneBy({
      id,
    });

    if (!account) {
      return this.response({ message: 'アカウントが存在していません' });
    }

    let avatarPublic = clearAvatar?.toLowerCase() === 'true' ? '' : account.avatar;

    if (avatar) {
      const upload = await this.uploadService.uploadFile(id.toString(), avatar);
      const moveFileResult = await this.awsS3.confirmAndMoveFile(upload.key);

      avatarPublic = moveFileResult?.key ?? '';
    }

    const result = await this.accountRepository.update(account.id, {
      name,
      avatar: avatarPublic,
    });

    if (!result) {
      return this.response({ message: 'プロフィールの更新に失敗しました。' });
    }

    return {
      id: account.id,
      name,
      email: account.email,
      shop: account.shop,
      avatar: avatarPublic,
      tourCompleted: account.tourCompleted,
    };
  }

  async completeTour({ id }: { id: number }) {
    const account: AccountEntity = await this.accountRepository.findOneBy({
      id,
    });

    if (!account) {
      return this.response({ message: 'アカウントが存在していません' });
    }

    const result = await this.accountRepository.update(account.id, {
      tourCompleted: true,
    });

    if (!result) {
      return this.response({ message: 'プロフィールの更新に失敗しました。' });
    }

    return { id: account.id, name: account.name, email: account.email, avatar: account.avatar, tourCompleted: true };
  }

  async resetPassword(data: ResetPasswordRequestDTO): Promise<ResetPasswordResponseDTO> {
    const account: AccountEntity = await this.accountRepository.findOneBy({
      email: data.email,
    });

    if (!account) {
      return this.response({ messageErrors: { email: 'メールアドレスが存在しません。' } });
    }

    const lastChangePasswordAt = new Date();

    await this.accountRepository.update(account.id, {
      lastChangePasswordAt,
    });

    const payload: any = {
      email: account.email,
      lastChangePasswordAt: lastChangePasswordAt,
    };

    const resetToken = await this.jwtService.signAsync(payload, { expiresIn: this.configService.get('JWT_RESET_PASSWORD_IN') });

    return { name: account.name, resetToken };
  }

  async changePasswordWithToken(data: ChangePasswordRequestDTO): Promise<boolean> {
    try {
      const payload: any = await this.jwtService.verifyAsync<IDataSign>(data?.token);

      const account: AccountEntity = await this.accountRepository.findOneBy({
        email: payload.email,
      });

      if (!account) {
        return this.response({ messageErrors: { email: 'メールアドレスはすでに存在しています。' } });
      }

      if (
        !account?.lastChangePasswordAt ||
        payload?.lastChangePasswordAt?.substring(0, 19) !== account?.lastChangePasswordAt?.toISOString()?.substring(0, 19)
      ) {
        return this.response({ message: 'メールアドレスはすでに存在しています。' });
      }

      const hashedPassword = hashString(data.pwd, this.configService.get('CRYPTO_SALT'));
      await this.accountRepository.update(account.id, { pwd: hashedPassword, lastChangePasswordAt: null });

      return true;
    } catch (ex) {
      return this.response({ message: 'トークンの有効期限が切れました' });
    }
  }

  async deleteInactiveUsers() {
    const accounts: AccountEntity[] = await this.accountRepository.find({
      where: {
        deletedAt: LessThan(
          moment()
            .subtract(process.env.INACTIVE_ACCOUNT_DURATION ? Number(process.env.INACTIVE_ACCOUNT_DURATION) : 6, 'months')
            .toDate(),
        ),
      },
      withDeleted: true,
    });

    try {
      for (const account of accounts) {
        await this.accountRepository.delete(account?.id);
        await this.formService.deleteByUserId(account?.id);
      }
    } catch (error) {
      return this.response({ message: '非アクティブユーザーの削除に失敗しました。' });
    }

    return accounts.length;
  }

  async updateMfaCommonKey({ accountId, secret }: { accountId: number; secret: string }) {
    const account: AccountEntity = await this.accountRepository.findOneBy({
      id: accountId,
    });

    if (!account) {
      return this.response({ message: 'アカウントが存在していません' });
    }

    const result = await this.accountRepository.update(account.id, {
      mfaCommonKey: secret,
    });

    if (!result) {
      return this.response({ message: 'MFAシークレットの更新に失敗しました。' });
    }

    return result;
  }

  async makeOEMLoginRequest(email: string) {
    const url = process.env.MYPAGE_SCLINK_URL;
    try {
      const response = await this.fetchOEMData(url, email);

      return this.processOEMResponse(response);
    } catch (error) {
      return this.response({ message: 'OEMログインリクエストの作成に失敗しました。' });
    }
  }

  private async fetchOEMData(url: string, email: string) {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Smooth Contact User Key Regist',
        'X-BiND-API-Control': 'X-BiND-API-Control',
      },
      body: new URLSearchParams({ u: email }),
    });

    return response.text();
  }

  private processOEMResponse(responseText: string) {
    const xmlDoc = this.parseXML(responseText);
    const status = this.getElementContent(xmlDoc, 'status');
    if (status !== 'success') {
      const error = this.getElementContent(xmlDoc, 'error');

      return this.response({ message: error });
    }

    return this.extractUserData(xmlDoc);
  }

  private parseXML(responseText: string): Document {
    const parser = new DOMParser();

    return parser.parseFromString(responseText, 'application/xml');
  }

  private getElementContent(xmlDoc: Document, tagName: string): string | null {
    return xmlDoc.getElementsByTagName(tagName)[0]?.textContent || null;
  }

  private extractUserData(xmlDoc: Document) {
    return {
      ukey: this.getElementContent(xmlDoc, 'userId'),
      email: this.getElementContent(xmlDoc, 'mailAddress'),
      username: this.getElementContent(xmlDoc, 'userName'),
      availableStr: this.getElementContent(xmlDoc, 'available'),
      expirationDateStr: this.getElementContent(xmlDoc, 'expirationDate'),
    };
  }

  // Only use for OEM login
  async makeLogin(
    userKey: string,
    email: string,
    username: string,
    available: string,
    expirationDateStr: string,
    callbackUrl?: string,
    setting?: BindUpSettingDto,
  ) {
    let account = await this.accountRepository.findOneBy({ userKey });

    if (!account) {
      account = await this.handleNewUser(email, username, userKey);
    }

    const updatedAccount = await this.updLastLoginDate(
      {
        ...account,
        userKey: userKey,
        email: email,
        username: username,
        expirationDate: this.parseExpirationDate(expirationDateStr),
      },
      false,
    );

    const payloadSigned: IDataSign = {
      id: updatedAccount?.id, // Account ID from existing or newly created user
      email: updatedAccount?.email || email,
      name: updatedAccount?.name || username,
      callbackUrl: callbackUrl || '',
      setting: setting,
      course: updatedAccount?.course ?? Course.FREE,
      expirationDate: expirationDateStr,
    };

    return await this.getSuccessfullyLoginPayload(payloadSigned);
  }

  private async updateExistingAccount(
    account: any,
    {
      userKey,
      email,
      username,
      available,
      expirationDateStr,
    }: { userKey: string; email: string; username: string; available: string; expirationDateStr: string },
  ) {
    const formattedExpirationDate = this.parseExpirationDate(expirationDateStr);

    return await this.updateAccount(account, { userKey, email, username, available, formattedExpirationDate });
  }

  private async handleNewUser(email: string, username: string, userKey: string) {
    const user = await this.accountRepository.findOneBy({ email });
    if (!user) {
      return await this.registerNewUser(email, username, userKey);
    }

    return await this.processExistingUser(user, userKey);
  }

  private async processExistingUser(user: any, userKey: string) {
    if (!user.userKey) {
      return await this.updateUserKey(user.id, userKey);
    }

    if (user.userKey !== userKey) {
      return this.response({ message: `API戻りのユーザーキーとDBの値が不一致：API=${userKey}, DB=${user.userKey}` });
    }

    return user;
  }

  private async registerNewUser(email: string, username: string, userKey: string) {
    const newUser = await this.accountRepository.save({ email, name: username, username, pwd: '' });
    if (!newUser) {
      return this.response({ message: `新しいユーザーの登録に失敗しました: userKey=${userKey}, email=${email}` });
    }

    return newUser;
  }

  private async updateUserKey(userId: number, userKey: string) {
    const updatedAccount = await this.accountRepository.update(userId, { userKey });
    if (!updatedAccount) {
      return this.response({ message: `ユーザーキーの更新に失敗しました: userKey=${userKey}` });
    }

    return { id: userId };
  }

  private parseExpirationDate(expirationDateStr: string): Date | null {
    try {
      const expirationDate = new Date(expirationDateStr.replace(/\./g, '-'));
      if (isNaN(expirationDate.getTime())) {
        throw new Error('Invalid date');
      }

      return expirationDate;
    } catch {
      logger.error('Invalid expiration date format: ' + expirationDateStr);

      return null;
    }
  }

  private async updateAccount(
    account: AccountEntity,
    updates: { userKey: string; email: string; username: string; available: string; formattedExpirationDate: Date },
  ) {
    const updatedAccount = await this.accountRepository.update(account.id, {
      userKey: updates.userKey,
      email: updates.email,
      username: updates.username,
      course: updates.available == 'true' ? Course.PRO : Course.FREE,
      expirationDate: updates.formattedExpirationDate,
    });

    if (!updatedAccount) {
      return false;
    }

    return { id: account.id };
  }

  private async getSuccessfullyLoginPayload(payload: IDataSign) {
    const accessToken = await this.jwtService.signAsync(payload, { expiresIn: this.configService.get('JWT_AUTH_IN') });
    const refreshToken = await this.jwtService.signAsync(payload, { expiresIn: this.configService.get('JWT_REFRESH_IN') });

    return {
      accessToken,
      refreshToken,
    };
  }

  async loginViaAidStart(aidstartForm: AidstartFormDTO) {
    if (!aidstartForm.sak || !this.isValidSak(aidstartForm.sak)) {
      return false;
    }

    return this.loginViaAidStartLogic(aidstartForm);
  }

  async loginViaAidStartLogic(aidstartForm: AidstartFormDTO) {
    if (aidstartForm.dsid) {
      return await this.handleDsUserLogin(aidstartForm);
    }

    if (aidstartForm.tempdsid) {
      return await this.handleAiTempDsUserLogin(aidstartForm);
    }

    if (aidstartForm.oemId) {
      return await this.handleToBUserLogin(aidstartForm);
    }

    return false;
  }

  // Handle DS User Login
  private async handleDsUserLogin(aidstartForm: AidstartFormDTO) {
    return await this.bindLogin(aidstartForm);
  }

  // Handle AI Temp DS User Login
  private async handleAiTempDsUserLogin(aidstartForm: AidstartFormDTO) {
    return await this.aiLogin(aidstartForm);
  }

  // Handle ToB User Login
  private async handleToBUserLogin(aidstartForm: AidstartFormDTO) {
    return await this.oemLogin(aidstartForm);
  }

  // Validation for SAK (security token)
  private isValidSak(sak: string): any {
    try {
      const decryptedSak = SAKDecode(sak);
      if (!decryptedSak) {
        return false;
      }

      const [sakWord, expiry] = decryptedSak.split('@@') || [];

      return sakWord === 'smoothcontact' && !this.isExpired(expiry);
    } catch (error) {
      logger.error('Failed to parse SAK', error);

      return false;
    }
  }

  // Check if the SAK has expired
  private isExpired(sakTime: string): boolean {
    const limitTime = new Date().getTime() - 3 * 60 * 1000;

    return limitTime > parseInt(sakTime, 10);
  }

  // Bind DS User Login
  private async bindLogin(aidstartForm: AidstartFormDTO) {
    if (aidstartForm.autoData?.hasOwnProperty(CHANGE_DS_ID)) {
      await this.aiUpdateDsId(aidstartForm.dsid, aidstartForm.autoData[CHANGE_DS_ID]);
    }

    const scMap = await this.makeOEMLoginRequest(aidstartForm.dsid);
    if (!Object.keys(scMap).length) {
      return false;
    }

    return await this.makeBiNDLogin(scMap);
  }

  // Create BiND Login for a DS User
  private async makeBiNDLogin(scMap: any) {
    // Find the account by userKey (ukey)
    const account = await this.accountRepository.findOneBy({ userKey: scMap.ukey });

    // If account doesn't exist, handle as a new user
    if (!account) {
      return this.handleNewUser(scMap.email, scMap.username, scMap.ukey);
    }

    // Otherwise, update the existing account
    return this.updateExistingAccount(account, scMap);
  }

  // AI DS ID Update
  private async aiUpdateDsId(dsid: string, tempdsid: string): Promise<any> {
    // Find the account by tempdsid (email)
    const account = await this.accountRepository.findOneBy({ email: tempdsid });

    if (!account) {
      return false;
    } // Return false if no account is found

    // Update the account's email to the new dsid
    const { affected } = await this.accountRepository.update(account.id, { email: dsid });

    // If no rows were affected, return false (update failed)
    if (affected === 0) {
      return false;
    }

    // Return the updated account id
    return { id: account.id };
  }

  // AI User Login
  private async aiLogin(aidstartForm: AidstartFormDTO) {
    const account = await this.makeAILogin(aidstartForm.tempdsid);

    // If account doesn't exist or update fails, return false
    if (!account) {
      return false;
    }

    // Update lastLoginDate for the account and check if the update is successful
    const updated = await this.accountRepository.update(account.id, { lastLoginDate: new Date() });

    // If update wasn't successful, return false
    if (!updated.affected) {
      return false;
    }

    // Return the account id
    return { id: account.id };
  }

  // Handle AI Login and User Creation
  private async makeAILogin(tempdsid: string) {
    // Try to find the account by email (tempdsid)
    let account = await this.accountRepository.findOneBy({ email: tempdsid });

    if (!account) {
      // If account doesn't exist, create a new one
      account = await this.accountRepository.save({
        username: 'AI_TEMP',
        name: 'AI_TEMP',
        email: tempdsid,
        loginType: LoginType.AI_TEMP,
        course: CourseValue[Course.FREE],
        lastLoginDate: new Date(),
        registDate: new Date(),
        updateDate: new Date(),
        deleteFlag: 0,
        pwd: '',
      });
    }

    return account;
  }

  // OEM User Login
  private async oemLogin(aidstartForm: AidstartFormDTO) {
    const { token, oemId, courseId } = aidstartForm;

    if (!token) {
      return false;
    }

    const decodedToken = SAKDecode(token);
    if (!decodedToken) {
      return false;
    }

    const [tokenOemId, tokenCourseId] = decodedToken.split('@@');

    // Validate token
    if (!this.isValidOemToken(tokenOemId, tokenCourseId, oemId, courseId)) {
      return false;
    }

    // Token is valid, proceed with login
    return await this.loginWithToken(token, aidstartForm);
  }

  // Check if OEM Token is valid
  private isValidOemToken(tokenOemId: string, tokenCourseId: string, oemId: string, courseId: string): boolean {
    return tokenOemId === oemId && tokenCourseId === courseId;
  }

  // Login with OEM Token
  private async loginWithToken(token: string, aidstartForm: AidstartFormDTO) {
    const { oemId, courseId, courceType } = aidstartForm;

    const account = await this.accountRepository.findOneBy({ username: `${oemId}@${courseId}` });
    if (account) {
      // If the token doesn't match, return false
      if (account.accessToken !== token) {
        return false;
      }

      // Update the course only if it's not an enterprise course
      if (!DigitalStageUtil.isAboveEnterpriseCourse(account.course)) {
        account.course = parseInt(courceType) == 0 ? Course.PRO : Course.FREE;
      }

      // Update last login date
      account.lastLoginDate = new Date();

      // Save and return the updated account
      return this.accountRepository.save(account);
    }

    // If account doesn't exist, register a new user
    return this.registerOemUser(token, aidstartForm);
  }

  // Register a New OEM User
  private async registerOemUser(token: string, aidstartForm: AidstartFormDTO) {
    const { oemId, courseId, courceType } = aidstartForm;
    const currentDate = new Date();

    const newUser = {
      username: `${oemId}@${courseId}`,
      name: `${oemId}@${courseId}`,
      email: null,
      accessToken: token,
      loginType: LoginType.OEM,
      course: parseInt(courceType) == 0 ? Course.PRO : Course.FREE,
      lastLoginDate: currentDate,
      registDate: currentDate,
      updateDate: currentDate,
      deleteFlag: 0,
      pwd: '',
    };

    return this.accountRepository.save(newUser);
  }

  async loginViaOEMStart(token: string, oemId: string, oemgo: string, courseId: string, courceType: string, cb: string, s: BindUpSettingDto) {
    const decodedToken = SAKDecode(token);
    if (!decodedToken) {
      return false;
    }

    const [tokenOemId, tokenCourseId] = decodedToken.split('@');
    if (!tokenOemId || !tokenCourseId) {
      return false;
    }

    if (!this.isValidOemToken(tokenOemId, tokenCourseId, oemId, courseId)) {
      return false;
    }

    const account = await this.loginWithToken(token, { oemId, courseId, courceType });
    if (!account) {
      return false;
    }

    const payloadSigned: IDataSign = {
      id: account?.id, // Account ID from existing or newly created user
      email: account?.email,
      name: account?.name,
      oemId,
      oemgo: oemgo || '',
      callbackUrl: cb || '',
      setting: s,
      course: courceType == '0' ? Course.PRO : Course.FREE,
    };

    return await this.getSuccessfullyLoginPayload(payloadSigned);
  }

  async createOrUpdateDigitalAccount({
    email,
    pwd,
    userName,
    userKey,
    loginType,
    expirationDate,
  }: {
    email: string;
    pwd: string;
    userName?: string;
    userKey: string;
    loginType: LoginType;
    expirationDate?: string;
  }) {
    let serialAccount: AccountEntity = await this.accountRepository.findOneBy({
      email: email,
      loginType: Or(In([LoginType.DEGITALSTAGEID]), IsNull()),
    });

    if (!serialAccount) {
      // Create new serial account
      serialAccount = await this.accountRepository.save({
        email: email,
        username: userName ?? null,
        name: userName ?? null,
        pwd: pwd,
        loginType,
        userKey,
        expirationDate: expirationDate ? new Date(expirationDate) : null,
        course: Course.PRO,
      });
    } else {
      await this.accountRepository.update(serialAccount.id, {
        username: userName ?? serialAccount?.username,
        name: userName ?? serialAccount?.username,
        course: serialAccount?.course ?? Course.PRO,
        userKey,
        loginType,
        expirationDate: expirationDate ? new Date(expirationDate) : null,
      });
    }

    return {
      ...serialAccount,
      email: email,
      pwd: pwd,
      username: userName ?? serialAccount?.username,
      name: userName ?? serialAccount?.username,
      loginType,
      userKey,
      expirationDate: expirationDate ? new Date(expirationDate) : null,
      course: serialAccount?.course ?? Course.PRO,
    };
  }
}
