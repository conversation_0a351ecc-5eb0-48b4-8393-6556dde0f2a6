import React from 'react';
import { createSvgIcon } from '@mui/material';

const InputPullDownOutlineIcon = createSvgIcon(
  <svg width="14" height="8" viewBox="0 0 14 8" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="outline" clipPath="url(#clip0_5057_10199)">
      <path
        id="Vector"
        d="M12.2442 2.11344C12.2968 2.11344 12.3398 2.15642 12.3398 2.20896V6.01075C12.3398 6.06329 12.2968 6.10627 12.2442 6.10627H1.62693C1.57439 6.10627 1.53141 6.06329 1.53141 6.01075V2.20896C1.53141 2.15642 1.57439 2.11344 1.62693 2.11344H12.2442ZM12.2442 0.776123H1.62693C0.83648 0.776123 0.194092 1.41851 0.194092 2.20896V6.01075C0.194092 6.8012 0.83648 7.44359 1.62693 7.44359H12.2442C13.0347 7.44359 13.6771 6.8012 13.6771 6.01075V2.20896C13.6771 1.41851 13.0347 0.776123 12.2442 0.776123Z"
        fill="currentColor"
      />
      <path id="Vector_2" d="M10.0449 5.16301L11.2939 3.55585H8.7124L10.0449 5.16301Z" fill="currentColor" />
    </g>
    <defs>
      <clipPath id="clip0_5057_10199">
        <rect width="13.483" height="6.66746" fill="white" transform="translate(0.194092 0.776123)" />
      </clipPath>
    </defs>
  </svg>,
  'InputPullDownOutlineIcon'
);

export default InputPullDownOutlineIcon;
