import 'dotenv/config';

import { BadRequestException, ValidationError, ValidationPipe } from '@nestjs/common';
import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as compression from 'compression';
import * as express from 'express';

import { ExceptionFilter } from './core/filters/exception.filter';
import { AppModule } from './modules/app.module';

async function bootstrap() {
  const PORT = parseInt(process.env.BACKEND_PORT || process.env.PORT, 10);
  const nestApp: NestExpressApplication = await NestFactory.create(AppModule);
  nestApp.useBodyParser('json', { limit: '10mb' });

  nestApp.enableCors({ exposedHeaders: ['Content-Disposition'] });
  nestApp.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      transformOptions: { exposeDefaultValues: true, enableImplicitConversion: true },
      exceptionFactory: (validationErrors: ValidationError[] = []) => {
        return new BadRequestException(validationErrors);
      },
    }),
  );

  const { httpAdapter } = nestApp.get(HttpAdapterHost);
  nestApp.useGlobalFilters(new ExceptionFilter(httpAdapter));
  nestApp.use(compression());

  await nestApp.init();

  const app = express();

  app.use(nestApp.getHttpAdapter().getInstance());

  app.listen(PORT, () => {
    // eslint-disable-next-line no-console
    console.log(`Backend App listening on port ${PORT} at ${new Date()}`);
  });
}

bootstrap();
