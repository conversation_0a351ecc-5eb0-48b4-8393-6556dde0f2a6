import CodeIcon from '@mui/icons-material/Code';
import DoneIcon from '@mui/icons-material/Done';
import LinkIcon from '@mui/icons-material/Link';
import Timeline from '@mui/lab/Timeline';
import TimelineConnector from '@mui/lab/TimelineConnector';
import TimelineContent from '@mui/lab/TimelineContent';
import TimelineDot from '@mui/lab/TimelineDot';
import TimelineItem, { timelineItemClasses } from '@mui/lab/TimelineItem';
import TimelineSeparator from '@mui/lab/TimelineSeparator';
import { Box, Stack, TextField, Typography } from '@mui/material';
import AceEditor from 'react-ace';
import 'ace-builds/src-noconflict/mode-html';
import { Link } from 'react-router-dom';

const FormHtml = ({ extId }: { extId: string }) => {
  return (
    <Box sx={{ width: '100%' }}>
      <Timeline
        sx={{
          [`& .${timelineItemClasses.root}:before`]: {
            flex: 0,
            padding: 0,
          },
        }}
      >
        <TimelineItem>
          <TimelineSeparator>
            <TimelineDot color="info">
              <CodeIcon />
            </TimelineDot>
            <TimelineConnector />
          </TimelineSeparator>
          <TimelineContent>
            <Stack direction="column" gap={1} mt={1}>
              <Typography variant="h6">フォームのHTMLタグを作成する</Typography>
              <Typography>1. フォームタグを作成し、入力フィールドを追加してください。各入力には一意のname属性が必要です。</Typography>
              <Typography>2. 送信ボタンのタグを追加してください。</Typography>
              <AceEditor
                style={{ width: '100%' }}
                mode="html"
                width="100%"
                height="450px"
                onLoad={() => {}}
                onChange={() => {}}
                fontSize={14}
                showGutter={true}
                value={`<!-- クラス、属性を変更しない -->
<form class="sc-html-embed" data-sc-form="${extId}">
  <!-- ↓ フィールドを追加/変更することができます。 -->
  <div data-sc-show-thank-you-message></div>
  <div>
    <label>ユーザー名*</label>
    <input name="ユーザー名" type="text" data-sc-type="input" data-sc-required />
    <div style="color: red" data-sc-show-if-error="ユーザー名">ユーザー名 is required</div>
  </div>
  <div>
    <label>メール*</label>
    <input name="メール" value="" type="text" data-sc-type="email" data-sc-required />
    <div style="color: red" data-sc-show-if-error="メール">メール is required</div>
  </div>
  <div>
    <label>Eメール確認*</label>
    <input name="Eメール確認" value="" data-sc-type="email_confirmation" data-sc-confirmed-name="メール" />
    <div style="color: red" data-sc-show-if-error="Eメール確認">Eメール確認 not match</div>
  </div>
  <button type="submit" data-sc-error-text="Some required fields are missing." data-sc-submitting-text="Submitting…">
    Submit
  </button>
</form>`}
                setOptions={{
                  showLineNumbers: true,
                  highlightActiveLine: false,
                  tabSize: 2,
                }}
              />
              <Typography sx={{ fontWeight: '700' }}>「ここで編集する内容は保存出来ませんのでご注意してください。」</Typography>
              <Typography>
                💡
                <Link target="_blank" to="/form-guide">
                  Code Guide
                </Link>
                で多くのバリデーションユーティリティが利用できます。
              </Typography>
              <Typography>💡 {`'sc_'`}から始まるname属性を使用しないでください。</Typography>
              <Typography>💡UTF-8のみサポートしています。その他の文字コードではデータが正常に送信されません。</Typography>
            </Stack>
          </TimelineContent>
        </TimelineItem>
        <TimelineItem>
          <TimelineSeparator>
            <TimelineDot color="info">
              <LinkIcon />
            </TimelineDot>
            <TimelineConnector />
          </TimelineSeparator>
          <TimelineContent>
            <Stack direction="column" gap={1} mt={1}>
              <Typography variant={'h6'} component="span">
                scriptタグを設定
              </Typography>
              <Typography>以下のscriptタグをコピーして、headタグ内に貼り付けてください。</Typography>
              <TextField disabled={true} value={`<script src="${import.meta.env.VITE_APP_URL}/html-embed.js" charset="UTF-8"></script>`} fullWidth />
            </Stack>
          </TimelineContent>
        </TimelineItem>
        <TimelineItem>
          <TimelineSeparator>
            <TimelineDot color="success">
              <DoneIcon />
            </TimelineDot>
          </TimelineSeparator>
          <TimelineContent>
            <Stack direction="column" gap={1} mt={1}>
              <Typography variant="h6" component="span">
                設定が完了しました！
              </Typography>
            </Stack>
          </TimelineContent>
        </TimelineItem>
      </Timeline>
    </Box>
  );
};

export default FormHtml;
