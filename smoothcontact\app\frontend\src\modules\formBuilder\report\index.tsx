import React, { ChangeEvent, FC, useState } from 'react';
import ReportHeader from './components/ReportHeader';
import ReportBar<PERSON>hart from './components/ReportBarChart';
import ReportSelectionType from './components/ReportSelectionType';
import SCBreadcrumbs from '@/components/common/SCBreadcrumbs';
import useLogic from '@/modules/formBuilder/report/useLogic';
import { FormControlNames } from '@/utils/formBuilderUtils';
import ReportOtherAnswer from './components/ReportOtherAnswer';
import SCModal from '@/components/common/SCModal';
import SCButton from '@/components/common/SCButton';
import { Container, Typography, Box, Stack, Grid, CircularProgress, TextField, FormControl, MenuItem } from '@mui/material';
import ReportCrossTabulation from './components/ReportCrossTabulation';

// A reusable SelectComponent for target and cross-tabulation selections
const SelectComponent: FC<{
  value: string;
  onChange: (event: ChangeEvent<HTMLInputElement>) => void;
  options: Record<string, string>;
  label: string;
}> = ({ value, onChange, options, label }) => (
  <FormControl fullWidth>
    <TextField select label={label} value={value} onChange={onChange} fullWidth>
      {Object.entries(options).map(([key, label]) => (
        <MenuItem key={key} value={key}>
          {label}
        </MenuItem>
      ))}
    </TextField>
  </FormControl>
);

const FormBuilderReportModule: FC = () => {
  const { data, loading, downloadCsv, downloadStatictisCsv } = useLogic();
  const [isCrossTabulationModalOpen, setIsCrossTabulationModalOpen] = useState(false);

  // State management for selections and visibility
  const [isDisplayBarChart, setIsDisplayBarChart] = useState(true);
  const [targetTabulationSelection, setTargetTabulationSelection] = useState<string>('');
  const [crossTabulationSelection, setCrossTabulationSelection] = useState<string>('');
  const [confirmedSelections, setConfirmedSelections] = useState({
    target: { key: '', value: '' },
    cross: { key: '', value: '' },
  });

  // Loader while data is being fetched
  if (loading || !data) {
    return (
      <Box sx={{ position: 'fixed', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}>
        <CircularProgress />
      </Box>
    );
  }

  const allowedSelectionControl = [FormControlNames.RADIO, FormControlNames.CHECKLIST, FormControlNames.DROPDOWN, FormControlNames.CHECKBOX];

  // Tabulation options filtered by allowed controls
  const tabulationOptions = Object.fromEntries(
    Object.entries(data.elements)
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      .filter(([_, element]) => allowedSelectionControl.includes(element.controlName))
      .map(([key, element]) => [key, element.labelName])
  );

  // Handle modal actions and selection updates
  const handleSwapClick = () => {
    setIsDisplayBarChart((prev) => !prev);
    setConfirmedSelections({ target: { key: '', value: '' }, cross: { key: '', value: '' } });
  };

  const handleTargetTabulationSelectionChange = (event: ChangeEvent<HTMLInputElement>) => {
    setTargetTabulationSelection(event.target.value);
  };

  const handleCrossTabulationSelectionChange = (event: ChangeEvent<HTMLInputElement>) => {
    setCrossTabulationSelection(event.target.value);
  };

  const handleCrossTabulationModalConfirm = () => {
    setIsCrossTabulationModalOpen(false);
    setIsDisplayBarChart(false);

    setConfirmedSelections({
      target: { key: targetTabulationSelection, value: tabulationOptions[targetTabulationSelection] },
      cross: { key: crossTabulationSelection, value: tabulationOptions[crossTabulationSelection] },
    });
  };

  // Render report components based on element types
  const renderReportComponents = () => {
    return Object.values(data.elements).map((element, index) => {
      const statistics = Object.fromEntries(
        Object.entries(element.statistics)
          .filter(([key]) => key !== '')
          .map(([key, value]) => [key, value])
      );

      const isSelectionType = [FormControlNames.RADIO, FormControlNames.CHECKLIST, FormControlNames.DROPDOWN, FormControlNames.CHECKBOX].includes(
        element.controlName
      );

      return isSelectionType ? (
        <ReportSelectionType
          key={index}
          label={element.labelName}
          statistics={statistics}
          otherData={element.list.filter((item) => item !== '')}
          isFullWidth={element.list.length > 0}
        />
      ) : (
        <ReportOtherAnswer
          key={index}
          statistics={element.statistics}
          otherData={element.list}
          title={element.labelName}
          controlName={element.controlName}
        />
      );
    });
  };

  return (
    <Container>
      <Box sx={{ py: 2 }}>
        <Stack direction="column" gap={3}>
          <SCBreadcrumbs items={[{ child: <Typography color="secondary">レポート : {data.form.name}</Typography> }]} />
          <ReportHeader
            extId={data.form.extId}
            formTitle={data.form.name}
            formSubmissionTotal={data.form.totalSubmission}
            downloadCsv={downloadCsv}
            downloadStatictisCsv={downloadStatictisCsv}
            onCrossTabulationClick={() => setIsCrossTabulationModalOpen(true)}
            onSwapClick={handleSwapClick}
            isDisabledSwitchButton={isDisplayBarChart}
          />
          {isDisplayBarChart && <ReportBarChart chartData={data.general} />}

          {confirmedSelections.target?.key &&
            confirmedSelections.target?.value &&
            confirmedSelections.cross?.key &&
            confirmedSelections.cross?.value && (
              <ReportCrossTabulation targetTabulationSelection={confirmedSelections.target} crossTabulationSelection={confirmedSelections.cross} />
            )}

          <Stack sx={{ marginTop: '5px' }}>
            <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 3 }}>
              {renderReportComponents()}
            </Grid>
          </Stack>
        </Stack>
      </Box>

      <SCModal
        title="クロス集計の設定"
        width={600}
        isOpen={isCrossTabulationModalOpen}
        onClose={() => setIsCrossTabulationModalOpen(false)}
        closeBtnLabel="キャンセル"
        primaryAction={
          <SCButton
            variant="contained"
            sx={{ color: '#fff', fontSize: '14px' }}
            onClick={handleCrossTabulationModalConfirm}
            disabled={!targetTabulationSelection || !crossTabulationSelection}
          >
            クロス集計を適用
          </SCButton>
        }
      >
        <Typography variant="h6" sx={{ pt: 1, pb: 2 }}>
          クロス集計を行うためのデータを選んでください
        </Typography>

        <Stack direction="row" spacing={3} sx={{ width: '100%' }}>
          <SelectComponent
            label="ベースデータ"
            value={targetTabulationSelection}
            onChange={handleTargetTabulationSelectionChange}
            options={tabulationOptions}
          />
          <SelectComponent
            label="掛け合わせるデータ"
            value={crossTabulationSelection}
            onChange={handleCrossTabulationSelectionChange}
            options={tabulationOptions}
          />
        </Stack>
      </SCModal>
    </Container>
  );
};

export default FormBuilderReportModule;
