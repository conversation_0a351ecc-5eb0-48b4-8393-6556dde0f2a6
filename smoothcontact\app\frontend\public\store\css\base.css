@charset "utf-8";
.-dress_98938767a6ba4d55a7e9ebc619148d64 .a-header .c-menu.-menu_b li a {
  color: #ffffff;
  height: 60px;
  font-size: 14px;
  line-height: 4.285714286;
  background-color: rgb(36, 203, 212);
  padding: 0px 1em 0px 1em;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .a-header .c-menu.-menu_b li {
  border-right-style: none;
  border-left: 1px solid #38bbc2;
  border-bottom-style: none;
  border-top-style: none;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .a-header .c-menu.-menu_b li a:hover {
  background-color: rgb(56, 187, 194);
  color: #ffffff;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-title {
  color: #28868b;
  background-color: transparent;
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-large_headline {
  color: #28868b;
  font-size: 235%;
  font-weight: normal;
  line-height: 1.4;
  display: inline-block;
  border-bottom: 1px solid #28868b;
  padding-bottom: 0.8em;
  margin-top: 1em;
  margin-bottom: 1em;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-small_headline {
  color: #81c4c7;
  font-size: 120%;
  font-weight: normal;
  line-height: 1;
  border-left: 2px solid #a5d9db;
  padding-left: 1em;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-lead {
  background-color: transparent;
  font-size: 120%;
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-blockquote {
  color: #7a7a7a;
  background-color: transparent;
  border-color: #939393;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-enclosure {
  border-color: #a5d9db;
  padding: 1.5em 2em 1.5em 2em;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-note {
  color: #939393;
  background-color: transparent;
  font-size: 85%;
  border-color: #939393;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-credit {
  color: #939393;
  font-size: 80%;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-page_title {
  font-size: 140%;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-hr {
  background-color: transparent;
  border-color: #7a7a7a;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-title a {
  color: #38bbc2;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-large_headline a {
  color: #38bbc2;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-small_headline a {
  color: #38bbc2;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-lead a {
  color: #38bbc2;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-page_title a {
  background-color: transparent;
  color: #38bbc2;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu li a:hover {
  opacity: 1;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_a {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_a li {
  border-color: #939393;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_a li a {
  color: #24cbd4;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_a li a:hover {
  color: #38bbc2;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_a li .c-unlink {
  color: #939393;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_a li .c-current {
  color: #38bbc2;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_b {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_b li {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_b li a {
  color: #28868b;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_b li a:hover {
  color: #38bbc2;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_b li .c-unlink {
  color: #939393;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_b li .c-current {
  color: #38bbc2;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_c {
  background-color: rgb(40, 134, 139);
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_c li {
  background-color: transparent;
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_c li a {
  color: #ffffff;
  background-color: rgb(40, 134, 139);
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_c li a:hover {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_c li .c-unlink {
  color: #cccccc;
  background-color: transparent;
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_c li .c-current {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_d {
  background-color: transparent;
  border-style: none;
  padding: 0px;
  margin: 0px;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_d li {
  background-color: transparent;
  display: block;
  border-style: none;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_d li a {
  color: #24cbd4;
  background-color: transparent;
  display: block;
  font-size: 120%;
  background-image: url('images/arrow_blue.png');
  background-repeat: no-repeat;
  background-position: 12px center;
  border: 2px solid #24cbd4;
  border-radius: 2px;
  padding: 0.5em 0.5em 0.5em 20px;
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_d li a:hover {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  background-image: url('images/arrow.png');
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_d li .c-unlink {
  color: #cccccc;
  background-color: rgb(40, 134, 139);
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_d li .c-current {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_e {
  background-image: linear-gradient(to bottom, rgb(56, 187, 194) 0%, rgb(40, 134, 139) 50%);
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_e li {
  background-color: transparent;
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_e li a {
  color: #ffffff;
  background-color: transparent;
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_e li a:hover {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_e li .c-unlink {
  color: #cccccc;
  background-color: transparent;
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-menu_e li .c-current {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-v.-menu_e {
  background-color: transparent;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-v.-menu_e li a {
  background-image: linear-gradient(to bottom, rgb(56, 187, 194) 0%, rgb(40, 134, 139) 100%);
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-v.-menu_e li a:hover {
  background-image: linear-gradient(to bottom, rgb(56, 187, 194) 0%, rgb(56, 187, 194) 100%);
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-v.-menu_e li .c-unlink {
  color: #5b5b5b;
  background-color: rgb(56, 187, 194);
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu.-v.-menu_e li .c-current {
  background-image: linear-gradient(to bottom, rgb(56, 187, 194) 0%, rgb(56, 187, 194) 100%);
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion li a:hover {
  opacity: 1;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f {
  background-color: transparent;
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li {
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > a {
  color: #7a7a7a;
  background-color: transparent;
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > a:hover {
  color: #28868b;
  background-color: transparent;
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > .c-unlink {
  color: #939393;
  background-color: transparent;
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > a + ul:before {
  border-top-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > .c-unlink + ul:before {
  border-top-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > ul li:first-child:before {
  border-bottom-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > ul > li {
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > ul > li a {
  color: #ffffff;
  background-color: rgb(40, 134, 139);
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > ul > li a:hover {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > ul > li .c-unlink {
  color: #939393;
  background-color: rgb(40, 134, 139);
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-g .lavalamp-object {
  background-color: rgb(189, 227, 229);
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-g li {
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-g li a {
  color: #7a7a7a;
  background-color: transparent;
  border-color: transparent;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-g li .c-unlink {
  color: #939393;
  background-color: transparent;
  border-color: transparent;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-j li {
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-j a {
  color: #28868b;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-j li .c-unlink {
  color: #939393;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-j .lavalamp-object {
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-h li {
  border-color: transparent;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-h li a {
  color: #ffffff;
  background-color: rgb(40, 134, 139);
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-h li a:hover {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #21595c;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-h .c-current a {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #21595c;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-h li .c-unlink {
  color: #939393;
  background-color: rgb(240, 240, 240);
  border-color: #b7b7b7;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-i li {
  border-color: transparent;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-i li a {
  color: #ffffff;
  background-color: rgb(40, 134, 139);
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-i li a:hover {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #21595c;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-i li.c-current a {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #21595c;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-i li .c-unlink {
  color: #939393;
  background-color: rgb(240, 240, 240);
  border-color: #b7b7b7;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu li a {
  text-decoration: none;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion li a {
  text-decoration: none;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-menu {
  margin: 0px;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list_table {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list_table th {
  color: #28868b;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list_table td {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list_news {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list_news th {
  color: #28868b;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list_news td {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list_indent {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list_indent th {
  color: #28868b;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list_indent td {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list-no_mark {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list-numbers {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list-alphabet {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list-alphabet_small {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list-greece {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-list-greece_small {
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-breadcrumb {
  color: #7a7a7a;
  background-color: transparent;
  border-color: #7a7a7a;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-breadcrumb a {
  color: #28868b;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-pager a {
  color: #28868b;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-pager a:hover {
  color: #22696d;
  background-color: transparent;
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-pager .c-current {
  color: #22696d;
  background-color: transparent;
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-pager span {
  color: #7a7a7a;
  background-color: transparent;
  border-color: #7a7a7a;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .c-link_top {
  color: #28868b;
  background-color: transparent;
  border-color: #28868b;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .b-tab_navigation li a {
  color: #ffffff;
  background-color: rgb(40, 134, 139);
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64.b-tab_navigation li a {
  color: #ffffff;
  background-color: rgb(40, 134, 139);
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .b-tab_navigation li.-active a {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64.b-tab_navigation li.-active a {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .b-tab_navigation li a:hover {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64.b-tab_navigation li a:hover {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .b-tab_contents {
  background-color: transparent;
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64.b-tab_contents {
  background-color: transparent;
  border-color: #22696d;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .b-accordion_navigation a {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  text-decoration: none;
  background-image: url('images/arrow.png');
  background-repeat: no-repeat;
  background-position: 10px center;
  border-radius: 2px;
  padding: 0.5em 1em 0.5em 30px;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64.b-accordion_navigation a {
  color: #ffffff;
  background-color: rgb(56, 187, 194);
  text-decoration: none;
  background-image: url('images/arrow.png');
  background-repeat: no-repeat;
  background-position: 10px center;
  border-radius: 2px;
  padding: 0.5em 1em 0.5em 30px;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .b-accordion_navigation.-active a {
  background-color: rgb(56, 187, 194);
  border-bottom: 0px;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64.b-accordion_navigation.-active a {
  background-color: rgb(56, 187, 194);
  border-bottom: 0px;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .b-accordion_navigation a:hover {
  background-color: rgb(36, 203, 212);
}

.-dress_98938767a6ba4d55a7e9ebc619148d64.b-accordion_navigation a:hover {
  background-color: rgb(36, 203, 212);
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .b-accordion .column {
  background-color: transparent;
  border: 1px solid #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64.b-accordion .column {
  background-color: transparent;
  border: 1px solid #38bbc2;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .b-accordion_navigation {
  border-style: none;
  margin-top: 2px;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64.b-accordion_navigation {
  border-style: none;
  margin-top: 2px;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .a-header {
  background-color: rgb(36, 203, 212);
  color: #ffffff;
  border-color: #bde3e5;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .a-billboard {
  border-color: #bde3e5;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .a-main {
  border-color: #bde3e5;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .a-site_contents {
  border-color: #bde3e5;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .a-side-a {
  border-color: #bde3e5;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .a-side-b {
  border-color: #bde3e5;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .a-footer {
  background-color: rgb(240, 240, 240);
  border-color: #bde3e5;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .a-header .c-page_title {
  color: #ffffff;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .a-header a {
  color: #ffffff;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 {
  color: #555555;
  font-size: 14px;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 a {
  color: #24cbd4;
  text-decoration: underline;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .bg-window {
  background-color: rgb(255, 255, 255);
  border-color: #bde3e5;
}

.-dress_98938767a6ba4d55a7e9ebc619148d64 .bg-document {
  background-color: transparent;
  border-color: #bde3e5;
}

@media print, screen and (max-width: 768px) {
  .-dress_98938767a6ba4d55a7e9ebc619148d64 #spNavigationTrigger {
    background-color: rgb(255, 255, 255);
    border-color: #38bbc2;
  }

  .-dress_98938767a6ba4d55a7e9ebc619148d64 #spNavigationTrigger .c-sp-navigation_line1,
  .-dress_98938767a6ba4d55a7e9ebc619148d64 #spNavigationTrigger .c-sp-navigation_line2,
  .-dress_98938767a6ba4d55a7e9ebc619148d64 #spNavigationTrigger .c-sp-navigation_line3 {
    border-color: #38bbc2;
  }

  .-dress_98938767a6ba4d55a7e9ebc619148d64 #js-globalNavigation {
    background-color: rgb(255, 255, 255);
    border-color: #38bbc2;
  }

  .-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f {
    background-color: rgb(40, 134, 139);
  }

  .-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > a {
    color: #ffffff;
  }

  .-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > a:hover {
    color: #ffffff;
    background-color: rgb(56, 187, 194);
  }

  .-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > .c-unlink {
    color: #939393;
  }

  .-dress_98938767a6ba4d55a7e9ebc619148d64 .m-motion.-f > li > ul > li .c-unlink {
    background-color: transparent;
  }

  .-dress_98938767a6ba4d55a7e9ebc619148d64 #js-globalNavigation .c-menu,
  .-dress_98938767a6ba4d55a7e9ebc619148d64 #js-globalNavigation .m-motion {
    height: 100undefined;
  }

  .-dress_98938767a6ba4d55a7e9ebc619148d64 #js-globalNavigation .c-menu li a,
  .-dress_98938767a6ba4d55a7e9ebc619148d64 #js-globalNavigation .m-motion li a {
    text-align: left;
  }

  .-dress_98938767a6ba4d55a7e9ebc619148d64 #js-globalNavigation .c-menu li .c-unlink,
  .-dress_98938767a6ba4d55a7e9ebc619148d64 #js-globalNavigation .m-motion li .c-unlink {
    text-align: left;
  }

  .-dress_98938767a6ba4d55a7e9ebc619148d64 #js-globalNavigation .c-menu li.c-sp-closer a,
  .-dress_98938767a6ba4d55a7e9ebc619148d64 #js-globalNavigation .m-motion li.c-sp-closer a {
    text-align: right;
  }
}

/* created version 8.13*/
