function popup(u, t, w, h, s, r) {
  if (_bind.fn.isEditBlock()) return !1;

  var param = '';
  w > 0 && (param += 'width=' + w + ','),
    h > 0 && (param += 'height=' + h + ','),
    t || (t = '_blank'),
    (param += 'scrollbars=' + s + ',resizable=' + r);
  var popwin = window.open(u, t, param);
  popwin && popwin.focus();
}

function idflash(f, w, h, bg) {
  var ua = navigator.userAgent,
    tm = new Date().getTime(),
    idbase = f.substring(0, f.lastIndexOf('/') + 1),
    buf = '';
  (buf =
    '<object classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000" codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=9,0,45,0"'),
    (buf += ' id="idswf"'),
    (buf += ' width="' + w + '"'),
    (buf += ' height="' + h + '"'),
    (buf += '>'),
    (buf += '<param name="movie" value="' + f + '&tm=' + tm + '" />'),
    (buf += '<param name="base" value="." />'),
    (buf += '<param name="bgcolor" value="' + bg + '" />'),
    (buf += '<param name="wmode" value="transparent" />'),
    (buf += '<param name="FlashVars" value="b=' + ua + '&idbase=' + idbase + '"/>'),
    (buf += '<embed src="' + f + '&tm=' + tm + '" type="application/x-shockwave-flash"'),
    (buf += ' id="idswf"'),
    (buf += ' width="' + w + '"'),
    (buf += ' height="' + h + '"'),
    (buf += ' base="."'),
    (buf += ' wmode="transparent"'),
    (buf += ' bgcolor="' + bg + '"'),
    (buf += ' FlashVars="b=' + ua + '&idbase=' + idbase + '"'),
    (buf += '></embed>'),
    (buf += '</object>'),
    document.write(buf);
}
var _bind = {
  def: {},
  fn: {},
  base: {},
  device: {},
  loader: {},
  slide: {},
  menu: {},
  other: {},
  bridge: {},
  resize: {},
  window: {
    _width: $(window).width(),
    _height: $(window).height(),
  },
  syncLoad: !0,
  status: {
    getStatus: function () {
      var _self = this,
        _frameEl = window.frameElement;
      if (null !== _frameEl && !_bind.fn.isEmpty(_frameEl)) {
        var _frameId = _frameEl.id;
        if ('preview-area' === _frameId) {
          var _frameSrc = $(_frameEl).attr('src');
          (_self.sitePreview = !0), _frameSrc.indexOf('bindapp=1') > -1 && (_self.siteEditing = !0);
        } else 'id-template-item-preview' === _frameId ? (_self.dressListPreview = !0) : 'id-preview' === _frameId && (_self.dressPreview = !0);
      }
    },
    dressListPreview: !1,
    dressPreview: !1,
    siteEditing: !1,
    sitePreview: !1,
  },
};
!(function (_bd) {
  (_bd.fn = {
    isEmpty: function (aParam) {
      var ret = !1;

      return null === aParam ? (ret = !0) : void 0 === aParam ? (ret = !0) : '' === aParam ? (ret = !0) : 0 === aParam.length && (ret = !0), ret;
    },
    onResize: function (aFunc, aNoLoad) {
      var event = 'resize';
      aNoLoad ||
        _.defer(aFunc, {
          type: 'load',
        }),
        $(window).on(event, _.throttle(aFunc, _bd.def.resizeTime));
    },
    isEditBlock: function () {
      return location.search.indexOf('bindapp=1') > -1 ? 1 : location.search.indexOf('bindapp=2') > -1 && 2;
    },
    isAnimationOff: function () {
      var ret = !1;

      return this.isEditBlock() ? (ret = !0) : location.search.indexOf('animation_off=1') != -1 && (ret = !0), ret;
    },
    bdRefresh: function () {
      this.isEditBlock() &&
        (_bd.blockEdit
          ? _bd.blockEdit.resize()
          : setTimeout(function () {
            _bd.blockEdit.resize();
          }, 1e3));
    },
    heightRefresh: function ($t) {
      this.bdRefresh();
      var eventMode = _bd.device.ipad || _bd.device.iphone ? 'orientationchange' : 'resize';
      $(window).trigger(eventMode);
    },
    setFooter: function (init) {
      var $footer = $('#a-footer');
      if ($footer.data('float')) return !1;

      var orgStyle = $footer.attr('style');
      if (($footer.css('margin-top', ''), $footer.length > 0 && $footer.offset().top + $footer.outerHeight() < _bind.window._height))
        if (_bind.fn.isEditBlock()) {
          var _footerHeight = 0 === $footer.outerHeight() ? 100 : $footer.outerHeight(),
            _plusValue = _bind.window._height - $footer.offset().top - _footerHeight - 34;
          if (_plusValue < 0) return !1;

          $footer.css('marginTop', _plusValue);
        } else {
          var _plusValue = _bind.window._height - $footer.offset().top - $footer.outerHeight();
          if (_plusValue < 0) return !1;

          $footer.css('marginTop', _plusValue);
        }
      else !init && _bind.fn.isEditBlock() && $footer.attr('style', orgStyle);
    },
    nowDevice: function () {
      return _bd.window._width < _bd.device.spSize ? 'sp' : _bd.window._width <= _bd.device.tabletSize ? 'tablet' : 'pc';
    },
    moveClass: function ($addElem, $removeElem) {
      $removeElem && $removeElem.removeClass('-active'), $addElem && $addElem.addClass('-active');
    },
    smoothScroll: function (event, hashName) {
      event.preventDefault();
      var target = $('#' + decodeURIComponent(hashName)),
        _headerHeightNegative = $('#a-header').data('float') ? $('#a-header').outerHeight(!0) * -1 : 0;
      $('[data-bk-id]').each(function () {
        var $__t = $(this);
        if ($__t.data('sticky')) {
          var _stickyHeight = $__t.outerHeight(!0);
          _headerHeightNegative -= _stickyHeight;
        }
      }),
        _bd.fn.scrollAnimation(target, _headerHeightNegative);
    },
    scrollAnimation: function ($t, _offset) {
      var _scrollSpeed = 500;
      if (_offset) var _offNum = _offset;
      else var _offNum = 0;

      $t.velocity('stop').velocity('scroll', {
        offset: _offNum,
        duration: _scrollSpeed,
      });
    },
    paddings: function (elem) {
      var $elem = $(elem);

      return (
        !($elem.length <= 0) && {
          top: parseInt($elem.css('paddingTop').replace('px', ''), 10),
          right: parseInt($elem.css('paddingRight').replace('px', ''), 10),
          bottom: parseInt($elem.css('paddingBottom').replace('px', ''), 10),
          left: parseInt($elem.css('paddingLeft').replace('px', ''), 10),
        }
      );
    },
    margins: function (elem) {
      var $elem = $(elem);

      return (
        !($elem.length <= 0) && {
          top: parseInt($elem.css('marginTop').replace('px', ''), 10),
          right: parseInt($elem.css('marginRight').replace('px', ''), 10),
          bottom: parseInt($elem.css('marginBottom').replace('px', ''), 10),
          left: parseInt($elem.css('marginLeft').replace('px', ''), 10),
        }
      );
    },
    documentGetFullscreenElement: function (document_obj) {
      return (
        document_obj.fullscreenElement ||
        document_obj.webkitFullscreenElement ||
        document_obj.webkitCurrentFullscreenElement ||
        document_obj.mozFullScreenElement ||
        document_obj.msFullscreenElement ||
        null
      );
    },
  }),
    (_bd.base = {
      spGlobalNavigation: {
        target: null,
        slideType: null,
        onAnim: !1,
        _slideAnimationSpeed: 400,
        init: function () {
          var _self = this,
            $body = $('body'),
            $window = $(window),
            $spNaviTrigger = $('#spNavigationTrigger');
          if ($spNaviTrigger.data('isLoadTrigger')) return $spNaviTrigger.removeClass('js-hide'), !1;

          var $globalNavigation = $('#js-globalNavigation');
          _self.slideType = $globalNavigation.data('slide-type');
          var _btnPosition = $globalNavigation.data('btn-position'),
            _isRelativeOnTablet = $globalNavigation.data('relative-on-tablet'),
            $globalNavigationBaseBlock = $globalNavigation.closest('div[data-bk-id]');
          $globalNavigationBaseBlock.addClass('js-globalNavigationBaseBlock');
          var _nowDevice = _bd.fn.nowDevice();
          if (((_self.beforeDevice = _nowDevice), 'pc' === _nowDevice)) _self.removeCloseBtn();
          else {
            if ($('#js-sp-menu_closer').length > 0) return !1;

            _self.appendCloseBtn(), _self.closeBtnAction();
          }

          _self.navigationHeightControll(),
            $spNaviTrigger.data('isLoadTrigger', !0),
            $spNaviTrigger.append($('<div />').addClass('c-sp-navigation_line1 c-sp-navigation_line')),
            $spNaviTrigger.append($('<div />').addClass('c-sp-navigation_line2 c-sp-navigation_line')),
            $spNaviTrigger.append($('<div />').addClass('c-sp-navigation_line3 c-sp-navigation_line')),
            setTimeout(function () {
              'rightSlide' === _self.slideType
                ? ($globalNavigation.addClass('-js-menuSlide-right_side_sliders'),
                  $spNaviTrigger.on('click', function (e) {
                    return (
                      e.preventDefault(),
                      1 == _self.onAnim
                        ? void e.stopPropagation()
                        : ((_self.onAnim = !0),
                          $body.addClass('js-no_scroll'),
                          $globalNavigation.stop(!0, !0).velocity(
                            {
                              left: 0,
                            },
                            _self._slideAnimationSpeed,
                            function () {
                              _self.onAnim = !1;
                            }
                          ),
                          void $window.on('touchmove.noScroll', function (e) {
                            e.preventDefault();
                          }))
                    );
                  }),
                  _self.closeBtnAction(_self.slideType))
                : 'leftSlide' === _self.slideType
                  ? ($globalNavigation.addClass('-js-menuSlide-left_side_sliders'),
                    $spNaviTrigger.on('click', function (e) {
                      return (
                        e.preventDefault(),
                        1 == _self.onAnim
                          ? void e.stopPropagation()
                          : ((_self.onAnim = !0),
                            $body.addClass('js-no_scroll'),
                            $globalNavigation.stop(!0, !0).velocity(
                              {
                                right: 0,
                              },
                              _self._slideAnimationSpeed,
                              function () {
                                _self.onAnim = !1;
                              }
                            ),
                            void $window.on('touchmove.noScroll', function (e) {
                              e.preventDefault();
                            }))
                      );
                    }),
                    _self.closeBtnAction(_self.slideType))
                  : 'upSlide' === _self.slideType
                    ? ($globalNavigation.addClass('-js-menuSlide-up_sliders'),
                      $spNaviTrigger.on('click', function (e) {
                        return (
                          e.preventDefault(),
                          1 == _self.onAnim
                            ? void e.stopPropagation()
                            : ((_self.onAnim = !0),
                              $body.addClass('js-no_scroll'),
                              $globalNavigation.stop(!0, !0).velocity('slideDown', _self._slideAnimationSpeed, function () {
                                _self.onAnim = !1;
                              }),
                              void $window.on('touchmove.noScroll', function (e) {
                                e.preventDefault();
                              }))
                        );
                      }),
                      _self.closeBtnAction(_self.slideType))
                    : null === _self.slideType &&
                    ($globalNavigation.addClass('-js-menuSlide-no_animation'),
                      $spNaviTrigger.on('click', function (e) {
                        e.preventDefault(),
                          $body.addClass('js-no_scroll'),
                          $globalNavigation.show(),
                          $window.on('touchmove.noScroll', function (e) {
                            e.preventDefault();
                          });
                      }),
                      _self.closeBtnAction(_self.slideType)),
                'leftTop' === _btnPosition
                  ? $spNaviTrigger.addClass('-js-menuPosition-left_top')
                  : 'rightTop' === _btnPosition
                    ? $spNaviTrigger.addClass('-js-menuPosition-right_top')
                    : 'top' === _btnPosition
                      ? $spNaviTrigger.addClass('-js-menuPosition-top')
                      : 'leftTop_fixed' === _btnPosition
                        ? $spNaviTrigger.addClass('-js-menuPosition-left_top_fixed')
                        : 'rightTop_fixed' === _btnPosition
                          ? $spNaviTrigger.addClass('-js-menuPosition-right_top_fixed')
                          : 'top_fixed' === _btnPosition && $spNaviTrigger.addClass('-js-menuPosition-top_fixed'),
                _isRelativeOnTablet && $spNaviTrigger.addClass('-js-menuPosition-relative_on_tablet');
            }, 10);
        },
        navigationHeightControll: function () {
          var $globalNavigation = $('#js-globalNavigation');
          $globalNavigation.height() > _bd.window._height && $globalNavigation.height(_bd.window._height);
        },
        appendCloseBtn: function () {
          var $li = $('<li />').addClass('c-right c-sp-closer'),
            $anchor = $('<a />').attr('id', 'js-sp-menu_closer').attr('href', '#'),
            $icon = $('<span />').addClass('icon-close');
          $anchor.append($icon), $li.append($anchor), $('#js-globalNavigation ul').first().prepend($li);
        },
        removeCloseBtn: function () {
          $('#js-globalNavigation li.c-sp-closer').remove();
        },
        closeBtnAction: function () {
          var _self = this,
            $body = $('body'),
            $window = $(window),
            $globalNavigation = $('#js-globalNavigation'),
            $closeBtn = $('#js-sp-menu_closer');
          $closeBtn.off(),
            'rightSlide' === _self.slideType
              ? $closeBtn.on('click', function (e) {
                return (
                  e.preventDefault(),
                  1 == _self.onAnim
                    ? void e.stopPropagation()
                    : ((_self.onAnim = !0),
                      $body.removeClass('js-no_scroll'),
                      $window.off('.noScroll'),
                      void $globalNavigation.stop(!0, !0).velocity(
                        {
                          left: _bd.window._width,
                        },
                        _self._slideAnimationSpeed,
                        function () {
                          _self.onAnim = !1;
                        }
                      ))
                );
              })
              : 'leftSlide' === _self.slideType
                ? $closeBtn.on('click', function (e) {
                  return (
                    e.preventDefault(),
                    1 == _self.onAnim
                      ? void e.stopPropagation()
                      : ((_self.onAnim = !0),
                        $body.removeClass('js-no_scroll'),
                        $window.off('.noScroll'),
                        void $globalNavigation.stop(!0, !0).velocity(
                          {
                            right: _bd.window._width,
                          },
                          _self._slideAnimationSpeed,
                          function () {
                            _self.onAnim = !1;
                          }
                        ))
                  );
                })
                : 'upSlide' === _self.slideType
                  ? $closeBtn.on('click', function (e) {
                    return (
                      e.preventDefault(),
                      1 == _self.onAnim
                        ? void e.stopPropagation()
                        : ((_self.onAnim = !0),
                          $body.removeClass('js-no_scroll'),
                          $window.off('.noScroll'),
                          void $globalNavigation.stop(!0, !0).velocity('slideUp', _self._slideAnimationSpeed, function () {
                            _self.onAnim = !1;
                          }))
                    );
                  })
                  : null === _self.slideType &&
                  $closeBtn.on('click', function (e) {
                    e.preventDefault(), $body.removeClass('js-no_scroll'), $window.off('.noScroll'), $globalNavigation.hide();
                  });
          var $normalMenus = $globalNavigation.find('li').find('a:not(#js-sp-menu_closer)');

          return (
            $normalMenus.off('click.closeSpMenu'),
            'pc' !== _bd.fn.nowDevice() &&
            void $normalMenus.on('click.closeSpMenu', function (e) {
              $closeBtn.click();
            })
          );
        },
        resize: function () {
          var _tFunc = this,
            _nowDevice = _bd.fn.nowDevice();
          if ('pc' === _nowDevice) $('body').removeClass('js-no_scroll'), _tFunc.removeCloseBtn(), (_tFunc.beforeDevice = _nowDevice);
          else {
            if ('pc' === _tFunc.beforeDevice) {
              if (
                ('rightSlide' === _tFunc.slideType
                  ? $('#js-globalNavigation').css('left', _bd.window._width)
                  : 'leftSlide' === _tFunc.slideType
                    ? $('#js-globalNavigation').css('right', _bd.window._width)
                    : $('#js-globalNavigation').css('left', 0),
                  $('#js-sp-menu_closer').length > 0)
              )
                return !1;

              _tFunc.appendCloseBtn(), _tFunc.closeBtnAction(), _tFunc.navigationHeightControll();
            } else $('body').removeClass('js-no_scroll');

            _tFunc.beforeDevice = _nowDevice;
          }
        },
      },
      photoAlbum: {
        target: null,
        init: function () {
          function createOverlayBox() {
            (_tFunc.$overLay = $('<div />').addClass('c-overlay').hide()),
              _tFunc.$overLay.fadeIn(200),
              _tFunc.$overLay.on('click', function (e) {
                e.preventDefault();
                var $_t = $(this);
                _tFunc.removeFadeoutBox($_t), _tFunc.loadEnd();
              }),
              $('body').append(_tFunc.$overLay);
          }
          var _tFunc = this;
          (_tFunc.$block = $(_tFunc.target)),
            (_tFunc.$column = $(_tFunc.target).find('div.column')),
            (_tFunc.$columnZoom = _tFunc.$column.find('.js-zoomImage')),
            (_tFunc.$cImg = _tFunc.$columnZoom.closest('div.c-img')),
            (_tFunc.columnZoomLength = _tFunc.$columnZoom.length - 1),
            (_tFunc.activeNow = 0),
            _tFunc.$cImg.each(function () {
              var $t = $(this),
                $anchor = $t.find('.js-zoomImage');
              if (0 !== $anchor.length) {
                var $clickTarget;
                ($clickTarget = $t.find('.js-zoomImage').next('div.c-mouseover_position').length > 0 ? $t.find('div.c-mouseover_position') : $anchor),
                  $clickTarget.on('click', function (e) {
                    return (
                      e.preventDefault(),
                      !($('div.c-overlay').length > 0) &&
                      ((_tFunc.activeNow = 0),
                        _bd.status.sitePreview
                          ? (alert('画像拡大機能はプレビュー状態時のみ確認いただけます。'), !1)
                          : (createOverlayBox(),
                            (_tFunc.activeNow = _tFunc.$cImg.index($t.closest('div.c-img'))),
                            _tFunc.$cImg.find('.js-zoomImage').next('div.c-mouseover_position').length > 0
                              ? (_tFunc.$commentBox = _tFunc.$cImg.eq(_tFunc.activeNow).find('.js-zoomImage').next('div.c-mouseover_position'))
                              : $t.find('.c-img_comment').length > 0
                                ? (_tFunc.$commentBox = $t.find('.c-img_comment'))
                                : (_tFunc.$commentBox = null),
                            _tFunc.activeChanger(_tFunc.activeNow),
                            void _tFunc.changeImages('firstFlag')))
                    );
                  });
              }
            }),
            $(window).off('keydown'),
            $(window).on('keydown', function (e) {
              _tFunc.keyDownAction(e.keyCode);
            });
        },
        changeImages: function (_index) {
          var _tFunc = this,
            _firstFlag = 'firstFlag' === _index;
          if (_firstFlag) {
            _tFunc.$imageBox = $('<div />').addClass('c-overlay_outerBox');
            var $imageBox = _tFunc.$imageBox.addClass('js-no_scroll'),
              $imageInner = $('<div />').addClass('c-overlay_imageInner'),
              $_target = _tFunc.$activecImg.find('.js-zoomImage'),
              $tImage = $('<img />');
          } else {
            var $imageBox = _tFunc.$imageBox.addClass('js-no_scroll'),
              $_target = _tFunc.$columnZoom.eq(_index),
              $targetImg = $_target.closest('.c-img'),
              $imageInner = _tFunc.$imageBox.find('.c-overlay_imageInner'),
              $tImage = $imageInner.find('img');
            $targetImg.find('.js-zoomImage').next('div.c-mouseover_position').length > 0
              ? (_tFunc.$commentBox = $targetImg.find('.js-zoomImage').next('div.c-mouseover_position'))
              : $targetImg.find('.c-img_comment').length > 0
                ? (_tFunc.$commentBox = $targetImg.find('.c-img_comment'))
                : ($imageInner.find('.c-overlay_commentBox').remove(), (_tFunc.$commentBox = null));
          }

          $tImage.on('load', function () {
            if (
              (_firstFlag &&
                ($imageInner.append($tImage),
                  $imageBox.append($imageInner),
                  $imageBox.css({
                    left: '50%',
                    top: '50%',
                    width: 'auto',
                    height: 'auto',
                  }),
                  _tFunc.$overLay.append($imageBox),
                  $imageBox.on('click', function (e) {
                    e.stopPropagation();
                  })),
                !_bind.fn.isEmpty(_tFunc.$commentBox))
            )
              if (0 === $('div.c-overlay div.c-overlay_commentBox').length) {
                var $commentBox = $('<div />').addClass('c-overlay_commentBox'),
                  _comments = _tFunc.$commentBox.clone();
                _comments.find('.js-photo_mouseover').css({
                  width: 'auto',
                  height: 'auto',
                }),
                  $commentBox.append(_comments),
                  $imageInner.append($commentBox);
              } else {
                var _comments = _tFunc.$commentBox.clone();
                _comments.find('.js-photo_mouseover').css({
                  width: 'auto',
                  height: 'auto',
                }),
                  $imageInner.find('.c-overlay_commentBox').html(_comments);
              }

            var _targetSize = $_target.attr('rel').split(','),
              _originImg = {};
            _.each(_targetSize, function (val) {
              val.match('width=')
                ? (_originImg.width = parseInt(val.replace('width=', ''), 10))
                : val.match('height=') && (_originImg.height = parseInt(val.replace('height=', ''), 10));
            }),
              (_tFunc.maxImageSize = {
                width: _originImg.width,
                height: _originImg.height,
              }),
              _bind.fn.isEmpty(_tFunc.$commentBox)
                ? (_tFunc.goalSize = {
                  width: _originImg.width + _bd.fn.paddings($imageBox).left + _bd.fn.paddings($imageBox).right,
                  height: _originImg.height + _bd.fn.paddings($imageBox).top + _bd.fn.paddings($imageBox).bottom,
                })
                : ((_tFunc.goalSize = {
                  width: _originImg.width + _bd.fn.paddings($imageBox).left + _bd.fn.paddings($imageBox).right,
                  height: _originImg.height + _bd.fn.paddings($imageBox).top + _bd.fn.paddings($imageBox).bottom,
                }),
                  0.9 * _bind.window._height > _tFunc.goalSize.height &&
                  (_tFunc.goalSize.height += $imageInner.find('.c-overlay_commentBox').outerHeight(!0))),
              //!_bind.fn.isEmpty($commentBox) ? $(this).height() + _bd.fn.paddings($imageBox).top + _bd.fn.paddings($imageBox).bottom + $commentBox.outerHeight(true) : $(this).height() + _bd.fn.paddings($imageBox).top + _bd.fn.paddings($imageBox).bottom
              _tFunc.loadEnd(),
              _firstFlag && ($imageBox.width(10), $imageBox.height(10)),
              _tFunc.imageBoxResizer(),
              _firstFlag && (_tFunc.nextBtns(), _tFunc.prevBtns(), _tFunc.closeBtns(), _tFunc.btnController()),
              _bind.fn.isEmpty(_tFunc.$commentBox) || _tFunc.commentFadein($imageInner.find('.c-img_comment')),
              $tImage.off();
          }),
            _tFunc.loadStart(),
            $tImage.attr('src', $_target.attr('href'));
        },
        removeFadeoutBox: function ($box) {
          var _tFunc = this;
          $box.fadeOut(200, function () {
            $box.remove(), _tFunc.activeRemove();
          });
        },
        activeRemove: function () {
          this.$cImg.removeClass('active');
        },
        activeChanger: function (_index) {
          var _tFunc = this;
          _tFunc.activeRemove(), _tFunc.$cImg.eq(_index).addClass('active'), (_tFunc.$activecImg = _tFunc.$cImg.filter('.active'));
        },
        loadStart: function () {
          var $loadBox = $('<div />').addClass('js-loading').attr('id', 'js-loading');
          $('body').append($loadBox);
        },
        loadEnd: function () {
          $('#js-loading').remove();
        },
        nextBtns: function () {
          var _tFunc = this;
          if ($('div.c-overlay div.c-overlay-next').length > 0) return !1;

          var $next = $('<div />').addClass('c-overlay-next'),
            $nextIcon = $('<span />').addClass('icon-right_arrow');
          $next.append($nextIcon),
            $('div.c-overlay div.c-overlay_outerBox').append($next),
            $next.on('click', function (e) {
              e.preventDefault(),
                e.stopPropagation(),
                _tFunc.columnZoomLength > _tFunc.activeNow &&
                (_tFunc.activeChanger(++_tFunc.activeNow), _tFunc.changeImages(_tFunc.activeNow), _tFunc.btnController());
            });
        },
        prevBtns: function () {
          var _tFunc = this;
          if ($('div.c-overlay div.c-overlay-prev').length > 0) return !1;

          var $prev = $('<div />').addClass('c-overlay-prev'),
            $prevIcon = $('<span />').addClass('icon-left_arrow');
          $prev.append($prevIcon),
            $('div.c-overlay div.c-overlay_outerBox').append($prev),
            $prev.on('click', function (e) {
              e.preventDefault(),
                e.stopPropagation(),
                0 !== _tFunc.activeNow && (_tFunc.activeChanger(--_tFunc.activeNow), _tFunc.changeImages(_tFunc.activeNow), _tFunc.btnController());
            });
        },
        closeBtns: function () {
          if ($('div.c-overlay div.c-overlay-close_btn').length > 0) return !1;

          var _tFunc = this,
            $closeBtn = $('<div />').addClass('c-overlay-close_btn'),
            $closeIcon = $('<span />').addClass('icon-close');
          $closeBtn.append($closeIcon),
            $('div.c-overlay div.c-overlay_outerBox').append($closeBtn),
            $closeBtn.on('click', function (e) {
              e.preventDefault(), _tFunc.removeFadeoutBox($('div.c-overlay'));
            });
        },
        commentFadein: function ($commentBox) {
          $commentBox.hide(),
            setTimeout(function () {
              $commentBox.fadeIn(200);
            }, 400);
        },
        btnController: function () {
          var _tFunc = this;
          _tFunc.$imageBox.find('div.c-overlay-prev').removeClass('js-hide'),
            _tFunc.$imageBox.find('div.c-overlay-next').removeClass('js-hide'),
            0 === _tFunc.activeNow && $('div.c-overlay .c-overlay-prev').addClass('js-hide'),
            _tFunc.columnZoomLength <= _tFunc.activeNow && $('div.c-overlay .c-overlay-next').addClass('js-hide');
        },
        rationImageMeasure: function () {
          var _tFunc = this,
            _winOrient = _bind.window._width > _bind.window._height ? 'horizontal' : 'vertical',
            _orient = _tFunc.goalSize.width > _tFunc.goalSize.height ? 'horizontal' : 'vertical',
            _windowRation = 'horizontal' === _winOrient ? _bind.window._height / _bind.window._width : _bind.window._width / _bind.window._height,
            _imgRation = 'horizontal' === _orient ? _tFunc.goalSize.height / _tFunc.goalSize.width : _tFunc.goalSize.width / _tFunc.goalSize.height,
            _imgRationOpposite =
              'horizontal' === _orient ? _tFunc.goalSize.width / _tFunc.goalSize.height : _tFunc.goalSize.height / _tFunc.goalSize.width;

          return {
            windowOrientation: _winOrient,
            orientation: _orient,
            imgRation: _imgRation,
            imgRationOpposite: _imgRationOpposite,
            windowRation: _windowRation,
          };
        },
        commentHeightCal: function (_width) {
          var _tFunc = this,
            _boxWidth = _width ? _width : 500,
            _commentHeight = 0,
            $commentClone = _tFunc.$commentBox.clone();

          return (
            $commentClone
              .css({
                height: 'auto',
                width: _boxWidth,
              })
              .find('*')
              .css({
                height: 'auto',
                width: _boxWidth,
              }),
            $('body').append($commentClone),
            (_commentHeight = $commentClone.height()),
            $commentClone.remove(),
            _commentHeight
          );
        },
        maxSizeController: function (_rations) {
          var _tFunc = this,
            _commentHeight = 0;
          _tFunc.$commentBox && (_commentHeight = _tFunc.commentHeightCal(0.9 * _bind.window._width));
          var _maxSize = {
            width: 0.9 * _bind.window._width,
            height: 0.9 * _bind.window._height - _commentHeight,
          };
          (_tFunc.goalSize.width = _tFunc.maxImageSize.width), (_tFunc.goalSize.height = _tFunc.maxImageSize.height);
          _bind.fn.paddings('div.c-overlay_outerBox');
          'horizontal' === _rations.windowOrientation
            ? 'horizontal' === _rations.orientation
              ? (_maxSize.width < _tFunc.goalSize.width &&
                ((_tFunc.goalSize.width = _maxSize.width), (_tFunc.goalSize.height = _maxSize.width * _rations.imgRation)),
                _maxSize.height < _tFunc.goalSize.height &&
                ((_tFunc.goalSize.height = _maxSize.height), (_tFunc.goalSize.width = _tFunc.goalSize.height * _rations.imgRationOpposite)))
              : (_maxSize.height < _tFunc.goalSize.height &&
                ((_tFunc.goalSize.height = _maxSize.height), (_tFunc.goalSize.width = _maxSize.height * _rations.imgRation)),
                _maxSize.width < _tFunc.goalSize.width &&
                ((_tFunc.goalSize.width = _maxSize.width), (_tFunc.goalSize.height = _maxSize.width * _rations.imgRationOpposite)))
            : 'horizontal' === _rations.orientation
              ? (_maxSize.width < _tFunc.goalSize.width &&
                ((_tFunc.goalSize.width = _maxSize.width), (_tFunc.goalSize.height = _maxSize.width * _rations.imgRation)),
                _maxSize.height < _tFunc.goalSize.height &&
                ((_tFunc.goalSize.height = _maxSize.height), (_tFunc.goalSize.width = _maxSize.height * _rations.imgRationOpposite)))
              : (_maxSize.height < _tFunc.goalSize.height &&
                ((_tFunc.goalSize.height = _maxSize.height), (_tFunc.goalSize.width = _maxSize.height * _rations.imgRation)),
                _maxSize.width < _tFunc.goalSize.width &&
                ((_tFunc.goalSize.width = _maxSize.width), (_tFunc.goalSize.height = _tFunc.goalSize.width * _rations.imgRationOpposite))),
            (_tFunc.goalSize.height += _commentHeight);
        },
        imageBoxResizer: function () {
          var _tFunc = this,
            _rationSize = _tFunc.rationImageMeasure();
          _tFunc.maxSizeController(_rationSize),
            _tFunc.$imageBox.velocity('stop').velocity({
              left: (_bd.window._width - _tFunc.goalSize.width) / 2,
              top: (_bd.window._height - _tFunc.goalSize.height) / 2,
              width: _tFunc.goalSize.width,
              height: _tFunc.goalSize.height,
            });
        },
        keyDownAction: function (_code) {
          var _tFunc = this,
            $outerBox = $('div.c-overlay');
          switch (_code) {
            case 27:
              _tFunc.removeFadeoutBox($outerBox);
              break;

            case 39:
              if ($outerBox.find('div.c-overlay-next').hasClass('js-hide')) return !1;

              $outerBox.find('div.c-overlay-next').click();
              break;

            case 37:
              if ($outerBox.find('div.c-overlay-prev').hasClass('js-hide')) return !1;

              $outerBox.find('div.c-overlay-prev').click();
              break;

            default:
              return !1;
          }
        },
        resize: function () {
          var _self = this;
          void 0 !== _self.goalSize && _self.imageBoxResizer();
        },
      },
      photoComment: {
        target: null,
        init: function (_t) {
          var $this = $(this.target);
          $this.find('.js-photo_mouseover').css({
            width: $this.outerWidth(!0),
            height: $this.outerHeight(!0),
          });
          var $anchor = $this.find('.js-photo_mouseover').closest('.c-mouseover_position').prev();
          if ($anchor.length > 0 && !$anchor.hasClass('js-zoomImage') && 'A' == $anchor[0].tagName) {
            $this.find('.js-photo_mouseover').addClass('js-mouse_pointer');
            var $prevs = $this.find('.js-photo_mouseover').closest('.c-mouseover_position').prev();
            $this.find('.js-photo_mouseover').on('click', function (e) {
              e.preventDefault(), '_blank' === $prevs.attr('target') ? window.open($prevs.attr('href')) : (window.location = $prevs.attr('href'));
            });
          }
        },
        resize: function () {
          this.init();
        },
      },
      accordion: {
        target: null,
        init: function () {
          var _animationSpeed = 400,
            $accoThis = $(this.target).closest('.b-accordion'),
            $oneColumns = $accoThis.find('.column'),
            $oneNavigationAnchor = $accoThis.find('.b-accordion_navigation'),
            _animationFlag = !1,
            mouseEvent = 'click';
          $accoThis.hasClass('-mo') && (mouseEvent = 'mouseenter'),
            'first' === $accoThis.data('open-columns') || 'last' === $accoThis.data('open-columns')
              ? ('first' === $accoThis.data('open-columns')
                ? (_bd.fn.moveClass($oneNavigationAnchor.first()), $oneColumns.first().show())
                : 'last' === $accoThis.data('open-columns') && (_bd.fn.moveClass($oneNavigationAnchor.last()), $oneColumns.last().show()),
                $accoThis.hasClass('-mo') &&
                $oneNavigationAnchor.on('click', function (e) {
                  e.preventDefault();
                }),
                $oneNavigationAnchor.on(mouseEvent, function (e) {
                  if ((e.preventDefault(), _animationFlag)) return !1;

                  _animationFlag = !0;
                  var $t = $(this);
                  if ($t.hasClass('-active')) return (_animationFlag = !1), !1;

                  var _idx = $oneNavigationAnchor.index($t);
                  $oneNavigationAnchor.removeClass('-active'),
                    $t.addClass('-active'),
                    $oneColumns.stop(!0, !1).velocity('slideUp', _animationSpeed, 'easeOutExpo', function () {
                      _animationFlag = !1;
                    }),
                    $oneColumns
                      .eq(_idx)
                      .stop(!0, !1)
                      .velocity('slideDown', _animationSpeed, 'easeOutExpo', function () {
                        $(this).show(), (_animationFlag = !1);
                      });
                }))
              : ('all' !== $accoThis.data('open-columns') && 'allClose' !== $accoThis.data('open-columns')) ||
              ('all' === $accoThis.data('open-columns') && (_bd.fn.moveClass($oneNavigationAnchor), $oneColumns.show()),
                $accoThis.hasClass('-mo') &&
                $oneNavigationAnchor.on('click', function (e) {
                  e.preventDefault();
                }),
                $oneNavigationAnchor.on(mouseEvent, function (e) {
                  if ((e.preventDefault(), _animationFlag)) return !1;

                  _animationFlag = !0;
                  var $t = $(this),
                    _idx = $oneNavigationAnchor.index($t);
                  $t.hasClass('-active')
                    ? ($oneColumns
                      .eq(_idx)
                      .stop(!0, !1)
                      .velocity('slideUp', _animationSpeed, 'easeOutExpo', function () {
                        _animationFlag = !1;
                      }),
                      _bd.fn.moveClass(null, $t))
                    : ($oneColumns
                      .eq(_idx)
                      .stop(!0, !1)
                      .velocity('slideDown', _animationSpeed, 'easeOutExpo', function () {
                        $(this).show(), (_animationFlag = !1);
                      }),
                      _bd.fn.moveClass($t));
                }));
        },
        resize: function () {
          return !1;
        },
      },
      tab: {
        target: null,
        type: null,
        slideGap: 0,
        init: function () {
          var _self = this,
            $tabThis = $(_self.target).closest('.b-tab'),
            $tabContents = $tabThis.find('.b-tab_contents'),
            _animationFlag = !1,
            mouseEvent = 'click';
          if ((($tabThis.hasClass('-mo') || $tabThis.hasClass('-slider')) && (mouseEvent = 'mouseenter'), $tabThis.hasClass('-slider'))) {
            _self.type = 'slider';
            var _oneColumnWidthOuter,
              $column = $tabThis.find('.column'),
              $slideBox = $('<div />').addClass('b-tab_outer-slidebox'),
              _oneColumnWidth =
                $tabThis.find('.g-column').length > -1 ? $tabThis.find('.g-column').outerWidth() : $tabThis.find('.column').first().outerWidth(),
              _arrayColumn = [];
            $slideBox.width('auto'),
              $column.height('auto'),
              $tabContents.height('auto'),
              $column.each(function () {
                var $ct = $(this),
                  _gColumnFlag = $ct.closest('div.g-column').length > 0,
                  $outers = _gColumnFlag ? $ct.closest('div.g-column') : $ct,
                  _ctPaddings = {
                    left: _bd.fn.paddings($outers).left,
                    right: _bd.fn.paddings($outers).right,
                  },
                  _columnPadding = 0;
                _gColumnFlag && (_columnPadding = _bd.fn.paddings($ct).left + _bd.fn.paddings($ct).right);
                var _oneColumnWidthWithPaddingsConsideration = _oneColumnWidth - _ctPaddings.left - _ctPaddings.right - _columnPadding;
                (_oneColumnWidthOuter = _oneColumnWidth - _ctPaddings.left - _ctPaddings.right),
                  $ct.width(_oneColumnWidthWithPaddingsConsideration),
                  _arrayColumn.push($ct.outerHeight(!0));
              }),
              $slideBox.append($column),
              $slideBox.width($column.length * _oneColumnWidth),
              $column.height(Math.max.apply(null, _arrayColumn));
            var _tabContentsVerticaPaddings = _bd.fn.paddings($tabContents).top + _bd.fn.paddings($tabContents).bottom;
            $tabContents.height(Math.max.apply(null, _arrayColumn) - _tabContentsVerticaPaddings),
              $tabContents.append($slideBox),
              $tabThis.find('.b-tab_navigation li').on('click', function (e) {
                return !1;
              }),
              $tabThis.find('.b-tab_navigation li').on(mouseEvent, function (e) {
                e.preventDefault(), e.stopPropagation(), (_animationFlag = !0);
                var $t = $(this),
                  _liIndex = $tabThis.find('.b-tab_navigation li').index($t);
                $column.eq(_liIndex).outerHeight(!0);
                _bd.fn.moveClass($t, $tabThis.find('.b-tab_navigation li')),
                  (_self.slideGap = _oneColumnWidthOuter),
                  $slideBox.velocity('stop', !0).velocity(
                    {
                      left: _self.slideGap * _liIndex * -1,
                    },
                    400,
                    'easeOutExpo',
                    function () {
                      _animationFlag = !1;
                    }
                  );
              });
          } else
            (_self.type = 'another'),
              $tabThis.hasClass('-mo') &&
              $tabThis.find('.b-tab_navigation li').on('click', function (e) {
                return !1;
              }),
              $tabThis.find('.b-tab_navigation li').on(mouseEvent, function (e) {
                e.preventDefault(), e.stopPropagation(), (_animationFlag = !0);
                var $t = $(this),
                  $closestbTab = $t.closest('.b-tab'),
                  $closestTabContents = $closestbTab.find('.b-tab_contents'),
                  _idx = $closestbTab.find('.b-tab_navigation li').index($t),
                  $closestTabContentsOneColumns = $tabThis.find('.column'),
                  $isNextColumn = $closestTabContentsOneColumns.eq(_idx),
                  _nextContentsHeight =
                    $isNextColumn.outerHeight(!0) + _bind.fn.paddings($closestTabContents).top + _bind.fn.paddings($closestTabContents).bottom;
                _bd.fn.moveClass($t, $tabThis.find('.b-tab_navigation li'));
                var $tabNavigation = $tabThis.find('.b-tab_navigation');
                $tabContents.css('minHeight', 'auto'),
                  $tabNavigation.css('minHeight', 'auto'),
                  $closestTabContentsOneColumns.removeClass('js-show'),
                  $isNextColumn.addClass('js-show'),
                  $closestTabContents.velocity('stop', !0).velocity(
                    {
                      height: _nextContentsHeight,
                    },
                    function () {
                      if (((_animationFlag = !1), 'sp' !== _bind.fn.nowDevice() && $tabThis.hasClass('-menu'))) {
                        var _columnHeight = Math.max.apply(null, [$tabNavigation.outerHeight(!0), $tabContents.outerHeight(!0)]);
                        $tabContents.css('minHeight', _columnHeight), $tabNavigation.css('minHeight', _columnHeight);
                      }
                    }
                  );
              });

          $tabThis.find('.b-tab_navigation li').eq(0).trigger(mouseEvent);
        },
        resize: function () {
          var _self = this,
            $tabThis = $(_self.target).closest('.b-tab'),
            $tabContents = $tabThis.find('.b-tab_contents'),
            $tabNavigation = $tabThis.find('.b-tab_navigation');
          $tabThis.find('.column');
          if (null !== _bd.fn.documentGetFullscreenElement(document)) return !1;

          if ('slider' === _self.type) this.init();
          else if (
            ($tabContents.css({
              minHeight: 'auto',
              height: 'auto',
            }),
              $tabNavigation.css({
                minHeight: 'auto',
                height: 'auto',
              }),
              'sp' !== _bd.fn.nowDevice() && $tabThis.hasClass('-menu'))
          ) {
            var _columnHeight = Math.max.apply(null, [$tabNavigation.outerHeight(!0), $tabContents.outerHeight(!0)]);
            $tabContents.css({
              minHeight: _columnHeight,
              height: _columnHeight,
            }),
              $tabNavigation.css({
                minHeight: _columnHeight,
                height: _columnHeight,
              });
          }
        },
      },
      floatContents: {
        target: null,
        _marginWide: null,
        _targetColumns: null,
        _isResizing: !1,
        _isFixedWidthColumnsFlag: !1,
        gutter: null,
        init: function () {
          var _self = this,
            $t = $(_self.target),
            _fixWidth = $t.data('float-width'),
            $target = $t.find('>div').hasClass('column') ? $t : $t.find('>div'),
            $firstColumn = $target.find('div.column').eq(0);
          'sp' !== _bd.fn.nowDevice()
            ? $t.hasClass('c-space_normal')
              ? (_self._marginWide = 0.04)
              : $t.hasClass('c-space_wide')
                ? (_self._marginWide = 0.06)
                : $t.hasClass('c-space_narrow')
                  ? (_self._marginWide = 0.02)
                  : (_self._marginWide = 0)
            : $t.hasClass('c-sp-space_normal')
              ? (_self._marginWide = 0.04)
              : $t.hasClass('c-sp-space_wide')
                ? (_self._marginWide = 0.06)
                : $t.hasClass('c-sp-space_narrow')
                  ? (_self._marginWide = 0.02)
                  : $t.hasClass('c-sp-space_init') && (_self._marginWide = 0);
          var _className = $target[0].className,
            _classNames = null === _className ? null : _className.split(' ');
          _.each(_classNames, function (_clsName) {
            'sp' !== _bd.fn.nowDevice()
              ? _clsName.match(/-col\d/) && !_clsName.match(/-sp-col\d/) && (_self._targetColumns = parseInt(_clsName.replace('-col', ''), 10))
              : _clsName.match(/-sp-col\d/) && (_self._targetColumns = parseInt(_clsName.replace('-sp-col', ''), 10));
          }),
            null === _self._targetColumns && (_self._targetColumns = 1);
          var _columnWidth,
            _paddings = _bd.fn.paddings($firstColumn).left + _bd.fn.paddings($firstColumn).right,
            _margins = _bd.fn.margins($firstColumn).left + _bd.fn.margins($firstColumn).right,
            _borders =
              parseInt($firstColumn.css('borderLeftWidth').replace('px', ''), 10) +
              parseInt($firstColumn.css('borderRightWidth').replace('px', ''), 10),
            _spacer = _paddings + _margins + _borders;
          if (_fixWidth && 'sp' !== _bd.fn.nowDevice()) {
            if (_fixWidth.indexOf('px') !== -1) {
              _self._isFixedWidthColumnsFlag = !0;
              var removePixel = parseInt(_fixWidth.replace('px', ''), 10);
              (_self._targetColumns = Math.floor($target.width() / (removePixel - _spacer))), $target.find('div.column').width(removePixel - _spacer);
            } else if (_fixWidth.indexOf('%') !== -1) {
              var _noPercent = _fixWidth.replace('%', ''),
                removePercent = 0.01 * parseInt(_noPercent, 10),
                oneSentenceContents = Math.floor($target.width() * removePercent);
              (_columnWidth = oneSentenceContents),
                (_self._targetColumns = Math.floor($target.width() / oneSentenceContents - _spacer)),
                $target.find('div.column').width(_columnWidth - _spacer);
            }
          } else
            (_columnWidth = $target.width() - $target.width() * (1.5 * _self._marginWide)),
              $target.find('div.column').width(_columnWidth / _self._targetColumns - _spacer);

          1 !== _self._targetColumns || _self._isFixedWidthColumnsFlag
            ? (_self.gutter = ($target.width() / _self._targetColumns) * (1.88 * _self._marginWide))
            : (_self.gutter = 0),
            $target.find('.column').each(function (i) {
              i >= _self._targetColumns && $(this).css('marginTop', _self.gutter);
            }),
            _self._isResizing ||
            $target.masonry({
              itemSelector: 'div.column',
              gutter: _self.gutter,
            });
        },
        resize: function () {
          var _self = this,
            $t = $(_self.target),
            $target = $t.find('>div');
          (_self._isResizing = !0),
            _self.init(),
            $target.masonry({
              itemSelector: 'div.column',
              gutter: _self.gutter,
            }),
            $target.masonry('reloadItems');
        },
      },
      fixedController: {
        target: null,
        mainMargin: {
          pc: null,
          sp: null,
        },
        init: function (_t) {
          function setSideFloat($side, timer) {
            if ('sp' === _bd.fn.nowDevice()) return !1;

            var $wrap = $side.find('>div');
            $side.css('position', 'relative'),
              $wrap.css({
                position: 'absolute',
                top: 0,
                width: $side.width(),
              });
            var plusHeader = 0,
              $ghostHeader = $('#a-ghost_header');
            isHeaderFloat ? (plusHeader = $header.outerHeight(!0)) : $ghostHeader[0] && (plusHeader = $ghostHeader.outerHeight(!0)),
              $win.on('scroll.fixedable', function (e) {
                timer > 0 && clearTimeout(timer),
                  (timer = setTimeout(function () {
                    var wt = $win.scrollTop() + plusHeader,
                      offset = $side.offset().top;
                    wt > offset ? (wt -= offset) : (wt = 0);
                    var maxH = $contents.height(),
                      sideH = $wrap.height();
                    maxH < sideH + wt && (wt = maxH - sideH),
                      wt < 0 && ((wt = 0), $contents.css('minHeight', sideH)),
                      isHeaderFloat && isFirstView && ((wt = 0), (isFirstView = !1)),
                      $wrap.velocity('stop').velocity(
                        {
                          top: wt,
                        },
                        1e3,
                        'easeOutExpo'
                      );
                  }, 100));
              });
          }
          if (_bd.fn.isEditBlock()) return !1;

          var $win = $(window),
            $header = $('#a-header'),
            $footer = $('#a-footer'),
            $billboard = $('#a-billboard'),
            $sideA = $('#a-side-a'),
            $sideB = $('#a-side-b'),
            $main = $('#a-main'),
            $contents = $('#a-site_contents'),
            isHeaderFloat = !1,
            isFooterFloat = !1,
            isFirstView = !0,
            _bodyId = $('body').attr('id');
          if (
            ('l-5' === _bodyId &&
              (null === this.mainMargin.pc && 'sp' !== _bd.fn.nowDevice()
                ? (this.mainMargin.pc = parseInt(Math.floor($main.css('marginLeft').replace('px', 0)), 10) - 1)
                : null === this.mainMargin.sp &&
                'sp' === _bd.fn.nowDevice() &&
                (this.mainMargin.sp = parseInt(Math.floor($main.css('marginLeft').replace('px', 0)), 10) - 1)),
              $header.data('float'))
          ) {
            (isHeaderFloat = !0),
              $header.css({
                position: 'fixed',
                zIndex: 170,
                width: '100%',
                maxWidth: 'none',
                top: 0,
              }),
              setTimeout(function () {
                'sp' !== _bd.fn.nowDevice() || ('sp' === _bd.fn.nowDevice() && $('#a-billboard').hasClass('-sp-height100'))
                  ? $billboard.css('paddingTop', $header.outerHeight(!0))
                  : 'sp' === _bd.fn.nowDevice() && $billboard.css('marginTop', $header.outerHeight(!0));
              }, 10);
          }

          if (
            ($footer.data('float') &&
              ((isFooterFloat = !0),
                $footer.css({
                  position: 'fixed',
                  zIndex: 170,
                  width: '100%',
                  maxWidth: 'none',
                  bottom: 0,
                })),
              $sideA.data('float'))
          ) {
            var timerA = 0;
            setSideFloat($sideA, timerA),
              'sp' !== _bd.fn.nowDevice() && 'l-5' === _bodyId
                ? $main.css('marginLeft', this.mainMargin.pc + $sideA.outerWidth(!0))
                : $main.css('marginLeft', this.mainMargin.sp);
          }

          if ($sideB.data('float')) {
            var timerB = 0;
            setSideFloat($sideB, timerB);
          }

          ($sideA.data('float') || $sideB.data('float')) && $win.scroll();
        },
        resize: function () {
          var _self = this,
            $sideA = $('#a-side-a'),
            $sideB = $('#a-side-b'),
            $main = $('#a-main');
          'sp' === _bd.fn.nowDevice()
            ? ($sideA.data('float') &&
              ($(window).off('scroll.fixedable'),
                $sideA.find('>div').css({
                  width: '100%',
                  position: 'static',
                }),
                $main.css('marginLeft', _self.mainMargin.sp)),
              $sideB.data('float') &&
              ($(window).off('scroll.fixedable'),
                $sideB.find('>div').css({
                  width: '100%',
                  position: 'static',
                })))
            : _self.init();
        },
      },
      ghostHeader: {
        target: null,
        status: {
          startPoint: 0,
        },
        init: function () {
          var _tFunc = this,
            $header = $('#a-header'),
            $t = $(_tFunc.target),
            _nowDevice = _bd.fn.nowDevice();
          if (_bd.fn.isEditBlock() || 'sp' === _nowDevice || 'tablet' === _nowDevice)
            return $t.removeClass('js-ghost_mode'), $(window).off('scroll.ghostHeaderScroll'), !1;

          $t.addClass('js-ghost_mode'),
            setTimeout(function () {
              $(window).scroll();
            }, 1);
          var _effectType = $t.data('effect');
          (_tFunc.status.startPoint = $header.outerHeight(!0)),
            $(window).off('scroll.ghostHeaderScroll'),
            $(window).on('scroll.ghostHeaderScroll', function () {
              $(window).scrollTop() > _tFunc.status.startPoint && 'fade' === _effectType
                ? $t.addClass('-fade-mode').css({
                  left: $header.offset().left,
                })
                : $t.removeClass('-fade-mode').css({
                  left: 'auto',
                });
            });
        },
        resize: function () {
          this.init();
        },
      },
      bgMovie: {
        target: null,
        nowDevice: null,
        movieTarget: null,
        init: function () {
          var _tFunc = this;

          return (
            _tFunc.nowDevice !== _bind.fn.nowDevice() &&
            void setTimeout(function () {
              var $box = $(_tFunc.target);
              if (((_tFunc.nowDevice = _bind.fn.nowDevice()), 'sp' === _tFunc.nowDevice || _bind.fn.isEditBlock()))
                return _tFunc.movieTarget && $box.YTPPlayerDestroy(), void ('page' === $box.attr('id') && $box.css('backgroundImage', 'none'));

              var $contain;
              ($contain = $box.hasClass('bg-window') ? 'body' : $box),
                (_tFunc.movieTarget = null),
                (_tFunc.movieTarget = $box.mb_YTPlayer({
                  videoURL: $box.data('video'),
                  autoPlay: !0,
                  showControls: !1,
                  mute: $box.data('mute'),
                  loop: $box.data('loop'),
                  containment: $contain,
                })),
                _tFunc.soundBtnMuted(),
                $box.on('YTPEnd', function () {
                  setTimeout(function () {
                    $box.css('background', 'transparent');
                  }, 1);
                });
            }, 100)
          );
        },
        soundBtnMuted: function () {
          var _t = this,
            $box = $(_t.target),
            $soundBtns = $('.c-sound_btn1,.c-sound_btn2,.c-sound_btn3,.c-sound_btn4');
          (_bind.device.mobile || _bind.device.ipad || _bind.device.android) && $soundBtns.addClass('js-hide'),
            $box.data('mute') ? $soundBtns.removeClass('on') : $soundBtns.addClass('on'),
            $box.on('YTPReady', function () {
              $soundBtns.on('click', function () {
                $soundBtns.toggleClass('on'), $box.toggleVolume();
              });
            });
        },
        resize: function () {
          this.init();
        },
      },
      heightColumnController: {
        target: null,
        resizeFlag: !0,
        init: function () {
          var _oneLineColumns,
            _self = this,
            $t = $(_self.target),
            $columns = $t.find('div.column'),
            _columnMaxLength = $columns.length,
            _className = $t.find('div.g-column').length > 0 ? $t.find('div.g-column').attr('class') : null,
            _classNames = null === _className ? null : _className.split(' ');
          if (
            (null === _classNames
              ? (_oneLineColumns = 1)
              : (_.each(_classNames, function (_clsName) {
                'sp' !== _bind.fn.nowDevice()
                  ? _clsName.match(/-col\d/) && !_clsName.match(/-sp-col\d/) && (_oneLineColumns = parseInt(_clsName.replace('-col', ''), 10))
                  : _clsName.match(/-sp-col\d/) && (_oneLineColumns = parseInt(_clsName.replace('-sp-col', ''), 10));
              }),
                isNaN(_oneLineColumns) && (_oneLineColumns = 1)),
              $columns.height('auto'),
              'sp' === _bd.fn.nowDevice() && 1 === _oneLineColumns)
          )
            return !1;

          for (var _lines = Math.ceil(_columnMaxLength / _oneLineColumns), i = 1; i <= _lines; i++) {
            for (var _columnHeight, _columnsAry = [], j = 0; j < _oneLineColumns; j++) {
              var _eqNumber = j + (i - 1) * _oneLineColumns;
              $columns.eq(_eqNumber).height('auto'), _columnsAry.push($columns.eq(_eqNumber).height());
            }
            (_columnHeight = Math.max.apply(null, _columnsAry)),
              $columns.each(function (k) {
                _oneLineColumns < i * _oneLineColumns - k || k >= i * _oneLineColumns || (0 !== _columnHeight && $(this).height(_columnHeight));
              });
          }
          _self.resizeFlag &&
            $('body').hasClass('l-fixed-side') &&
            setTimeout(function () {
              $(window).resize(), (_self.resizeFlag = !1);
            }, 10);
        },
        resize: function () {
          this.init();
        },
      },
      popupWindow: {
        init: function () {
          var self = this;
          $('#page').on('click', '.js-popup', function (e) {
            e.preventDefault();
            var el = $(this),
              overLay = self.createOverlayBox(el);
            self.addImageBox(overLay, el);
          });
        },
        createOverlayBox: function (aEl) {
          var overLay = $('<div />').addClass('c-overlay').hide();
          overLay.fadeIn(200), this.scrollController(!0);
          var self = this;

          return (
            overLay.on('click', function (e) {
              e.preventDefault();
              var el = $(this);
              self.removeFadeoutBox(el), self.scrollController(!1);
            }),
            $('body').append(overLay),
            overLay
          );
        },
        addImageBox: function ($outerBox, aEl) {
          var popupBox = $('<div />').addClass('c-overlay_outerBox'),
            popupIframe = $('<iframe />').addClass('c-popup_iframe');
          popupIframe.attr('src', aEl.attr('href')),
            popupBox.css({
              left: '50%',
              top: '50%',
              width: 'auto',
              height: 'auto',
            }),
            $outerBox.append(popupBox),
            popupBox.on('click', function (e) {
              e.stopPropagation();
            }),
            popupBox.width(10),
            popupBox.height(10),
            this.imageBoxResizer(popupBox, popupIframe, aEl),
            this.closeBtns();
        },
        imageBoxResizer: function (aBox, aFrame, aEl) {
          var _suitableSize = this.maxSizeController(aEl);
          aBox.velocity('stop').velocity(
            {
              left: (_bd.window._width - _suitableSize.width) / 2,
              top: (_bd.window._height - _suitableSize.height) / 2,
              width: _suitableSize.width,
              height: _suitableSize.height,
            },
            function () {
              aBox.append(aFrame);
            }
          );
        },
        maxSizeController: function (aEl) {
          var width = aEl.data('width'),
            height = aEl.data('height'),
            resize = !_bind.fn.isEmpty(aEl.data('resize')) && aEl.data('resize'),
            _suitableSize = {
              width: 0,
              height: 0,
            };

          return (
            0.9 * _bd.window._width < width && resize ? (_suitableSize.width = 0.9 * _bd.window._width) : (_suitableSize.width = width),
            0.9 * _bd.window._height < height && resize ? (_suitableSize.height = 0.9 * _bd.window._height) : (_suitableSize.height = height),
            _suitableSize
          );
        },
        removeFadeoutBox: function ($box) {
          $box.fadeOut(200, function () {
            $box.remove();
          });
        },
        closeBtns: function () {
          if ($('div.c-overlay div.c-overlay-close_btn').length > 0) return !1;

          var $closeBtn = $('<div />').addClass('c-overlay-close_btn'),
            $closeIcon = $('<span />').addClass('icon-close');
          $closeBtn.append($closeIcon), $('div.c-overlay div.c-overlay_outerBox').append($closeBtn);
          var self = this;
          $closeBtn.on('click', function (e) {
            e.preventDefault(), self.removeFadeoutBox($('div.c-overlay')), self.scrollController(!1);
          });
        },
        scrollController: function (flag) {
          flag ? $('body').addClass('js-no_scroll') : $('body').removeClass('js-no_scroll');
        },
        resize: function () {
          return !1;
        },
      },
      smoothScroll: {
        target: null,
        init: function (_sync) {
          if (_bd.fn.isEditBlock()) return !1;

          var _self = this,
            $t = $(_self.target),
            _href = $t.attr('href'),
            _fileFlag = !1,
            _hrefSplit = _href.split('/'),
            _cleanHref = _self._replaceDots(_hrefSplit).toString(),
            _pathName = document.location.pathname,
            _pathSplit = _pathName.split('/'),
            _cleanPath = _self._replaceDots(_pathSplit).toString(),
            _pathFirst = _cleanPath.substr(0, 1);
          if (
            ('/' === _pathFirst && (_cleanPath = _cleanPath.substr(1)),
              _cleanHref.toString() === _cleanPath.toString() && (_fileFlag = !0),
              ('#' === _href && $t.hasClass('c-link_top')) ||
              ('' === _href && $t.hasClass('c-link_top')) ||
              ('javascript:void(0);' === _href && $t.hasClass('c-link_top')))
          )
            $t.on('click', function (e) {
              var $_t = $('html,body');
              _bd.fn.scrollAnimation($_t, 0);
            });
          else if (_href.match('#') || (_href.match('#') && _fileFlag)) {
            var _hashName = _href.split('#').pop();
            try {
              '' != _hashName &&
                $('#' + _hashName).length > 0 &&
                $t.on('click', function (e) {
                  _bd.fn.smoothScroll(e, _hashName);
                });
            } catch (e) { }
          }
        },
        _replaceDots: function (vals) {
          var _shift,
            _split = vals;

          return (
            _.each(_split, function (_val, i) {
              ('..' !== _val && '' !== _val) || (_shift = _split.splice(i, i + 1));
            }),
            _split.pop(),
            _split ? _split : null
          );
        },
      },
      smoothContact: {
        target: null,
        init: function () {
          var _self = this,
            $target = $(_self.target);
          'sp' !== _bind.fn.nowDevice()
            ? ($target.width($target.data('width')), $target.height($target.data('height')))
            : ($target.width($target.data('sp-width')), $target.height($target.data('sp-height')));
        },
        resize: function () {
          this.init();
        },
      },
      tracking: {
        target: null,
        init: function () {
          var _tFunc = this,
            $this = $(_tFunc.target),
            _tid = $('body').data('ga-tracking'),
            _afterlink = ($this.attr('href'), $this.data('download-mode'), $this.data('after-link')),
            _tracking = $this.data('tracking-id');

          return (
            !_bind.fn.isEditBlock() &&
            void $this.on('click', function (e) {
              _bind.fn.isEmpty(_tid) ||
                ga('send', {
                  hitType: 'event',
                  eventCategory: _tracking,
                  eventAction: 'Download',
                }),
                _bind.fn.isEmpty(_afterlink) || setTimeout('document.location = "' + _afterlink + '"', 200);
            })
          );
        },
      },
      cartThumbnail: {
        target: null,
        init: function () {
          var _tFunc = this,
            $targetImg = $(_tFunc.target).closest('div.g-column').find('img.js-change_cart_img');
          $(_tFunc.target)
            .find('li a')
            .on('click', function (e) {
              return e.preventDefault(), $targetImg.attr('src', $(this).attr('href')), !1;
            });
        },
      },
      height100: {
        target: null,
        init: function () {
          var $t = this.target,
            $rePositionTarget = $t.find('div.site_frame'),
            headerFloatFlag = $('#a-header').data('float');
          if (($rePositionTarget.css('top', 'auto'), $t.css('paddingTop', 0), 'sp' === _bd.fn.nowDevice())) return !1;

          var _positions,
            _winHeight = _bd.window._height,
            _haveClass = $t.attr('class');
          _.each(_haveClass.split(' '), function (cls) {
            if (cls.match('-catch') && !cls.match('-sp-catch')) {
              var noCatch = cls.replace('-catch-', ''),
                _leftTop = noCatch.split('_');
              _positions = _leftTop[1];
            }
          });
          var _rePositionTargetHeight = $rePositionTarget.outerHeight(!0),
            _positionNumber = 0;
          'top' === _positions
            ? (_positionNumber = 0)
            : 'center' === _positions
              ? ((_positionNumber = (_winHeight - _rePositionTargetHeight) / 2),
                headerFloatFlag && (_positionNumber -= $('#a-header').outerHeight(!0) / 2))
              : 'bottom' === _positions && (_positionNumber = _winHeight - _rePositionTargetHeight),
            $rePositionTarget.css({
              top: _positionNumber,
            });
        },
        resize: function () {
          this.init();
        },
      },
      height100Sp: {
        target: null,
        init: function () {
          var $t = this.target,
            $rePositionTarget = $t.find('div.site_frame'),
            headerFloatFlag = $('#a-header').data('float');
          if ($t.hasClass('-height100') && 'sp' !== _bd.fn.nowDevice()) return !1;

          if (($rePositionTarget.css('top', 'auto'), 'sp' !== _bd.fn.nowDevice())) return !1;

          var _winHeight = _bd.window._height;
          headerFloatFlag && $t.css('paddingTop', $('#a-header').outerHeight(!0));
          var _positions,
            _haveClass = $t.attr('class');
          _.each(_haveClass.split(' '), function (cls) {
            if (cls.match('-sp-catch')) {
              var noCatch = cls.replace('-sp-catch-', ''),
                _leftTop = noCatch.split('_');
              _positions = _leftTop[1];
            }
          });
          var _rePositionTargetHeight = $rePositionTarget.outerHeight(!0);
          headerFloatFlag && (_rePositionTargetHeight -= $('#a-header').outerHeight(!0));
          var _positionNumber = 0;
          'top' === _positions
            ? (_positionNumber = 0)
            : 'center' === _positions
              ? ((_positionNumber = (_winHeight - _rePositionTargetHeight) / 2),
                headerFloatFlag && (_positionNumber -= $('#a-header').outerHeight(!0) / 2))
              : 'bottom' === _positions && (_positionNumber = _winHeight - _rePositionTargetHeight),
            $rePositionTarget.css({
              top: _positionNumber,
            });
        },
        resize: function () {
          this.init();
        },
      },
      blockAnimation: {
        target: null,
        status: {
          animationTarget: [],
          windowEndPoint: 0,
        },
        init: function () {
          var $animationBlocks,
            _tFunc = this,
            _status = _tFunc.status,
            $initBlockAnimation = $('.init-block_animation'),
            $initSpBlockAnimation = $('.init-sp-block_animation');
          if (
            ((_status.animationTarget = []),
              'sp' !== _bd.fn.nowDevice()
                ? (($animationBlocks = $initBlockAnimation),
                  $initBlockAnimation.each(function () {
                    _status.animationTarget.push($(this));
                  }),
                  $initBlockAnimation.removeClass('init-block_animation'))
                : (($animationBlocks = $initSpBlockAnimation),
                  $initSpBlockAnimation.each(function () {
                    _status.animationTarget.push($(this));
                  }),
                  $initSpBlockAnimation.removeClass('init-sp-block_animation')),
              _bd.fn.isAnimationOff())
          )
            return !1;

          var _addClass = 'sp' === _bd.fn.nowDevice() ? 'init-sp-block_animation' : 'init-block_animation';
          _.each(_status.animationTarget, function ($t) {
            $t.find('.column').addClass(_addClass);
          }),
            _tFunc.eachAnimation($animationBlocks);
        },
        eachAnimation: function ($animationBlocks) {
          var _tFunc = this;
          _tFunc.animationMain($animationBlocks),
            $(window).off('scroll.scrollBlockAnimation'),
            $(window).on('scroll.scrollBlockAnimation', function () {
              _tFunc.animationMain($animationBlocks);
            });
        },
        animationMain: function ($animationBlocks) {
          var _tFunc = this;
          $animationBlocks.each(function () {
            var $_t = $(this),
              _nowDevice = _bd.fn.nowDevice(),
              _animationType = 'sp' === _nowDevice ? $_t.data('sp-animated') : $_t.data('animated');
            if (!_animationType || 'true' === $_t.data('block-animation-flag')) return !1;

            var _scrollTop = $(window).scrollTop();
            _tFunc.status.windowEndPoint = _scrollTop + _bd.window._height;
            var _targetTop = $_t.offset().top;
            if (
              _tFunc.status.windowEndPoint - _bd.window._height / 4 > _targetTop ||
              Math.abs(_bd.window._height - $('body').get(0).scrollHeight) < 300 ||
              (_tFunc.status.windowEndPoint === $('body').get(0).scrollHeight && _targetTop > _scrollTop + 300)
            ) {
              var $gNavi = $('#js-globalNavigation'),
                gNaviSlideType = $gNavi.data('slide-type'),
                hideNavi = 'pc' !== _nowDevice && ('rightSlide' === gNaviSlideType || 'leftSlide' === gNaviSlideType);
              $_t.data('block-animation-flag', !0),
                $_t.find('.column').each(function (i) {
                  var $__t = $(this);
                  setTimeout(function () {
                    hideNavi && $gNavi.hide(), $__t.addClass('-' + _animationType);
                  }, 100 * i),
                    $__t.on('webkitAnimationEnd AnimationEnd', function () {
                      hideNavi && $gNavi.show(), _tFunc.animationEndActions($__t, _animationType);
                    });
                });
            }
          });
        },
        animationEndActions: function ($target, animationType) {
          'sp' !== _bd.fn.nowDevice() ? $target.removeClass('init-block_animation') : $target.removeClass('init-sp-block_animation');
        },
        resize: function () {
          var _tFunc = this,
            _status = _tFunc.status,
            $initBlockAnimation = $('.init-block_animation'),
            $initSpBlockAnimation = $('.init-sp-block_animation');
          $initBlockAnimation.removeClass('init-block_animation'),
            $initSpBlockAnimation.removeClass('init-sp-block_animation'),
            'sp' !== _bd.fn.nowDevice()
              ? _.each(_status.animationTarget, function (dom) {
                $(dom).addClass('init-block_animation');
              })
              : _.each(_status.animationTarget, function (dom) {
                $(dom).addClass('init-sp-block_animation');
              }),
            this.init();
        },
      },
      blockSticky: {
        target: null,
        status: {
          beforeScrollTop: 0,
        },
        init: function () {
          var _tFunc = this,
            $t = $(_tFunc.target);

          return _bd.fn.isEditBlock() || 'sp' === _bd.fn.nowDevice()
            ? ($('.-js-block_sticky').each(function () {
              var $t = $(this);
              $t.removeClass('-js-block_sticky').css({
                width: 'auto',
                top: 'auto',
              }),
                _tFunc.removeFakebox($t.attr('id'));
            }),
              !1)
            : ($t.removeClass('-js-block_sticky'),
              (_tFunc.status.stickyTop = $t.offset().top),
              _tFunc.stickyMainLogic($t),
              void $(window).on('scroll.scrollBlockSticky', function () {
                _tFunc.stickyMainLogic($t);
              }));
        },
        stickyMainLogic: function ($t) {
          var _tFunc = this,
            _scrollTop = $(window).scrollTop();
          if (_scrollTop > _tFunc.status.beforeScrollTop) {
            if (_scrollTop > _tFunc.status.stickyTop) {
              if ((_tFunc.stickyModeTransition($t, 0), _tFunc.fakeBoxExistFlag($t.attr('id')))) return !1;

              $t.after(_tFunc.createFakebox($t.attr('id')));
            }
          } else if (_scrollTop < _tFunc.status.stickyTop) _tFunc.removeSticky($t.attr('id'));
          else {
            if ((_tFunc.stickyModeTransition($t, 0), _tFunc.fakeBoxExistFlag($t.attr('id')))) return !1;

            $t.after(_tFunc.createFakebox($t.attr('id')));
          }

          _tFunc.status.beforeScrollTop = _scrollTop;
        },
        stickyModeTransition: function ($sticky, _top) {
          var _stikyWidth = ($sticky.offset().left, $sticky.outerWidth(!0));
          $sticky.addClass('-js-block_sticky').css({
            width: _stikyWidth,
            top: _top,
          });
        },
        fakeBoxExistFlag: function (_id) {
          var _flag = !1;

          return $('#fakebox-' + _id).length > 0 && (_flag = !0), _flag;
        },
        createFakebox: function (_id) {
          var $t = $('#' + _id),
            $fakebox = $('<div />').attr('id', 'fakebox-' + _id);

          return (
            $fakebox.css({
              width: $t.outerWidth(!0),
              height: $t.outerHeight(!0),
            }),
            $fakebox
          );
        },
        removeSticky: function (_id) {
          var $t = $('#' + _id);
          $t.removeClass('-js-block_sticky').css({
            left: 'auto',
            width: 'auto',
          }),
            this.removeFakebox($t.attr('id'));
        },
        removeFakebox: function (_id) {
          var $fakebox = $('#fakebox-' + _id);
          $fakebox.remove();
        },
        getTopPositionInt: function ($id) {
          return parseInt($id.css('top').replace('px', ''), 10);
        },
        resize: function () {
          var _tFunc = this;
          $(window).off('scroll.scrollBlockSticky'), _tFunc.removeSticky($(_tFunc.target).attr('id')), _tFunc.init();
        },
      },
      sideFixedColumn: {
        target: null,
        init: function () {
          if ('sp' === _bd.fn.nowDevice()) {
            var $sideA = $('#a-side-a'),
              $sideB = $('#a-side-b');
            $sideA.width('auto'), $sideB.width('auto');
          } else {
            var _tFunc = this,
              $t = $(_tFunc.target),
              _layoutType = $t.data('layout-type'),
              $sideA = $('#a-side-a'),
              $sideB = $('#a-side-b'),
              _sideASize = ($('#a-main'), $sideA.data('fixed-size')),
              _sideBSize = $sideB.data('fixed-size');
            'sidefixed' === _layoutType &&
              (_sideASize && 'null' !== _sideASize && $sideA.width(_sideASize), _sideBSize && 'null' !== _sideBSize && $sideB.width(_sideBSize));
          }
        },
        resize: function () {
          this.init();
        },
      },
      viewPcSite: {
        target: null,
        status: !1,
        init: function () {
          var _tFunc = this;
          if ('sp' === _bd.fn.nowDevice() || _tFunc.status) {
            (_tFunc.status = !0), $('.c-device_outer').remove();
            var _locationHost = location.hostname,
              _hostName = _locationHost.replace('www.', ''),
              $deviceOuter = $('<div />').addClass('c-device_outer'),
              $deviceChangeButton = $('<button />').addClass('c-device_changer'),
              _buttonPosition = $('body').data('view-pc-position');
            'pc' === _tFunc.getHostItem(_hostName)
              ? (_tFunc.setViewportPcSize(),
                $deviceChangeButton.html('スマホサイトを表示する'),
                $deviceChangeButton.on('click', function (e) {
                  e.preventDefault(), _tFunc.removeHostItem(_hostName), _tFunc.setViewportSpSize();
                }))
              : ($deviceChangeButton.html('PCサイトを表示する'),
                $deviceChangeButton.on('click', function (e) {
                  e.preventDefault(), _tFunc.setHostItem(_hostName), _tFunc.setViewportPcSize();
                })),
              $deviceOuter.append($deviceChangeButton),
              'top' === _buttonPosition ? $('body').prepend($deviceOuter) : $('body').append($deviceOuter);
          }
        },
        setViewportPcSize: function () {
          var $viewport = $('#a-viewport');
          $viewport.attr('content', 'width=980, initial-scale=1');
        },
        setViewportSpSize: function () {
          var $viewport = $('#a-viewport');
          $viewport.attr('content', 'width=device-width, initial-scale=1');
        },
        setHostItem: function (hostName) {
          localStorage.setItem(hostName + '_compulsoryPc', 'pc');
        },
        removeHostItem: function (hostName) {
          localStorage.setItem(hostName + '_compulsoryPc', 'sp');
        },
        getHostItem: function (hostName) {
          return localStorage.getItem(hostName + '_compulsoryPc');
        },
        resize: function () {
          this.init();
        },
      },
      followBlocks: {
        target: null,
        init: function () {
          if (_bd.fn.isEditBlock()) return !1;

          var _tFunc = this,
            $t = $(_tFunc.target),
            _followId = 'sp' !== _bd.fn.nowDevice() ? $t.data('follow-blocks') : $t.data('sp-follow-blocks'),
            $followTarget = $('#' + _followId);
          if (!_followId)
            return $t.data('follow-blocks') && _tFunc.resets($followTarget), $t.data('sp-follow-blocks') && _tFunc.resets($followTarget), !1;

          $t.closest(_bd.def.allFoundationIds);
          _tFunc.resets($t);
          var _targetTop = $followTarget.position().top,
            _negativeMargins = _targetTop;
          $followTarget.addClass('-follow-target'),
            $t.addClass('-follow-blocks').css({
              top: _negativeMargins,
              height: $followTarget.outerHeight(!0),
              width: $followTarget.outerWidth(!0),
            }),
            setTimeout(function () {
              _bd.syncLoad || _tFunc.loadTimer();
            }, 1);
        },
        loadTimer: function () {
          function _loadTimerFunc() {
            return setTimeout(function () {
              _bd.syncLoad ? (_tFunc.init(), clearTimeout(_loadTimer)) : _loadTimerFunc();
            }, 100);
          }
          var _tFunc = this,
            _loadTimer = _loadTimerFunc();
        },
        resets: function ($t) {
          $t.removeClass('-follow-blocks').css({
            top: 'auto',
            height: 'auto',
            width: 'auto',
          });
        },
        resize: function () {
          this.init();
        },
      },
      blogNewIcon: {
        target: null,
        init: function () {
          var _tFunc = this,
            $t = $(_tFunc.target),
            _data = new Date($t.data('expired')),
            _today = new Date();
          _today > _data && $t.addClass('js-hide');
        },
        resize: function () {
          return !1;
        },
      },
      pageAnimation: {
        target: null,
        init: function () {
          var $page = $('.bg-window'),
            $gNavi = $('#js-globalNavigation');
          if (_bd.fn.isAnimationOff()) return void $page.removeClass('animsition');

          var pgAnim = $(document.body).data('page-animation');
          if ('none' != pgAnim) {
            var gNaviSlideType = $gNavi.data('slide-type'),
              hideNavi = 'pc' !== _bd.fn.nowDevice() && ('rightSlide' === gNaviSlideType || 'leftSlide' === gNaviSlideType),
              inClass = '',
              outClass = '';
            'fade' == pgAnim
              ? ((inClass = 'fade-in'), (outClass = 'fade-out'))
              : 'fade-down' == pgAnim
                ? ((inClass = 'fade-in-down-sm'), (outClass = 'fade-out-down-sm'))
                : 'fade-up' == pgAnim
                  ? ((inClass = 'fade-in-up-sm'), (outClass = 'fade-out-up-sm'))
                  : 'fade-left' == pgAnim
                    ? ((inClass = 'fade-in-left-sm'), (outClass = 'fade-out-left-sm'))
                    : 'fade-right' == pgAnim
                      ? ((inClass = 'fade-in-right-sm'), (outClass = 'fade-out-right-sm'))
                      : 'zoom' == pgAnim
                        ? ((inClass = 'zoom-in'), (outClass = 'zoom-out'))
                        : 'flip-x' == pgAnim
                          ? ((inClass = 'flip-in-x-fr'), (outClass = 'flip-out-x-fr'))
                          : 'flip-y' == pgAnim
                            ? ((inClass = 'flip-in-y-fr'), (outClass = 'flip-out-y-fr'))
                            : 'rotate' == pgAnim && ((inClass = 'rotate-in-sm'), (outClass = 'rotate-out-sm')),
              $page
                .animsition({
                  inClass: inClass,
                  outClass: outClass,
                  linkElement: 'a[href]:not(.js-link_scroller,.js-zoomImage,.js-popup,[target=_blank],#js-sp-menu_closer)',
                })
                .on('animsition.inStart animsition.outStart', function () {
                  hideNavi && $gNavi.hide();
                })
                .on('animsition.inEnd', function () {
                  hideNavi && $gNavi.show();
                });
          }
        },
      },
    });
})(_bind),
  (function (_bd) {
    _bd.bridge = {
      onInit: function () {
        var editorFlg = location.search.indexOf('bindapp=1') > -1;
        editorFlg && _bd.loader.loadJS(parent.ctxpath + '/template/sitetemplate/' + parent.responsiveModuleVer + '/_editor/scripts/blockEditor.js');
        var layoutFlg = location.search.indexOf('bindapp=2') > -1;
        if (layoutFlg) {
          var scripts = [
            parent.ctxpath + '/template/sitetemplate/' + parent.responsiveModuleVer + '/_editor/lib/lib.js',
            parent.ctxpath + '/template/sitetemplate/' + parent.responsiveModuleVer + '/_editor/scripts/layoutEditor.js',
          ],
            len = scripts.length,
            i = 0;
          !(function appendScript() {
            var script = document.createElement('script');
            (script.src = scripts[i]), document.body.appendChild(script), ++i < len && (script.onload = appendScript);
          })(),
            _bd.loader.loadCSS(parent.ctxpath + '/template/sitetemplate/' + parent.responsiveModuleVer + '/_editor/styles/bind.css'),
            _bd.loader.loadCSS(parent.ctxpath + '_modules/css/blockEditor.css');
        }
      },
    };
  })(_bind),
  (function (_bd) {
    var scriptSrc = document.getElementById('script-js').src,
      param = {};
    _.each(scriptSrc.replace(/^.*\?(.*)$/g, '$1').split(','), function (aValue) {
      var value = aValue.split('='),
        key = value[0];
      value[1];
      'l' == key && (param.level = val),
        's' == key && (param.textsize = val),
        't' == key && (param.theme = val),
        'f' == key && (param.font = val),
        'fs' == key && (param.fontsize = val),
        'rs' == key && (param.rs = val),
        'wf' == key && (param.wf = val),
        'c' == key && (param.cornerskin = val);
    });
    var plugins = scriptSrc.split('/scripts/bind.js')[0].split('/');
    (plugins = _.initial(plugins)),
      (plugins = plugins.join('/') + '/_plugins/'),
      (_bd.def = {
        appParam: param,
        urlModule: scriptSrc.split('scripts/bind.js')[0],
        urlPlugins: plugins,
        urlSync: '//sync5-res.digitalstage.jp/',
        urlCart: '//shops-api2.weblife.me/',
        urlCartOem: '//shops-api.blks.jp/',
        allFoundationIds: '#a-header,#a-ghost_header,#a-billboard,#a-site_contents,#a-main,#a-side-a,#a-side-b,#a-footer',
        spSize: 640,
        resizeTime: 100,
        responsive: !0,
      });
  })(_bind),
  (function (_bd) {
    var ua = navigator.userAgent.toLowerCase(),
      win = ua.indexOf('windows') > -1 || ua.indexOf('win32') > -1,
      opr = ua.indexOf('opera') > -1;
    _bd.device = {
      spSize: 641,
      tabletSize: 768,
      ua: ua,
      win: win,
      win7: !!(win && ua.indexOf('nt 6.1') > -1),
      vista: !!(win && ua.indexOf('nt 6.0') > -1),
      xp: !(!win || !(ua.indexOf('nt 5.1') > -1 || ua.indexOf('windows xp') > 0)),
      mac: ua.indexOf('macintosh') > -1 || ua.indexOf('mac_power') > -1,
      ie: (ua.indexOf('msie') > -1 || ua.indexOf('trident') > -1) && !opr,
      edge: ua.indexOf('applewebkit') >= 0 && ua.indexOf('edge') == -1,
      ffx: ua.indexOf('firefox') > 0,
      chr: ua.indexOf('chrome/') > 0,
      ie110: ua.indexOf('trident/7') > 0 && !opr,
      ie100: ua.indexOf('msie 10') > 0 && !opr,
      ie90: ua.indexOf('msie 9') > 0 && !opr,
      ie80: ua.indexOf('msie 8') > 0 && !opr,
      sf: ua.indexOf('safari') > 0,
      ipad: ua.indexOf('ipad') > 0 && ua.indexOf('safari') > 0,
      iphone: ua.indexOf('iphone') > 0 && ua.indexOf('safari') > 0,
      android: ua.indexOf('android') > 0,
      mobile:
        (ua.indexOf('windows') != -1 && ua.indexOf('phone') != -1) ||
        ua.indexOf('iphone') != -1 ||
        ua.indexOf('ipod') != -1 ||
        (ua.indexOf('android') != -1 && ua.indexOf('mobile') != -1) ||
        (ua.indexOf('firefox') != -1 && ua.indexOf('mobile') != -1) ||
        ua.indexOf('blackberry') != -1,
    };
  })(_bind);
var _dress = {};
!(function (_drs) {
  (_drs.selected = {
    init: function (_t, doms) {
      var _self = this;

      return _self.highlighter(_t), _self.domPreview(_t, doms);
    },
    highlighter: function (_t) {
      var range = document.createRange();
      range.selectNodeContents(_t[0]);
      var selection = window.getSelection();
      selection.removeAllRanges(), selection.addRange(range);
    },
    domPreview: function ($t, _doms) {
      var _self = this,
        _selectDoms = '',
        extrablockClassese = _doms.extrablocks.split(','),
        outersClassese = _doms.outers.split(','),
        partsClassese = _doms.parts.split(','),
        componentClassese = _doms.component.split(','),
        tagsTablePartsClassese = _doms.tagstableparts.split(','),
        listClassese = _doms.tagslist.split(','),
        tagsAnchorClassese = _doms.taganchor.split(',');

      return (
        (_selectDoms += _self.domSearcher($t, extrablockClassese) ? _self.domSearcher($t, extrablockClassese) + ' ' : ''),
        (_selectDoms += _self.domSearcher($t, outersClassese) ? _self.domSearcher($t, outersClassese) + ' ' : ''),
        (_selectDoms += _self.domSearcher($t, partsClassese) ? _self.domSearcher($t, partsClassese) + ' ' : ''),
        (_selectDoms += _self.domSearcher($t, componentClassese) ? _self.domSearcher($t, componentClassese) + ' ' : ''),
        (_selectDoms += _self.domSearcher($t, tagsTablePartsClassese) ? _self.domSearcher($t, tagsTablePartsClassese) + ' ' : ''),
        (_selectDoms += _self.domSearcher($t, listClassese) ? _self.domSearcher($t, listClassese) + ' ' : ''),
        (_selectDoms += _self.domSearcher($t, tagsAnchorClassese) ? _self.domSearcher($t, tagsAnchorClassese) + ' ' : '')
      );
    },
    domSearcher: function ($t, val, flags) {
      var _return;

      return (
        flags
          ? ($t.closest(val).length > 0 || $t.hasClass(val.replace('.', ''))) && (_return = val)
          : _.each(val, function (_val) {
            ($t.closest(_val).length > 0 || $t.hasClass(_val.replace('.', ''))) && (_return = _val);
          }),
        _return
      );
    },
  }),
    $(window).on('load', function () {
      _bind.status.dressPreview &&
        window.addEventListener(
          'message',
          function (event) {
            var _protocolUrl = location.protocol,
              _hostUrl = location.host;

            return (
              event.origin === _protocolUrl + '//' + _hostUrl &&
              void ('removed' === event.data
                ? $('*').off('click.selected')
                : ($('*').off('click.selected'),
                  $('*').on('click.selected', function (e) {
                    e.preventDefault(), e.stopPropagation(), event.source.postMessage(_drs.selected.init($(this), event.data), event.origin);
                  })))
            );
          },
          !1
        );
    });
})(_dress),
  (function (_bd) {
    (_bd.loader = {
      onInit: function () {
        function checkElement(aEl) {
          _.each(aEl.childNodes, function (aChild) {
            _bd.fn.isEmpty(aChild) ||
              ('DIV' == aChild.nodeName && (_bd.fn.isEmpty(aChild.getAttribute('data-bk-id')) || (checkPlugins(aChild), baseBiND(aChild))),
                aChild.hasChildNodes() && checkElement(aChild));
          });
        }

        function baseBiND(aEl) {
          var $aEl = $(aEl),
            _dataFloat = $aEl.data('float-contents');
          if (
            (_dataFloat
              ? _init.loadManager(aEl, 'floatContents', !0)
              : $aEl.hasClass('b-tab')
                ? _init.loadManager(aEl, 'tab')
                : $aEl.hasClass('b-accordion') && _init.loadManager(aEl, 'accordion'),
              $aEl.hasClass('-js-bindZoom') && _init.loadManager(aEl, 'photoAlbum'),
              ($aEl.hasClass('init-block_animation') || $aEl.hasClass('init-sp-block_animation')) &&
              (loadFlag.blockAnimation || (_init.loadManager(null, 'blockAnimation'), (loadFlag.blockAnimation = !0))),
              $aEl.data('sticky') && !_bd.fn.isEditBlock())
          ) {
            if (loadFlag.sticky) return;

            _init.loadManager(aEl, 'blockSticky'), (loadFlag.sticky = !0);
          }

          $aEl.data('follow-blocks') && !_bd.fn.isEditBlock()
            ? _init.loadManager(aEl, 'followBlocks')
            : $aEl.data('sp-follow-blocks') && !_bd.fn.isEditBlock() && _init.loadManager(aEl, 'followBlocks'),
            $aEl.hasClass('b-album') && _init.loadManager(aEl, 'heightColumnController'),
            $aEl.find('SPAN,A,DIV,AREA,IFRAME').each(function () {
              var _t = this,
                className = _t.className;
              className.indexOf('js-link_scroller') != -1 && _init.loadManager(_t, 'smoothScroll'),
                className.indexOf('c-photo_mouseover') != -1 && _init.loadManager(_t, 'photoComment'),
                className.indexOf('js-tracking') != -1 && _init.loadManager(_t, 'tracking'),
                className.indexOf('js-sc-form') != -1 && _init.loadManager(_t, 'smoothContact'),
                $(_t).data('expired') && _init.loadManager(_t, 'blogNewIcon');
            });
        }

        function checkPlugins(aEl) {
          $(aEl)
            .find('SPAN,A,DIV,AREA,IMG')
            .each(function () {
              var _t = this,
                className = _t.className;
              className.indexOf('js-slide') != -1
                ? loadShift(_t, aEl)
                : className.indexOf('js-sync') != -1
                  ? loadSync(_t)
                  : className.indexOf('js-motion') != -1
                    ? loadMenu(_t)
                    : className.indexOf('bind_cart') != -1
                      ? loadCart(_t)
                      : className.indexOf('js-other') != -1
                        ? loadOther(_t)
                        : $(_t).data('clickable-map')
                          ? loadClickableMap(_t)
                          : _t.getAttribute('data-plugin') && loadThirdPlugin(_t);
            });
        }

        function createLoadDataParam() {
          return {
            kind: '',
            name: '',
            target: [],
          };
        }

        function setLoadDataParam(aUrl, aKind, aName, aTarget) {
          var param = null;
          _bd.fn.isEmpty(aTarget)
            ? (loadData[aUrl] = {})
            : (_bd.fn.isEmpty(loadData[aUrl])
              ? ((param = createLoadDataParam()), (param.kind = aKind), (param.name = aName))
              : (param = loadData[aUrl]),
              param.target && (param.target[param.target.length] = aTarget)),
            (loadData[aUrl] = param);
        }

        function loadShift(aEl, parentNode) {
          var args = aEl.className.split(' '),
            keys = args[1].split('s-slide-')[1].split('_'),
            name = keys[0],
            mode = keys[1],
            target = {
              el: aEl,
              mode: mode,
              slide: {
                width: parseInt(aEl.getAttribute('data-width'), 10),
                height: parseInt(aEl.getAttribute('data-height'), 10),
                imagewidth: parseInt(aEl.getAttribute('data-imagewidth'), 10),
                imageheight: parseInt(aEl.getAttribute('data-imageheight'), 10),
              },
              interval: parseInt(aEl.getAttribute('data-interval') || 6500),
              duration: parseInt(aEl.getAttribute('data-duration') || 2e3),
              autost: args.indexOf('s-slide-auto') != -1,
              loop: args.indexOf('s-slide-loop') != -1,
              size_limit: args.indexOf('s-slide-size_limit') != -1,
              pcHide: $(parentNode).hasClass('is-pc-hide'),
              spHide: $(parentNode).hasClass('is-sp-hide'),
            },
            js = _bd.def.urlPlugins + 'slide/' + name + '/engine.js';
          setLoadDataParam(js, 'slide', name, target);
          var css = _bd.def.urlPlugins + 'slide/' + name + '/style.css';
          setLoadDataParam(css);
        }

        function loadSync(aEl) {
          var swf = null;
          (_bd.syncLoad = !1),
            'SPAN' != aEl.tagName ||
            syncFlag.blog ||
            ((swf = aEl.className.indexOf('-blog -main01') > -1 || aEl.className.indexOf('-blog -side01') > -1), (syncFlag.blog = !0)),
            swf && !syncFlag.other && (setLoadDataParam(_bd.def.urlSync + '_modules/js/swfaddress.js', 'sync', null, null), (syncFlag.other = !0)),
            syncFlag.preset ||
            (setLoadDataParam(_bd.def.urlSync + '_modules/css/sync-loader.css', 'sync', null, null),
              setLoadDataParam(_bd.def.urlSync + '_modules/js/sync-loader.js', 'sync', null, null),
              (syncFlag.preset = !0));
        }

        function loadCart(aEl) {
          document.getElementsByTagName('body')[0].getAttribute('data-oem')
            ? (setLoadDataParam(_bd.def.urlCartOem + 'js/serialize.js', 'sync', null, null),
              setLoadDataParam(_bd.def.urlCartOem + 'fx.js', 'sync', null, null))
            : (setLoadDataParam(_bd.def.urlCart + 'js/serialize.js', 'sync', null, null),
              setLoadDataParam(_bd.def.urlCart + 'fx.js', 'sync', null, null));
        }

        function loadMenu(aEl) {
          var ul = jQuery(aEl).children('ul');
          if (!_bd.fn.isEmpty(ul) && ul.hasClass('m-motion')) {
            var keys = ul.get(0).className.split(' '),
              name = keys.length > 1 ? keys[1].substr(1) : '',
              mode = keys.length > 2 ? keys[2] : '';
            _bd.fn.isEmpty(mode) || (mode = mode.substr(1));
            var target = {
              el: ul.get(0),
              mode: mode,
            },
              url = _bd.def.urlPlugins + 'menu/' + name + '/engine.js';
            setLoadDataParam(url, 'menu', name, target);
            var css = _bd.def.urlPlugins + 'menu/' + name + '/style.css';
            setLoadDataParam(css);
          }
        }

        function loadClickableMap(aEl) {
          $(aEl).rwdImageMaps();
        }

        function loadOther(aEl) {
          var args = aEl.className.split(' '),
            params = args[1],
            keys = params.split('_'),
            name = keys[0],
            mode = keys[1],
            target = {
              el: aEl,
              mode: mode,
            },
            url = _bd.def.urlPlugins + 'other/' + name + '/engine.js';
          setLoadDataParam(url, 'other', name, target);
          var css = _bd.def.urlPlugins + 'other/' + name + '/style.css';
          setLoadDataParam(css);
        }

        function loadThirdPlugin(aEl) {
          var libs = aEl.getAttribute('data-libs');
          if (!_bd.fn.isEmpty(libs)) {
            var name = aEl.getAttribute('data-plugin');
            (libs = decodeURIComponent(libs)),
              (libs = libs.split(',')),
              (libs = _.groupBy(libs, function (aUrl) {
                var ret = 'other';

                return aUrl.indexOf('.js') != -1 && (ret = 'js'), ret;
              })),
              _bd.fn.isEmpty(libs.js) ||
              (_bd.fn.isEmpty(loadThird[name]) && (loadThird[name] = []), (loadThird[name] = loadThird[name].concat(libs.js))),
              _bd.fn.isEmpty(libs.other) ||
              _.each(libs.other, function (aUrl) {
                setLoadDataParam(aUrl);
              }),
              aEl.removeAttribute('data-libs');
          }
        }
        var loadData = {},
          loadThird = {},
          syncFlag = {
            blog: !1,
            other: !1,
            preset: !1,
          },
          loadFlag = {
            sticky: !1,
            blockAnimation: !1,
          },
          body = document.body,
          $body = $(body),
          _init = this,
          ua = window.navigator.userAgent;
        if (/Android/.test(ua) && /Linux/.test(ua) && !/Chrome/.test(ua)) {
          var $doms = $('<div />').attr('id', 'isAndroidBrowser').addClass('js-android_browser'),
            $h2 = $('<h2 />').html('ご注意ください。'),
            _urlLink =
              'intent://' +
              location.hostname +
              '#Intent;scheme=' +
              location.protocol +
              ';action=android.intent.action.VIEW;package=com.android.chrome;end',
            $link = $('<a />').attr('href', _urlLink).html('Google Chrome'),
            $p = $('<p />').html(
              '現在お使いのAndroid標準ブラウザは、Google社のサポートが終了しており脆弱性が存在するため、動作保証外です。<br>正しく表示されないページがあります。<br>'
            );
          $p.append($link), $p.append('でのご利用を推奨しております。'), $doms.append($h2), $doms.append($p), $('body').prepend($doms);
        }

        _bind.fn.isEmpty(window.css_list) ||
          _.each(window.css_list, function (aUrl) {
            _bd.loader.loadCSS(aUrl);
          }),
          setLoadDataParam(_bd.def.urlModule + 'bindicon/style.css'),
          _init.loadManager(null, 'spGlobalNavigation'),
          $body.data('view-pc-button') && _init.loadManager(null, 'viewPcSite'),
          checkElement(body);
        var $bgBox = $('div.bg-window,#a-header,#a-billboard,#a-site_contents,#a-main,#a-side-a,#a-side-b,#a-footer,#a-ghost_header');
        $bgBox.each(function () {
          var $this = $(this);
          $this.hasClass('-bg-video') && _init.loadManager($this, 'bgMovie');
        }),
          $body.data('fontplus') &&
          $.ajax({
            type: 'GET',
            url: 'https://module.bindsite.jp/type-fonts/allowed/domain.json',
            dataType: 'jsonp',
            jsonpCallback: 'callback',
          })
            .done(function (json) {
              var bindAry = json.domain,
                _protocol = window.location.hostname;
              if (bindAry.indexOf(_protocol) > -1) {
                var magic = ['fontplus', 'script', 'accessor', 'webfont-pub.weblife.me'].reverse().join('/'),
                  src = window.location.protocol + '//' + magic + '.js?gBaf4X~siMM%3D&aa=1';
                _init.loadJS(src, function () {
                  FONTPLUS.start();
                });
              }
            })
            .fail(function (data) { }),
          $body.find('div.-height100').length > 0 && _init.loadManager($body.find('div.-height100'), 'height100'),
          $body.find('div.-sp-height100').length > 0 && _init.loadManager($body.find('div.-sp-height100'), 'height100Sp'),
          $('[data-float]').length > 0 && _init.loadManager($('[data-float]'), 'fixedController'),
          $('#a-ghost_header').length > 0 && _init.loadManager($('#a-ghost_header'), 'ghostHeader'),
          $('[data-layout-type]').length > 0 && _init.loadManager($('[data-layout-type]'), 'sideFixedColumn'),
          _bd.fn.setFooter(!0),
          _bd.slide.fn.smoothScroll(),
          _.each(loadData, function (aDataParam, aUrl) {
            String.prototype.endsWith ||
              (String.prototype.endsWith = function (searchString, position) {
                var subjectString = this.toString();
                ('number' != typeof position || !isFinite(position) || Math.floor(position) !== position || position > subjectString.length) &&
                  (position = subjectString.length),
                  (position -= searchString.length);
                var lastIndex = subjectString.lastIndexOf(searchString, position);

                return lastIndex !== -1 && lastIndex === position;
              }),
              aUrl.endsWith('.js')
                ? _bd.loader.loadJS(aUrl, function () {
                  _bd.fn.isEmpty(aDataParam) ||
                    _.each(aDataParam.target, function (aParam) {
                      _bd[aDataParam.kind][aDataParam.name].render(aParam);
                    });
                })
                : aUrl.endsWith('.css') && _bd.loader.loadCSS(aUrl);
          }),
          _.each(loadThird, function (aItems, aName) {
            (aItems = _.uniq(aItems)),
              _bd.loader.asyncLoadJS(aItems, function () {
                _.delay(function () {
                  _bd.fn.heightRefresh(), _bd.fn.setFooter();
                }, 500);
              });
          });
      },
      loadManager: function (_target, _element, _flags) {
        var _base = _bd.base[_element];
        _base.target = _target;
        var _ext = $.extend(!0, {}, _base);
        if ((_ext.init(), void 0 !== _ext.resize || _flags)) {
          var eventMode = _bd.device.ipad || _bd.device.iphone ? 'orientationchange' : 'resize';
          $(window).on(eventMode, function () {
            _ext.resize(), _bd.fn.setFooter(!0);
          });
        }
      },
      loadJS: function (aUrl, aFunc) {
        var isReady = !1,
          script = document.createElement('script');
        (script.type = 'text/javascript'),
          (script.src = aUrl),
          (script.onload = script.onreadystatechange =
            function () {
              isReady || (this.readyState && 'loaded' != this.readyState && 'complete' != this.readyState) || ((isReady = !0), aFunc && aFunc());
            });
        var tag = document.getElementsByTagName('script')[0];
        tag.parentNode.insertBefore(script, tag);
      },
      loadCSS: function (aUrl) {
        var tag = document.getElementsByTagName('link')[0],
          link = document.createElement('link');
        (link.rel = 'stylesheet'), (link.type = 'text/css'), (link.href = aUrl), tag.parentNode.appendChild(link);
      },
      asyncLoadJS: function (aItems, aFunc) {
        var load = function (aUrl) {
          return function () {
            var ret = new $.Deferred();
            ret.promise();
            var isReady = !1,
              script = document.createElement('script');
            (script.type = 'text/javascript'),
              (script.src = aUrl),
              (script.async = !0),
              (script.onload = script.onreadystatechange =
                function () {
                  isReady || (this.readyState && 'loaded' != this.readyState && 'complete' != this.readyState) || ((isReady = !0), ret.resolve());
                });
            var tag = document.getElementsByTagName('script')[0];

            return tag.parentNode.insertBefore(script, tag), ret;
          };
        },
          deferred = new $.Deferred().resolve();
        _.each(aItems, function (aItem) {
          deferred = deferred.then(load(aItem));
        }),
          deferred.then(aFunc);
      },
    }),
      (_bd.slide.fn = {
        getSize: function (aSlide, aEl) {
          var elWidth = aEl.width(),
            elHeight = aEl.height(),
            slideWidth = aSlide.width,
            slideHeight = aSlide.height,
            aspect = elHeight / elWidth;
          slideWidth && slideHeight && (aspect = slideHeight / slideWidth);
          var height = elWidth * aspect;

          return {
            width: elWidth,
            height: height,
            aspect: aspect,
          };
        },
        smoothScroll: function () {
          var scroll = function (e) {
            var el = $(this),
              _href = el.attr('href'),
              _fileFlag = !1,
              _hrefSplit = _href.split('/'),
              _cleanHref = _bind.base.smoothScroll._replaceDots(_hrefSplit).toString(),
              _pathName = document.location.pathname,
              _pathSplit = _pathName.split('/'),
              _cleanPath = _bind.base.smoothScroll._replaceDots(_pathSplit).toString(),
              _pathFirst = _cleanPath.substr(0, 1);
            if (
              ('/' === _pathFirst && (_cleanPath = _cleanPath.substr(1)),
                _cleanHref.toString() === _cleanPath.toString() && (_fileFlag = !0),
                ('#' === _href && el.hasClass('c-link_top')) ||
                ('' === _href && el.hasClass('c-link_top')) ||
                ('javascript:void(0);' === _href && el.hasClass('c-link_top')))
            )
              _bd.fn.scrollAnimation($('html,body'), 0);
            else if (_href.match('#') || (_href.match('#') && _fileFlag)) {
              var _hashName = _href.split('#').pop();
              try {
                $('#' + _hashName).length > 0 && _bd.fn.smoothScroll(e, _hashName);
              } catch (e) { }
            }
          };
          $('body').on('click', '.box_widthFull .js-link_scroller', scroll);
        },
      });
  })(_bind),
  (function (_bd) {
    $(window).on('resize load orientationchange', function () {
      (_bd.window._width = window.innerWidth),
        (_bd.window._height = window.innerHeight),
        'sp' == _bd.fn.nowDevice() || 'tablet' == _bd.fn.nowDevice()
          ? _bd.base.spGlobalNavigation.init()
          : ($('#spNavigationTrigger').addClass('js-hide'), $('#js-globalNavigation').attr('style', ''));
    });
  })(_bind),
  (function () {
    _bind.status.getStatus(), _bind.base.popupWindow.init(), _bind.loader.onInit();
    var pageAnim = $(document.body).data('page-animation');
    if ((pageAnim && 'none' != pageAnim && _bind.base.pageAnimation.init(), _bind.bridge.onInit(), _bind.fn.isEditBlock())) {
      var refnc = function () {
        void 0 === _bind.blockEdit ? setTimeout(refnc, 500) : window.dispatchEvent(new Event('resize'));
      };
      refnc();
    }
  })();
