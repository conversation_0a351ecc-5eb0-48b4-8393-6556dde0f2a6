import React, { useState } from 'react';
import { Collapse, Typography, Box } from '@mui/material';
import { ExpandLess, ExpandMore } from '@mui/icons-material';
import { SvgIconComponent } from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import IconButton from '@/components/common/SCIconButton';

interface SCCollapseCardProps {
  title: string;
  titleSize?: 'small' | 'medium' | 'large';
  icon?: SvgIconComponent;
  children: React.ReactNode;
  defaultOpen?: boolean; // new prop
}

const TITLE_DEFAULT_SIZE = 'medium';

const titleSizeMap = {
  small: 10,
  medium: 12,
  large: 14,
};

const SCCollapseCard: React.FC<SCCollapseCardProps> = ({ title, titleSize, icon: Icon, children, defaultOpen = true }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const theme = useTheme();
  const titleFontSize = titleSizeMap[titleSize] ?? titleSizeMap[TITLE_DEFAULT_SIZE];

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }} onClick={handleToggle}>
        {Icon && <Icon sx={{ mr: 1 }} color="primary" />}
        <Typography fontWeight="bold" fontSize={titleFontSize} m={0} color={theme.palette.secondary.main}>
          {title}
        </Typography>
        <IconButton onClick={handleToggle} size="small" disableRipple={true} disableFocusRipple={true}>
          {isOpen ? <ExpandLess /> : <ExpandMore />}
        </IconButton>
      </Box>
      <Collapse in={isOpen}>{children}</Collapse>
    </Box>
  );
};
export default SCCollapseCard;
