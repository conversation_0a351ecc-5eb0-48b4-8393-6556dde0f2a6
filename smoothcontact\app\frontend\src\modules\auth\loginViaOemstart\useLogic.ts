import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import useAxios from '@/hooks/useAxios';
import { LoginViaOemStartResponseDTO } from './dto/response.dto';
import { useLogout } from '@/hooks/useLogout';
import { useToast } from '@/provider/toastProvider';
import { BindUpSettingDto } from '@/types/dto/bindup.dto';

const useLogic = () => {
  const logout = useLogout();
  const navigate = useNavigate();
  const location = useLocation();

  const { toast } = useToast();
  const { apiCaller, loading } = useAxios<LoginViaOemStartResponseDTO>();

  const handleLoginViaOemStart = async () => {
    const queryParams = new URLSearchParams(location.search);

    const token = queryParams.get('token');
    const oemId = queryParams.get('oemId');
    const oemgo = queryParams.get('oemgo');
    const courseId = queryParams.get('courseId');
    const courseType = queryParams.get('courceType');

    const callbackUrl = queryParams.get('cb');
    const formSetting = queryParams.get('s');

    if (!token || !oemId || !oemgo) {
      console.error('Invalid query parameters');

      return;
    }

    try {
      const result = await apiCaller({
        method: 'POST',
        url: '/api/account/login-via-oemstart',
        data: { token, oemId, oemgo, courseId, courseType, callbackUrl, formSetting },
      });

      if (!result?.data || !result.success) {
        logout.handleLogout();
        toast({ isError: true, message: 'Login failed' });
        navigate('/login');

        return;
      }

      const settingParsed: BindUpSettingDto = JSON.parse(formSetting || '{}');
      // Fix ID-9024: Login from OEM must a session
      sessionStorage.setItem('accessToken', result?.data?.accessToken);
      sessionStorage.setItem('refreshToken', result?.data?.refreshToken);

      if (settingParsed?.formid) {
        navigate(`/form-builder/${settingParsed.formid}/edit-proxy?fromOem=true`);

        return;
      }

      navigate('/form-builder');
    } catch (error) {
      toast({ isError: true, message: 'Login via OEM Start failed' });
    }
  };

  useEffect(() => {
    handleLoginViaOemStart();
  }, []);

  return { loading };
};

export default useLogic;
