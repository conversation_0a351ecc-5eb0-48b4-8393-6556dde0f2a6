import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import useAxios from '@/hooks/useAxios';
import { useParams } from 'react-router-dom';
import useRecaptcha from '@/hooks/useRecaptcha';
import {
  FormColorSetting,
  FormElement,
  FormEmbedAppSetting,
  FormHistoryType,
  FormItemValue,
  FormItemValueDetail,
  FormMailSetting,
  FormScheduleSetting,
  FormStatus,
  FormSubmissionData,
  GeneralSetting,
  SubmissionFormValue,
  TemplateType,
} from '@/types/FormTemplateTypes';
import { FORM_COLOR_SETTING_INIT, FormControlNames, OptionAfterSubmitForm } from '@/utils/formBuilderUtils';
import { getGoogleTagIdFromSnippet } from '@/utils/helper';

const templateInitialValue: TemplateType = {
  formElements: [] as FormElement[],
  formGeneralSetting: {} as GeneralSetting,
  formColorSetting: FORM_COLOR_SETTING_INIT as FormColorSetting,
  formMailSetting: {} as FormMailSetting,
  formScheduleSetting: {} as FormScheduleSetting,
  formEmbedAppSetting: {} as FormEmbedAppSetting,
  publishHistory: [] as FormHistoryType[],
  status: FormStatus.DRAFT,
};

const FORM_DATA_NOT_ALLOW_CONTROL = [FormControlNames.COMMENT];

const handleFormAction = (action: string, data: Record<string, any>) => {
  if (window === window.parent) {
    switch (action) {
      case 'redirect':
        data.url && window.location.assign(data.url);
        break;
    }

    return;
  }

  window.parent.postMessage(JSON.stringify({ action, ...data }), '*');
};

export default function useLogic() {
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [template, setTemplate] = useState<TemplateType>(templateInitialValue);
  const [formData, setFormData] = useState<Record<string, FormItemValue>>(null);
  const [error, setError] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string>('');
  const [formMessage, setFormMessage] = useState<string>('');
  const { apiCaller } = useAxios<TemplateType>();
  const { id: extId } = useParams();
  const { token, refreshToken } = useRecaptcha({ enabled: template?.formGeneralSetting?.isSettingReCAPTCHA || false });

  const formElement = template?.formElements?.[0] || null;
  const formElementChildrenFiltered = formElement?.children.filter((child) => !FORM_DATA_NOT_ALLOW_CONTROL.includes(child.controlName)) || [];

  useEffect(() => {
    const link1 = document.createElement('link');
    link1.rel = 'stylesheet';
    link1.href = 'https://fonts.fontplus.dev/v1/css/jOW3USBm';
    document.head.appendChild(link1);

    const link2 = document.createElement('link');
    link2.rel = 'stylesheet';
    link2.href = 'https://fonts.fontplus.dev/v1/css/1YLRAG6A';
    document.head.appendChild(link2);

    const link3 = document.createElement('link');
    link3.rel = 'stylesheet';
    link3.href = 'https://fonts.fontplus.dev/v1/css/t8EwrwXo';
    document.head.appendChild(link3);

    // Cleanup: Remove the link tag when the component is unmounted
    return () => {
      document.head.removeChild(link1);
      document.head.removeChild(link2);
      document.head.removeChild(link3);
    };
  }, []);

  useEffect(() => {
    if (!extId) {
      return;
    }

    getForm(extId);
  }, [extId]);

  useEffect(() => {
    if (template?.formEmbedAppSetting?.isEnableGoogleAdsSetting) {
      const eventSnippet = template.formEmbedAppSetting?.eventSnippet || '';
      handleFormAction('google_ads', {
        id: getGoogleTagIdFromSnippet(eventSnippet),
        script: eventSnippet,
      });
    }

    if (template?.formEmbedAppSetting?.isLinkageYahoo) {
      handleFormAction('yahoo_ads', {
        script: template.formEmbedAppSetting?.conversionMeasurementTags || '',
      });
    }
  }, [template]);

  const getForm = async (extId: string) => {
    try {
      setFormMessage('');
      setLoading(true);
      const result = await apiCaller({
        url: `/api/embed/${extId}`,
        method: 'get',
      });

      if (result?.success) {
        const template = result?.data;

        setTemplate(template);
        const maximumNumberFormsReceived = template?.formScheduleSetting?.maximumNumberFormsReceived ?? 9999999999;
        const isFormClosed = dayjs().isAfter(template.releaseEndDate) || maximumNumberFormsReceived <= template?.submissionCount;

        if (dayjs().isBefore(template.releaseStartDate)) {
          setFormMessage(template.formScheduleSetting.displayTextBeforePublicForm);
        } else if (isFormClosed) {
          setFormMessage(template.formScheduleSetting.displayTextAfterPublicForm);
        }

        return;
      }

      setFormMessage(result?.message);
      setError(!result?.success);
    } catch (e) {
      setError(true);
    } finally {
      setLoading(false);
    }
  };

  const getValueDetailById = (id: string): FormItemValueDetail => {
    const elementChild = formElement.children.find((child) => child.id === id);
    switch (elementChild.controlName) {
      case FormControlNames.CHECKLIST:
        const checkboxValues = (formData?.[id] || []) as string[];

        return checkboxValues?.map?.((value) => {
          return {
            value,
            isOther: !elementChild.items.some((item) => item.value === value),
          };
        });

      case FormControlNames.RADIO:
        const radioValue = formData?.[id] || '';

        return {
          value: radioValue as string,
          isOther: !elementChild.items.some((item) => item.value === radioValue),
        };

      default:
        return null;
    }
  };

  const submitForm = async () => {
    const formValues: SubmissionFormValue[] = formElementChildrenFiltered?.map?.((child) => {
      return {
        id: child.id,
        controlName: child.controlName,
        labelName: child.labelName,
        value: formData?.[child.id] || '',
        valueDetail: getValueDetailById(child.id),
      };
    });
    const data: FormSubmissionData = {
      formValues,
    };
    if (token) {
      data.token = token;
    }

    try {
      setSubmitting(true);
      setSubmitError('');
      setFormMessage('');
      const result = await apiCaller({
        url: `/api/embed/${extId}/submit`,
        method: 'post',
        data,
      });

      if (!result.success) {
        setSubmitError(result.error);
        setSubmitting(false);

        return;
      }

      if (template?.formMailSetting?.screenAfterSendingType === OptionAfterSubmitForm.specifiedUrl && template?.formMailSetting?.specifiedUrl) {
        setSubmitting(true);
        handleFormAction('redirect', { url: template?.formMailSetting?.specifiedUrl });

        return;
      }

      setSubmitting(false);
      window.location.href = `/front/output/${template.extId}/thank-you`;
    } catch (e) {
      setSubmitError(e.message);
      setSubmitting(false);
    } finally {
      refreshToken();
    }
  };

  return {
    template,
    error,
    loading,
    formData,
    submitting,
    submitError,
    formMessage,
    setFormData,
    submitForm,
    handleFormAction,
  };
}
