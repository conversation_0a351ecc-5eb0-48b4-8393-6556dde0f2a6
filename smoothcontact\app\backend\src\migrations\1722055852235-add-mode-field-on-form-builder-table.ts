import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddModeFieldOnFormBuilderTable1722055852235 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}form_builder`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'mode',
        type: 'varchar',
        length: '6',
        isNullable: true,
        default: null,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'mode',
        type: 'varchar',
        length: '6',
        isNullable: true,
        default: null,
      }),
    ]);
  }
}
