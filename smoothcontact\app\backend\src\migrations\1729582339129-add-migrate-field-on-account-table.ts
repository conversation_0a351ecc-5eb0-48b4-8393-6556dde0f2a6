import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddMigrateFieldOnAccountTable1729582339129 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}account`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'sc_account_id',
        type: 'integer',
        isNullable: true,
      }),
      new TableColumn({
        name: 'username',
        type: 'varchar',
        length: '255',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'access_token',
        type: 'varchar',
        length: '255',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'login_type',
        type: 'varchar',
        length: '255',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'course',
        type: 'integer',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'user_key',
        type: 'text',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'expiration_date',
        type: 'datetime',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'mfa_common_key',
        type: 'varchar',
        length: '255',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'last_login_date',
        type: 'datetime',
        isNullable: true,
        default: null,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'sc_account_id',
        type: 'integer',
        isNullable: true,
      }),
      new TableColumn({
        name: 'username',
        type: 'varchar',
        length: '255',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'access_token',
        type: 'varchar',
        length: '255',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'login_type',
        type: 'varchar',
        length: '255',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'course',
        type: 'integer',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'user_key',
        type: 'text',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'expiration_date',
        type: 'datetime',
        isNullable: true,
        default: 'CURRENT_TIMESTAMP',
      }),
      new TableColumn({
        name: 'mfa_common_key',
        type: 'varchar',
        length: '255',
        isNullable: true,
        default: null,
      }),
      new TableColumn({
        name: 'last_login_date',
        type: 'datetime',
        isNullable: true,
        default: null,
      }),
    ]);
  }
}
