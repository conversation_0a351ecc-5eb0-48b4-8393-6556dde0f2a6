import { useAxiosInstance } from '@/provider/axiosProvider';
import { downloadFileFromStream } from '@/utils/helper';
import axios, { AxiosRequestConfig, CancelTokenSource } from 'axios';
import { useCallback, useState } from 'react';

export interface ApiResponse<T> {
  data: T | null;
  error: string | null;
  statusCode?: number;
  message?: string;
  messageErrors?: Partial<Record<keyof T, string>>;
  success?: boolean;
}

const useAxios = <T>() => {
  const axiosInstance = useAxiosInstance();
  const [loading, setLoading] = useState<boolean>(false);
  const [cancelTokenSource, setCancelTokenSource] = useState<CancelTokenSource | null>(null);

  const apiCaller = useCallback(
    async (config: AxiosRequestConfig) => {
      setLoading(true);
      const source = axios.CancelToken.source();
      setCancelTokenSource(source);

      try {
        const response: any = await axiosInstance({ ...config, cancelToken: source?.token });

        if (response?.headers?.['content-disposition'] && response?.data instanceof Blob) {
          downloadFileFromStream(response);

          return {
            success: true,
            data: null,
            error: '',
          } as ApiResponse<T>;
        }

        return { ...response?.data } as ApiResponse<T>;
      } catch (errorObj: any) {
        const response = errorObj?.response;

        return {
          ...response?.data,
          error: response?.data instanceof Blob ? 'Download Failed' : response?.data?.message,
        } as ApiResponse<T>;
      } finally {
        setLoading(false);
      }
    },
    [axiosInstance]
  );

  const cancelRequest = useCallback(() => {
    cancelTokenSource?.cancel('Request canceled by the user.');
  }, [cancelTokenSource]);

  return { apiCaller, loading, setLoading, cancelRequest };
};

export default useAxios;
