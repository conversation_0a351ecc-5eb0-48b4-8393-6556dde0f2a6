import { getRealStatus, openInNewTab } from '@/utils/helper';
import SCButton from '../common/SCButton';
import SCIconButton from '../common/SCIconButton';
import SCLoadingButton from '../common/SCLoadingButton';
import PublishFormButton from './common/PublishFormButton';
import PlayArrowOutlinedIcon from '@mui/icons-material/PlayArrowOutlined';
import SaveIcon from '@mui/icons-material/Save';
import ShareIcon from '@mui/icons-material/Share';
import { Box } from '@mui/material';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormStatus, FormType } from '@/types/FormTemplateTypes';

const FormActionButton: React.FC = () => {
  const { saveForm, selectedTemplate, error, isFormChanged, loading, setSharingModal, resetSelectedTemplate } = useFormBuilder();

  const currentStatus = getRealStatus(
    selectedTemplate?.status,
    selectedTemplate?.releaseStartDate,
    selectedTemplate?.releaseEndDate,
    Number(selectedTemplate?.submissionCount ?? 0) >= Number(selectedTemplate?.formScheduleSetting?.maximumNumberFormsReceived ?? **********)
  );

  const FormCurrentStatusLabel: { [key in FormStatus]: string } = {
    [FormStatus.DRAFT]: 'フォームを公開',
    [FormStatus.PUBLISHED]: 'フォームを公開',
    [FormStatus.PREPUBLISHED]: '公開予約済み',
    [FormStatus.CLOSED]: 'フォームを公開',
  };

  return (
    <Box display="flex" gap={1}>
      {selectedTemplate?.mode !== FormType.HTML && (
        <SCIconButton title="Preview" onClick={() => openInNewTab(`/form-builder/preview/${selectedTemplate?.extId}`)}>
          <PlayArrowOutlinedIcon />
        </SCIconButton>
      )}
      {selectedTemplate?.mode !== FormType.HTML && (
        <SCButton
          title="共有"
          disabled={error || selectedTemplate?.status === FormStatus.DRAFT}
          className="btn-grey"
          variant="outlined"
          color="secondary"
          onClick={() => setSharingModal(true)}
          startIcon={<ShareIcon fontSize="small" />}
        >
          共有
        </SCButton>
      )}
      <SCLoadingButton
        title="保存"
        disabled={error || !isFormChanged}
        onClick={() => saveForm(selectedTemplate)}
        className="btn-grey"
        variant="outlined"
        color="secondary"
        loading={loading ?? false}
        startIcon={<SaveIcon fontSize="small" />}
      >
        保存
      </SCLoadingButton>
      <SCButton
        title="編集をリセット"
        disabled={!isFormChanged}
        className="btn-grey"
        variant="outlined"
        color="secondary"
        onClick={() => resetSelectedTemplate()}
      >
        編集をリセット
      </SCButton>
      <PublishFormButton loading={loading ?? false} text={FormCurrentStatusLabel[currentStatus as FormStatus]} />
    </Box>
  );
};

export default FormActionButton;
