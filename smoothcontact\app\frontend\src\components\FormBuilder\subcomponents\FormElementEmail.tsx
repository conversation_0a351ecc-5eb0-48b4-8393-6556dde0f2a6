import { FormItemValue } from '@/types/FormTemplateTypes';
import Box from '@mui/material/Box';
import { useFormik } from 'formik';
import React, { FC, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import * as Yup from 'yup';
import InputFactory from './FormColorSetting/AnimationCustom/text/InputFactoryComponent';

interface FormMail {
  email: string;
  confirmEmail: string;
}

interface FormElementEmailProps {
  name?: string;
  label?: string;
  value: FormItemValue<string>;
  classInput?: string;
  classLabel?: string;
  required?: boolean;
  hasConfirmEmail?: boolean;
  placeholder?: string;
  verifyEmailPlaceholder?: string;
  isError?: boolean;
  isSubmitting?: boolean;
  inputAnimation: string;
  onChange?: (value: FormItemValue, isValid?: boolean) => void;
  entryFormSetting?: any;
}

const FormElementEmail: FC<FormElementEmailProps> = ({
  value,
  label,
  placeholder,
  verifyEmailPlaceholder,
  classInput,
  hasConfirmEmail,
  inputAnimation,
  isSubmitting,
  required,
  onChange,
  entryFormSetting,
}) => {
  const { t } = useTranslation();
  const validationSchema = Yup.object({
    email: required ? Yup.string().email().required() : Yup.string().email(),
    confirmEmail: hasConfirmEmail
      ? Yup.string()
          .email()
          .when('email', (values, field) => {
            const [email] = values;
            if (required || email) {
              return field.required().oneOf([email], 'validation.confirm');
            }

            return field.oneOf([email], 'validation.confirm');
          })
      : Yup.string(),
  });

  const form = useFormik<FormMail>({
    validationSchema,
    initialValues: {
      email: value as string,
      confirmEmail: value as string,
    },
    validateOnMount: false,
    validateOnChange: false,
    validateOnBlur: true,
    onSubmit: () => {},
  });

  useEffect(() => {
    if (form.dirty) {
      onChange && onChange(form.values.email, form.isValid);
    }
  }, [form.isValid, form.values]);

  useEffect(() => {
    if (isSubmitting) {
      form.setTouched({
        email: true,
        confirmEmail: true,
      });
      form.validateForm();
    }
  }, [isSubmitting]);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === ' ') {
      event.preventDefault();
    }
  };

  return (
    <>
      <InputFactory
        name="email"
        className={classInput}
        inputAnimation={inputAnimation}
        placeholder={placeholder}
        value={form.values.email}
        error={Boolean(form.errors.email)}
        helperText={form.errors.email && t(form.errors.email, { field: label })}
        onChange={form.handleChange}
        onBlur={form.handleBlur}
        onKeyDown={handleKeyDown}
        borderColor={entryFormSetting?.borderColor}
        backgroundColor={entryFormSetting?.bgColor}
        borderRadius={entryFormSetting?.borderRadius}
        fontSize={entryFormSetting?.fontSize}
      />
      {!!hasConfirmEmail && (
        <Box sx={{ marginTop: 1 }}>
          <InputFactory
            name="confirmEmail"
            className={classInput}
            inputAnimation={inputAnimation}
            placeholder={verifyEmailPlaceholder}
            value={form.values.confirmEmail}
            error={Boolean(form.errors.confirmEmail)}
            helperText={form.errors.confirmEmail && t(form.errors.confirmEmail, { field: `確認${label}`, compare: label })}
            onChange={form.handleChange}
            onBlur={form.handleBlur}
            borderColor={entryFormSetting?.borderColor}
            backgroundColor={entryFormSetting?.bgColor}
            borderRadius={entryFormSetting?.borderRadius}
            fontSize={entryFormSetting?.fontSize}
          />
        </Box>
      )}
    </>
  );
};

export default FormElementEmail;
