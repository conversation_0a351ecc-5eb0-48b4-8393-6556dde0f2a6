import SCIconButton from '@/components/common/SCIconButton';
import FormConfirmStandalone from '@/components/FormBuilder/subcomponents/FormConfirmStandalone';
import FormStandalone from '@/components/FormBuilder/subcomponents/FormStandalone';
import useLogic from '@/modules/formBuilder/preview/useLogic';
import {
  FormColorSetting,
  FormElement,
  FormEmbedAppSetting,
  FormHistoryType,
  FormItemValue,
  FormMailSetting,
  FormScheduleSetting,
  FormStatus,
  GeneralSetting,
  TemplateType,
} from '@/types/FormTemplateTypes';
import { FORM_COLOR_SETTING_INIT } from '@/utils/formBuilderUtils';
import { displayFontFamily } from '@/utils/helper';
import ComputerIcon from '@mui/icons-material/Computer';
import SmartphoneIcon from '@mui/icons-material/Smartphone';
import TabletMacIcon from '@mui/icons-material/TabletMac';
import { Box } from '@mui/material';
import { Stack } from '@mui/system';
import React, { useState } from 'react';
import './style.css';

const initTemplate: TemplateType = {
  formElements: [] as FormElement[],
  formGeneralSetting: {} as GeneralSetting,
  formColorSetting: FORM_COLOR_SETTING_INIT as FormColorSetting,
  formMailSetting: {} as FormMailSetting,
  formScheduleSetting: {} as FormScheduleSetting,
  formEmbedAppSetting: {} as FormEmbedAppSetting,
  publishHistory: [] as FormHistoryType[],
  status: FormStatus.DRAFT,
};

const FormBuilderPreviewModule: React.FC = () => {
  const { data, viewMode, handleViewer, isTemplateMode, isBlackModeColor, formTemplateModePreset } = useLogic();
  const [isShowConfirm, setIsShowConfirm] = useState<boolean>(false);
  const [formData, setFormData] = useState<Record<string, FormItemValue>>(null);
  const template: TemplateType = data || initTemplate;

  const currentFormColorSetting = isTemplateMode
    ? ({
        ...FORM_COLOR_SETTING_INIT,
        optionMode: 'template_mode',
        layoutMode: template?.formColorSetting?.layoutMode,
        templateModeColor: template?.formColorSetting?.templateModeColor,
        bgColor: formTemplateModePreset?.bgColor,
        choiceSettings: {
          color: template?.formColorSetting?.choiceSettings?.color ?? '#333',
        },
        titleSettings: {
          ...FORM_COLOR_SETTING_INIT?.titleSettings,
          color: formTemplateModePreset?.buttonColor,
        },
        buttonSettings: {
          ...FORM_COLOR_SETTING_INIT?.buttonSettings,
          bgColor: formTemplateModePreset?.buttonColor,
          borderColor: formTemplateModePreset?.borderColor,
        },
        labelSettings: {
          ...FORM_COLOR_SETTING_INIT?.labelSettings,
          color: isBlackModeColor ? '#FFF' : formTemplateModePreset?.color,
        },
        descriptionSettings: {
          ...FORM_COLOR_SETTING_INIT?.descriptionSettings,
          color: isBlackModeColor ? '#FFF' : formTemplateModePreset?.color,
        },
        generalSettings: {
          ...FORM_COLOR_SETTING_INIT?.generalSettings,
          color: isBlackModeColor ? '#FFF' : formTemplateModePreset?.color,
        },
        entryFormSettings: {
          ...FORM_COLOR_SETTING_INIT?.entryFormSettings,
          color: isBlackModeColor ? '#555' : formTemplateModePreset?.color,
        },
      } as FormColorSetting)
    : template?.formColorSetting;

  const handleOnSubmit = (values: Record<string, FormItemValue>) => {
    setFormData(values);
    setIsShowConfirm(true);
  };

  if (!data) {
    return null;
  }

  return (
    <Box component="section" className="preview">
      <Stack direction="row" gap={1} component={'section'} justifyContent={'center'}>
        <SCIconButton disableRipple onClick={() => handleViewer('desktop')}>
          <ComputerIcon color="primary" />
        </SCIconButton>
        <SCIconButton onClick={() => handleViewer('tablet')}>
          <TabletMacIcon color="primary" />
        </SCIconButton>
        <SCIconButton onClick={() => handleViewer('mobile')}>
          <SmartphoneIcon color="primary" />
        </SCIconButton>
      </Stack>
      <Box
        component="section"
        className={`device device-${viewMode}`}
        sx={{
          scrollbarWidth: 'thin',
          '&::-webkit-scrollbar': {
            width: '0.4em',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#888',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: '#555',
          },
        }}
      >
        {isShowConfirm ? (
          <Box
            sx={{
              backgroundColor: 'transparent',
              fontSize: `${currentFormColorSetting?.generalSettings?.fontSize}${currentFormColorSetting?.generalSettings?.fontSizeUnit}`,
              fontFamily: displayFontFamily(currentFormColorSetting?.generalSettings?.fontFamily),
              color: currentFormColorSetting?.generalSettings?.color,
              pt: 2,
              pb: 2,
            }}
          >
            <FormConfirmStandalone
              formElements={template?.formElements}
              colorSetting={{ ...currentFormColorSetting, layoutMode: viewMode === 'mobile' ? 'vertical' : currentFormColorSetting?.layoutMode }}
              values={formData}
              onBack={() => setIsShowConfirm(false)}
            />
          </Box>
        ) : (
          <Box
            sx={{
              backgroundColor: 'transparent',
              fontSize: `${currentFormColorSetting?.generalSettings?.fontSize}${currentFormColorSetting?.generalSettings?.fontSizeUnit}`,
              fontFamily: displayFontFamily(currentFormColorSetting?.generalSettings?.fontFamily),
              color: currentFormColorSetting?.generalSettings?.color,
              pt: 2,
              pb: 2,
            }}
          >
            <FormStandalone
              formElements={template?.formElements}
              generalSetting={template?.formGeneralSetting}
              colorSetting={{ ...currentFormColorSetting, layoutMode: viewMode === 'mobile' ? 'vertical' : currentFormColorSetting?.layoutMode }}
              screenType="pc"
              initialValues={formData}
              onSubmit={handleOnSubmit}
            />
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default FormBuilderPreviewModule;
