import React, { FC } from 'react';
import { Box, Stack, styled, Typography } from '@mui/material';
import Divider from '@mui/material/Divider';

export interface BlockOption {
  label: string;
  value: string | number;
}
interface SCBlockWithOptionProps {
  title: string;
  children?: React.ReactNode;
}

const BlockContainer = styled(Box)(({ theme }) => {
  return {
    backgroundColor: theme.palette.common.white,
    boxShadow: `-20px 0px 20px 0px ${theme.palette.grey[100]}`,
  };
});

const BlockHeader = styled(Stack)(({ theme }) => {
  return {
    backgroundColor: theme.palette.common.white,
  };
});

const SCBlockWithOption: FC<SCBlockWithOptionProps> = ({ title, children }) => {
  return (
    <BlockContainer borderRadius={2}>
      <BlockHeader py={1} px={2} borderRadius={2} mt={'-8px'}>
        <Typography color="text.secondary" fontWeight="bold">
          {title}
        </Typography>
      </BlockHeader>
      <Divider />
      {children}
    </BlockContainer>
  );
};
export default SCBlockWithOption;
