import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddLastChangePasswordAtFieldOnAccountTable1721276693945 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}account`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'last_change_password_at',
        type: 'datetime',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'last_change_password_at',
        type: 'datetime',
        isNullable: true,
      }),
    ]);
  }
}
