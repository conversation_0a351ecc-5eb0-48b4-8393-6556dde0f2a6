import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddStatusFieldOnFormSubmission1717990598732 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}form_submission`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'status',
        type: 'tinyint',
        isNullable: false,
        default: 0,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'status',
        type: 'tinyint',
        isNullable: false,
      }),
    ]);
  }
}
