import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

import { RootService } from '@/core/services/root.service';
import { logger } from '@/core/logger/index.logger';

export enum WebFontType {
  JAPAN_FONT = 'JAPAN_FONT',
  JAPAN_FREE_FONT = 'JAPAN_FREE_FONT',
  GOOGLE_FONT = 'GOOGLE_FONT',
  SHOPIFY_FONT = 'SHOPIFY_FONT',
}

export const SHOPIFY_FONTS: any[] = [
  { fontName: 'Noto Sans JP', fontFamily: 'Noto Sans JP', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Noto Serif JP', fontFamily: 'Noto Serif JP', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'M PLUS 1', fontFamily: 'M PLUS 1', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'M PLUS Rounded 1c', fontFamily: 'M PLUS Rounded 1c', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Sawarabi Mincho', fontFamily: 'Sawarabi Mincho', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'BIZ UDPMincho', fontFamily: 'BIZ UDPMincho', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Dela Gothic One', fontFamily: 'Dela Gothic One', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Hina Mincho', fontFamily: 'Hina Mincho', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Kaisei Decol', fontFamily: 'Kaisei Decol', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Kaisei Opti', fontFamily: 'Kaisei Opti', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Zen Old Mincho', fontFamily: 'Zen Old Mincho', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Zen Kurenaido', fontFamily: 'Zen Kurenaido', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Zen Maru Gothic', fontFamily: 'Zen Maru Gothic', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Zen Kaku Gothic New', fontFamily: 'Zen Kaku Gothic New', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Shippori Mincho', fontFamily: 'Shippori Mincho', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Murecho', fontFamily: 'Murecho', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Mochiy Pop P One', fontFamily: 'Mochiy Pop P One', type: WebFontType.SHOPIFY_FONT },
  { fontName: 'Kaisei Tokumin', fontFamily: 'Kaisei Tokumin', type: WebFontType.SHOPIFY_FONT },
];

export type WebFontItem = {
  fontFamily: string;
  fontName: string;
  type: WebFontType;
};

@Injectable()
export class WebFontService extends RootService {
  constructor(private readonly configService: ConfigService) {
    super();
  }

  async getWebFonts() {
    const [japanWebFonts, japanFreeWebFonts, googleWebFonts] = await Promise.all([
      this.getJapanWebFonts(),
      this.getJapanFreeWebFonts(),
      this.getGoogleWebFonts(),
    ]);

    return {
      JAPAN_FONT: japanWebFonts?.JAPAN_FONT,
      JAPAN_FREE_FONT: japanFreeWebFonts?.JAPAN_FREE_FONT,
      GOOGLE_FONT: googleWebFonts?.GOOGLE_FONT,
      SHOPIFY_FONT: SHOPIFY_FONTS,
    };
  }

  async getJapanWebFonts() {
    let fontGroups: Record<WebFontType, WebFontItem[]> = null;
    try {
      const response = await axios(`${this.configService.get('J_FONTS_URL')}`);

      if (response?.status === 200) {
        const dataArray = response?.data;

        if (dataArray.length === 0) {
          return {
            JAPAN_FONT: [],
            JAPAN_FREE_FONT: [],
            GOOGLE_FONT: [],
            SHOPIFY_FONT: SHOPIFY_FONTS,
          };
        }

        fontGroups = {
          JAPAN_FONT: dataArray?.map?.((font) => {
            return {
              fontFamily: font?.styles?.[0]?.css_font_family ?? font?.font_family,
              fontName: font?.styles?.[0]?.font_name ?? font?.font_family,
              type: WebFontType.JAPAN_FONT,
            };
          }),
          JAPAN_FREE_FONT: [],
          GOOGLE_FONT: [],
          SHOPIFY_FONT: SHOPIFY_FONTS,
        };
      } else {
        return null;
      }
    } catch (error) {
      logger.error('getJapanWebFonts', error);
      return null;
    }

    return fontGroups;
  }

  async getJapanFreeWebFonts(withStyleLink: boolean = false) {
    let fontGroups: Record<WebFontType, WebFontItem[]> = null;
    try {
      const response = await axios(`${this.configService.get('J_FREE_FONTS_URL')}`);

      if (response?.status === 200) {
        const dataArray = response?.data;

        if (dataArray.length === 0) {
          return {
            JAPAN_FONT: [],
            JAPAN_FREE_FONT: [],
            GOOGLE_FONT: [],
            SHOPIFY_FONT: SHOPIFY_FONTS,
          };
        }

        fontGroups = {
          JAPAN_FONT: [],
          JAPAN_FREE_FONT: dataArray?.map?.((font) => {
            return {
              fontFamily: font?.styles?.[0]?.css_font_family ?? font?.font_family,
              fontName: font?.styles?.[0]?.font_name ?? font?.font_family,
              type: WebFontType.JAPAN_FREE_FONT,
              styleLink: withStyleLink ? font?.styles?.[0]?.styleLink : undefined,
            };
          }),
          GOOGLE_FONT: [],
          SHOPIFY_FONT: SHOPIFY_FONTS,
        };
      } else {
        return null;
      }
    } catch (error) {
      logger.error('getJapanFreeWebFonts', error);
      return null;
    }

    return fontGroups;
  }

  async getGoogleWebFonts() {
    let fontGroups: Record<WebFontType, WebFontItem[]> = null;
    try {
      const response = await axios(
        `${this.configService.get('GOOGLE_FONTS_API_URL')}?key=${this.configService.get('GOOGLE_FONTS_API_KEY')}&sort=popularity`,
      );

      if (response?.status === 200) {
        const result = response?.data;

        if (result.length === 0) {
          return {
            JAPAN_FONT: [],
            JAPAN_FREE_FONT: [],
            GOOGLE_FONT: [],
            SHOPIFY_FONT: SHOPIFY_FONTS,
          };
        }

        const googleFonts: WebFontItem[] = result?.items?.map?.((font) => {
          const fontCss: string[] = [font?.family];

          switch (font?.category) {
            case 'serif':

            case 'sans-serif':
              fontCss.push(font?.category);
              break;

            case 'display':

            case 'handwriting':
              fontCss.push('cursive');
              break;

            default:
              break;
          }

          return { fontFamily: fontCss.join(', '), fontName: font?.family, type: WebFontType.GOOGLE_FONT };
        });

        fontGroups = {
          JAPAN_FONT: [],
          JAPAN_FREE_FONT: [],
          GOOGLE_FONT: googleFonts,
          SHOPIFY_FONT: SHOPIFY_FONTS,
        };
      } else {
        return null;
      }
    } catch (error) {
      logger.error('getGoogleWebFonts', error);
      return null;
    }

    return fontGroups;
  }
}
