declare global {
  function gtag(...args: any[]): void;
}

const useGoogleAnalytics = () => {
  const sendEvent = (action: string, category: string, label?: string, value?: number) => {
    // if gtag is not defined, do nothing
    if (typeof gtag !== 'function') {
      return;
    }

    gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  };

  return { sendEvent };
};

export default useGoogleAnalytics;
