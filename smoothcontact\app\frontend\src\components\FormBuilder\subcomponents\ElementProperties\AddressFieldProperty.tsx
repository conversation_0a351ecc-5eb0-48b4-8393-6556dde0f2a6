import { Box, Checkbox, FormControl, FormControlLabel, FormGroup, FormLabel } from '@mui/material';
import { FormikValues } from 'formik';
import { FC } from 'react';

interface AddressFieldPropertyProps {
  form: FormikValues;
}

const AddressFieldProperty: FC<AddressFieldPropertyProps> = (props) => {
  const { form } = props;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
      <FormControl component="fieldset">
        <FormLabel>郵便番号設定</FormLabel>
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                value={true}
                {...form.register('oneFieldPostcode', { nameOfValueProps: 'checked' })}
                checked={form?.values?.oneFieldPostcode ?? false}
              />
            }
            label="郵便番号欄を１つにする"
          />
          <FormControlLabel
            control={
              <Checkbox
                value={true}
                {...form.register('displayPostCodeLink', { nameOfValueProps: 'checked' })}
                checked={form?.values?.displayPostCodeLink ?? false}
              />
            }
            label="日本郵便公式サイトのリンクを表示"
          />
        </FormGroup>
      </FormControl>
    </Box>
  );
};

export default AddressFieldProperty;
