import LoginComponent from './components/login';
import MfaComponent from './components/mfa';
import useLogic from './useLogic';

export const LoginModule: React.FC = () => {
  const { mfaFormHandler, loginFormHandler, loading, account, verifyMode, backToLogin, switchVerifyMode } = useLogic();

  if (account?.mfaEnabled) {
    return (
      <MfaComponent
        formHandler={mfaFormHandler}
        loading={loading}
        backToLogin={backToLogin}
        verifyMode={verifyMode}
        switchVerifyMode={switchVerifyMode}
      />
    );
  }

  return <LoginComponent formHandler={loginFormHandler} loading={loading} />;
};
