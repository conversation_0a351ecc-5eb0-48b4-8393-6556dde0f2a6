import { Autocomplete, TextField, Typography } from '@mui/material';

interface SCAutocompleteOptions {
  value: string;
  label: string;
}

interface SCAutocompleteProps {
  options: SCAutocompleteOptions[];
  value: string;
  error: boolean;
  helperText: string;
  label: string;
  onChange: (event: any, newValue: any) => void;
  onBlur: (event: any) => void;
}

const SCFont: React.FC<SCAutocompleteProps> = ({ options, value, error, helperText, label, onChange, onBlur }) => {
  return (
    <Autocomplete
      disableClearable
      disablePortal
      options={options}
      value={value}
      onChange={onChange}
      isOptionEqualToValue={(option: any, value: any) => option.value === value}
      onBlur={onBlur}
      renderOption={(props, option) => (
        <li {...props}>
          <Typography style={{ fontFamily: option.value }}>{option.label}</Typography>
        </li>
      )}
      renderInput={(params) => (
        <TextField {...params} style={{ fontFamily: value }} value={value} error={!!error} helperText={!!error && String(helperText)} label={label} />
      )}
    />
  );
};

export default SCFont;
