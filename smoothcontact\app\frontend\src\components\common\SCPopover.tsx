import React, { useState } from 'react';
import { Button, Popover, Typography, List, ListItem } from '@mui/material';

const SCPopover: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);

  const handleOpen = () => {
    // Create a dummy element as an anchor reference
    const virtualElement = document.createElement('div');
    document.body.appendChild(virtualElement);
    setAnchorEl(virtualElement);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    if (anchorEl) {
      document.body.removeChild(anchorEl);
      setAnchorEl(null);
    }
  };

  return (
    <div>
      <Button variant="contained" onClick={handleOpen}>
        Open Centered Popover
      </Button>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'center',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'center',
          horizontal: 'center',
        }}
        PaperProps={{
          style: { padding: '20px', textAlign: 'center', maxWidth: '400px' },
        }}
      >
        <Typography variant="h6" gutterBottom>
          Centered Popover
        </Typography>
        <List>
          <ListItem button>Option 1</ListItem>
          <ListItem button>Option 2</ListItem>
          <ListItem button>Option 3</ListItem>
        </List>
        <Button variant="outlined" onClick={handleClose} style={{ marginTop: '10px' }}>
          Close
        </Button>
      </Popover>
    </div>
  );
};

export default SCPopover;
