import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddModeFieldOnSubmissionTable1724744282956 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}form_submission`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'mode',
        type: 'varchar',
        length: '10',
        isNullable: true,
        default: "'manual'",
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumns(this.TABLE_NAME, [
      new TableColumn({
        name: 'mode',
        type: 'varchar',
        length: '10',
        isNullable: true,
      }),
    ]);
  }
}
