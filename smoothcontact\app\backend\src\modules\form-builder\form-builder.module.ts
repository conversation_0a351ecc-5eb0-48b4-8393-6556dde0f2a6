import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { UploadModule } from '@/modules/upload/upload.module';

import { AccountModule } from '../account/account.module';
import { AccountEntity } from '../account/entities/account.entity';
import { SubmissionModule } from '../submission/submission.module';
import { FormBuilderEntity } from './entities/form-builder.entity';
import { FormBuilderController } from './form-builder.controller';
import { FormBuilderService } from './form-builder.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([FormBuilderEntity, AccountEntity]),
    UploadModule,
    forwardRef(() => AccountModule),
    forwardRef(() => SubmissionModule),
  ],
  providers: [FormBuilderService],
  controllers: [FormBuilderController],
  exports: [FormBuilderService],
})
export class FormBuilderModule {}
