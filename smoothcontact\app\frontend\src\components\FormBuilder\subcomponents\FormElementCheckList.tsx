import { Box, Checkbox, FormControlLabel, FormGroup, FormHelperText, Typography } from '@mui/material';
import React, { useEffect, useMemo } from 'react';
import InputFactory from './FormColorSetting/AnimationCustom/text/InputFactoryComponent';

interface FormElementCheckListProps {
  classContainer?: string;
  classInput?: string;
  value: string[];
  items: { label: string; value: string }[];
  name: string;
  error: boolean;
  helperText?: string;
  isVertical?: boolean;
  displayOtherOption?: boolean;
  onChange?: (value: string[]) => void;
  inputAnimation?: string;
  entryFormSetting?: any;
}

const enum CheckBoxType {
  DEFAULT = 'default',
  OTHER = 'other',
}

const FormElementCheckList: React.FC<FormElementCheckListProps> = ({
  classContainer,
  classInput,
  value,
  items,
  name,
  error,
  helperText,
  isVertical,
  displayOtherOption,
  onChange,
  inputAnimation,
  entryFormSetting,
}) => {
  const [checked, setChecked] = React.useState<string[]>(value);
  const [otherChecked, setOtherChecked] = React.useState<boolean>(false);
  const [otherValue, setOtherValue] = React.useState<string>('');

  const itemsMap = new Map<string, string>(
    items?.map?.((i) => {
      return [i.value, i.label];
    })
  );

  const checkedMap = useMemo(() => {
    if (!checked) return new Map<string, string>();

    return new Map<string, string>(
      checked?.map?.((i) => {
        return [itemsMap.has(i) ? i : 'other', i];
      })
    );
  }, [checked]);

  const handleUpdateCheckedValues = (value: string, type: 'default' | 'other', isRemove = false) => {
    const existed = checkedMap.has(value);

    if (type === CheckBoxType.OTHER) {
      if (isRemove || !value) {
        checkedMap.delete(CheckBoxType.OTHER);
      } else {
        checkedMap.delete(CheckBoxType.OTHER);
        checkedMap.set(CheckBoxType.OTHER, value);
      }
    } else {
      if (isRemove) {
        checkedMap.delete(value);
      }

      if (!existed && !isRemove) {
        checkedMap.set(value, value);
      }
    }

    const sortedChecked = Array.from(checkedMap.keys()).sort((a, b) => {
      if (a === CheckBoxType.OTHER) return 1;

      if (b === CheckBoxType.OTHER) return -1;

      const indexA = items.findIndex((item) => item.value === a);
      const indexB = items.findIndex((item) => item.value === b);

      return indexA - indexB;
    });

    const newChecked = sortedChecked.map((key) => checkedMap.get(key));

    onChange && onChange(newChecked.length === 0 ? null : newChecked);
    setChecked(newChecked);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleUpdateCheckedValues(e.target.value, 'default', !e.target.checked);
  };

  const handleOtherValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    handleUpdateCheckedValues(e.target.value, CheckBoxType.OTHER);
    setOtherValue(e.target.value);
  };

  const handleOtherOptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOtherChecked(e.target.checked);
    !e.target.checked && setOtherValue('');
    handleUpdateCheckedValues(otherValue, CheckBoxType.OTHER, !e.target.checked);
  };

  useEffect(() => {
    checkedMap.forEach((v, k) => {
      if (k === CheckBoxType.OTHER) {
        setOtherValue(v);
        setOtherChecked(true);
      }
    });
  }, []);

  return (
    <>
      <FormGroup>
        <Box
          display={isVertical ? 'flex' : 'block'}
          sx={{
            flexDirection: isVertical ? 'column' : 'row',
          }}
        >
          {items?.map?.((i) => (
            <FormControlLabel
              className={classContainer}
              name={name}
              key={i.value}
              control={<Checkbox />}
              label={i.label}
              value={i.value}
              checked={checkedMap.has(i.value)}
              onChange={handleChange}
            />
          ))}
        </Box>
        {displayOtherOption && (
          <Box sx={{ display: 'flex' }}>
            <FormControlLabel
              className={classContainer}
              name={name}
              key="other"
              control={<Checkbox value={otherValue} checked={otherChecked} onChange={handleOtherOptionChange} />}
              label={
                <Box display="flex" alignItems="center">
                  <Typography variant="body1">その他：</Typography>
                  <div style={{ paddingLeft: '10px' }}>
                    <InputFactory
                      name="checklist-other-option"
                      placeholder=""
                      className={classInput}
                      borderColor={entryFormSetting?.borderColor}
                      backgroundColor={entryFormSetting?.bgColor}
                      borderRadius={entryFormSetting?.borderRadius}
                      fontSize={entryFormSetting?.fontSize}
                      onChange={handleOtherValueChange}
                      value={otherValue}
                      disabled={!otherChecked}
                      inputAnimation={inputAnimation}
                      marginTop="0"
                    />
                  </div>
                </Box>
              }
            />
          </Box>
        )}
      </FormGroup>
      {helperText && <FormHelperText error={error}>{helperText}</FormHelperText>}
    </>
  );
};

export default FormElementCheckList;
