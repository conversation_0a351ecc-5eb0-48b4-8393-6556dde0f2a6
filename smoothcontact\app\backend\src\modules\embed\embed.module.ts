import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { EmbedController } from '@/modules/embed/embed.controller';
import { EmbedService } from '@/modules/embed/embed.service';
import { FormBuilderModule } from '@/modules/form-builder/form-builder.module';
import { SubmissionModule } from '@/modules/submission/submission.module';
import { UploadModule } from '@/modules/upload/upload.module';

@Global()
@Module({
  imports: [FormBuilderModule, UploadModule, SubmissionModule],
  controllers: [EmbedController],
  providers: [ConfigService, EmbedService],
  exports: [],
})
export class EmbedModule {}
