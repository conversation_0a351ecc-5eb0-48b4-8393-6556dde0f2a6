import SCColorPicker from '@/components/common/SCColorPicker';
import SCFontSelector from '@/components/common/SCFontSelector';
import { KeyboardArrowDown } from '@mui/icons-material';
import { FormControl, Grid, MenuItem, Select, Stack, TextField, Typography } from '@mui/material';
import { FC } from 'react';
import { CustomModeComponentProps } from './CustomModeComponent';

const DescriptionCustomComponent: FC<CustomModeComponentProps> = (props) => {
  const { form, webFonts } = props;

  return (
    <>
      <Typography variant="body2">項目説明文</Typography>
      <Stack direction="row" spacing={2}>
        <TextField
          label="文字サイズ"
          variant="outlined"
          type="number"
          {...form.register('descriptionSettings.fontSize')}
          value={form?.values?.descriptionSettings?.fontSize ?? 12}
          error={!!form?.errors?.descriptionSettings?.fontSize}
          helperText={form?.errors?.descriptionSettings?.fontSize ? '整数を半角で入力してください' : ''}
        />
        <Select
          IconComponent={() => <KeyboardArrowDown />}
          {...form.register('descriptionSettings.fontSizeUnit')}
          value={form?.values?.descriptionSettings?.fontSizeUnit ?? 'px'}
          displayEmpty
        >
          <MenuItem value={'px'}>px</MenuItem>
          <MenuItem value={'rem'}>rem</MenuItem>
        </Select>
      </Stack>
      <Grid container alignItems="center">
        <Grid item xs={4} container justifyContent="flex-end">
          <Typography variant="body2">文字色</Typography>
        </Grid>
        <Grid item xs={6} sx={{ pl: 2 }}>
          <SCColorPicker name="descriptionSettings.color" color={form?.values?.descriptionSettings?.color ?? '#000'} form={form} />
        </Grid>
      </Grid>
      <FormControl fullWidth>
        <SCFontSelector
          name={'descriptionSettings.fontFamily'}
          fontFamily={form?.values?.descriptionSettings?.fontFamily ?? ''}
          fontName={form?.values?.descriptionSettings?.fontName ?? form?.values?.descriptionSettings?.fontFamily ?? ''}
          source={webFonts}
          onFontChange={(font) => {
            form?.setFieldValue('descriptionSettings.fontFamily', `${font?.fontFamily}`);
            form?.setFieldValue('descriptionSettings.fontName', `${font?.fontName}`);
          }}
        />
      </FormControl>
    </>
  );
};

export default DescriptionCustomComponent;
