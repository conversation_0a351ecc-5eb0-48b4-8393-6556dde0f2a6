<!doctype html>
<html lang="ja">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <script type="module">
      if (!import.meta.env || !import.meta.env.PROD) {
        // Because the base HTML is rendered in the server side, we need to manually embed the code to enable HMR in our
        // code, so that the vite server is properly enabled to run HMR
        // const script = document.createElement('script');
        // script.setAttribute('type', 'module');
        // script.setAttribute('src', './dev_embed.js');
        // document.getElementsByTagName('head')[0].append(script);
      }
    </script>
    <!-- HEAD -->
  </head>
  <body>
    <div id="app"><!--index.jsx injects App.jsx here--></div>
    <script type="module" src="/index.tsx"></script>
    <script>
      const global = globalThis;
    </script>
    <!-- BODY -->
  </body>
</html>
