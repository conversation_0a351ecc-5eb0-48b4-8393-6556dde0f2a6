import { isEmailHalfSize } from '@/utils/validate';
import * as Yup from 'yup';

const EMAIL_REGEX = /^(?!.*\.\.@)(?!.*\.\.)[+\w\-_][+\w\-._]*@[\w\-._]+\.[A-Za-z]+$/;

export const loginValidationSchema = Yup.object().shape({
  email: Yup.string()
    .required('メールアドレスが必須')
    .email('メールアドレスの形式ではありません。')
    .max(50, 'メールアドレスは半角英数字記号、50文字以内で入力してください。')
    .test('is-email-halfSize', 'メールアドレスは半角英数字記号、50文字以内で入力してください。', isEmailHalfSize)
    .matches(EMAIL_REGEX, 'メールアドレスの形式ではありません。'),
  pwd: Yup.string()
    .required('パスワードが必須')
    .min(6, 'パスワードは6文字以上で入力してください')
    .max(16, 'パスワードは16文字以内で入力してください')
    .matches(/^[a-zA-Z0-9_-]*$/, 'パスワードに使える文字は半角のアルファベット、"_" ＆ "-"記号'),
});

export const mfaValidationSchema = Yup.object().shape({
  code: Yup.string()
    .required('OTP認証 が必須となります')
    .min(6, 'パスワードは6文字以上で入力してください')
    .max(8, 'パスワードは8文字以内で入力してください'),
});
