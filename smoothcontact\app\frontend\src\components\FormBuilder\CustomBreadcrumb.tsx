import React, { useEffect, useMemo, useState } from 'react';
import SCBreadcrumbs from '@/components/common/SCBreadcrumbs';
import SCChip from '@/components/common/SCChip';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormStatusLabel, FormType, SideBarLabel, SideBarType } from '@/types/FormTemplateTypes';
import { getRealStatus, getStatusColor } from '@/utils/helper';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { IconButton, Menu, MenuItem, Stack, Typography } from '@mui/material';
import RenameModal from './common/RenameModal';
import ChangeFormTypeModal from './common/ChangeFormTypeModal';

const CustomBreadCrumb = () => {
  const { selectedTemplate, sideBarType, saveForm, isFromBindUp } = useFormBuilder();
  const [openRenameModal, setOpenRenameModal] = useState<boolean>(false);
  const [openChangeFormTypeModal, setOpenChangeFormTypeModal] = useState<boolean>(false);
  const [formName, setFormName] = useState<string>('');
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  // Checklist items based on mode
  const checklistItems: { [key in FormType]: string[] } = useMemo(
    () => ({
      [FormType.MANUAL]: [
        'デザインやレイアウト編集機能は無効になります。',
        '現在のフォームは無効化され、再度有効にするにはコードを貼り直す必要があります。',
        'Google AdsやYahoo!プロモーション広告との連携が無効になります。',
      ],
      [FormType.HTML]: [
        'HTMLコードの編集機能は無効化されます。',
        '現在のフォームは無効化され、再度有効にするにはコードを貼り直す必要があります。',
        '標準モードに切り替えた後、カスタマイズしたコードは失われます。',
        '再度コード形式に切り替えると、HTML編集をやり直す必要があります。',
      ],
    }),
    []
  );

  useEffect(() => {
    setFormName(selectedTemplate?.name ?? '');
  }, [selectedTemplate]);

  const breadcrumbsStatusLabel = useMemo(() => {
    const realStatus = getRealStatus(
      selectedTemplate?.status,
      selectedTemplate?.releaseStartDate,
      selectedTemplate?.releaseEndDate,
      Number(selectedTemplate?.submissionCount ?? 0) >= Number(selectedTemplate?.formScheduleSetting?.maximumNumberFormsReceived ?? 9999999999)
    );
    const statusColor = getStatusColor(realStatus);
    const label = (FormStatusLabel as { [key: number]: string })?.[realStatus] ?? '';

    return <SCChip size="small" color={statusColor} label={label} />;
  }, [selectedTemplate]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const getChangeFormModeTitle = () => {
    if (selectedTemplate?.mode === FormType.HTML) {
      return '標準モードに切り替えると、デザインやレイアウト編集機能が再び利用可能になります。変更後も、標準形式とコード形式の間で双方向に切り替え可能ですが、以下の注意点をご確認ください：';
    } else {
      return 'コード形式に切り替えると、HTMLタグを直接編集できるようになります。変更後も、標準形式とコード形式の間で双方向に切り替え可能ですが、以下の注意点をご確認ください：';
    }
  };

  return (
    <>
      <SCBreadcrumbs
        items={[
          { child: sideBarType === SideBarType.ELEMENT ? 'エディター' : SideBarLabel[sideBarType] },
          {
            child: (
              <>
                <Stack direction="row" spacing={1} alignItems={'center'}>
                  <Typography
                    sx={{
                      maxWidth: '10em',
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis',
                    }}
                    color="secondary"
                  >
                    {formName}
                  </Typography>
                  <>
                    <IconButton size="small" onClick={handleClick}>
                      <ExpandMoreIcon />
                    </IconButton>
                    <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
                      <MenuItem
                        disableRipple
                        onClick={() => {
                          setOpenRenameModal((pre) => !pre);
                          handleClose();
                        }}
                      >
                        <Stack>
                          <Typography>名前を変更</Typography>
                        </Stack>
                      </MenuItem>
                      {!isFromBindUp && (
                        <MenuItem
                          disableRipple
                          onClick={() => {
                            setOpenChangeFormTypeModal((pre) => !pre);
                            handleClose();
                          }}
                        >
                          <Stack>
                            <Typography>
                              {selectedTemplate?.mode !== FormType.HTML ? '＜開発者向け＞コード形式への変更' : '＜標準モード＞デザイン編集への変更'}
                            </Typography>
                          </Stack>
                        </MenuItem>
                      )}
                    </Menu>
                  </>
                  {breadcrumbsStatusLabel}
                </Stack>
              </>
            ),
            icon: CheckCircleOutlineIcon,
          },
        ]}
      />

      {openRenameModal && (
        <RenameModal
          isOpen={openRenameModal}
          onClose={() => setOpenRenameModal((pre) => !pre)}
          onRename={(newName: string) => saveForm({ name: newName })}
          currentName={formName}
        />
      )}

      {openChangeFormTypeModal && (
        <ChangeFormTypeModal
          isOpen={openChangeFormTypeModal}
          onClose={() => setOpenChangeFormTypeModal(false)}
          onSubmit={() => saveForm({ mode: selectedTemplate?.mode !== FormType.HTML ? FormType.HTML : FormType.MANUAL })}
          checklistItems={checklistItems[selectedTemplate?.mode as FormType]}
          title={getChangeFormModeTitle()}
        />
      )}
    </>
  );
};

export default CustomBreadCrumb;
