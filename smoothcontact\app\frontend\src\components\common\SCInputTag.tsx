import { SvgIconComponent } from '@mui/icons-material';
import { Autocomplete, TextField } from '@mui/material';
import { FormikValues } from 'formik';
import React, { useState } from 'react';

interface TagInputProps {
  label?: string;
  placeholder?: string;
  name: string;
  form: FormikValues;
  IconComponent?: SvgIconComponent;
}

const TagInput: React.FC<TagInputProps> = ({ name, form, label, placeholder }) => {
  const [inputValue, setInputValue] = useState('');

  const handleUpdateInput = (value: string) => {
    if (!value) return;

    const currentValues = form?.values?.[name] || [];
    if (!currentValues.includes(name)) {
      form.setFieldValue(name, [...currentValues, value]);
    }

    setInputValue('');
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    const trimmedInput = inputValue.trim();
    if (event.key === 'Enter') {
      event.preventDefault();
      handleUpdateInput(trimmedInput);
    }

    if (event.key === 'Backspace' && inputValue === '') {
      event.preventDefault();
      const currentValues = form?.values?.[name] || []; // TODO
      form.setFieldValue(name, currentValues?.slice?.(0, -1));
    }
  };

  const handleBlur = () => {
    const trimmedInput = inputValue.trim();
    handleUpdateInput(trimmedInput);
  };

  return (
    <Autocomplete
      multiple
      freeSolo
      options={[]}
      value={form?.values?.[name] || []}
      inputValue={inputValue}
      onInputChange={(event, newInputValue) => {
        setInputValue(newInputValue);
      }}
      onChange={(event, newValue) => {
        form.setFieldValue(name, newValue);
      }}
      onBlur={handleBlur}
      renderInput={(params) => (
        <TextField
          {...params}
          variant="outlined"
          label={label}
          placeholder={placeholder}
          onKeyDown={handleKeyDown}
          error={form?.errors?.[name]}
          helperText={!!form?.errors?.[name] && form?.errors?.[name]}
        />
      )}
    />
  );
};

export default TagInput;
