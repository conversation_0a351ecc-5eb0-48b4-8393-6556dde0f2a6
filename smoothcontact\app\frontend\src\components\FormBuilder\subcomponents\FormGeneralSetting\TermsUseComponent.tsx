import { FC } from 'react';
import { Box, Checkbox, FormControlLabel, InputAdornment, TextField, Typography } from '@mui/material';
import { FormikValues } from 'formik';
import SwitchStyled from '@/components/common/SCToggleSwitch';
import CheckBoxOutlineBlankRoundedIcon from '@mui/icons-material/CheckBoxOutlineBlankRounded';
import { useTranslation } from 'react-i18next';
import SettingItem from './SettingItemComponent';
import HttpsIcon from '@mui/icons-material/Https';

interface TermsUseProps {
  form: FormikValues;
}

const TermsUse: FC<TermsUseProps> = ({ form }) => {
  const { t } = useTranslation();
  const isEnable = form?.values?.isDisplayTermsUse ?? false;

  return (
    <>
      <SettingItem label={t('form_builder.terms_use.label')} isEnable={isEnable} />
      <Typography color="text.secondary">{t('form_builder.terms_use.description')}</Typography>
      {isEnable && (
        <>
          <Typography sx={{ pl: 1 }}>{t('form_builder.terms_use.display_sample_label')}</Typography>
          <FormControlLabel
            control={<Checkbox icon={<CheckBoxOutlineBlankRoundedIcon fontSize={'small'} />} />}
            label={
              <>
                <a href={form?.values?.termsUse} target="_blank" rel="noopener noreferrer" style={{ color: '#1DA1A8', textDecoration: 'none' }}>
                  利用規約
                </a>
                に同意にする
              </>
            }
            sx={{ background: '#F7F7F7' }}
          />
        </>
      )}
      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'left', alignItems: 'center' }}>
        <SwitchStyled value={true} checked={isEnable} {...form.register('isDisplayTermsUse', { nameOfValueProps: 'checked' })} />
        <Typography color="text.secondary">設定は無効です</Typography>
      </Box>
      {isEnable && (
        <TextField
          name="termsUse"
          label={t('form_builder.terms_use.text_label')}
          placeholder="https://example.com/terms-use"
          error={!!form?.errors?.termsUse}
          helperText={form?.errors?.termsUse && '有効なURLを入力してください'}
          value={form?.values?.termsUse ?? ''}
          {...form.register('termsUse')}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start" sx={{ color: '#4CAF50' }}>
                <HttpsIcon />
              </InputAdornment>
            ),
          }}
        />
      )}
    </>
  );
};

export default TermsUse;
