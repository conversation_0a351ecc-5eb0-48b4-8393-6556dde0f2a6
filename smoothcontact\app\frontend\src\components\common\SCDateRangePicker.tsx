import { Stack } from '@mui/system';
import { DateTimePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';
import React, { useState } from 'react';

interface SCDateTimeRangePickerProps {
  onChange: (start: Dayjs | null, end: Dayjs | null) => void;
  start?: Date | null;
  end?: Date | null;
}

const SCDateTimeRangePicker: React.FC<SCDateTimeRangePickerProps> = ({ onChange, start, end }) => {
  const [startDateTime, setStartDateTime] = useState<Dayjs | null>(dayjs(start));
  const [endDateTime, setEndDateTime] = useState<Dayjs | null>(dayjs(end));

  const handleStartDateChange = (date: Dayjs | null) => {
    setStartDateTime(dayjs(date));
    onChange(date, endDateTime);
  };

  const handleEndDateChange = (date: Dayjs | null) => {
    setEndDateTime(dayjs(date));
    onChange(startDateTime, date);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Stack direction="row" alignItems={'center'} gap={2}>
        <DateTimePicker label="Start Date Time" value={startDateTime} onChange={handleStartDateChange} /> —
        <DateTimePicker label="End Date Time" value={endDateTime} onChange={handleEndDateChange} />
      </Stack>
    </LocalizationProvider>
  );
};

export default SCDateTimeRangePicker;
