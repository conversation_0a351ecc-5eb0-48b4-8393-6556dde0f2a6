import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateFormBuilderTable1713862480554 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}form_builder`;

  // eslint-disable-next-line max-lines-per-function
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: this.TABLE_NAME,
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'ext_id',
            type: 'varchar',
            length: '6',
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'datetime',
            isNullable: true,
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'datetime',
            isNullable: true,
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'deleted_at',
            type: 'datetime',
            isNullable: true,
          },
          {
            name: 'last_published_at',
            type: 'datetime',
            isNullable: true,
          },
          {
            name: 'shop',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'tinyint',
            isNullable: false,
          },
          {
            name: 'form_elements',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'form_general_setting',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'form_color_setting',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'form_mail_setting',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'form_schedule_setting',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'form_embed_app_setting',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'version',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'view',
            type: 'varchar',
            length: '10',
            isNullable: true,
          },
          {
            name: 'created_by',
            type: 'integer',
            isNullable: false,
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable(this.TABLE_NAME);
  }
}
