import { defineConfig } from 'vite';

export default defineConfig({
  build: {
    cssCodeSplit: false,
    emptyOutDir: false,
    minify: true,
    terserOptions: {
      format: {
        comments: false,
      },
    },
    target: 'esnext',
    rollupOptions: {
      input: ['./src/embed/html-embed.js'],
      output: {
        // compact: true,
        entryFileNames: 'html-embed.js',
      },
    },
  },
});
