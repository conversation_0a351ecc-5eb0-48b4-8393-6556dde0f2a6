import { FC } from 'react';
import { Checkbox, FormControlLabel, TextField } from '@mui/material';
import { FormikValues } from 'formik';

interface AnnotationComponentProps {
  form: FormikValues;
}

const Annotation: FC<AnnotationComponentProps> = ({ form }) => {
  return (
    <>
      <FormControlLabel
        control={
          <Checkbox
            value={true}
            checked={form?.values?.annotation}
            name="annotation"
            {...form.register('annotation', { nameOfValueProps: 'checked' })}
          />
        }
        label="注釈を表示"
        sx={{ '.MuiFormControlLabel-label': { fontSize: '12px' } }}
      />
      {form?.values?.annotation && (
        <TextField label="利用規約の遷移先" {...form.register('annotation')} value={form?.values?.annotation} variant="outlined" />
      )}
    </>
  );
};

export default Annotation;
