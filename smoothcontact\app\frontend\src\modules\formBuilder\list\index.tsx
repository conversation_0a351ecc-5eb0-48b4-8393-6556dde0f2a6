import { HocModalProps, withModal } from '@/hoc/withModal';
import React from 'react';
import FormBuilderListComponent from './components';
import { FormBuilderListProvider } from './store/FormBuilderListProvider';

const FormBuilderIndexModule: React.FC = ({ setOpenModal }: HocModalProps) => {
  return (
    <FormBuilderListProvider setOpenModal={setOpenModal}>
      <FormBuilderListComponent />
    </FormBuilderListProvider>
  );
};

export default withModal(FormBuilderIndexModule);
