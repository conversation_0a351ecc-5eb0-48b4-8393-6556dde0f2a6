import { Controller, Get, Query, UseGuards } from '@nestjs/common';

import { BaseController } from '@/core/controllers/api.controller';
import { AuthGuard } from '@/core/guard/auth.guard';
import { AttachmentService } from '@/modules/attachment/attachment.service';

@UseGuards(AuthGuard)
@Controller('api/attachments')
export class AttachmentController extends BaseController {
  constructor(protected readonly attachmentService: AttachmentService) {
    super();
  }

  @Get('/signed-url')
  async getAttachment(@Query('path') path: string) {
    const url = await this.attachmentService.getSignedUrl(path);
    if (!url) {
      return this.failResponse({
        message: 'Invalid path',
        statusCode: 404,
      });
    }

    return this.successResponse({
      data: {
        path,
        url,
      },
    });
  }
}
