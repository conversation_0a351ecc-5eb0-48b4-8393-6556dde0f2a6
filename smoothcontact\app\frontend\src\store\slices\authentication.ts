import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface IAuthState {
  accessToken: string;
  refreshToken: string;
  isAuthenticated: boolean;
}

export const initialState: IAuthState = {
  accessToken: '',
  refreshToken: '',
  isAuthenticated: false,
};

export const authSlice = createSlice({
  name: 'authentication',
  initialState,
  reducers: {
    setAuthState(state, action: PayloadAction<Partial<IAuthState>>) {
      return { ...state, ...action.payload };
    },
    logout() {
      setTimeout(() => {
        window.previousURLSaved = '';
      }, 500);

      return initialState;
    },
  },
});

export const authAction = authSlice.actions;

export const authReducer = authSlice.reducer;
