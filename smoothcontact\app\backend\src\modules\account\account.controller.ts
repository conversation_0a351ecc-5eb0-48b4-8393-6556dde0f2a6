import { Body, Controller, Get, HttpStatus, Param, Post, Put, Query, Request, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';

import { MailConst } from '@/common/constant';
import { BaseController } from '@/core/controllers/api.controller';
import { AuthGuard } from '@/core/guard/auth.guard';
import { BindUpSettingDto } from '@/types/bindup.dto';
import { SAKDecode } from '@/utils/helpers';

import { MailService } from '../mail/mail.service';
import { AccountService } from './account.service';
import {
  BackupCodeLoginRequestDTO,
  ChangePasswordRequestDTO,
  LoginRequestDTO,
  MfaLoginRequestDTO,
  RefreshTokenRequestDTO,
  ResetPasswordRequestDTO,
  ShopifyRegisterRequestDTO,
} from './dto/request.dto';
import { LoginResponseDTO, ProfileResponseDTO, RegisterResponseDTO } from './dto/response.dto';
import { AccountEntity } from './entities/account.entity';
import { logger } from '@/core/logger/index.logger';

@Controller('api/account')
export class AccountController extends BaseController {
  constructor(
    private readonly accountService: AccountService,
    private readonly mailService: MailService,
  ) {
    super();
  }

  @Get('shopify-login')
  async shopifyAuth(@Query() queryParams: { accessToken: string }) {
    const result = await this.accountService.shopifyAuth(queryParams);

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.BAD_REQUEST });
    }

    return this.successResponse({ data: result }, LoginResponseDTO);
  }

  @Post('shopify-register')
  async shopifyRegister(@Body() data: ShopifyRegisterRequestDTO) {
    const result = await this.accountService.shopifyRegister(data);

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.BAD_REQUEST });
    }

    return this.successResponse({ data: result }, RegisterResponseDTO);
  }

  @Post('register')
  async register(@Body() data: AccountEntity) {
    const result = await this.accountService.register(data);

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.BAD_REQUEST });
    }

    this.mailService.sendMail({
      transporterName: 'default',
      to: data.email,
      subject: MailConst.register.title,
      template: MailConst.register.template,
      context: {
        name: data.name,
      },
    });

    return this.successResponse({ data: result }, RegisterResponseDTO);
  }

  @Post('login')
  async login(@Body() data: LoginRequestDTO) {
    const result = await this.accountService.emailAndPasswordLogin(data);

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.BAD_REQUEST });
    }

    return this.successResponse({ data: result }, LoginResponseDTO);
  }

  @Post('verify-mfa')
  async verifyMfa(@Body() data: MfaLoginRequestDTO) {
    const result = await this.accountService.verifyMfa(data);

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.BAD_REQUEST });
    }

    return this.successResponse({ data: result });
  }

  @Post('verify-backup-code')
  async verifyBackupCode(@Body() data: BackupCodeLoginRequestDTO) {
    const result = await this.accountService.verifyBackupCode(data);

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.BAD_REQUEST });
    }

    return this.successResponse({ data: result });
  }

  @Get('/me')
  @UseGuards(AuthGuard)
  async getMe(@Request() request: Request) {
    const me = await this.accountService.getMe(request?.user?.id ?? '');

    if (!me) {
      return this.failResponse({ statusCode: HttpStatus.UNAUTHORIZED });
    }

    const extraUserData = request.user;

    return this.successResponse(
      {
        data: {
          ...me,
          ...extraUserData,
        },
      },
      ProfileResponseDTO,
    );
  }

  @Put('/refresh-token')
  async refreshToken(@Body() refreshTokenDto: RefreshTokenRequestDTO) {
    return this.successResponse({ data: await this.accountService.refreshToken(refreshTokenDto) }, LoginResponseDTO);
  }

  @Put('/profile/:id')
  @UseGuards(AuthGuard)
  @UseInterceptors(
    FileInterceptor('avatar', {
      storage: diskStorage({
        destination: './uploads',
      }),
      limits: { fileSize: ******** /* 10MB */ },
    }),
  )
  async updateProfile(
    @Param('id') id: number,
    @UploadedFile() avatar: Express.Multer.File,
    @Body('name') name: string,
    @Body('clearAvatar') clearAvatar: string,
  ) {
    const result = await this.accountService.updateProfile({ id, name, avatar, clearAvatar });

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.BAD_REQUEST });
    }

    return this.successResponse({ data: result });
  }

  @Put('/complete-tour/:id')
  @UseGuards(AuthGuard)
  async updateTourStatus(@Param('id') id: number) {
    const result = await this.accountService.completeTour({ id });

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.UNAUTHORIZED });
    }

    return this.successResponse({ data: result }, ProfileResponseDTO);
  }

  @Post('reset-password')
  async resetPassword(@Body() data: ResetPasswordRequestDTO) {
    const result = await this.accountService.resetPassword(data);

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.BAD_REQUEST });
    }

    this.mailService.sendMail({
      transporterName: 'default',
      to: data.email,
      subject: MailConst.resetPassword.title,
      template: MailConst.resetPassword.template,
      context: {
        name: result.name,
        resetPasswordLink: `${process.env.APP_URL}/change-password?token=${result.resetToken}`,
      },
    });

    return this.successResponse();
  }

  @Post('change-password')
  async changePasswordWithToken(@Body() data: ChangePasswordRequestDTO) {
    const result = await this.accountService.changePasswordWithToken(data);

    if (!result) {
      return this.failResponse({ statusCode: HttpStatus.BAD_REQUEST, message: 'Token is invalid' });
    }

    return this.successResponse();
  }

  @Post('login-via-oem')
  async loginViaOEM(@Body() { sak, cb, s }: { sak: string; cb: string; s: string }) {
    const decryptedSak = SAKDecode(sak);
    const sakParsed = this.SAKParse(decryptedSak);
    if (!this.isValidSak(sakParsed)) {
      return this.failResponse({ success: false, statusCode: HttpStatus.BAD_REQUEST, message: 'Invalid sak' });
    }

    const [sakUkey, sakDsid] = sakParsed;
    const OEMLoginResult = await this.accountService.makeOEMLoginRequest(sakDsid);

    if (!this.isValidLoginResult(OEMLoginResult, sakUkey)) {
      return this.failResponse({ success: false, statusCode: HttpStatus.BAD_REQUEST });
    }

    let setting: any;
    try {
      setting = JSON.parse(s);
    } catch (error) {
      logger.error('Failed to parse setting', error);
      setting = null;
    }

    const bindLoginPayload = await this.accountService.makeLogin(
      OEMLoginResult.ukey ?? '',
      OEMLoginResult.email ?? '',
      OEMLoginResult.username ?? '',
      OEMLoginResult.availableStr ?? '',
      OEMLoginResult.expirationDateStr ?? '',
      cb,
      setting,
    );

    if (!bindLoginPayload) {
      return this.failResponse({ success: false, statusCode: HttpStatus.BAD_REQUEST });
    }

    return this.successResponse({ success: true, data: bindLoginPayload }, LoginResponseDTO);
  }

  private isValidSak(sakParsed: string[]): boolean {
    // Implement the logic to validate the sak
    const [userId, email, time] = sakParsed;
    if (!userId || !email || !time) {
      return false;
    }

    return !this.isSakExpired(time);
  }

  private isSakExpired(time: string): boolean {
    if (!time) {
      return true;
    }

    const limitTime = new Date().getTime() - 3 * 60 * 1000;

    return limitTime > parseInt(time, 10);
  }

  private isValidLoginResult(result: any, sakUkey: string): boolean {
    return result && result.ukey === sakUkey;
  }

  private SAKParse(sak: string): string[] {
    return sak.split('@@');
  }

  @Post('login-via-oemstart')
  async loginViaOEMStart(
    @Body()
    {
      token,
      oemId,
      oemgo,
      courseId,
      courseType,
      callbackUrl,
      formSetting,
    }: {
      token: string;
      oemId: string;
      oemgo: string;
      courseId: string;
      courseType: string;
      callbackUrl: string;
      formSetting: BindUpSettingDto;
    },
  ) {
    const result = await this.accountService.loginViaOEMStart(token, oemId, oemgo, courseId, courseType, callbackUrl, formSetting);
    if (!result) {
      return this.failResponse({ success: false, statusCode: HttpStatus.BAD_REQUEST });
    }

    return this.successResponse({ success: true, data: result }, LoginResponseDTO);
  }
}
