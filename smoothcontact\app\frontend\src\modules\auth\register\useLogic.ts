import useAxios, { ApiResponse } from '@/hooks/useAxios';
import { registerRequestConfig } from '@/services/account.service';
import { RegisterRequestDto } from './dto/request.dto';
import { useToast } from '@/provider/toastProvider';
import { HttpStatusCode } from 'axios';
import { useFormHandler } from '@/hooks/useFormHandler';
import { validationSchema } from './validator';
import { useState } from 'react';

export type FormValue = {
  firstName: string;
  lastName: string;
  email: string;
  pwd: string;
  rePwd: string;
  privacyPolicy: boolean;
  termsOfUse: boolean;
};

export default function useLogic() {
  const { apiCaller, loading } = useAxios();
  const { toast } = useToast();
  const [registerSuccess, setRegisterSuccess] = useState(false);

  const register = async (data: FormValue) => {
    const dataBody = new RegisterRequestDto({
      name: `${data.lastName} ${data.firstName}`,
      email: data.email,
      pwd: data.pwd,
    });

    const result: ApiResponse<any> = await apiCaller(registerRequestConfig(dataBody));

    if (result?.success) {
      toast({ isError: false, message: '登録が完了しました。' });

      setRegisterSuccess(true);

      return;
    }

    if (result?.statusCode !== HttpStatusCode.BadRequest) {
      toast({ isError: true, message: result.message });
    }

    formHandle.setErrors(result.messageErrors ?? {});
  };

  const formHandle = useFormHandler<FormValue>({
    initialValues: { firstName: '', lastName: '', email: '', pwd: '', rePwd: '', privacyPolicy: false, termsOfUse: false },
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: async (values) => {
      await register(values);
    },
  });

  return { registerSuccess, formHandle, loading };
}
