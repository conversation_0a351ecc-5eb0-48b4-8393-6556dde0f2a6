import { useAppDispatch } from '@/store/hook';
import { appAction } from '@/store/slices/app';
import { useEffect, useState } from 'react';
import { Persistor } from 'redux-persist';

type Props = {
  onBeforeLift?: () => void;
  children: React.ReactNode | ((state: boolean) => React.ReactNode);
  loading?: React.ReactNode;
  persistor: Persistor;
};

export const PersistGate: React.FC<Props> = ({ onBeforeLift, children, persistor }) => {
  const [bootstrapped, setBootstrapped] = useState(false);
  let unsubscribe: (() => void) | undefined;
  const dispatch = useAppDispatch();

  useEffect(() => {
    unsubscribe = persistor.subscribe(handlePersistorState);
    handlePersistorState();

    return () => {
      unsubscribe && unsubscribe();
    };
  }, []);

  const handlePersistorState = (): void => {
    const { bootstrapped } = persistor.getState();
    if (bootstrapped) {
      dispatch(appAction.setAppState({ authLoaded: true }));
      if (onBeforeLift) {
        Promise.resolve(onBeforeLift()).finally(() => setBootstrapped(true));
      } else {
        setBootstrapped(true);
      }

      unsubscribe && unsubscribe();
    }
  };

  if (typeof children === 'function') {
    return <>{children(bootstrapped)}</>;
  }

  // return <>{bootstrapped ? children : loading}</>;
  return <>{children}</>;
};
