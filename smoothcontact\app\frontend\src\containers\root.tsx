import { useSyncProfile } from '@/hooks/useSyncProfile';
import { NotFoundModule } from '@/modules/notFound';
import { UnavailableModule } from '@/modules/unavailable';
import ErrorBoundary from '@/routes/errorBoundary';
import { useAppSelector } from '@/store/hook';
import React from 'react';

export const RootLayout = ({ children }: { children: React.ReactElement }) => {
  const { notFound<PERSON><PERSON><PERSON>, <PERSON>rror500Che<PERSON> } = useAppSelector((state) => ({
    notFoundChecker: state.app.notFoundChecker,
    Error500Checker: state.app.Error500Checker,
  }));

  useSyncProfile();

  if (notFoundChecker.redirect) {
    return <NotFoundModule />;
  }

  if (Error500Checker.redirect) {
    return <UnavailableModule />;
  }

  return (
    <>
      <ErrorBoundary>{children}</ErrorBoundary>
    </>
  );
};
