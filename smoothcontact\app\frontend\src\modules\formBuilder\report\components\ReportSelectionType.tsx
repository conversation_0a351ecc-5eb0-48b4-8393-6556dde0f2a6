import React, { FC } from 'react';
import { Grid, Typography } from '@mui/material';
import SCSimpleCard from '@/components/common/SCSimpleCard';
import LinearProgressWithLabel from './LinearProgressWithLabel';
import ReportOtherAnswer from './ReportOtherAnswer';

interface ReportSelectionTypeProps {
  label: string;
  statistics: Record<string, number>;
  otherData?: string[];
  isFullWidth: boolean;
}

const ReportSelectionType: FC<ReportSelectionTypeProps> = ({ label, statistics, otherData = [], isFullWidth }) => {
  const totalCount = Object.values(statistics).reduce((acc, curr) => acc + curr, 0) + otherData.length;
  if (totalCount === 0) return null;

  return (
    <Grid item xs={isFullWidth ? 12 : 6} sx={{ paddingTop: 4 }}>
      <SCSimpleCard>
        <Typography color="text.primary" variant="h6" sx={{ py: 1, wordBreak: 'break-all' }}>
          {label}
        </Typography>

        {/* Display main statistics */}
        {statistics &&
          Object.entries(statistics).map(([key, value], index) => (
            <LinearProgressWithLabel key={index} label={key} value={value} percentage={(value / totalCount) * 100} />
          ))}

        {/* Display other option */}
        {otherData.length > 0 && (
          <LinearProgressWithLabel key={'other'} label={'その他'} value={otherData.length} percentage={(otherData.length / totalCount) * 100} />
        )}

        {/* Displays statistics with text format */}
        {otherData.length > 0 && <ReportOtherAnswer otherData={otherData} statistics={statistics} title={'その他の内容'} controlName="" />}
      </SCSimpleCard>
    </Grid>
  );
};

export default ReportSelectionType;
