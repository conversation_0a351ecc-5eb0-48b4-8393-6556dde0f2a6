import { Injectable, Res } from '@nestjs/common';
import { Response } from 'express';
import * as <PERSON> from 'papapar<PERSON>';

@Injectable()
export class CsvService {
  downloadCsv(fileName: string, data: string, @Res() res: Response) {
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename=${encodeURI(fileName)}`);
    res.send('\uFEFF' + data);
  }

  async transformToCsv(headers: string[], rows: (string | number)[][], includeHeader?: boolean): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        const csvContent = Papa.unparse(
          {
            fields: headers,
            data: rows,
          },
          { header: includeHeader ?? true },
        );

        resolve(csvContent);
      } catch (error) {
        reject(new Error('CSV形式への変換に失敗しました。'));
      }
    });
  }
}
