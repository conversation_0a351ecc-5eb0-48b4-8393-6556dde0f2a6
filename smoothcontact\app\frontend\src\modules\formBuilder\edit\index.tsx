import { HocModalProps, withModal } from '@/hoc/withModal';
import useLogic from '@/modules/formBuilder/edit/useLogic';
import * as React from 'react';
import FormBuilderEditComponent from './components';
import { FormBuilderProvider } from './store/FormBuilderProvider';

const FormBuilderEditModule: React.FC = ({ setOpenModal }: HocModalProps) => {
  const { data, save, loading } = useLogic();

  if (!data) {
    return null;
  }

  return (
    <FormBuilderProvider template={data} loading={loading} save={save} setOpenModal={setOpenModal}>
      <FormBuilderEditComponent />
    </FormBuilderProvider>
  );
};

export default withModal(FormBuilderEditModule);
