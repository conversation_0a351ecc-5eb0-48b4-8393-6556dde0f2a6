module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint', 'simple-import-sort'],
  extends: ['plugin:@typescript-eslint/recommended', 'plugin:prettier/recommended'],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: ['.eslintrc.js', '.next/', 'out/', 'node_modules/', 'logs/', 'dist/', 'test/'],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    'simple-import-sort/imports': 'off',
    'simple-import-sort/exports': 'off',
    'object-curly-newline': 'error',
    'no-console': 'error',
    '@typescript-eslint/no-unused-vars': 'error',
    curly: 'error',
    'no-param-reassign': ['error', { props: false }],
    'import/prefer-default-export': 'off',
    'no-restricted-syntax': [
      'error',
      {
        selector: 'ExportDefaultDeclaration',
        message: 'Prefer named exports',
      },
    ],
    complexity: ['error', { max: 16 }],
    'max-lines-per-function': ['error', 100],
    '@typescript-eslint/naming-convention': [
      'error',
      {
        selector: ['parameter', 'variable', 'function'],
        format: ['camelCase', 'PascalCase'],
      },
      {
        selector: ['class', 'interface', 'enum'],
        format: ['PascalCase'],
      },
      {
        selector: 'variable',
        modifiers: ['const'],
        format: ['UPPER_CASE', 'camelCase', 'PascalCase'],
      },
      {
        selector: ['enumMember'],
        format: ['UPPER_CASE'],
      },
    ],
  },
};
