/* eslint-disable */
import FormItemButton from '@/components/common/FormItemButton';
import SCAlert from '@/components/common/SCAlert';
import SCBlockWithOption from '@/components/common/SCBlockWithOption';
import SCCard from '@/components/common/SCCard';
import SCChip from '@/components/common/SCChip';
import SCCollapseCard from '@/components/common/SCCollapseCard';
import SCDrawer from '@/components/common/SCDrawer';
import SCIconButton from '@/components/common/SCIconButton';
import SCModal from '@/components/common/SCModal';
import SCSideMenu from '@/components/common/SCSideMenu';
import SCSimpleCard from '@/components/common/SCSimpleCard';
import SCToggleSwitch from '@/components/common/SCToggleSwitch';
import InputPullDownOutlineIcon from '@/components/icon/InputPullDownOutlineIcon';
import { Add, AddCard } from '@mui/icons-material';
import BrushIcon from '@mui/icons-material/Brush';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import ElectricalServicesIcon from '@mui/icons-material/ElectricalServices';
import EmailOutlinedIcon from '@mui/icons-material/EmailOutlined';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import LayersOutlinedIcon from '@mui/icons-material/LayersOutlined';
import NotesIcon from '@mui/icons-material/Notes';
import SettingsIcon from '@mui/icons-material/Settings';
import { Box, Button, Card, Chip, Container, Divider, Stack, Typography } from '@mui/material';
import Avatar from '@mui/material/Avatar';
import FormControlLabel from '@mui/material/FormControlLabel';
import Grid from '@mui/material/Grid';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import * as React from 'react';

export const ItemTypes = { ITEM: 'item' };

export const ComponentModule: React.FC = () => {
  const [openDrawer, setOpenDrawer] = React.useState(false);
  const [openModal, setOpenModal] = React.useState(false);
  const [chipMenuAnchorEl, setChipMenuAnchorEl] = React.useState<null | HTMLElement>(null);
  const chipMenuOpen = Boolean(chipMenuAnchorEl);

  const handleCloseChipMenu = () => {
    setChipMenuAnchorEl(null);
  };

  const handleClickChipMenu = (event: any) => {
    setChipMenuAnchorEl(event.currentTarget);
  };
  const toggleDrawer = () => {
    setOpenDrawer((state) => !state);
  };

  const toggleModal = () => {
    setOpenModal((state) => !state);
  };

  const handleClickFormItemButton = () => {};

  const handleClickSideMenu = (key: string) => {};

  return (
    <Box>
      <Container>
        <h1>Components</h1>
        <hr />
        <Grid container spacing={2}>
          <Grid item xs={3}>
            <SCCard title="アンケート" />
          </Grid>
          <Grid item xs={3}>
            <SCCard image="https://via.placeholder.com/400x200" title="アンケート" label="Pro" />
          </Grid>
          <Grid item xs={3}>
            <SCCard title="アンケート" label="Pro" icon={Add} cardStyle="dashed" />
          </Grid>
        </Grid>
        <hr />
        <SCCollapseCard title="問い合わせ管理：全てのフォーム" titleSize="large">
          <div>The content</div>
        </SCCollapseCard>
        <hr />
        <SCCollapseCard title="アンケート" icon={Add}>
          <div>The content</div>
        </SCCollapseCard>
        <hr />
        <SCCollapseCard title="アンケート" icon={Add} defaultOpen={false}>
          <div>The content always hidden</div>
        </SCCollapseCard>
        <hr />
        <SCSimpleCard>asdasD</SCSimpleCard>
        <hr />
        <SCSimpleCard>
          <Stack direction="column" justifyContent="center" alignItems="center" spacing={2} p={3}>
            <Typography variant="h3" fontWeight="bold">
              フォームはまだありません
            </Typography>
            <Typography variant="body1">空白のフォームを選択するか、上で別のテンプレートを選択して作成を開始してください</Typography>
          </Stack>
        </SCSimpleCard>
        <Divider sx={{ my: 3 }}>
          <Chip label="Breadcrumbs" size="small" />
        </Divider>
        <Divider sx={{ my: 3 }}>
          <Chip label="Form Edit Button" size="small" />
        </Divider>
        <Card sx={{ p: 2 }}>
          <Stack direction="row" spacing={2}>
            <FormItemButton icon={NotesIcon} onClick={handleClickFormItemButton} />
            <FormItemButton icon={AddCard} onClick={handleClickFormItemButton} />
            <FormItemButton icon={NotesIcon} onClick={handleClickFormItemButton} />
            <FormItemButton icon={InputPullDownOutlineIcon} onClick={handleClickFormItemButton} />
          </Stack>
        </Card>
        <Divider sx={{ my: 3 }}>
          <Chip label="Side menu" size="small" />
        </Divider>
        <SCSimpleCard
          sx={{
            bgcolor: 'grey.50',
          }}
        >
          <SCSideMenu
            items={[
              { key: 'layer', icon: LayersOutlinedIcon },
              { key: 'brush', icon: BrushIcon },
              { key: 'mail', icon: EmailOutlinedIcon },
              { key: 'calendar', icon: CalendarMonthIcon },
              { key: 'setting', icon: SettingsIcon },
              { key: 'electrical', icon: ElectricalServicesIcon },
              { key: 'help', icon: HelpOutlineIcon },
            ]}
            onClick={handleClickSideMenu}
          />
        </SCSimpleCard>
        <Divider sx={{ my: 3 }}>
          <Chip label="Block with options" size="small" />
        </Divider>
        <SCSimpleCard
          sx={{
            bgcolor: 'grey.50',
          }}
        >
          <SCBlockWithOption title="ヘッダー">
            <Box>
              <Box p={1}>adasd</Box>
              <Divider />
              <Box p={1}>adasd</Box>
            </Box>
          </SCBlockWithOption>
        </SCSimpleCard>
        <Divider sx={{ my: 3 }}>
          <Chip label="Toogle Switch" size="small" />
        </Divider>
        <SCSimpleCard>
          <Stack spacing={2} direction="row" flexWrap="wrap">
            <FormControlLabel control={<SCToggleSwitch sx={{ m: 1 }} defaultChecked />} label="Toggle Switch" />
            <FormControlLabel control={<SCToggleSwitch sx={{ m: 1 }} />} label="Toggle Switch OFF" />
            <FormControlLabel control={<SCToggleSwitch sx={{ m: 1 }} />} label="Toggle Switch OFF" />
            <FormControlLabel control={<SCToggleSwitch sx={{ m: 1 }} />} label="Toggle Switch OFF" />
            <FormControlLabel control={<SCToggleSwitch sx={{ m: 1 }} />} label="Toggle Switch OFF" />
            <FormControlLabel control={<SCToggleSwitch sx={{ m: 1 }} />} label="Toggle Switch OFF" />
            <FormControlLabel control={<SCToggleSwitch sx={{ m: 1 }} />} label="Toggle Switch OFF" />
            <FormControlLabel control={<SCToggleSwitch sx={{ m: 1 }} />} label="Toggle Switch OFF" />
          </Stack>
        </SCSimpleCard>
        <Divider sx={{ my: 3 }}>
          <Chip label="Drawer & Alert" size="small" />
        </Divider>
        <SCSimpleCard>
          <SCAlert title="This is title props" color="warning">
            Content of Alert
          </SCAlert>
          <FormControlLabel control={<SCToggleSwitch sx={{ m: 1 }} checked={openDrawer} onChange={toggleDrawer} />} label="Open Drawer" />
          <SCDrawer
            position="right"
            isOpen={openDrawer}
            onClose={toggleDrawer}
            primaryAction={
              <>
                <Button color="secondary" startIcon={<AddCard />}>
                  Send mail
                </Button>
              </>
            }
            secondaryAction={
              <SCIconButton>
                <AddCard />
              </SCIconButton>
            }
          >
            <SCAlert title="未対応" color="warning">
              このチケットは未対応です
            </SCAlert>
          </SCDrawer>
        </SCSimpleCard>
        <Divider sx={{ my: 3 }}>
          <Chip label="Modal" size="small" />
        </Divider>
        <SCSimpleCard>
          <FormControlLabel control={<SCToggleSwitch sx={{ m: 1 }} checked={openModal} onChange={toggleModal} />} label="Open Modal" />
          <SCModal
            title="リンクをシェア/サイトに埋め込む"
            isOpen={openModal}
            onClose={toggleModal}
            width={600}
            primaryAction={
              <>
                <Button variant="contained" startIcon={<AddCard />}>
                  Send mail
                </Button>
              </>
            }
          >
            Content of Modal
          </SCModal>
        </SCSimpleCard>
        <Divider sx={{ my: 3 }}>
          <Chip label="Chip" size="small" />
        </Divider>
        <SCSimpleCard>
          <Stack spacing={2} direction="row">
            <SCChip size="small" label="対応完了" />
            <SCChip size="small" label="返信待ち" color="success" />
            <SCChip size="small" label="未対応" color="info" />
            <SCChip size="small" label="開発対応が必要" color="warning" />
            <SCChip size="small" label="開発対応が必要" color="error" />
            <SCChip size="small" label="山田太郎" avatar={<Avatar>TY</Avatar>} />
            <SCChip size="small" label="未対応 Clickable" color="info" onClick={handleClickChipMenu} />
            <Menu
              anchorEl={chipMenuAnchorEl}
              open={chipMenuOpen}
              onClose={handleCloseChipMenu}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'left',
              }}
            >
              <MenuItem onClick={handleCloseChipMenu} sx={{ textWrap: 'wrap', width: 250 }}>
                <Box>
                  <Typography variant="body1">非公開</Typography>
                  <Typography variant="caption" paragraph={true}>
                    編集中はこのステースを維持してください
                  </Typography>
                </Box>
              </MenuItem>
              <MenuItem onClick={handleCloseChipMenu} sx={{ textWrap: 'wrap', width: 250 }}>
                <Box>
                  <Typography variant="body1">公開予約</Typography>
                  <Typography variant="caption">フォームのURLが生成され、公開時間まで投稿を制限できます</Typography>
                </Box>
              </MenuItem>
            </Menu>
          </Stack>
        </SCSimpleCard>
        <Divider sx={{ my: 3 }}>
          <Chip label="Chip" size="small" />
        </Divider>
        <Card sx={{ mt: 4, p: 3 }}>
          <Typography variant="h5" fontWeight="bold" textAlign="center">
            END
          </Typography>
        </Card>
      </Container>
    </Box>
  );
};
