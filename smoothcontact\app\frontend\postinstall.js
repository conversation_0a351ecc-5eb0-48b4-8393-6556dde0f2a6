import fse from 'fs-extra';
import path from 'path';

const topDir = import.meta.dirname;
fse.emptyDirSync(path.join(topDir, 'public', 'tinymce'));
fse.copySync(path.join(topDir, 'node_modules', 'tinymce'), path.join(topDir, 'public', 'tinymce'), { overwrite: true });
// copy src/tinymce/langs to public/tinymce/langs
fse.copySync(path.join(topDir, 'src', 'tinymce', 'langs'), path.join(topDir, 'public', 'tinymce', 'langs'), { overwrite: true });
