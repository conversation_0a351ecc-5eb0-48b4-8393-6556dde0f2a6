export function validateNumber(value?: string) {
  const regEx = /^(\s*|\d+)$/;

  return regEx.test(value || '');
}

export function checkMax(maxVal: number) {
  return (value?: string) => {
    if (!value) {
      return true;
    }

    return Number(value) <= maxVal;
  };
}

export function validateInternalEmail(value?: string) {
  if (!value) {
    return false;
  }

  const emailSplits = value.split('@');

  if (emailSplits.length != 2) {
    return false;
  }

  return true;
}

export function isFullSize(value?: string) {
  if (!value) {
    return true;
  }

  // eslint-disable-next-line no-irregular-whitespace
  const regexp = new RegExp(/^[ａ-ｚＡ-Ｚ０-９ぁ-んァ-ン一-龥　]+$/);

  return regexp.test(value);
}

export function isHalfSize(value?: string) {
  if (!value) {
    return true;
  }

  // eslint-disable-next-line no-irregular-whitespace
  const regexp = new RegExp(/^[^ａ-ｚＡ-Ｚ０-９ぁ-んァ-ン一-龥　]+$/);

  return regexp.test(value);
}

export function isHalfSizeAlphanumeric(value?: string) {
  if (!value) {
    return true;
  }

  const regexp = new RegExp(/^[a-zA-Z0-9]+$/);

  return regexp.test(value);
}

export function checkFieldErrorHelper<Values>(form: any, fieldName: keyof Values, disableCheck?: boolean) {
  if (disableCheck) {
    return;
  }

  return form?.errors[fieldName];
}

export function isEmailHalfSize(value?: string) {
  if (!value) {
    return true;
  }

  const regexp = new RegExp(/^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/);

  return regexp.test(value);
}
