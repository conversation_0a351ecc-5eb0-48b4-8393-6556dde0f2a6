import { Body, Controller, HttpCode, Post, Request, UseGuards } from '@nestjs/common';

import { BaseController } from '@/core/controllers/api.controller';
import { AuthGuard } from '@/core/guard/auth.guard';
import { IApplyBindResponseDto, IBiNDSetting } from '@/modules/form/dto/bind.dto';
import { FormService } from '@/modules/form/form.service';
import { FormBuilderEntity } from '@/modules/form-builder/entities/form-builder.entity';
import { APIResponseBase } from '@/types/response.type';

@UseGuards(AuthGuard)
@Controller('form')
export class FormController extends BaseController {
  constructor(private readonly formService: FormService) {
    super();
  }

  @Post('/list/applyBiND')
  @HttpCode(200)
  async applyBiND(@Request() request: Request, @Body('scFormId') scFormId: number): Promise<APIResponseBase<IApplyBindResponseDto>> {
    const formBuilder = await this.formService.getFormBuilderById(scFormId, request.user.id);
    if (!formBuilder) {
      this.failResponse({
        data: {
          url: '',
        },
      });
    }

    const biNDSetting: IBiNDSetting = {
      widthPc: '80',
      widthUnitPc: '2',
      heightPc: 625,
      widthSp: '100',
      widthUnitSp: '2',
      heightSp: 740,
      autoResize: '1',
      url: this.getFormUrl(formBuilder),
      formid: scFormId,
    };

    return this.successResponse({
      success: true,
      data: {
        url: this.generateBiNDCallbackUrl(request.user.callbackUrl || '', biNDSetting),
      },
    });
  }

  protected getFormUrl(formBuilder: FormBuilderEntity): string {
    return process.env.APP_URL + '/front/output/' + formBuilder.extId + '?bdsite=1';
  }

  protected generateBiNDCallbackUrl(callbackUrl: string, biNDSetting: IBiNDSetting): string {
    const biNDSettingString = JSON.stringify(biNDSetting);

    return callbackUrl + '&s=' + encodeURIComponent(biNDSettingString);
  }
}
