import { RootLayout } from '@/containers/root';
import { AxiosProvider } from '@/provider/axiosProvider';
import { useAppSelector } from '@/store/hook';
import { removeAppAuthTokens } from '@/utils/helper';
import { Box, CssBaseline } from '@mui/material';
import { ReactNode } from 'react';
import { createSearchParams, useNavigate } from 'react-router-dom';
import ResponsiveAppBar from '../header';

export const AdminLayout = ({ children, layoutLess = false }: { children: ReactNode; layoutLess: boolean }) => {
  const navigate = useNavigate();
  const { authLoaded, profile } = useAppSelector((store) => ({
    authLoaded: store.app.authLoaded,
    profile: store.app.profile,
  }));

  const logout = () => {
    removeAppAuthTokens();
    let redirectTarget: string | object = '/login';

    if (profile.callbackUrl && profile.setting) {
      const urlQuery = createSearchParams({ fromOem: 'true', cb: profile.callbackUrl || '', s: JSON.stringify(profile.setting) });
      redirectTarget = {
        pathname: '/login',
        search: urlQuery.toString(),
      };
    }

    setTimeout(() => {
      navigate(redirectTarget);
    }, 100);
  };

  if (!authLoaded) {
    return null;
  }

  return (
    <AxiosProvider>
      <RootLayout>
        <Box component="main">
          {!layoutLess && <ResponsiveAppBar logout={logout} />}
          {!layoutLess && <CssBaseline />}
          <Box>{children}</Box>
        </Box>
      </RootLayout>
    </AxiosProvider>
  );
};
