import SCPageTitle from '@/components/common/SCPageTitle';
import { AdminLayout } from '@/components/layout/admin';
import { AuthLayout } from '@/components/layout/auth';
import { PublicLayout } from '@/components/layout/public';
import { ConfirmProvider } from '@/provider/confirmProvider';
import { LayoutType } from '@/types/app';
import { Route, RouterProvider, createBrowserRouter, createRoutesFromElements } from 'react-router-dom';
import PrivateRoute from './privateRoute';

const BLOCKED_PAGES = ['register/index', 'reset-password/index', 'change-password/index'];

export default function Routes({ pages }: { pages: Record<string, unknown> }) {
  return (
    <ConfirmProvider>
      <RouterProvider
        router={router(pages)}
        future={{
          v7_startTransition: true,
        }}
      />
    </ConfirmProvider>
  );
}

export const router = (pages: any) => {
  const routes = useRoutes(pages);
  const publicRouteComponents = routes
    .filter(({ layoutType }) => layoutType === LayoutType.PUBLIC)
    .map(({ path, component: Component, layoutLess, title }) => {
      const element = layoutLess ? (
        <Component />
      ) : (
        <PublicLayout>
          <SCPageTitle title={title} />
          <Component />
        </PublicLayout>
      );

      return <Route key={path} path={path} element={element} />;
    });
  const authRouteComponents = routes
    .filter(({ layoutType }) => layoutType === LayoutType.AUTH)
    .map(({ path, component: Component, title }) => (
      <Route
        key={path}
        path={path}
        element={
          <AuthLayout>
            <SCPageTitle title={title} />
            <Component />
          </AuthLayout>
        }
      />
    ));
  const privateRouteComponents = routes
    .filter(({ layoutType }) => layoutType === LayoutType.PRIVATE)
    .map(({ path, component: Component, layoutLess, title }) => {
      const element = (
        <AdminLayout layoutLess={layoutLess}>
          <SCPageTitle title={title} />
          <Component />
        </AdminLayout>
      );

      return <Route key={path} path={path} element={element} />;
    });

  const NotFound = routes.find(({ path }) => path === '/404')?.component;

  return createBrowserRouter(
    createRoutesFromElements(
      <Route path="/">
        {publicRouteComponents}
        {authRouteComponents}
        <Route element={<PrivateRoute />}>{privateRouteComponents}</Route>
        <Route path="*" element={<NotFound />} />
      </Route>
    ),
    {
      future: {
        v7_relativeSplatPath: true,
        v7_fetcherPersist: true,
        v7_skipActionErrorRevalidation: true,
        v7_normalizeFormMethod: true,
        v7_partialHydration: true,
      },
    }
  );
};

function useRoutes(pages: Record<string, any>) {
  const routes = Object.keys(pages)
    .filter((key) => !BLOCKED_PAGES.includes(key.replace('./pages/', '').replace(/\.(t|j)sx?$/, '')))
    .map((key) => {
      let path = key
        .replace('./pages', '')
        .replace(/\.(t|j)sx?$/, '')
        /**
         * Replace /index with /
         */
        .replace(/\/index$/i, '/')
        /**
         * Only lowercase the first letter. This allows the developer to use camelCase
         * dynamic paths while ensuring their standard routes are normalized to lowercase.
         */
        .replace(/\b[A-Z]/, (firstLetter) => firstLetter.toLowerCase())
        /**
         * Convert /[handle].jsx and /[...handle].jsx to /:handle.jsx for react-router-dom
         */
        .replace(/\[(?:[.]{3})?(\w+?)\]/g, (_match, param) => `:${param}`);

      if (path.endsWith('/') && path !== '/') {
        path = path.substring(0, path.length - 1);
      }

      if (!pages[key].default) {
        console.warn(`${key} doesn't export a default React component`);
      }

      return {
        path,
        layoutType: pages[key]?.default?.layoutType ?? LayoutType.PUBLIC,
        layoutLess: pages[key]?.default?.layoutLess ?? false,
        component: pages[key].default,
        title: pages[key]?.default?.title ?? '',
      };
    })
    .filter((route) => route.component);

  return routes;
}
