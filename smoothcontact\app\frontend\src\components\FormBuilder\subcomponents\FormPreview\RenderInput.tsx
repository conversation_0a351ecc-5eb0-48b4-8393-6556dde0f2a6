import { FormBorderColors } from '@/common/constants';
import { FormElementChildrenType } from '@/types/FormTemplateTypes';
import { FormControlNames, FormInputAnimationTypes } from '@/utils/formBuilderUtils';
import { TextField, Typography } from '@mui/material';
import FocusInInputComponent from '../FormColorSetting/AnimationCustom/text/FocusInInputComponent';
import LabelTopInputComponent from '../FormColorSetting/AnimationCustom/text/LabelTopInputComponent';
import { RenderItemProps } from './RenderItem';

const renderInput = (
  inputAnimationType: string,
  item: FormElementChildrenType,
  { isError, helperText, value, onChange, onBlur }: RenderItemProps,
  classes: any,
  entryFormSetting?: any
) => {
  const commonProps = {
    name: item.id,
    className: classes,
    type: item.dataType,
    error: isError,
    helperText,
    value,
    onChange,
    onBlur,
    multiline: item.controlName === FormControlNames.INPUT_MULTILINE,
    minRows: item.controlName === FormControlNames.INPUT_MULTILINE ? item.rows : undefined,
    background: entryFormSetting?.bgColor ?? '#FFFFFF',
    borderColor: entryFormSetting?.borderColor ?? FormBorderColors.DEFAULT,
    inputProps: { maxLength: item?.max ?? undefined },
  };
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { borderColor, background, ...newCommonProps } = commonProps;

  switch (inputAnimationType) {
    case FormInputAnimationTypes.LABEL_TOP:
      return (
        <>
          <LabelTopInputComponent {...commonProps} label={item.placeholder} />
          {item?.max > 0 && <Typography align="right">{`${value.toString().length}/${item.max}`}</Typography>}
        </>
      );

    case FormInputAnimationTypes.LINE_COLOR:
      return (
        <>
          <TextField
            {...newCommonProps}
            placeholder={item.placeholder}
            fullWidth
            variant="standard"
            sx={{
              '& .MuiInputBase-root': {
                background: 'transparent!important',
              },
            }}
          />
          {item?.max > 0 && <Typography align="right">{`${value.toString().length}/${item.max}`}</Typography>}
        </>
      );

    case FormInputAnimationTypes.FOCUS_IN:
      return (
        <>
          <FocusInInputComponent {...commonProps} placeholder={item.placeholder} />
          {item?.max > 0 && <Typography align="right">{`${value.toString().length}/${item.max}`}</Typography>}
        </>
      );

    default:
      return (
        <>
          <TextField {...newCommonProps} placeholder={item.placeholder} fullWidth variant="outlined" size="small" />
          {item?.max > 0 && <Typography align="right">{`${value.toString().length}/${item.max}`}</Typography>}
        </>
      );
  }
};

export default renderInput;
