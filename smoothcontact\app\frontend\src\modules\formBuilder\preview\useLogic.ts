import useAxios, { ApiResponse } from '@/hooks/useAxios';
import { useToast } from '@/provider/toastProvider';
import { getRequestConfig } from '@/services/form-builder.service';
import { TemplateType } from '@/types/FormTemplateTypes';
import { FormTemplateModePresentType, FormTemplateModePresets } from '@/utils/formBuilderUtils';
import { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

export default function useLogic() {
  const { toast } = useToast();
  const [data, setData] = useState<TemplateType | null>(null);
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const { apiCaller, loading } = useAxios<TemplateType>();
  const { id: extId } = useParams();

  useEffect(() => {
    const link1 = document.createElement('link');
    link1.rel = 'stylesheet';
    link1.href = 'https://fonts.fontplus.dev/v1/css/jOW3USBm';
    document.head.appendChild(link1);

    const link2 = document.createElement('link');
    link2.rel = 'stylesheet';
    link2.href = 'https://fonts.fontplus.dev/v1/css/1YLRAG6A';
    document.head.appendChild(link2);

    const link3 = document.createElement('link');
    link3.rel = 'stylesheet';
    link3.href = 'https://fonts.fontplus.dev/v1/css/t8EwrwXo';
    document.head.appendChild(link3);

    // Cleanup: Remove the link tag when the component is unmounted
    return () => {
      document.head.removeChild(link1);
      document.head.removeChild(link2);
      document.head.removeChild(link3);
    };
  }, []);

  const get = async (extId: string) => {
    const { data, success, error }: ApiResponse<any> = await apiCaller(getRequestConfig(extId));

    if (!success) {
      toast({
        isError: true,
        message: error,
      });

      return;
    }

    setData(data);
  };

  const handleViewer = (mode: 'desktop' | 'tablet' | 'mobile') => {
    setViewMode(mode);
  };

  useEffect(() => {
    if (!extId) {
      return;
    }

    get(extId);
  }, [extId]);

  const isTemplateMode = useMemo(() => {
    return data?.formColorSetting?.optionMode === 'template_mode';
  }, [data]);

  const isBlackModeColor = useMemo(() => {
    return data?.formColorSetting?.templateModeColor === FormTemplateModePresentType.BLACK;
  }, [data]);

  const formTemplateModePreset = useMemo(() => {
    return FormTemplateModePresets[data?.formColorSetting?.templateModeColor ?? FormTemplateModePresentType.BASIC];
  }, [data?.formColorSetting?.templateModeColor]);

  return {
    data,
    loading,
    viewMode,
    handleViewer,
    isTemplateMode,
    isBlackModeColor,
    formTemplateModePreset,
  };
}
