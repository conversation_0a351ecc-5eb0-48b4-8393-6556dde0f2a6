import { Expose } from 'class-transformer';

import { Course } from '../entities/account.entity';

export class RegisterResponseDTO {
  @Expose()
  email: string;

  @Expose()
  shop: string;

  @Expose()
  name: string;
}

export class LoginResponseDTO {
  @Expose()
  accessToken?: string;

  @Expose()
  refreshToken?: string;

  @Expose()
  loginType?: string;

  @Expose()
  account?: { id?: number; mfaEnabled?: boolean };
}

export class ProfileResponseDTO {
  @Expose()
  id: string;

  @Expose()
  email: string;

  @Expose()
  name: string;

  @Expose()
  shop: string;

  @Expose()
  tourCompleted?: boolean;

  @Expose()
  avatar?: string;

  @Expose()
  isVerifiedMfa?: boolean;

  @Expose()
  course: Course;

  @Expose()
  base32?: string;

  @Expose()
  qrCode?: string;

  @Expose()
  callbackUrl?: string;

  @Expose()
  setting?: string;

  @Expose()
  oemId?: string;

  @Expose()
  oemgo?: string;

  @Expose()
  expirationDate?: string;
}

export class CheckHmacResult {
  @Expose()
  host: string;

  @Expose()
  session: string;

  @Expose()
  shop: string;

  @Expose()
  timestamp: number;
}

export class ResetPasswordResponseDTO {
  @Expose()
  name: string;

  @Expose()
  resetToken: string;
}

export class ShopifyAuthResponseDTO {
  @Expose()
  email: string;

  @Expose()
  shop: string;
}
