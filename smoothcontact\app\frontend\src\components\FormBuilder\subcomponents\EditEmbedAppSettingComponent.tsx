import SCChip from '@/components/common/SCChip';
import SwitchStyled from '@/components/common/SCToggleSwitch';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormEmbedAppSetting } from '@/types/FormTemplateTypes';
import { SettingFlag } from '@/utils/formBuilderUtils';
import { Box, FormLabel, Stack, TextField } from '@mui/material';
import Divider from '@mui/material/Divider';
import { FC, useEffect } from 'react';
import * as Yup from 'yup';
import { useTranslation } from 'react-i18next';

interface EditEmbedAppSettingComponentProps {}

const EditEmbedAppSettingComponent: FC<EditEmbedAppSettingComponentProps> = () => {
  const { selectedTemplate, formEmbedAppSetting, editEmbedAppSetting, setError, isFormChanged } = useFormBuilder();
  const { t } = useTranslation();

  const validationSchema = Yup.object<FormEmbedAppSetting>({
    isEnableGA4Setting: Yup.boolean().optional(),
    gA4TrackingID: Yup.string().when('isEnableGA4Setting', {
      is: (isEnableGA4Setting: boolean) => isEnableGA4Setting,
      then: (schema) =>
        schema.required(
          t('validation.required', {
            field: '測定ID',
          })
        ),
    }),
    isEnableGoogleAdsSetting: Yup.boolean().optional(),
    eventSnippet: Yup.string().when('isEnableGoogleAdsSetting', {
      is: (isEnableGoogleAdsSetting: boolean) => isEnableGoogleAdsSetting,
      then: (schema) => schema.required('イベントスニペットを入力してください'),
    }),
    isLinkageYahoo: Yup.boolean().optional(),
    conversionMeasurementTags: Yup.string().when('isLinkageYahoo', {
      is: (isLinkageYahoo: boolean) => isLinkageYahoo,
      then: (schema) => schema.required('コンバージョン測定タグを入力してください'),
    }),
  });

  const form = useFormHandler<FormEmbedAppSetting>({
    initialValues: {
      isEnableGA4Setting: formEmbedAppSetting?.isEnableGA4Setting,
      gA4TrackingID: formEmbedAppSetting?.gA4TrackingID,
      isEnableGoogleAdsSetting: formEmbedAppSetting?.isEnableGoogleAdsSetting,
      eventSnippet: formEmbedAppSetting?.eventSnippet,
      isLinkageYahoo: formEmbedAppSetting?.isLinkageYahoo,
      conversionMeasurementTags: formEmbedAppSetting?.conversionMeasurementTags,
    },
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: (e) => {
      console.log('submit', e);
    },
  });

  useEffect(() => {
    if (form.isValid) {
      editEmbedAppSetting(form.values);
    }

    setError(!form.isValid);
  }, [form.isValid, form.values]);

  useEffect(() => {
    if (!isFormChanged) {
      form.resetForm({
        values: selectedTemplate?.formEmbedAppSetting,
      });
    }
  }, [isFormChanged]);

  return (
    <Stack direction="column" spacing={2}>
      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'left', alignItems: 'center' }}>
        <FormLabel sx={{ pl: 1 }}>Google Analytics連携</FormLabel>
        <SCChip
          size="small"
          label={form?.values?.isEnableGA4Setting ? SettingFlag.enabled : SettingFlag.disabled}
          color={form?.values?.isEnableGA4Setting ? 'success' : 'default'}
        />
      </Box>

      <FormLabel sx={{ pl: 1 }} component="legend">
        トラッキングIDを設定することで、フォームの表示回数や送信完了回数、利用動向を測定します
      </FormLabel>
      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'left', alignItems: 'center' }}>
        <SwitchStyled checked={form?.values?.isEnableGA4Setting} name="isEnableGA4Setting" {...form.register('isEnableGA4Setting')} />
        <FormLabel sx={{ pl: 1 }}>設定は有効です</FormLabel>
      </Box>
      <TextField
        name="gA4TrackingID"
        label="測定ID"
        value={form.values.gA4TrackingID}
        error={!!form.errors.gA4TrackingID}
        helperText={form.errors.gA4TrackingID}
        {...form.register('gA4TrackingID')}
      />
      <Divider />

      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'left', alignItems: 'center' }}>
        <FormLabel sx={{ pl: 1 }}>Google Ads連携</FormLabel>
        <SCChip
          size="small"
          label={form?.values?.isEnableGoogleAdsSetting ? SettingFlag.enabled : SettingFlag.disabled}
          color={form?.values?.isEnableGoogleAdsSetting ? 'success' : 'default'}
        />
      </Box>

      <FormLabel sx={{ pl: 1 }} component="legend">
        トラッキングコードを設定することで、送信完了メッセージが表示された回数をコンバージョン件数として計測できます
      </FormLabel>
      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'left', alignItems: 'center' }}>
        <SwitchStyled
          checked={form?.values?.isEnableGoogleAdsSetting}
          name="isEnableGoogleAdsSetting"
          {...form.register('isEnableGoogleAdsSetting')}
        />
        <FormLabel sx={{ pl: 1 }}>設定は有効です</FormLabel>
      </Box>

      <TextField
        multiline={true}
        rows={4}
        name="eventSnippet"
        label="イベントスニペット"
        value={form.values.eventSnippet}
        error={!!form.errors.eventSnippet}
        helperText={form.errors.eventSnippet}
        {...form.register('eventSnippet')}
      />
      <Divider />

      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'left', alignItems: 'center' }}>
        <FormLabel sx={{ pl: 1 }}>Yahoo!プロモーション広告連携</FormLabel>
        <SCChip
          size="small"
          label={form?.values?.isLinkageYahoo ? SettingFlag.enabled : SettingFlag.disabled}
          color={form?.values?.isLinkageYahoo ? 'success' : 'default'}
        />
      </Box>

      <FormLabel sx={{ pl: 1 }} component="legend">
        トラッキングコードを設定することで、送信完了メッセージが表示された回数をコンバージョン件数として計測できます
      </FormLabel>
      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'left', alignItems: 'center' }}>
        <SwitchStyled checked={form?.values?.isLinkageYahoo} name="isLinkageYahoo" {...form.register('isLinkageYahoo')} />
        <FormLabel sx={{ pl: 1 }}>設定は有効です</FormLabel>
      </Box>
      <TextField
        multiline={true}
        rows={4}
        name="conversionMeasurementTags"
        label="コンバージョン測定タグ"
        value={form.values.conversionMeasurementTags}
        error={!!form.errors.conversionMeasurementTags}
        helperText={form.errors.conversionMeasurementTags}
        {...form.register('conversionMeasurementTags')}
      />
      <Divider />
    </Stack>
  );
};

export default EditEmbedAppSettingComponent;
