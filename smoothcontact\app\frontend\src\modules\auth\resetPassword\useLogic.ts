import useAxios from '@/hooks/useAxios';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useToast } from '@/provider/toastProvider';
import { resetPasswordRequestConfig } from '@/services/account.service';
import { ResetPasswordRequestDTO } from './dto/request.dto';
import { validationSchema } from './validator';
import { HttpStatusCode } from 'axios';

export type FormValue = {
  email: string;
};

export default function useLogic() {
  const { toast } = useToast();
  const { apiCaller, loading } = useAxios();

  const resetPassword = async (formData: FormValue) => {
    const dataBody = new ResetPasswordRequestDTO();
    dataBody.email = formData.email;

    const result: any = await apiCaller(resetPasswordRequestConfig(dataBody));

    if (result?.success) {
      toast({ isError: false, message: 'パスワード再設定のメールを送信しました。' });
      formHandler.resetForm({ values: { email: '' } });

      return;
    }

    if (result?.statusCode !== HttpStatusCode.BadRequest) {
      toast({ isError: true, message: result.message });
    }

    formHandler.setErrors(result.messageErrors ?? {});
  };

  const formHandler = useFormHandler<FormValue>({
    initialValues: { email: '' },
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: async (values) => {
      await resetPassword(values);
    },
  });

  return { resetPassword, loading, formHandler };
}
