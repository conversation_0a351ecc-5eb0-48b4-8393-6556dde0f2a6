import RenderItemWithLayout from '@/components/common/RenderItemWithLayout';
import FormElementTitle from '@/components/FormBuilder/subcomponents/FormElementTitle';
import RenderItem from '@/components/FormBuilder/subcomponents/FormPreview/RenderItem';
import FormPrivacyAndTerms from '@/components/FormBuilder/subcomponents/FormPrivacyAndTerms';
import useFormStyles from '@/hooks/useFormStyle';
import { FormColorSetting, FormElement, FormElementChildrenType, FormItemValue, GeneralSetting } from '@/types/FormTemplateTypes';
import { ISO } from '@/utils/dateTime';
import { FormControlInitialValue, FormControlNames } from '@/utils/formBuilderUtils';
import { Box, Container, Stack } from '@mui/material';
import dayjs from 'dayjs';
import { useFormik } from 'formik';
import { debounce } from 'lodash';
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import * as Yup from 'yup';

interface FormStandaloneProps {
  formElements: FormElement[];
  generalSetting?: GeneralSetting;
  colorSetting: FormColorSetting;
  screenType?: string;
  initialValues?: Record<string, FormItemValue>;
  isPreview?: boolean;
  onSubmit?: (values: Record<string, FormItemValue>) => void;
}

const FormStandalone: FC<FormStandaloneProps> = ({ formElements, generalSetting, colorSetting, initialValues, isPreview, onSubmit }) => {
  const { t } = useTranslation();
  const formElement = formElements?.[0];
  const [formPreventSubmit, setFormPreventSubmit] = useState(false);
  const formElementChildren = formElement?.children || [];
  const formSpacing = `${colorSetting?.generalSettings?.spacing || 0}${colorSetting?.generalSettings?.spacingUnit}`;
  const { classes } = useFormStyles({ colorSetting, isSettingPreview: isPreview });
  const formInitialValues = useMemo(() => {
    const privacyAndTermsInitialValues = {
      agreePrivacyAndTerms: false,
    };

    return formElementChildren.reduce(
      (acc: Record<string, any>, child) => {
        const defaultValue = child.required ? null : FormControlInitialValue[child.controlName] || '';

        acc[child.id] = initialValues?.[child.id] || defaultValue;

        return acc;
      },
      {
        ...privacyAndTermsInitialValues,
      }
    );
  }, [formElementChildren, initialValues]);

  const [formInternalValues, setFormInternalValues] = useState(formInitialValues);

  const isShowFormElement = useCallback(
    (element: FormElementChildrenType) => {
      if (!element.parentId || element.level === 0) return true;

      const parent = formElementChildren.find((child) => child?.id?.toString() === element?.parentId?.toString());

      if (!parent) return true;

      switch (parent.controlName) {
        case FormControlNames.CHECKLIST:

        case FormControlNames.RADIO:

        case FormControlNames.DROPDOWN:
          const checklist = (Array.isArray(formInternalValues[parent.id]) ? formInternalValues[parent.id] : [formInternalValues[parent.id]]) || [];

          return Array.isArray(element?.condition)
            ? element?.condition?.every?.((cond) => checklist.includes(cond))
            : checklist.includes(element?.condition);

        default:
          const value = formInternalValues[parent.id] as string;

          return element.condition?.includes(value);
      }
    },
    [formInternalValues, formElementChildren]
  );

  const getPrivacyAndTermsValidation = () => {
    if (!generalSetting?.isSettingPrivacyPolicy && !generalSetting?.isDisplayTermsUse) {
      return null;
    }

    if (generalSetting?.isSettingPrivacyPolicy && generalSetting?.isDisplayTermsUse) {
      return {
        agreePrivacyAndTerms: Yup.boolean().oneOf([true], 'プライバシーポリシーと利用規約に同意してください'),
      };
    }

    if (generalSetting?.isSettingPrivacyPolicy) {
      return {
        agreePrivacyAndTerms: Yup.boolean().oneOf([true], 'プライバシーポリシーに同意してください'),
      };
    }

    if (generalSetting?.isDisplayTermsUse) {
      return {
        agreePrivacyAndTerms: Yup.boolean().oneOf([true], '利用規約に同意してください'),
      };
    }
  };

  const isValidDate = (date: string, maxAge?: number) => {
    const dateObj = dayjs(date, ISO.DATE);
    if (!dateObj.isValid()) {
      return false;
    }

    if (maxAge) {
      const diff = dayjs().diff(dateObj, 'year', true);
      if (diff < maxAge) {
        return false;
      }
    }

    return true;
  };

  const validationObject = useMemo(() => {
    const privacyAndTermsValidation = getPrivacyAndTermsValidation();

    return formElementChildren.reduce(
      (acc: Record<string, any>, child) => {
        if (isShowFormElement(child) === false) return acc;

        switch (child.controlName) {
          case FormControlNames.ADDRESS:
            acc[child.id] = Yup.object();
            break;

          case FormControlNames.FULL_NAME:
            acc[child.id] = Yup.object();
            break;

          case FormControlNames.CHECKLIST:
            acc[child.id] = Yup.array().nullable();
            break;

          case FormControlNames.PHONE:
            acc[child.id] = Yup.string().matches(/^(0\d{1,4})-(\d{1,4})-(\d{4})$|^(0\d{1,4})(\d{8})$/, '無効な電話番号');
            break;

          case FormControlNames.INPUT_TEXTFIELD:
            if (child.characterType === 'number') {
              acc[child.id] = Yup.string().matches(/^\d+$/, 'このフィールドは数字のみで入力してください。');
            } else if (child.characterType === 'alphanumeric') {
              acc[child.id] = Yup.string().matches(/^[a-zA-Z0-9]*$/, 'このフィールドは英数字のみで入力してください。');
            } else {
              acc[child.id] = Yup.string();
            }

            break;

          case FormControlNames.DATE:
            let dateFormat;
            switch (child?.dataType) {
              case 'date':
                dateFormat = ISO.DATE;
                acc[child.id] = Yup.string();
                break;

              case 'time':
                dateFormat = ISO.TIME;
                acc[child.id] = Yup.string();
                break;

              default:
                dateFormat = ISO.DATE_TIME;
                acc[child.id] = Yup.string();
                break;
            }

            if (child.dateLimit && child.dateLimit !== 'none') {
              acc[child.id] = Yup.string().test('invalid', '入力された時間は無効です', (value) => {
                if (!value) return true;

                const inputTime = dayjs(value, dateFormat);
                const currentTime = dayjs();

                return child.dateLimit === 'future' ? inputTime.isAfter(currentTime) : inputTime.isBefore(currentTime);
              });
            }

            break;

          case FormControlNames.FILE_UPLOAD:
            acc[child.id] = !child?.required ? Yup.object().nullable() : Yup.object();
            break;

          case FormControlNames.BIRTHDAY:
            acc[child.id] = Yup.string().test('invalid', '年齢条件が満たしていません。', (value) => {
              if (!child.limitedAgeRequired) {
                return true;
              }

              return isValidDate(value, child.limitedAge);
            });
            break;

          default:
            acc[child.id] = Yup.string();
            break;
        }

        if (child?.required) {
          if (child.controlName === FormControlNames.CHECKLIST) {
            acc[child.id] = acc[child.id]
              .required(
                t('validation.required', {
                  field: child.labelName,
                })
              )
              .min(
                1,
                t('validation.required', {
                  field: child.labelName,
                })
              );
          } else {
            acc[child.id] = acc[child.id]?.required(
              t('validation.required', {
                field: child.labelName,
              })
            );
          }
        }

        if (child.min) {
          acc[child.id] = acc[child.id].min(child.min, `最小 ${child.min} 文字`);
        }

        if (child.max) {
          acc[child.id] = acc[child.id].max(child.max, `最大 ${child.max} 文字`);
        }

        if (child.controlName === FormControlNames.EMAIL) {
          acc[child.id] = acc[child.id].email('無効なメール');
        }

        return acc;
      },
      {
        ...privacyAndTermsValidation,
      }
    );
  }, [formElementChildren, formInternalValues]);

  const validationSchema = useMemo(() => {
    return isPreview ? Yup.object() : Yup.object().shape(validationObject);
  }, [validationObject]);

  const form = useFormik<Record<string, FormItemValue>>({
    initialValues: formInitialValues,
    validationSchema,
    validateOnBlur: true,
    validateOnChange: true,
    validateOnMount: false,
    onSubmit,
  });

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (formPreventSubmit) return;

    form.submitForm();
  };

  const layoutMode = useMemo(() => {
    return colorSetting?.layoutMode || 'vertical';
  }, [colorSetting?.layoutMode]);

  useEffect(() => {
    setFormInternalValues(form.values);
  }, [form.values]);

  const handleForceChange = useCallback(
    debounce((name: string, value: FormItemValue) => {
      form.setFieldTouched(name, true, true);
      form.setFieldValue(name, value);
    }, 300),
    [form]
  );

  const handleValidChange = useCallback((isValid: boolean) => {
    setFormPreventSubmit(!isValid);
  }, []);

  return (
    <Container
      sx={{ width: '100%', maxWidth: '1200px', marginRight: 'auto', marginLeft: 'auto', paddingLeft: '0!important', paddingRight: '0!important' }}
    >
      {formElements?.length > 0 ? (
        <Stack spacing={2}>
          <Box>
            <FormElementTitle
              heading={formElement?.container?.heading}
              description={formElement?.container?.description}
              headingClass={classes?.title}
              descriptionClass={classes?.general}
              display={formElement?.container?.display}
              lineColor={colorSetting?.generalSettings?.borderColor}
            />
          </Box>
          <Stack component="form" onSubmit={handleFormSubmit} direction="column" spacing={formSpacing}>
            {formElementChildren?.map?.((child) => {
              if (!isShowFormElement(child)) return null;

              return (
                <RenderItemWithLayout
                  hasCondition={child?.condition?.length > 0}
                  key={child.id}
                  label={child.labelName}
                  description={child.description}
                  isBottomDescription={child.isBottomDescription}
                  labelSettings={colorSetting?.labelSettings}
                  descriptionSettings={colorSetting?.descriptionSettings}
                  isRequired={child?.required}
                  layout={layoutMode}
                  showLabel={child?.controlName !== FormControlNames.COMMENT}
                >
                  <RenderItem
                    item={child}
                    formColorSetting={colorSetting}
                    helperText={form.touched[child.id] && form.errors[child.id]}
                    isError={form.touched[child.id] && !!form.errors[child.id]}
                    isPreview={isPreview}
                    isSubmitting={form.isSubmitting}
                    value={form.values?.[child.id] || ''}
                    onChange={form?.handleChange}
                    onBlur={form?.handleBlur}
                    onForceChange={handleForceChange}
                    onValid={handleValidChange}
                    inputAnimation={colorSetting?.animationSettings?.textEntryArea}
                    layout={layoutMode}
                  />
                </RenderItemWithLayout>
              );
            })}
            {generalSetting?.isSettingPrivacyPolicy || generalSetting?.isDisplayTermsUse ? (
              <FormPrivacyAndTerms
                className={classes.formGroup}
                error={form.touched.agreePrivacyAndTerms && form.errors.agreePrivacyAndTerms}
                isDisplayPrivacy={generalSetting?.isSettingPrivacyPolicy}
                isDisplayTerms={generalSetting?.isDisplayTermsUse}
                privacyUrl={generalSetting?.policyLink}
                termsUrl={generalSetting?.termsUse}
                isCombined={generalSetting?.isCombineIntoOneCheckbox}
                onAccept={(value) => handleForceChange('agreePrivacyAndTerms', value)}
              />
            ) : null}
            <Box display="block">
              <button className={classes.button} type="submit">
                {colorSetting?.buttonSettings?.confirmText || '確認'}
              </button>
            </Box>
          </Stack>
        </Stack>
      ) : (
        <>
          <div className="m-t-30">
            <p>You need to add Containers and Controls to see output here.</p>
          </div>
        </>
      )}
    </Container>
  );
};

export default FormStandalone;
