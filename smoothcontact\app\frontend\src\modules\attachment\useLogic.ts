import { useSearchParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import useAxios from '@/hooks/useAxios';

export interface IAttachment {
  url: string;
}

export default function useLogic() {
  const [searchParams] = useSearchParams();
  const { apiCaller, loading } = useAxios<IAttachment>();
  const [attachment, setAttachment] = useState<IAttachment | null>(null);

  const viewAttachmentHandler = async () => {
    const { data: attachment, success } = await apiCaller({
      url: `/api/attachments/signed-url`,
      params: {
        path: searchParams.get('path'),
      },
    });
    if (success && attachment?.url) {
      setAttachment(attachment);
      window.location.replace(attachment.url);
    }
  };

  useEffect(() => {
    viewAttachmentHandler();
  }, []);

  return {
    loading,
    attachment,
  };
}
