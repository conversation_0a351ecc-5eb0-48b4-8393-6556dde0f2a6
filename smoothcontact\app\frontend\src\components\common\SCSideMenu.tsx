import { createElement, FC } from 'react';
import { Box, Stack, styled, Tooltip } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import { SvgIconComponent } from '@mui/icons-material';

interface SCSideMenuItem {
  key: string;
  icon: SvgIconComponent;
  title?: string;
  enable?: boolean;
}
interface SCSideMenuProps {
  items: SCSideMenuItem[];
  selectedKey?: string;
  onClick?: (key: string) => void;
}

const SideMenuContainer = styled(Box)(({ theme }) => {
  return {
    backgroundColor: theme.palette.common.white,
  };
});

const IconButtonStyled = styled(IconButton)(({ theme }) => {
  return {
    width: '35px',
    height: '35px',
    borderRadius: 4,
    '&.selected': {
      backgroundColor: '#E6E6E6',
      color: theme.palette.primary.dark,
    },
    '&:hover': {
      backgroundColor: theme.palette.grey[50],
    },
    '&:focus': {
      outline: 'none',
    },
  };
});

const SCSideMenu: FC<SCSideMenuProps> = ({ items, selectedKey, onClick }) => {
  return (
    <SideMenuContainer
      display="inline-block"
      borderRadius={2}
      position="sticky"
      top="16px"
      p={1}
      sx={{
        alignItems: 'center',
      }}
    >
      <Stack spacing={2}>
        {items &&
          items
            ?.filter((item) => item?.enable === undefined || item?.enable)
            ?.map((item) => (
              <Tooltip
                key={`tooltip-${item.key}`}
                title={item.title ?? ''}
                slotProps={{
                  popper: {
                    modifiers: [
                      {
                        name: 'offset',
                        options: {
                          offset: [0, -10],
                        },
                      },
                    ],
                  },
                }}
              >
                <IconButtonStyled
                  key={item.key}
                  className={selectedKey === item.key ? 'selected' : ''}
                  onClick={() => {
                    if (onClick) {
                      onClick(item.key);
                    }
                  }}
                >
                  {createElement(item.icon)}
                </IconButtonStyled>
              </Tooltip>
            ))}
      </Stack>
    </SideMenuContainer>
  );
};
export default SCSideMenu;
