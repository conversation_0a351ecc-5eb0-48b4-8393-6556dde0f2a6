import { Injectable } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { Cron, CronExpression } from '@nestjs/schedule';

import { logger } from '@/core/logger/index.logger';

import { AccountService } from './account.service';

@Injectable()
export class AccountCleanupTask {
  constructor(private readonly moduleRef: ModuleRef) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleCron() {
    logger.info(`### Start running user cleanup task: ${new Date().toISOString()} ###`);
    const accountService = await this.moduleRef.resolve(AccountService);
    const result = await accountService.deleteInactiveUsers();
    logger.info(`### End running user cleanup task: ${new Date().toISOString()} - Deleted: ${result} ###`);
  }
}
