import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MulterModule } from '@nestjs/platform-express';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';

import { LoggerMiddleware } from '@/common/middleware/logger.middleware';
import { BindStartModule } from '@/modules/bind-start/bind-start.module';
import { EmbedModule } from '@/modules/embed/embed.module';
import { FormModule } from '@/modules/form/form.module';
import { HealthCheckModule } from '@/modules/health-check/health-check.module';
import { OEMStartModule } from '@/modules/oem-start/oem-start.module';
import { StaticModule } from '@/modules/static/static.module';

import { AccountModule } from './account/account.module';
import { AddressModule } from './address/address.module';
import { AidstartModule } from './aidstart/aidstart.module';
import { AttachmentModule } from './attachment/attachment.module';
import { CsvModule } from './csv/csv.module';
import { FormBuilderModule } from './form-builder/form-builder.module';
import { LoggerModule } from './logger/logger.module';
import { MailModule } from './mail/mail.module';
import { MfaModule } from './mfa/mfa.module';
import { TypeOrmConfigService } from './typeorm/typeorm.service';
import { WebFontModule } from './web-font/web-font.module';

@Module({
  imports: [
    MulterModule.register({
      dest: './uploads',
    }),
    ConfigModule.forRoot({ isGlobal: true }),
    TypeOrmModule.forRootAsync({ useClass: TypeOrmConfigService }),
    LoggerModule.forRoot(),
    ScheduleModule.forRoot(),
    AccountModule,
    FormBuilderModule,
    EmbedModule,
    MailModule,
    StaticModule,
    CsvModule,
    AddressModule,
    FormModule,
    MfaModule,
    AidstartModule,
    BindStartModule,
    OEMStartModule,
    HealthCheckModule,
    WebFontModule,
    AttachmentModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('/api/*');
  }
}
