import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

export function IsEndDateAfterStartDate(property: string, validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isEndDateAfterStartDate',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [property],
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];

          return !value || !relatedValue || (value && relatedValue && value >= relatedValue);
        },
        defaultMessage() {
          return `終了日は開始日より後の日付を指定してください。`;
        },
      },
    });
  };
}
