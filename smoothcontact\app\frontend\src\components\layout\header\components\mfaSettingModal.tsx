import SCModal from '@/components/common/SCModal';
import { Button, Typography } from '@mui/material';
import { Stack } from '@mui/system';

export interface MfaSettingModalProps {
  title: string;
  open: boolean;
  onClose: () => void;
  onSubmit: () => void;
  onGenerateBackupCode: () => void;
  profile: any;
}

export const MfaSettingModal = ({ title, open, profile, onClose, onSubmit, onGenerateBackupCode }: MfaSettingModalProps) => {
  return (
    <SCModal title={title ?? '公開期間を設定'} width={550} isOpen={open} onClose={onClose} closeBtnLabel={'キャンセル'} enableBackDropClick={true}>
      <Stack component="form" direction="column" gap={2}>
        <Typography variant="h6">有効化/無効化</Typography>
        <Typography variant="body1">現在の設定：{!profile?.isVerifiedMfa ? '無効' : '有効'}</Typography>

        <Button fullWidth variant="contained" color="primary" onClick={onSubmit}>
          {profile?.isVerifiedMfa ? '二段階認証の無効化' : '二段階認証の有効化'}
        </Button>

        <Typography variant="h6">バッグアップコード</Typography>
        <Button fullWidth variant="contained" color="secondary" onClick={onGenerateBackupCode} disabled={!profile?.isVerifiedMfa}>
          バッグアップコードの再発行
        </Button>
      </Stack>
    </SCModal>
  );
};
