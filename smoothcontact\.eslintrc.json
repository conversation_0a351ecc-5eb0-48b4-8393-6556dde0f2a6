{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2021, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "plugins": ["react", "react-hooks", "@typescript-eslint"], "rules": {"react/react-in-jsx-scope": "off", "@typescript-eslint/no-explicit-any": "off", "react/display-name": "off", "react/prop-types": "off", "no-case-declarations": "off", "no-fallthrough": ["error", {"allowEmptyCase": true}], "@typescript-eslint/naming-convention": ["error", {"selector": ["parameter", "variable", "function"], "format": ["camelCase", "PascalCase"], "leadingUnderscore": "allow"}, {"selector": ["class", "interface", "enum"], "format": ["PascalCase"]}, {"selector": "variable", "modifiers": ["const"], "format": ["UPPER_CASE", "camelCase", "PascalCase"], "leadingUnderscore": "allow"}, {"selector": ["enumMember"], "format": ["UPPER_CASE"]}]}, "settings": {"react": {"version": "detect"}}, "globals": {"tinymce": true}}