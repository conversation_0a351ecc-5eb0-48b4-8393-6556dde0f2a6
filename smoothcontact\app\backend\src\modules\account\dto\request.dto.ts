import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

import { IsCustomOptional } from '@/core/decorator/customOptional.decorator';

export interface ShopifyAuthRequestDTO {
  paramString: string;
}

export interface CheckHmacResultRequestDTO {
  host: string;
  session: string;
  shop: string;
  timestamp: number;
}

class LoginWithCallbackAndSetting {
  @IsCustomOptional()
  cb?: string;

  @IsCustomOptional()
  s?: string;
}

export class LoginRequestDTO extends LoginWithCallbackAndSetting {
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @MaxLength(255)
  @IsNotEmpty()
  pwd: string;
}

export class RefreshTokenRequestDTO {
  @IsNotEmpty()
  @MaxLength(1024)
  refreshToken: string;
}

export class ShopifyAuthReq {
  accessToken: string;
}

export class ResetPasswordRequestDTO {
  @IsNotEmpty()
  @IsEmail()
  email: string;
}

export class ChangePasswordRequestDTO {
  @IsNotEmpty()
  token: string;

  @IsNotEmpty()
  pwd: string;
}

export class ShopifyRegisterRequestDTO {
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @MaxLength(255)
  @IsNotEmpty()
  shop: string;

  @IsCustomOptional()
  @MaxLength(255)
  @IsNotEmpty()
  name?: string;
}

export class MfaLoginRequestDTO extends LoginWithCallbackAndSetting {
  @IsNotEmpty()
  accountId: number;

  @IsNotEmpty()
  code: string;
}

// TODO: This is a duplicate of MfaLoginRequestDTO, right?
export class BackupCodeLoginRequestDTO extends LoginWithCallbackAndSetting {
  @IsNotEmpty()
  accountId: number;

  @IsNotEmpty()
  code: string;
}

export class AidstartFormDTO {
  username?: string;
  password?: string;
  email?: string;

  token?: string;
  sak?: string;

  did?: string;
  tid?: string;
  dsid?: string;
  tempdsid?: string;

  oemId?: string;
  oemgo?: string;
  courseId?: string;
  courceType?: string;

  action?: string;
  data?: string;

  cb?: string;
  s?: string;
  logout?: string;
  maddress?: string;
  oemCourse?: number;
  autoData?: string;
}
