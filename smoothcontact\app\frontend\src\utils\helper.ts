import { FONT_DEFAULT } from '@/common/constants';
import { prefectures } from '@/common/prefectures';
import { FormStatus } from '@/types/FormTemplateTypes';
import { AxiosResponse } from 'axios';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';

export const MESSAGE_MAP: Record<string, string> = {
  FORM_NOT_FOUND: 'フォームが見つかりませんでした。フォームURLが誤っているか、フォームが非公開または削除された可能性があります。',
};
export const convertBlobToObject = async (blob: Blob) => {
  const reader = new FileReader();

  const promise = new Promise((resolve) => {
    reader.onload = function (event) {
      try {
        if (event.target?.result) {
          const jsonString = event.target.result as string;
          const parsedObject = JSON.parse(jsonString);

          resolve(parsedObject);
        }
      } catch (error) {
        resolve(undefined);
      }
    };
  });

  reader.readAsText(blob);

  return promise;
};

export const getMessageErrorRes = (errorsRes?: { [key: string]: any }, errorCodeObject?: any): string => {
  if (!Array.isArray(errorsRes)) {
    return '';
  }

  const errorCodeMsg = errorsRes?.[0]?.errorCode as any;
  if (!errorCodeMsg) {
    return '';
  }

  const errorCodeCus: any = errorCodeMsg;

  return errorCodeObject?.[errorCodeMsg] || errorCodeCus[errorCodeMsg] || '';
};

export const isJSON = (jsonString: string) => {
  try {
    const o = JSON.parse(jsonString);
    if (o && typeof o === 'object') {
      return o;
    }
  } catch (e) {
    /* empty */
  }

  return false;
};

export const isTransparentColor = (color: string) => color == 'transparent';

export const isHexColorCode = (s: string) => !isEmpty(s) && !!s.match(/^#[a-f0-9]{3}([a-f0-9]{3})?$/i);

export const alphaColor = (color: string, value: number = 0.8, defaultColor = '#1b367b') => {
  const colorMap: { [key: string]: number[] } = {
    red: [255, 0, 0],
    green: [0, 255, 0],
    blue: [0, 0, 255],
    yellow: [255, 255, 0],
    orange: [255, 165, 0],
    purple: [128, 0, 128],
    white: [255, 255, 255],
    black: [0, 0, 0],
    grey: [128, 128, 128],
    brown: [139, 69, 19],
    pink: [255, 192, 203],
    teal: [0, 128, 128],
    navy: [0, 0, 128],
    cyan: [0, 255, 255],
    lime: [0, 255, 0],
    magenta: [255, 0, 255],
    olive: [128, 128, 0],
    maroon: [128, 0, 0],
    silver: [192, 192, 192],
    gold: [255, 215, 0],
  };

  if (color == 'transparent') {
    return 'transparent';
  }

  color = color ? color?.trim().toLowerCase() : defaultColor?.trim().toLowerCase();

  if (/^#([0-9A-Fa-f]{3}){1,2}$/.test(color)) {
    color = color.slice(1);
    if (color.length === 3) {
      color = color
        .split('')
        .map((c) => c + c)
        .join('');
    }

    const r = parseInt(color.slice(0, 2), 16);
    const g = parseInt(color.slice(2, 4), 16);
    const b = parseInt(color.slice(4, 6), 16);
    const a = value ?? 1;

    return `rgba(${r}, ${g}, ${b}, ${a})`;
  }

  const rgbaMatch = /^rgba?\((\d{1,3}), (\d{1,3}), (\d{1,3})(?:, (\d?\.?\d+))?\)$/.exec(color);
  if (rgbaMatch) {
    const r = parseInt(rgbaMatch[1], 10);
    const g = parseInt(rgbaMatch[2], 10);
    const b = parseInt(rgbaMatch[3], 10);
    const a = rgbaMatch[4] !== undefined ? parseFloat(rgbaMatch[4]) : 1;

    return `rgba(${r}, ${g}, ${b}, ${a})`;
  }

  if (colorMap[color]) {
    return `rgba(${colorMap[color][0]}, ${colorMap[color][1]}, ${colorMap[color][2]}, ${value})`;
  }

  return defaultColor;
};

export const generateID = () => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

export const padZero = (value: number | string, size: number) => {
  return String(value).padStart(size, '0');
};

export const getIdFromUrl = () => {
  const paths: string[] = location.pathname.split('/');
  const len: number = paths.length - 1;

  return paths[len] === '' ? paths[len - 1] : paths[len];
};

export const getBaseFileName = (path: string) => {
  return path?.replace?.(/^.*[\\/]/, '');
};

export const isImageFile = (fileType: string): boolean => {
  return fileType.startsWith('image/');
};

export const fileSizeFormatted = (fileSize: number, unit: string = 'MB'): string => {
  return (fileSize / 1024 / 1024).toFixed(2) + ' ' + unit;
};

export function arraysHaveDifference(arr1: never[], arr2: never[]): boolean {
  if (arr1.length !== arr2.length) {
    return true;
  }

  arr1.sort();
  arr2.sort();

  for (let i = 0; i < arr1.length; i++) {
    const item1 = arr1[i];
    const item2 = arr2[i];

    if (typeof item1 === 'object' && typeof item2 === 'object') {
      if (hasChangeBetweenTwoObj(item1, item2)) {
        return true;
      }
    } else if (item1 !== item2) {
      return true;
    }
  }

  return false;
}

const checkValueHasDiff = (var1: unknown, var2: unknown) => {
  if ((var1 === undefined || var1 === null || var1 === '') && (var2 === undefined || var2 === null || var2 === '')) {
    return false;
  }

  if (typeof var1 !== typeof var2) {
    return true;
  }

  if (new Date(var1 as string)?.getTime() === new Date(var2 as string)?.getTime()) {
    return false;
  }

  return var1 !== var2;
};

export function isPureObject(obj: unknown) {
  if (typeof obj !== 'object' || obj == null) {
    return false;
  }

  const keys = Object.keys(obj);

  if (keys.length !== 0) {
    return true;
  }

  return false;
}

export function hasChangeBetweenTwoObj<T>(obj1: T, obj2: T, excludes?: any[]): boolean {
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object' || obj1 == null || obj2 == null) {
    if (obj1 != obj2) {
      return true;
    }

    return false;
  }

  const keys1 = Object.keys(obj1).filter((key: any) => !excludes?.includes(key));
  const keys2 = Object.keys(obj2).filter((key: any) => !excludes?.includes(key));

  if (keys1.length !== keys2.length) {
    return true;
  }

  for (const key of keys1) {
    if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
      if (arraysHaveDifference(obj1[key] as never[], obj2[key] as never[])) {
        return true;
      }

      continue;
    }

    if (isPureObject(obj1[key]) && isPureObject(obj2[key])) {
      if (hasChangeBetweenTwoObj(obj1[key], obj2[key], excludes)) {
        return true;
      }

      continue;
    }

    if (checkValueHasDiff(obj1[key], obj2[key])) {
      return true;
    }
  }

  return false;
}

export const assignDataToInstance = <T>(data: T, instance: T) => {
  const keys = Object.keys(data || {});
  keys.forEach((key) => {
    instance[key] = data[key];
  });
};

export const toQueryParams = (dto: any): string => {
  const params = new URLSearchParams();

  for (const key in dto) {
    if (dto[key] !== undefined && dto[key] !== null) {
      params.append(key, dto[key]);
    }
  }

  return params.toString();
};

export const downloadFileFromStream = (response: AxiosResponse) => {
  try {
    const href = window.URL.createObjectURL(response.data);
    const anchorElement = document.createElement('a');
    anchorElement.href = href;
    const disposition = response.headers['content-disposition'];
    let fileName = 'file';

    if (disposition && disposition.indexOf('attachment') !== -1) {
      const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
      const matches = filenameRegex.exec(disposition);
      if (matches != null && matches[1]) {
        fileName = matches[1].replace(/['"]/g, '');
        fileName = decodeURI(fileName);
      }
    }

    anchorElement.download = fileName;

    document.body.appendChild(anchorElement);
    anchorElement.click();

    document.body.removeChild(anchorElement);
    window.URL.revokeObjectURL(href);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log(error);
  }
};

export const openInNewTab = (url: string) => {
  window.open(url, '_blank');
};

export const getRealStatus = (
  status: FormStatus,
  releaseStartDate: Date | null,
  releaseEndDate: Date | null,
  isLimitedSubmission: boolean
): FormStatus => {
  // If status is Draft, return Draft immediately.
  if (status === FormStatus.DRAFT) {
    return FormStatus.DRAFT;
  }

  // If status is Published, check the current date once.
  if (status === FormStatus.PUBLISHED) {
    const currentDate = dayjs();

    if (!releaseStartDate && !releaseEndDate && !isLimitedSubmission) {
      return FormStatus.PUBLISHED;
    }

    // Pre-publish status check
    if (!!releaseStartDate && currentDate.isBefore(dayjs(releaseStartDate))) {
      return FormStatus.PREPUBLISHED;
    }

    // Closed status check
    if ((!!releaseEndDate && currentDate.isAfter(dayjs(releaseEndDate))) || isLimitedSubmission) {
      return FormStatus.CLOSED;
    }

    // If none of the above, it's Published status.
    return FormStatus.PUBLISHED;
  }

  // Default to Closed status if no condition matches.
  return FormStatus.CLOSED;
};

export const toISOStringSafely = (date: any) => {
  if (isNaN(date.getTime())) {
    return null;
  }

  return date.toISOString();
};

export const getStatusColor = (realStatus: number) => {
  switch (realStatus) {
    case FormStatus.PUBLISHED:
      return 'success';

    case FormStatus.PREPUBLISHED:
      return 'warning';

    case FormStatus.CLOSED:
      return 'default';

    default:
      return 'info';
  }
};

export const isEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) return true;

  if (typeof obj1 !== 'object' || typeof obj2 !== 'object' || obj1 == null || obj2 == null) return false;

  if (obj1 instanceof Date && obj2 instanceof Date) return obj1.getTime() === obj2.getTime();

  if (Array.isArray(obj1) && Array.isArray(obj2)) {
    if (obj1.length !== obj2.length) return false;

    for (let i = 0; i < obj1.length; i++) {
      if (!isEqual(obj1[i], obj2[i])) return false;
    }

    return true;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key) || !isEqual(obj1[key], obj2[key])) return false;
  }

  return true;
};

export function removeEmptyQueryParams(url: string): string {
  const urlObj = new URL(url);
  const params = new URLSearchParams(urlObj.search);

  // Remove empty parameters
  for (const [key, value] of params) {
    if (value === '') {
      params.delete(key);
    }
  }

  // Rebuild the URL with the updated query string
  urlObj.search = params.toString();

  return urlObj.toString();
}

export const removeEmptyAttributes = (values: { [key: string]: any }) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return Object.fromEntries(Object.entries(values).filter(([_, value]) => value !== ''));
};

export const getQueryParam = (name: string): string | null => {
  const urlParams = new URLSearchParams(window.location.search);

  return urlParams?.get?.(name) ?? null;
};

export const isInEnum = <T>(enumObj: T, value: any): value is T[keyof T] => {
  return Object.values(enumObj).includes(value as T[keyof T]);
};

export const removeNonKana = (text: string) => {
  const kanaRegex = /[\u3040-\u309F\u30A0-\u30FF]/g;

  return text.match(kanaRegex)?.join('') || '';
};

export const convertKatakanaToHiragana = (text: string) => {
  return text.replace(/[\u30A1-\u30F6]/g, (match) => {
    const chr = match.charCodeAt(0) - 0x60;

    return String.fromCharCode(chr);
  });
};

export const convertHiraganaToKatakana = (text: string) => {
  return text.replace(/[\u3041-\u3096]/g, (match) => {
    const chr = match.charCodeAt(0) + 0x60;

    return String.fromCharCode(chr);
  });
};

export const getGoogleTagIdFromSnippet = (snippet: string) => {
  // Regular expression to match the Google Tag ID
  const regex = /([A-Z]{1,2}-\d+)/;
  const match = snippet.match(regex);
  if (match && match[1]) {
    return match[1];
  }

  return '';
};

export const getPrefectureName = (prefectureCode: string) => {
  return prefectures.filter((prefecture) => prefecture.iso === prefectureCode)[0]?.prefecture_kanji || '';
};

export const displayFontFamily = (fontFamily: string) => {
  if (!fontFamily) {
    return FONT_DEFAULT?.includes(' ') ? `${FONT_DEFAULT}` : FONT_DEFAULT;
  }

  // if fontFamily include comma, wrap it with single quote
  // ex: "Noto Sans JP", sans-serif "Sen, sans-serif" => "'Sen', sans-serif"
  const fontFamilyArray = fontFamily.split(',');
  // if first part have quote or double quote, remove it
  const firstPart = fontFamilyArray[0].replace(/['"]+/g, '');

  return fontFamilyArray.length > 1 ? `'${firstPart}', ${fontFamilyArray[1]}` : firstPart;
};

export const getAppAccessToken = () => {
  const sessionToken = sessionStorage.getItem('accessToken');

  return sessionToken || localStorage.getItem('accessToken');
};

export const getAppRefreshToken = () => {
  const sessionToken = sessionStorage.getItem('refreshToken');

  return sessionToken || localStorage.getItem('refreshToken');
};

export const setAppAuthTokens = (accessToken: string, refreshToken: string) => {
  localStorage.setItem('accessToken', accessToken);
  localStorage.setItem('refreshToken', refreshToken);
};

export const removeAppAuthTokens = () => {
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  sessionStorage.removeItem('accessToken');
  sessionStorage.removeItem('refreshToken');
};

export const isSessionAuthenticated = () => {
  return !!sessionStorage.getItem('accessToken');
};

export const parseJapanesePostalCode = (postalCode: string): [string, string] | null => {
  if (!postalCode) {
    return null;
  }

  const cleanCode = postalCode?.replace?.(/\D/g, '');

  if (cleanCode.length !== 7) {
    return null;
  }

  if (postalCode.includes('-')) {
    const parts = postalCode.split('-');

    return parts.length === 2 ? [parts[0], parts[1]] : null;
  }

  return [cleanCode.substring(0, 3), cleanCode.substring(3, 7)];
};
