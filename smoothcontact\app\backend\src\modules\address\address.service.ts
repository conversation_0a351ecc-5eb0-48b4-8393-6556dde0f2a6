import { Injectable } from '@nestjs/common';
import axios from 'axios';

import { RootService } from '@/core/services/root.service';
import { logger } from '@/core/logger/index.logger';

@Injectable()
export class AddressService extends RootService {
  async getAddress(postalCode: string): Promise<any> {
    try {
      const response = await axios.get(`http://zipcloud.ibsnet.co.jp/api/search`, {
        params: { zipcode: postalCode },
      });

      if (response?.data && response?.data?.results) {
        return response.data.results[0];
      } else {
        return null;
      }
    } catch (error) {
      logger.error('getAddress', error);

      return null;
    }
  }
}
