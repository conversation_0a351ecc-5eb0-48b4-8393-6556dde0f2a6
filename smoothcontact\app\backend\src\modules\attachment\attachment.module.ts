import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AWSS3Module } from '@/libs/AWSS3';
import { AttachmentController } from '@/modules/attachment/attachment.controller';
import { AttachmentService } from '@/modules/attachment/attachment.service';
import { FormBuilderEntity } from '@/modules/form-builder/entities/form-builder.entity';
import { FormSubmissionEntity } from '@/modules/submission/entities/form-submission.entity';
import { SubmissionModule } from '@/modules/submission/submission.module';

@Module({
  imports: [TypeOrmModule.forFeature([FormSubmissionEntity]), TypeOrmModule.forFeature([FormBuilderEntity]), SubmissionModule, AWSS3Module],
  providers: [AttachmentService],
  controllers: [AttachmentController],
  exports: [],
})
export class AttachmentModule {}
