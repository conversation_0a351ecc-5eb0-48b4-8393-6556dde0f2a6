import { Course } from '@/common/constants';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { BindUpSettingDto } from '@/types/dto/bindup.dto';

export class ProfileResponseDto {
  id: number;
  name: string;
  shop: string;
  email: string;
  avatar?: string;
  tourCompleted?: boolean;
  isVerifiedMfa?: boolean;
  course?: Course;
  base32?: string;
  qrCode?: string;
  callbackUrl?: string;
  setting?: BindUpSettingDto;
  oemId?: string;
  oemgo?: string;
}

export interface IAppState {
  authLoaded: boolean;
  profile?: ProfileResponseDto;
  profileLoaded: boolean;
  profileFetchStatus: {
    forceGetProfile: boolean;
  };
  notFoundChecker: {
    redirect: boolean;
  };
  Error500Checker: {
    redirect: boolean;
    refresh: boolean;
  };
  modalConfirmUnChanged: {
    isShow: boolean;
  };
}

export const initialState: IAppState = {
  authLoaded: false,
  profileFetchStatus: { forceGetProfile: false },
  profileLoaded: false,
  notFoundChecker: {
    redirect: false,
  },
  Error500Checker: {
    redirect: false,
    refresh: false,
  },
  modalConfirmUnChanged: {
    isShow: false,
  },
};

export const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setAppState(state, action: PayloadAction<Partial<IAppState>>) {
      state = { ...state, ...action.payload };

      return state;
    },
    getMe(state) {
      state = { ...state, profileFetchStatus: { forceGetProfile: true } };

      return state;
    },
    redirectTo404(state) {
      state = { ...state, notFoundChecker: { redirect: true } };

      return state;
    },
    revert404(state) {
      state = { ...state, notFoundChecker: { redirect: false } };

      return state;
    },
    redirectTo500(state, action: PayloadAction<IAppState['Error500Checker']>) {
      state = { ...state, Error500Checker: action.payload };

      return state;
    },
    revert500(state) {
      state = { ...state, Error500Checker: { redirect: false, refresh: false } };

      return state;
    },
    showModalConfirmUnChanged(state) {
      state = {
        ...state,
        modalConfirmUnChanged: {
          isShow: true,
        },
      };

      return state;
    },
  },
});

export const appAction = appSlice.actions;
export const appReducer = appSlice.reducer;
