import React from 'react';
import Link, { LinkProps } from '@mui/material/Link';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import { SvgIconComponent } from '@mui/icons-material';
import HomeIcon from '@mui/icons-material/Home';
import styled from '@emotion/styled';
import { Link as RouterLink } from 'react-router-dom';
import { Box } from '@mui/system';

interface SCBreadcrumbsItem {
  child: React.ReactNode;
  icon?: SvgIconComponent;
  iconColor?: string;
  href?: string;
}

interface SCBreadcrumbsProps {
  items: SCBreadcrumbsItem[];
}

interface LinkRouterProps extends LinkProps {
  to?: string;
  replace?: boolean;
}

function LinkRouter(props: LinkRouterProps) {
  return <Link {...props} component={RouterLink as any} />;
}

const LinkStyled = styled(LinkRouter)`
  color: ${(props: any) => props.theme.palette.text.secondary};
  &:hover {
    text-decoration: none;
  }
`;

const SCBreadcrumbs: React.FC<SCBreadcrumbsProps> = ({ items }) => {
  return (
    <div role="presentation">
      <Breadcrumbs aria-label="breadcrumb">
        <LinkStyled sx={{ display: 'flex', alignItems: 'center' }} underline="none" to="/" component={RouterLink}>
          <HomeIcon sx={{ mr: 1 }} fontSize="inherit" />
          ダッシュボード
        </LinkStyled>
        {items.map((item, index) => {
          const Icon = item.icon;

          if (item.href) {
            return (
              <LinkStyled sx={{ display: 'flex', alignItems: 'center' }} underline="none" to={item.href} component={RouterLink} key={index}>
                {Icon && <Icon sx={{ mr: 1 }} fontSize="inherit" />}
                {item.child}
              </LinkStyled>
            );
          }

          return (
            <Box sx={{ display: 'flex', alignItems: 'center' }} key={index}>
              {Icon && <Icon sx={{ mr: 1 }} fontSize="inherit" />}
              {item.child}
            </Box>
          );
        })}
      </Breadcrumbs>
    </div>
  );
};

export default SCBreadcrumbs;
