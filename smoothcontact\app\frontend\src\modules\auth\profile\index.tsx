import SCLoadingButton from '@/components/common/SCLoadingButton';
import EditIcon from '@mui/icons-material/Edit';
import { Avatar, Box, Button, Container, FormControl, Grid, TextField, Typography, styled } from '@mui/material';
import { Stack } from '@mui/system';
import { useNavigate } from 'react-router-dom';
import useLogic from './useLogic';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

const ProfileModule: React.FC = () => {
  const { form, handleFileChange, resetAvatar, avatarPreview, loading } = useLogic();
  const navigate = useNavigate();

  return (
    <Box mt={4} component="form" onSubmit={form.handleSubmit} noValidate>
      <Container>
        <Stack direction={'column'} gap={4}>
          <Stack direction={'row'} justifyContent={'space-between'} alignItems={'center'}>
            <Typography variant="h5">設定</Typography>
            <Stack direction={'row'} gap={2}>
              <Button variant="outlined" color="secondary" onClick={() => navigate('/form-builder')}>
                キャンセル
              </Button>
              <SCLoadingButton loading={loading} disabled={!form?.dirty || !form?.isValid} variant="contained" color="primary" type="submit">
                保存
              </SCLoadingButton>
            </Stack>
          </Stack>
          <Grid container spacing={2}>
            <Grid item xs={3}>
              <Typography variant="body1">ユーザー</Typography>
            </Grid>
            <Grid item xs={9}>
              <Stack direction={'column'} alignItems={'flex-start'} gap={2}>
                <Stack direction={'row'} alignItems={'center'} gap={2}>
                  <Avatar variant="circular" src={avatarPreview} sx={{ width: 96, height: 96 }} />
                  <Button component="label" color="secondary" variant="outlined" tabIndex={-1} startIcon={<EditIcon />}>
                    画像を変更
                    <VisuallyHiddenInput
                      accept="image/*"
                      type="file"
                      onChange={(event) => {
                        handleFileChange(event);
                        event.target.value = null;
                      }}
                    />
                  </Button>
                  <Button component="label" variant="text" color="error" onClick={resetAvatar}>
                    画像を削除
                  </Button>
                </Stack>
                <FormControl fullWidth variant="outlined">
                  <TextField
                    variant="outlined"
                    label="表示名"
                    sx={{ width: '420px' }}
                    {...form?.register('name')}
                    value={form?.values?.name}
                    error={!!form?.errors?.name}
                    helperText={!!form?.errors?.name && form?.errors?.name}
                    placeholder="表示名を入力してください"
                  />
                </FormControl>
              </Stack>
            </Grid>
          </Grid>
        </Stack>
      </Container>
    </Box>
  );
};

export default ProfileModule;
