import { AxiosRequestConfig as AxiosRequestConfigRoot } from 'axios';

import { LayoutType } from '../app';

export {};

declare module 'react-dom/client';

declare module 'axios' {
  export interface AxiosRequestConfig<D = never> extends AxiosRequestConfigRoot<D> {
    path?: { [key: key]: string | number };
  }
}

declare global {
  interface ObjectConstructor {
    keys<T>(obj: T): Array<keyof T>;
  }
  interface Window {
    previousURLSaved?: string;
    accessToken?: string;
    devtoolControl?: unknown;
  }
}

declare module 'react' {
  interface FunctionComponent {
    layoutType?: LayoutType;
    layoutLess?: boolean;
    title?: string;
  }
  interface ReactElement {
    type?: {
      layout?: LayoutType;
    };
  }
}
