import { Injectable } from '@nestjs/common';

import { WinstonLoggerService } from './winston-logger.service';

@Injectable()
export class LoggerService {
  constructor(private winstonLoggerService: WinstonLoggerService) {}

  /* istanbul ignore next */
  async info(shop: string, message: string, meta?: any) {
    this.winstonLoggerService.info({
      shop: shop,
      message: message,
      meta: meta,
    });
  }

  /* istanbul ignore next */
  async error(shop: string, message: string, meta?: any) {
    this.winstonLoggerService.error({
      shop: shop,
      message: message,
      meta: meta,
    });
  }

  /* istanbul ignore next */
  async warn(shop: string, message: string, meta?: any) {
    this.winstonLoggerService.warn({
      shop: shop,
      message: message,
      meta: meta,
    });
  }

  /* istanbul ignore next */
  async debug(shop: string, message: string, meta?: any) {
    this.winstonLoggerService.debug({
      shop: shop,
      message: message,
      meta: meta,
    });
  }
}
