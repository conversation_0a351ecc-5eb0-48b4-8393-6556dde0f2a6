import { CanActivate, ExecutionContext, HttpStatus, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { Observable } from 'rxjs';

import { MessageCode } from '@/common/constant';
import { IDataSign } from '@/types/dataSign.type';
import { HttpException } from '@/types/httpException.type';

@Injectable()
export class AuthGuard implements CanActivate {
  public constructor(
    private readonly reflector: Reflector,
    private readonly jwtService: JwtService,
  ) {}

  extractTokenFromHeader(header?: string | undefined): string | null {
    return header?.startsWith('Bearer ') ? header.slice(7) : null;
  }

  async validateRequest(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest() as any;
    const token = this.extractTokenFromHeader(request.headers.authorization);

    if (token) {
      try {
        request.user = await this.jwtService.verifyAsync<IDataSign>(token);

        return true;
      } catch (ex) {
        if (ex.name === 'TokenExpiredError') {
          throw new HttpException({
            statusCode: HttpStatus.FORBIDDEN,
            messageCode: MessageCode.TOKEN_EXPIRED,
            message: 'トークンの有効期限が切れています。',
            data: null,
          });
        }
      }
    }

    throw new HttpException({
      statusCode: HttpStatus.UNAUTHORIZED,
      messageCode: MessageCode.TOKEN_INVALID,
      message: 'トークンが無効です。',
      data: null,
    });
  }

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    return this.validateRequest(context);
  }
}
