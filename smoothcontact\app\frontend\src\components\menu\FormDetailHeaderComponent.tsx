import React from 'react';
import SCAlert from '../common/SCAlert';
import { AlertColor } from '@mui/material';
import { FormStatus, FormStatusLabel } from '@/types/FormTemplateTypes';
import { getRealStatus } from '@/utils/helper';
import { formatDate, ISO } from '@/utils/dateTime';

export interface FormDetailHeaderProps {
  status: number;
  releaseStartDate: Date;
  releaseEndDate: Date;
  isLimitedSubmission: boolean;
}

const getStatusDetails = (realStatus: number, releaseStartDate: Date) => {
  const startDateTime = formatDate(releaseStartDate, ISO.DATE_TIME);
  switch (realStatus) {
    case FormStatus.DRAFT:
      return {
        background: 'info',
        title: FormStatusLabel[FormStatus.DRAFT],
        description: 'このフォームは非公開として保存されています。ユーザーには公開されません',
      };

    case FormStatus.PUBLISHED:
      return {
        background: 'success',
        title: FormStatusLabel[FormStatus.PUBLISHED],
        description: 'このフォームは現在公開されています',
      };

    case FormStatus.PREPUBLISHED:
      return {
        background: 'warning',
        title: FormStatusLabel[FormStatus.PREPUBLISHED],
        description: `フォームのリンクを知っているユーザーのみアクセスできますが投稿はできません。${startDateTime}に公開予定です`,
      };

    case FormStatus.CLOSED:
      return {
        background: 'error',
        title: FormStatusLabel[FormStatus.CLOSED],
        description: 'このフォームの公開は終了しています。フォームへのリンクを知っているユーザーのみアクセスできますが投稿はできません',
      };

    default:
      return {
        background: 'info',
        title: 'Unknown',
        description: 'Unknown status',
      };
  }
};

const FormDetailHeader = ({ status, releaseStartDate, releaseEndDate, isLimitedSubmission }: FormDetailHeaderProps) => {
  const realStatus = getRealStatus(status, releaseStartDate, releaseEndDate, isLimitedSubmission);
  const statusDetails = getStatusDetails(realStatus, releaseStartDate);

  return (
    <SCAlert title={statusDetails.title} severity={statusDetails.background as AlertColor}>
      {statusDetails.description}
    </SCAlert>
  );
};

export default FormDetailHeader;
