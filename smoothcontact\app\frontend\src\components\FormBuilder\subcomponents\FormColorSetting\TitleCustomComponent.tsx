import SCColorPicker from '@/components/common/SCColorPicker';
import SCFontSelector from '@/components/common/SCFontSelector';
import { KeyboardArrowDown } from '@mui/icons-material';
import { FormControl, Grid, MenuItem, Select, Stack, TextField, Typography } from '@mui/material';
import { FC } from 'react';
import { CustomModeComponentProps } from './CustomModeComponent';

const TitleCustomComponent: FC<CustomModeComponentProps> = (props) => {
  const { form, webFonts } = props;

  return (
    <>
      <Typography variant="body2">タイトル</Typography>
      <Stack direction="row" spacing={2}>
        <TextField
          label="文字サイズ"
          variant="outlined"
          type="number"
          error={!!form?.errors?.titleSettings?.fontSize}
          helperText={form?.errors?.titleSettings?.fontSize ? '整数を半角で入力してください' : ''}
          {...form.register('titleSettings.fontSize')}
          value={form?.values?.titleSettings?.fontSize}
        />
        <Select
          IconComponent={() => <KeyboardArrowDown />}
          {...form.register('titleSettings.fontSizeUnit')}
          value={form?.values?.titleSettings?.fontSizeUnit}
          displayEmpty
        >
          <MenuItem value={'px'}>px</MenuItem>
          <MenuItem value={'rem'}>rem</MenuItem>
        </Select>
      </Stack>

      <Grid container alignItems="center">
        <Grid item xs={4} container justifyContent="flex-end">
          <Typography variant="body2">文字色</Typography>
        </Grid>
        <Grid item xs={6} sx={{ pl: 2 }}>
          <SCColorPicker name="titleSettings.color" color={form?.values?.titleSettings?.color} form={form} />
        </Grid>
      </Grid>
      <FormControl fullWidth>
        <SCFontSelector
          name={'titleSettings.fontFamily'}
          fontFamily={form?.values?.titleSettings?.fontFamily ?? ''}
          fontName={form?.values?.titleSettings?.fontName ?? form?.values?.titleSettings?.fontFamily ?? ''}
          source={webFonts}
          onFontChange={(font) => {
            form?.setFieldValue('titleSettings.fontFamily', `${font?.fontFamily}`);
            form?.setFieldValue('titleSettings.fontName', `${font?.fontName}`);
          }}
        />
      </FormControl>
    </>
  );
};

export default TitleCustomComponent;
