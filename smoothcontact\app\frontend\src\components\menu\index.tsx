import { TemplateType } from '@/types/FormTemplateTypes';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Box, Divider, MenuItem, Typography } from '@mui/material';
import Menu from '@mui/material/Menu';
import React, { useState } from 'react';
import SCIconButton from '../common/SCIconButton';

export interface MenuItemProps {
  title?: string;
  type: string;
  item?: TemplateType;
  onClick?: (item: TemplateType) => void;
  isActive?: boolean;
  warningAction?: boolean;
  hidden?: boolean;
}

interface FormBuilderMenusProps {
  menus: MenuItemProps[];
  extId?: string;
  className?: string;
}

export default function FormBuilderMenus({ menus, extId, className }: FormBuilderMenusProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleAction = (menu: MenuItemProps) => {
    setAnchorEl(null);
    menu?.onClick?.(menu.item);
  };

  return (
    <Box key={`box-${extId}`}>
      <SCIconButton
        className={className ?? ''}
        key={`icon-button-${extId}`}
        aria-controls={open ? 'long-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleClick}
      >
        <ExpandMoreIcon fontSize="inherit" />
      </SCIconButton>
      <Menu key={`menu-${extId}`} MenuListProps={{ 'aria-labelledby': 'long-button' }} anchorEl={anchorEl} open={open} onClose={handleClose}>
        {menus
          ?.filter((item) => item?.hidden !== true)
          ?.map?.((menu, index) =>
            menu.type === 'divider' ? (
              <Divider key={`box-${extId}-${index}`} />
            ) : (
              <MenuItem color="error" key={`box-${extId}-${index}`} onClick={() => handleAction(menu)} disabled={!menu.isActive}>
                <Typography color={menu?.warningAction ? '#D32F2F' : 'inherit'}>{menu.title}</Typography>
              </MenuItem>
            )
          )}
      </Menu>
    </Box>
  );
}
