import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ServeStaticModule } from '@nestjs/serve-static';
import { TypeOrmModule } from '@nestjs/typeorm';
import { join } from 'path';

import { EmbedMiddleware } from '@/common/middleware/embed.middleware';
import { FormBuilderEntity } from '@/modules/form-builder/entities/form-builder.entity';
import { StaticController } from '@/modules/static/static.controller';
import { StaticService } from '@/modules/static/static.service';

import { WebFontModule } from '../web-font/web-font.module';

const MAX_AGE_ONE_YEAR = 31536000;

@Module({
  imports: [
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '../../../../', 'frontend/dist'),
      serveStaticOptions: {
        cacheControl: true,
        maxAge: MAX_AGE_ONE_YEAR,
      },
    }),
    TypeOrmModule.forFeature([FormBuilderEntity]),
    WebFontModule,
  ],
  controllers: [StaticController],
  providers: [StaticService],
  exports: [StaticService],
})
export class StaticModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(EmbedMiddleware).forRoutes('/front/output/:id([a-z0-9]+)');
    // consumer.apply(BasicAuthMiddleware).exclude('/api/(.*)', '/health-check').forRoutes('/*');
  }
}
