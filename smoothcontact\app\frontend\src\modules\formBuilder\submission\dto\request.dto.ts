import { OrderRequest, PaginationRequest } from '@/types/app';
import { assignDataToInstance } from '@/utils/helper';

export class SortReq {
  order?: string;
  orderBy?: string;
}

export class FormSubmissionFilterRequestDto extends PaginationRequest {
  order?: OrderRequest;

  orderBy?: string;

  constructor(data?: Partial<FormSubmissionFilterRequestDto>) {
    super();
    assignDataToInstance(data, this);
  }
}
