import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(localeData);

export enum ISO {
  DATE = 'YYYY/MM/DD',
  DATE_WITH_DOT = 'YYYY.MM.DD',
  DATE_WITH_JP_LABEL = 'YYYY年MM月DD日',
  DATE_TIME = 'YYYY/MM/DD HH:mm',
  DATE_WITH_STRIKETHROUGH = 'YYYY-MM-DD',
  DATE_WITH_MONTH = 'YYYY-MM',
  DATE_TIME_WITH_DOT = 'YYYY.MM.DD HH:mm',
  DATE_JP_FORMAT = 'YYYY年MM月DD日(ddd)',
  DATE_WITH_JP_FORMAT = 'YYYY年M月D日(ddd)',
  TIME = 'HH:mm',
}

export function formatDate(date: string | Date, template = ISO.DATE) {
  if (!date) {
    return '';
  }

  const timeStamp = new Date(date);

  if (!dayjs(timeStamp).isValid()) {
    return '';
  }

  return dayjs(timeStamp).format(template);
}

export function convertDateToDayjs(date?: Date) {
  if (!date) {
    return undefined;
  }

  const dayjsValue = dayjs(date);

  return dayjsValue.isValid() ? dayjsValue : undefined;
}

export function getStartDateOfDate(date: Date | string | number) {
  if (!date) {
    return undefined;
  }

  const dateObj = new Date(date);

  if (isNaN(dateObj.getTime())) {
    return undefined;
  }

  dateObj.setHours(0, 0, 1, 0);

  return dateObj;
}

export function getEndDateOfDate(date: Date | string | number) {
  if (!date) {
    return undefined;
  }

  const dateObj = new Date(date);

  if (isNaN(dateObj.getTime())) {
    return undefined;
  }

  dateObj.setHours(23, 59, 59, 0);

  return dateObj;
}

export function isValidDate(d: unknown) {
  if (d === null || d === undefined) {
    return false;
  }

  const date = new Date(d as string | number | Date);

  return !isNaN(date.getTime());
}

export function convertToJPTime(date: Date | string, optional?: { isStart?: boolean; isEnd?: boolean }) {
  if (!date) {
    return date;
  }

  const customDate = new Date(date);

  if (!isValidDate(customDate)) {
    return;
  }

  if (optional?.isStart) {
    customDate.setHours(0, 1, 0, 0);
  }

  if (optional?.isEnd) {
    customDate.setHours(23, 58, 0, 0);
  }

  const dateObj = dayjs(customDate);

  if (!dateObj.isValid()) {
    return;
  }

  dateObj.tz('Asia/Tokyo');
  const utcDate = dateObj.utc();

  return utcDate.toDate();
}

export const adjustMonth = (currentDate: Date, adjustDateNumber: number) => {
  currentDate.setMonth(currentDate.getMonth() + adjustDateNumber);

  return currentDate;
};

export const adjustDate = (currentDate: Date, adjustDateNumber: number) => {
  currentDate.setDate(currentDate.getDate() + adjustDateNumber);

  return currentDate;
};

export const compareDate = (dateCompare: string) => {
  const toDate = new Date(Date.now()).setHours(0, 0, 0, 0);
  const dateComparing = new Date(Date.parse(dateCompare)).setHours(0, 0, 0, 0);

  return dateComparing >= toDate;
};

export const comparePriorDate = (dateCompare: string) => {
  const toDate = new Date(Date.now());
  const dateComparing = new Date(dateCompare);
  dateComparing.setDate(dateComparing.getDate() - 3);

  return toDate <= dateComparing;
};
