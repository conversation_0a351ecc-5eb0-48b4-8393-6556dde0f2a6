import React from 'react';
import ReportBar<PERSON>hartLastMonth from './ReportBarChartLastMonth';
import ReportBarChartLastSixMonths from './ReportBarChartLastSixMonths';
import ReportBarChartLastYear from './ReportBarChartLastYear';
import ReportBarChartAllTime from './ReportBarChartAllTime';

interface ChartComponents {
  [key: string]: JSX.Element;
}

interface ChartData {
  lastMonth: { [key: string]: number };
  lastSixMonths: { [key: string]: number };
  lastYear: { [key: string]: number };
  allTime: { [key: string]: number };
}

export const getChartComponents = (chartData: ChartData): ChartComponents => ({
  lastMonth: <ReportBarChartLastMonth chartData={chartData.lastMonth} />,
  lastHalfYear: <ReportBarChartLastSixMonths chartData={chartData.lastSixMonths} />,
  lastYear: <ReportBarChartLastYear chartData={chartData.lastYear} />,
  all: <ReportBarChartAllTime chartData={chartData.allTime} />,
});
