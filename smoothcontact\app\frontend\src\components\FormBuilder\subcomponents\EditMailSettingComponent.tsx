import SCInputTag from '@/components/common/SCInputTag';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormMailSetting, FormType } from '@/types/FormTemplateTypes';
import { OptionAfterSubmitForm } from '@/utils/formBuilderUtils';
import { KeyboardArrowDown } from '@mui/icons-material';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import {
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  FormLabel,
  InputAdornment,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  Stack,
} from '@mui/material';
import Divider from '@mui/material/Divider';
import TextField from '@mui/material/TextField';
import { FC, useEffect } from 'react';
import * as Yup from 'yup';
import AutoReplyEmailComponent from './AutoReplyEmailComponent';
import FieldDisplay from './FormMailSetting/FieldDisplayComponent';
import { useAppSelector } from '@/store/hook';
import { Course } from '@/common/constants';

interface EditMailSettingComponentProps {}

const EditMailSettingComponent: FC<EditMailSettingComponentProps> = () => {
  const { selectedTemplate, formMailSetting, editMailSetting, setError, isFormChanged, hasSwitchContact } = useFormBuilder();
  const { profile } = useAppSelector((state) => ({
    profile: state.app.profile,
  }));
  const validationSchema = Yup.object<FormMailSetting>({
    emailAddress: Yup.array().of(Yup.string().email('メールアドレスの形式が無効です').optional()).optional(),
    screenAfterSendingType: Yup.mixed().oneOf([OptionAfterSubmitForm.displayMessage, OptionAfterSubmitForm.specifiedUrl]),
    message: Yup.string().when('screenAfterSendingType', {
      is: (screenAfterSendingType: string) => screenAfterSendingType === OptionAfterSubmitForm.displayMessage,
      then: () => Yup.string().required('メッセージを入力してください'),
    }),
    specifiedUrl: Yup.string().when('screenAfterSendingType', {
      is: (screenAfterSendingType: string) => screenAfterSendingType === OptionAfterSubmitForm.specifiedUrl,
      then: (schema) => schema.required('URLを入力してください').url('有効なURLを入力してください'),
    }),
    isAutoReply: Yup.boolean().optional(),
    autoReplyEmailAddress: Yup.string().when('isAutoReply', {
      is: (isAutoReply: boolean) => isAutoReply,
      then: (schema) => schema.required('メールアドレスを入力してください').email('有効なメールアドレスを入力してください'),
    }),
    autoReplySenderName: Yup.string().when('isAutoReply', {
      is: (isAutoReply: boolean) => isAutoReply,
      then: (schema) => schema.required('送信者名を入力してください'),
    }),
    autoReplySubject: Yup.string().when('isAutoReply', {
      is: (isAutoReply: boolean) => isAutoReply,
      then: (schema) => schema.required('件名を入力してください'),
    }),
    smtpHost: Yup.string().when('useCustomSMTP', {
      is: (useCustomSMTP: boolean) => useCustomSMTP,
      then: (schema) => schema.required('SMTP ホストを入力してください'),
    }),
    smtpPort: Yup.number().when('useCustomSMTP', {
      is: (useCustomSMTP: boolean) => useCustomSMTP,
      then: (schema) => schema.required('SMTP ポートを入力してください').typeError('数字を入力してください'),
    }),
    smtpUsername: Yup.string().when('useCustomSMTP', {
      is: (useCustomSMTP: boolean) => useCustomSMTP,
      then: (schema) => schema.required('SMTP ユーザー名を入力してください'),
    }),
    smtpPassword: Yup.string().when('useCustomSMTP', {
      is: (useCustomSMTP: boolean) => useCustomSMTP,
      then: (schema) => schema.required('SMTP パスワードを入力してください'),
    }),
    smtpFromEmail: Yup.string().when('useCustomSMTP', {
      is: (useCustomSMTP: boolean) => useCustomSMTP,
      then: (schema) => schema.required('From メールアドレスを入力してください').email('有効なメールアドレスを入力してください'),
    }),
  });

  const form = useFormHandler<FormMailSetting>({
    initialValues: formMailSetting,
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: (e) => {
      console.log('submit', e);
    },
  });

  useEffect(() => {
    if (form.isValid) {
      editMailSetting(form.values);
    }

    setError(!form.isValid);
  }, [form.isValid, form.values]);

  useEffect(() => {
    if (!isFormChanged) {
      form.resetForm({
        values: selectedTemplate?.formMailSetting,
      });
    }
  }, [isFormChanged]);

  return (
    <Stack direction="column" spacing={2}>
      <FormLabel sx={{ pl: 1 }} component="legend">
        受信したいメールアドレス
      </FormLabel>
      <SCInputTag name="emailAddress" form={form} IconComponent={EmailIcon} />
      <Divider />

      <FormControl component="fieldset">
        <FormLabel id="radio-buttons-received-email-label" sx={{ pl: 1 }} component="legend">
          送信終了後の画面
        </FormLabel>

        <RadioGroup name="screenAfterSendingType" value={form?.values?.screenAfterSendingType} {...form?.register('screenAfterSendingType')}>
          <FormControlLabel
            sx={{ mb: 1 }}
            value="display_message"
            control={<Radio value="display_message" sx={{ paddingLeft: 2 }} />}
            label="メッセージを表示"
          />
          <TextField
            sx={{ ml: 1 }}
            multiline
            rows={5}
            placeholder="フォームが正常に送信されました。"
            error={!!form?.errors?.message}
            helperText={form?.errors?.message}
            value={form?.values?.message}
            {...form.register('message')}
            disabled={form?.values?.screenAfterSendingType !== OptionAfterSubmitForm.displayMessage}
          />

          <FormControlLabel
            sx={{ mb: 1 }}
            value="specified_url"
            control={<Radio value="specified_url" sx={{ paddingLeft: 2 }} />}
            label="指定したURLへ移動"
          />
          <TextField
            sx={{ ml: 1 }}
            error={!!form?.errors?.specifiedUrl}
            helperText={form?.errors?.specifiedUrl}
            value={form?.values?.specifiedUrl ?? ''}
            {...form.register('specifiedUrl')}
            disabled={form?.values?.screenAfterSendingType !== OptionAfterSubmitForm.specifiedUrl}
            label={'URL'}
          />
        </RadioGroup>
      </FormControl>
      <Divider />
      <FormGroup>
        <FormLabel id="radio-buttons-custom-smtp-label" sx={{ pl: 1 }} component="legend">
          メールアドレスのドメイン設定
        </FormLabel>
        <FormHelperText sx={{ pt: 1 }}>送信先のメールアドレスのドメインを、自社ドメインかサービスドメインに設定するかを選択します</FormHelperText>
        <FormControlLabel
          sx={{ pl: 1 }}
          control={
            <Checkbox value={true} {...form.register('useCustomSMTP', { nameOfValueProps: 'checked' })} checked={form?.values?.useCustomSMTP} />
          }
          label="カスタム SMTP を使います"
        />
        {form?.values?.useCustomSMTP && (
          <Stack spacing={2} mt={2}>
            <TextField
              label="SMTP ホスト"
              placeholder="smtp.example.com"
              value={form?.values?.smtpHost}
              {...form.register('smtpHost')}
              error={!!form?.errors?.smtpHost}
              helperText={form?.errors?.smtpHost}
            />
            <FormControl>
              <InputLabel id="smtp-port-label">SMTP ポート</InputLabel>
              <Select
                IconComponent={() => <KeyboardArrowDown />}
                label="SMTP ポート"
                value={form?.values?.smtpPort}
                {...form.register('smtpPort')}
                error={!!form?.errors?.smtpPort}
              >
                <MenuItem value={25} key={0}>
                  25
                </MenuItem>
                <MenuItem value={465} key={1}>
                  465
                </MenuItem>
                <MenuItem value={587} key={2}>
                  587
                </MenuItem>
              </Select>
              <FormHelperText error={!!form?.errors?.smtpPort}>{form?.errors?.smtpPort}</FormHelperText>
            </FormControl>
            <TextField
              label="SMTP ユーザー名"
              placeholder="ユーザー名"
              value={form?.values?.smtpUsername}
              {...form.register('smtpUsername')}
              error={!!form?.errors?.smtpUsername}
              helperText={form?.errors?.smtpUsername}
            />
            <TextField
              label="SMTP パスワード"
              placeholder="パスワード"
              type="password"
              value={form?.values?.smtpPassword}
              {...form.register('smtpPassword')}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <LockIcon />
                  </InputAdornment>
                ),
              }}
              error={!!form?.errors?.smtpPassword}
              helperText={form?.errors?.smtpPassword}
            />
            <TextField
              label="From メールアドレス"
              placeholder="From メールアドレス"
              value={form?.values?.smtpFromEmail}
              {...form.register('smtpFromEmail')}
              error={!!form?.errors?.smtpFromEmail}
              helperText={form?.errors?.smtpFromEmail}
            />
          </Stack>
        )}
      </FormGroup>

      {selectedTemplate?.mode === FormType.HTML && <FieldDisplay form={form} />}

      <Divider />

      <FormGroup>
        <FormLabel sx={{ pl: 1 }} component="legend">
          自動返信メール
        </FormLabel>
        <FormControlLabel
          sx={{ pl: 1 }}
          control={
            <Checkbox
              value={true}
              {...form.register('isAutoReply', { nameOfValueProps: 'checked' })}
              checked={form?.values?.isAutoReply}
              disabled={hasSwitchContact}
            />
          }
          label="お客様に自動返信メールを送る"
        ></FormControlLabel>
        {profile?.course === Course.ENTERPRISE && (
          <FormHelperText sx={{ pb: 2 }}>個別送信先指定を設定している場合は自動返信メールの設定はできません</FormHelperText>
        )}
        {form?.values?.isAutoReply && selectedTemplate && <AutoReplyEmailComponent form={form} />}
      </FormGroup>
    </Stack>
  );
};

export default EditMailSettingComponent;
