import { FormScheduleSetting } from '@/types/FormTemplateTypes';
import { Checkbox, Divider, FormControlLabel, Stack, TextField, Typography } from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { FC, useEffect } from 'react';

import { useFormHandler } from '@/hooks/useFormHandler';

import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { ISO, isValidDate } from '@/utils/dateTime';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import * as Yup from 'yup';
import { FormScheduleSettingRequestDTO } from '@/modules/formBuilder/edit/dto/request.dto';

interface EditScheduleSettingComponentProps {}

const EditScheduleSettingComponent: FC<EditScheduleSettingComponentProps> = () => {
  const { editScheduleSetting, setError, selectedTemplate, isFormChanged } = useFormBuilder();
  const validationSchema = Yup.object<FormScheduleSetting>({
    releaseStartDate: Yup.date()
      .typeError('開始日時が無効です。')
      .nullable()
      .test('is-valid', '開始日時が無効です。', function (value) {
        if (!value) {
          return true;
        }

        return isValidDate(value);
      }),
    releaseEndDate: Yup.date()
      .typeError('終了日時が無効です。')
      .nullable()
      .test('is-valid', '開始日時が無効です。', function (value) {
        if (!value) return true;

        return isValidDate(value);
      })
      .test('is-later', '終了日が開始日より後になる', function (value) {
        if (!value) return true;

        const { releaseStartDate } = this.parent;

        if (!releaseStartDate || !value) return true;

        const start = new Date(releaseStartDate);
        const end = new Date(value);

        return end > start;
      }),
    displayTextBeforePublicForm: Yup.string().nullable().max(65353, '最大65,353 文字以内でご入力してください。'),
    displayTextAfterPublicForm: Yup.string().nullable().max(65353, '最大65,353 文字以内でご入力してください。'),
    displayTextHiddenForm: Yup.string().nullable().max(65353, '最大65,353 文字以内でご入力してください。'),
  });

  const form = useFormHandler<FormScheduleSettingRequestDTO>({
    initialValues: {
      releaseStartDate: selectedTemplate?.releaseStartDate ?? null,
      releaseEndDate: selectedTemplate?.releaseEndDate ?? null,
      displayTextBeforePublicForm: selectedTemplate?.formScheduleSetting?.displayTextBeforePublicForm ?? '',
      displayTextAfterPublicForm: selectedTemplate?.formScheduleSetting?.displayTextAfterPublicForm ?? '',
      displayTextHiddenForm: selectedTemplate?.formScheduleSetting?.displayTextHiddenForm ?? '',
      hideHiddenText: selectedTemplate?.formScheduleSetting?.hideHiddenText ?? false,
      maximumNumberFormsReceived: selectedTemplate?.formScheduleSetting?.maximumNumberFormsReceived ?? null,
    },
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: (e) => {
      console.log('submit', e);
    },
  });

  useEffect(() => {
    if (form.isValid) {
      editScheduleSetting(form.values);
    }

    setError(!form.isValid);
  }, [form.isValid, form.values]);

  useEffect(() => {
    if (!isFormChanged) {
      form.resetForm({
        values: {
          releaseStartDate: selectedTemplate?.releaseStartDate ?? null,
          releaseEndDate: selectedTemplate?.releaseEndDate ?? null,
          displayTextBeforePublicForm: selectedTemplate?.formScheduleSetting?.displayTextBeforePublicForm ?? '',
          displayTextAfterPublicForm: selectedTemplate?.formScheduleSetting?.displayTextAfterPublicForm ?? '',
          displayTextHiddenForm: selectedTemplate?.formScheduleSetting?.displayTextHiddenForm ?? '',
          hideHiddenText: selectedTemplate?.formScheduleSetting?.hideHiddenText ?? false,
          maximumNumberFormsReceived: selectedTemplate?.formScheduleSetting?.maximumNumberFormsReceived ?? null,
        },
      });
    }
  }, [isFormChanged]);

  return (
    <Stack direction="column" spacing={2}>
      <Typography variant="body1" fontSize={13} color="text.secondary" sx={{ pl: 2 }}>
        公開日時
      </Typography>
      <Typography variant="body1">公開開始日時</Typography>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DateTimePicker
          slotProps={{
            textField: {
              fullWidth: true,
              placeholder: '公開開始日時',
              variant: 'outlined',
              error: !!form?.errors?.releaseStartDate,
              helperText: form?.errors?.releaseStartDate ? String(form?.errors?.releaseStartDate) : '',
            },
          }}
          timeSteps={{ minutes: 5 }}
          format={ISO.DATE_TIME}
          {...form.register('releaseStartDate')}
          value={form?.values?.releaseStartDate ? dayjs(form?.values?.releaseStartDate) : null}
          onChange={(value) => {
            form.setFieldValue('releaseStartDate', isValidDate(value) ? new Date(value.toISOString()) : '');
          }}
          closeOnSelect={false}
        />
        <Typography variant="body1">公開終了日時</Typography>
        <DateTimePicker
          slotProps={{
            textField: {
              fullWidth: true,
              placeholder: '公開終了日時',
              variant: 'outlined',
              error: !!form?.errors?.releaseEndDate,
              helperText: form?.errors?.releaseEndDate ? String(form?.errors?.releaseEndDate) : '',
            },
          }}
          timeSteps={{ minutes: 5 }}
          format={ISO.DATE_TIME}
          {...form.register('releaseEndDate')}
          value={form?.values?.releaseEndDate ? dayjs(form?.values?.releaseEndDate) : null}
          onChange={(value) => {
            form.setFieldValue('releaseEndDate', isValidDate(value) ? value.utc().toISOString() : '');
          }}
          closeOnSelect={false}
        />
      </LocalizationProvider>
      <Divider />
      <Typography variant="body1" fontSize={13} color="text.secondary">
        フォーム公開前・終了後の表示テキスト
      </Typography>
      <TextField
        label="[開始前]の表示テキスト"
        multiline
        rows={3}
        error={!!form?.errors?.displayTextBeforePublicForm}
        helperText={
          form?.errors?.displayTextBeforePublicForm
            ? form?.errors?.displayTextBeforePublicForm
            : 'ステータスが[公開前]に設定されている時に表示される文言です'
        }
        {...form.register('displayTextBeforePublicForm')}
        value={form?.values?.displayTextBeforePublicForm ?? ''}
      />
      <TextField
        label="[終了後]の表示テキスト"
        multiline
        rows={3}
        error={!!form?.errors?.displayTextAfterPublicForm}
        helperText={
          form?.errors?.displayTextAfterPublicForm
            ? form?.errors?.displayTextAfterPublicForm
            : 'ステータスが[公開終了]に設定されている時に表示される文言です'
        }
        {...form.register('displayTextAfterPublicForm')}
        value={form?.values?.displayTextAfterPublicForm ?? ''}
      />
      <Divider />
      <Typography variant="body1" fontSize={13} color="text.secondary">
        フォーム非公開時の表示テキスト
      </Typography>
      <TextField
        label="「非公開」の表示テキスト"
        multiline
        rows={3}
        error={!!form?.errors?.displayTextHiddenForm}
        helperText={form?.errors?.displayTextHiddenForm}
        {...form.register('displayTextHiddenForm')}
        value={form?.values?.displayTextHiddenForm ?? ''}
      />
      <FormControlLabel
        control={
          <Checkbox
            value={true}
            {...form.register('hideHiddenText', { nameOfValueProps: 'checked' })}
            checked={form?.values.hideHiddenText ?? false}
          />
        }
        label="非公開のテキストを表示しない"
      />
      <Divider />
      {/* Maximum Number of Forms Received */}
      <Typography variant="body1" fontSize={13} color="text.secondary">
        フォームの最大受信件数
      </Typography>
      <TextField
        inputProps={{ min: 0 }}
        type="number"
        label="最大受信件数（件）"
        helperText={
          <>
            {!!form?.errors?.maximumNumberFormsReceived && (
              <Typography component="span" variant="caption">
                整数を半角で入力してください
                <br />
              </Typography>
            )}
            <Typography component="span" variant="caption">
              受信件数が設定件数に到達すると、フォームは自動的に公開終了となります <br />
              最大値は 9,999,999,999 です。
            </Typography>
          </>
        }
        error={!!form?.errors?.maximumNumberFormsReceived}
        {...form.register('maximumNumberFormsReceived')}
        value={form?.values?.maximumNumberFormsReceived ?? ''}
      />
    </Stack>
  );
};

export default EditScheduleSettingComponent;
