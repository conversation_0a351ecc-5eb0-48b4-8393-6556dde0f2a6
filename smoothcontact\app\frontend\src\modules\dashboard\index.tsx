import SCCard from '@/components/common/SCCard';
import SCCollapseCard from '@/components/common/SCCollapseCard';
import SCSimpleCard from '@/components/common/SCSimpleCard';
import AddIcon from '@mui/icons-material/Add';
import { Grid, Stack } from '@mui/material';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import * as React from 'react';
import { useNavigate } from 'react-router-dom';

export const DashBoardModule: React.FC = () => {
  const navigate = useNavigate();

  const handleGotoComponent = () => {
    navigate('/component');
  };

  return (
    <Box>
      <Box bgcolor="white" sx={{ py: 2 }}>
        <Container maxWidth="md">
          <SCCollapseCard title="新しいフォームを作成" icon={AddIcon}>
            <Grid container spacing={2} marginY={1}>
              <Grid item xs={3}>
                <SCCard title="空白のフォーム" icon={AddIcon} cardStyle="dashed" />
              </Grid>
              <Grid item xs={3}>
                <SCCard title="簡易お問い合わせ" />
              </Grid>
              <Grid item xs={3}>
                <SCCard title="アンケート" label="Pro" />
              </Grid>
              <Grid item xs={3}>
                <SCCard title="Component" label="Pro" onClick={handleGotoComponent} />
              </Grid>
            </Grid>
          </SCCollapseCard>
        </Container>
      </Box>
      <Box bgcolor="grey[50]" sx={{ py: 2 }}>
        <Container maxWidth="md">
          <SCSimpleCard>
            <Stack direction="column" justifyContent="center" alignItems="center" spacing={2} p={3}>
              <Typography variant="h5" fontWeight="bold">
                フォームはまだありません
              </Typography>
              <Typography variant="body1">空白のフォームを選択するか、上で別のテンプレートを選択して作成を開始してください</Typography>
            </Stack>
          </SCSimpleCard>
        </Container>
      </Box>
    </Box>
  );
};
