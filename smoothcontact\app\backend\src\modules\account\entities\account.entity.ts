import { Column, Entity } from 'typeorm';

import { BaseEntity } from '@/core/entity/base.entity';
import { LoginType } from '@/utils/digitalStage.util';

export enum Course {
  FREE = 1,
  PRO = 2,
  ENTERPRISE = 3,
}

@Entity('account')
export class AccountEntity extends BaseEntity {
  @Column({
    name: 'email',
    type: 'varchar',
    length: 255,
  })
  email: string;

  @Column({
    name: 'shop',
    type: 'varchar',
    length: 255,
  })
  shop: string;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 255,
  })
  name: string;

  @Column({
    name: 'pwd',
    type: 'varchar',
    length: 255,
  })
  pwd: string;

  @Column({
    name: 'avatar',
    type: 'varchar',
    length: 1024,
    nullable: true,
  })
  avatar: string;

  @Column({
    name: 'tour_completed',
    type: 'tinyint',
    nullable: false,
  })
  tourCompleted: boolean;

  @Column({
    name: 'last_change_password_at',
    type: 'datetime',
    nullable: true,
  })
  lastChangePasswordAt: Date;

  @Column({
    name: 'sc_account_id',
    type: 'integer',
    nullable: true,
  })
  scAccountId: number;

  @Column({
    name: 'username',
    type: 'varchar',
    length: 255,
  })
  username: string;

  @Column({
    name: 'access_token',
    type: 'varchar',
    length: 255,
  })
  accessToken: string;

  @Column({
    name: 'login_type',
    type: 'varchar',
    nullable: true,
  })
  loginType: LoginType;

  @Column({
    name: 'course',
    type: 'integer',
    nullable: true,
  })
  course: Course;

  @Column({
    name: 'user_key',
    type: 'text',
    nullable: true,
  })
  userKey: string;

  @Column({
    name: 'expiration_date',
    type: 'datetime',
    nullable: true,
  })
  expirationDate: Date;

  @Column({
    name: 'mfa_common_key',
    type: 'varchar',
    length: 255,
  })
  mfaCommonKey: string;

  @Column({
    name: 'last_login_date',
    type: 'datetime',
    nullable: true,
  })
  lastLoginDate?: Date;
}
