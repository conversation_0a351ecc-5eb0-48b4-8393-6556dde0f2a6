import basicImage from '@/assets/templates/thumb/basic.png';
import whiteImage from '@/assets/templates/thumb/basic01_white.png';
import blackImage from '@/assets/templates/thumb/basic02_black.png';
import blueImage from '@/assets/templates/thumb/basic03_blue.png';
import greenImage from '@/assets/templates/thumb/basic04_green.png';
import navyImage from '@/assets/templates/thumb/basic05_navy.png';
import purpleImage from '@/assets/templates/thumb/basic06_purple.png';
import redImage from '@/assets/templates/thumb/basic07_red.png';
import yellowImage from '@/assets/templates/thumb/basic08_yellow.png';
import { COLOR_DEFAULT, FONT_DEFAULT, FormBorderColors } from '@/common/constants';
import {
  FormColorSetting,
  FormContainerType,
  FormElement,
  FormElementChildrenType,
  FormEmbedAppSetting,
  FormHistoryType,
  FormMailSetting,
  FormScheduleSetting,
  FormStatus,
  GeneralSetting,
  NewFormMode,
  TemplateType,
} from '@/types/FormTemplateTypes';
import AlternateEmailIcon from '@mui/icons-material/AlternateEmail';
import ArrowDropDownCircleIcon from '@mui/icons-material/ArrowDropDownCircle';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import CommentIcon from '@mui/icons-material/Comment';
import EventIcon from '@mui/icons-material/Event';
import NotesIcon from '@mui/icons-material/Notes';
import PermContactCalendarIcon from '@mui/icons-material/PermContactCalendar';
import PersonIcon from '@mui/icons-material/Person';
import PhoneIcon from '@mui/icons-material/Phone';
import PlaceIcon from '@mui/icons-material/Place';
import RadioButtonCheckedIcon from '@mui/icons-material/RadioButtonChecked';
import TitleIcon from '@mui/icons-material/Title';
import { generateID } from './helper';

export enum FormControlNames {
  STEP_CONTAINER = 'step-container',
  INPUT_TEXTFIELD = 'text-field',
  INPUT_MULTILINE = 'multiline-text-field',
  COMMENT = 'comment',
  CHECKBOX = 'checkbox',
  RADIO = 'radio-group',
  DROPDOWN = 'select-drop-down',
  DATE = 'date-field',
  TIME = 'time-field',
  FILE_UPLOAD = 'file-upload',
  IMAGE_UPLOAD = 'image-upload',
  TOGGLE = 'toggle',
  CHECKLIST = 'checklist',
  SIGNATURE = 'signature',
  MULTI_CHOICES = 'multi-choices',
  SCAN_CODE = 'scan-code',
  VERIFIED_ID = 'verified-id',
  FULL_NAME = 'fullname',
  PHONE = 'phone',
  EMAIL = 'email',
  ADDRESS = 'address',
  BIRTHDAY = 'birthday',
  SUBMIT = 'submit',
}

export const FormControlTitle: { [x: string]: any } = {
  [FormControlNames.STEP_CONTAINER]: 'ページヘッダー',
  [FormControlNames.INPUT_TEXTFIELD]: 'テキスト',
  [FormControlNames.INPUT_MULTILINE]: '段落テキスト',
  [FormControlNames.COMMENT]: '説明文',
  [FormControlNames.CHECKBOX]: 'チェックボックス',
  [FormControlNames.RADIO]: 'ラジオボタン',
  [FormControlNames.DROPDOWN]: 'プルダウン',
  [FormControlNames.DATE]: '日時',
  [FormControlNames.FILE_UPLOAD]: '添付ファイル',
  [FormControlNames.IMAGE_UPLOAD]: 'IMAGE UPLOAD',
  [FormControlNames.CHECKLIST]: 'チェックボックス',
  [FormControlNames.SIGNATURE]: 'SIGNATURE',
  [FormControlNames.MULTI_CHOICES]: 'MULTI CHOICES',
  [FormControlNames.SCAN_CODE]: 'SCAN CODE',
  [FormControlNames.VERIFIED_ID]: 'VERIFIED ID',
  [FormControlNames.FULL_NAME]: '氏名',
  [FormControlNames.PHONE]: '電話番号',
  [FormControlNames.EMAIL]: 'メールアドレス',
  [FormControlNames.ADDRESS]: '住所',
  [FormControlNames.BIRTHDAY]: '生年月日',
  [FormControlNames.SUBMIT]: '送信',
};

export const FormTextDataTypes = {
  TEXT: 'text',
  NUMBER: 'number',
  ALPHANUMERIC: 'alphanumeric',
};

export const FormItemTypes = {
  CONTROL: 'control',
  CONTAINER: 'container',
  BUTTON: 'button',
};

export const FormAction = {
  REPORT: 'form-report',
  DETAIL: 'form-details',
  SHARING: 'form-sharing',
  EDITING: 'form-editing',
  SUBMISSION: 'form-submission',
  DUPLICATE: 'form-duplicate',
  DELETE: 'form-delete',
  FORM_TYPE: 'form-type',
};

export const FormContainerList: FormContainerType[] = [
  {
    id: '',
    controlName: FormControlNames.STEP_CONTAINER,
    displayText: 'ワークフローステップ',
    itemType: FormItemTypes.CONTAINER,
    heading: 'お問い合わせフォーム',
    description:
      'お問い合わせありがとうございます。 <br><br>ご返信までに３〜４営業日いただいております。<br> あらかじめご了承ください。<br>※ お客さまの入力情報はSSL暗号化通信により安全に送信されます。',
    display: 'left',
  },
];

export enum FormCategory {
  TEXT_ELEMENT = 'TEXT_ELEMENT',
  DATE_ELEMENT = 'DATE_ELEMENT',
  PERSONAL_ELEMENT = 'PERSONAL_ELEMENT',
  CHOICE_ELEMENT = 'CHOICE_ELEMENT',
  MEDIA_ELEMENT = 'MEDIA_ELEMENT',
}

export const FormCategoryText: { [key in FormCategory]: string } = {
  [FormCategory.TEXT_ELEMENT]: 'テキスト',
  [FormCategory.DATE_ELEMENT]: '日時',
  [FormCategory.PERSONAL_ELEMENT]: 'パーソナル',
  [FormCategory.CHOICE_ELEMENT]: '選択肢',
  [FormCategory.MEDIA_ELEMENT]: 'メディア',
};

export const FormControlInitialValue: { [x: string]: any } = {
  [FormControlNames.FILE_UPLOAD]: null,
  [FormControlNames.IMAGE_UPLOAD]: null,
  [FormControlNames.TOGGLE]: false,
  [FormControlNames.CHECKLIST]: [],
  [FormControlNames.MULTI_CHOICES]: [],
};

export const FormControlDefaultValue: { [x: string]: any } = {
  [FormControlNames.INPUT_TEXTFIELD]: '由美子',
  [FormControlNames.INPUT_MULTILINE]:
    'お問い合わせありがとうございます。 <br><br>ご返信までに３〜４営業日いただいております。<br> あらかじめご了承ください。<br>※ お客さまの入力情報はSSL暗号化通信により安全に送信されます。',
  [FormControlNames.COMMENT]: 'これはコメントのサンプルです',
  [FormControlNames.CHECKBOX]: false,
  [FormControlNames.RADIO]: '',
  [FormControlNames.DROPDOWN]: '',
  [FormControlNames.DATE]: {
    date: '2024/05/09',
    time: '12:00',
    dateTime: '2024/05/09 12:00',
  },
  [FormControlNames.TIME]: '',
  [FormControlNames.FILE_UPLOAD]: null,
  [FormControlNames.IMAGE_UPLOAD]: null,
  [FormControlNames.TOGGLE]: false,
  [FormControlNames.CHECKLIST]: [],
  [FormControlNames.SIGNATURE]: null,
  [FormControlNames.MULTI_CHOICES]: [],
  [FormControlNames.SCAN_CODE]: '',
  [FormControlNames.VERIFIED_ID]: '',
  [FormControlNames.FULL_NAME]: '山田 太郎',
  [FormControlNames.PHONE]: '09012345678',
  [FormControlNames.EMAIL]: '<EMAIL>',
  [FormControlNames.ADDRESS]: {
    postalCode: '100-0000',
    prefecture: '東京都',
    city: '千代田区',
    town: '千代田1-1-1',
    street: '千代田ビル',
    building: 'ビル名',
  },
  [FormControlNames.BIRTHDAY]: '2000/01/01',
};

export const FormControlList = [
  {
    id: '',
    controlName: FormControlNames.INPUT_TEXTFIELD,
    displayText: 'テキスト',
    placeholder: 'テキスト',
    description: '',
    labelName: 'テキスト',
    itemType: FormItemTypes.CONTROL,
    dataType: FormTextDataTypes.TEXT,
    icon: TitleIcon,
    required: false,
    category: FormCategory.TEXT_ELEMENT,
    containerId: '',
    characterType: 'text',
  },
  {
    id: '',
    controlName: FormControlNames.INPUT_MULTILINE,
    displayText: '段落テキスト',
    description: '',
    placeholder: '段落テキスト',
    labelName: '段落テキスト',
    rows: 4,
    itemType: FormItemTypes.CONTROL,
    icon: NotesIcon,
    required: false,
    category: FormCategory.TEXT_ELEMENT,
    containerId: '',
  },
  {
    id: '',
    controlName: FormControlNames.COMMENT,
    displayText: '説明文',
    description: '説明文が入ります。',
    placeholder: '',
    labelName: '説明文',
    rows: 4,
    itemType: FormItemTypes.CONTROL,
    icon: CommentIcon,
    category: FormCategory.TEXT_ELEMENT,
    containerId: '',
  },
  {
    id: '',
    controlName: FormControlNames.FULL_NAME,
    displayText: '氏名',
    description: '',
    placeholder: 'お名前 ',
    fullNamePlaceholder: {
      full: 'お名前',
      fullPronunciation: 'フリガナ',
      first: '名',
      last: '姓',
      firstPronunciation: 'メイ',
      lastPronunciation: 'セイ',
    },
    labelName: '氏名',
    rows: 4,
    itemType: FormItemTypes.CONTROL,
    icon: PersonIcon,
    required: false,
    category: FormCategory.PERSONAL_ELEMENT,
    containerId: '',
  },
  {
    id: '',
    controlName: FormControlNames.PHONE,
    displayText: '電話番号',
    description: '',
    placeholder: '電話番号',
    labelName: '電話番号',
    rows: 4,
    itemType: FormItemTypes.CONTROL,
    icon: PhoneIcon,
    required: false,
    category: FormCategory.PERSONAL_ELEMENT,
    containerId: '',
  },
  {
    id: '',
    controlName: FormControlNames.EMAIL,
    displayText: 'メールアドレス',
    description: '',
    placeholder: 'メールアドレス',
    labelName: 'メールアドレス',
    rows: 4,
    itemType: FormItemTypes.CONTROL,
    icon: AlternateEmailIcon,
    required: false,
    category: FormCategory.PERSONAL_ELEMENT,
    containerId: '',
    verifyEmail: false,
    verifyEmailPlaceholder: '確認用メールアドレス',
  },
  {
    id: '',
    controlName: FormControlNames.ADDRESS,
    displayText: '住所',
    description: '',
    placeholder: '',
    labelName: '住所',
    rows: 4,
    itemType: FormItemTypes.CONTROL,
    icon: PlaceIcon,
    required: false,
    category: FormCategory.PERSONAL_ELEMENT,
    containerId: '',
  },
  {
    id: '',
    controlName: FormControlNames.BIRTHDAY,
    displayText: '生年月日',
    description: '',
    placeholder: '生年月日',
    labelName: '生年月日',
    rows: 4,
    itemType: FormItemTypes.CONTROL,
    icon: PermContactCalendarIcon,
    required: false,
    category: FormCategory.PERSONAL_ELEMENT,
    containerId: '',
    limitedAge: 0,
    limitedAgeRequired: false,
  },
  {
    id: '',
    controlName: FormControlNames.DROPDOWN,
    displayText: 'プルダウン',
    description: '',
    labelName: 'プルダウン',
    itemType: FormItemTypes.CONTROL,
    icon: ArrowDropDownCircleIcon,
    required: false,
    items: [
      {
        id: 'option_1',
        value: 'Option 1',
        label: 'Option 1',
      },
      {
        id: 'option_2',
        value: 'Option 2',
        label: 'Option 2',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    containerId: '',
  },
  {
    id: '',
    controlName: FormControlNames.RADIO,
    displayText: 'ラジオボタン',
    description: '',
    labelName: 'ラジオボタン',
    itemType: FormItemTypes.CONTROL,
    icon: RadioButtonCheckedIcon,
    required: false,
    items: [
      {
        id: 'Radio1',
        value: 'Radio1',
        label: 'Radio1',
      },
      {
        id: 'Radio2',
        value: 'Radio2',
        label: 'Radio2',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    containerId: '',
  },
  {
    id: '',
    controlName: FormControlNames.CHECKLIST,
    displayText: 'チェックボックス',
    description: '',
    labelName: 'チェックボックス',
    itemType: FormItemTypes.CONTROL,
    icon: CheckCircleOutlineIcon,
    required: false,
    items: [
      {
        id: 'CheckA',
        value: 'CheckA',
        label: '選択肢A',
      },
      {
        id: 'CheckB',
        value: 'CheckB',
        label: '選択肢B',
      },
      {
        id: 'CheckC',
        value: 'CheckC',
        label: '選択肢C',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    containerId: '',
  },
  {
    id: '',
    controlName: FormControlNames.DATE,
    displayText: '日時',
    description: '',
    labelName: '日時',
    itemType: FormItemTypes.CONTROL,
    icon: EventIcon,
    required: false,
    category: FormCategory.DATE_ELEMENT,
    containerId: '',
    dataType: 'date',
    dateLimit: 'none',
    minuteStep: 5,
    showMinuteStep: false,
  },
  {
    id: '',
    controlName: FormControlNames.FILE_UPLOAD,
    displayText: '添付ファイル',
    description: '',
    labelName: '添付ファイル',
    itemType: FormItemTypes.CONTROL,
    icon: AttachFileIcon,
    required: false,
    category: FormCategory.MEDIA_ELEMENT,
    containerId: '',
  },
];

export enum FormTemplateModePresentType {
  WHITE = 'WHITE',
  BLACK = 'BLACK',
  BLUE = 'BLUE',
  GREEN = 'GREEN',
  NAVY = 'NAVY',
  PURPLE = 'PURPLE',
  RED = 'RED',
  YELLOW = 'YELLOW',
  BASIC = 'BASIC',
}

export const FORM_COLOR_SETTING_INIT: FormColorSetting = {
  generalSettings: {
    spacing: 30,
    spacingUnit: 'px',
    fontSize: 12,
    fontSizeUnit: 'px',
    color: '#000',
    borderColor: FormBorderColors.DEFAULT,
    fontFamily: FONT_DEFAULT,
  },
  animationSettings: {
    textEntryArea: 'no_animation',
    itemButton: 'no_animation',
  },
  buttonSettings: {
    text: '送信',
    submitText: '送信',
    confirmText: '確認',
    fontSize: 12,
    fontSizeUnit: 'px',
    borderRadius: 4,
    borderRadiusUnit: 'px',
    color: '#FFF',
    bgColor: '#1B367B',
    borderColor: '#000',
    fontFamily: FONT_DEFAULT,
  },
  entryFormSettings: {
    color: COLOR_DEFAULT,
    fontSize: 14,
    fontSizeUnit: 'px',
    borderRadius: 8,
    borderRadiusUnit: 'px',
    bgColor: '#FFF',
    borderColor: FormBorderColors.DEFAULT,
  },
  labelSettings: {
    fontSize: 12,
    fontSizeUnit: 'px',
    color: '#000',
    fontFamily: FONT_DEFAULT,
  },
  descriptionSettings: {
    fontSize: 12,
    fontSizeUnit: 'px',
    color: '#000',
    fontFamily: FONT_DEFAULT,
  },
  choiceSettings: {
    color: '#333333',
  },
  layoutMode: 'vertical',
  bgColor: '#FFF',
  titleSettings: {
    fontSize: 16,
    fontSizeUnit: 'px',
    color: '#000',
    fontFamily: FONT_DEFAULT,
  },
  optionMode: 'custom_mode',
  templateModeColor: FormTemplateModePresentType.BASIC,
};

export const FORM_MAIL_SETTING_INIT: FormMailSetting = {
  screenAfterSendingType: 'display_message',
  emailAddress: [],
  message: 'フォームが正常に送信されました。',
  specifiedUrl: '',
  isAutoReply: false,
  autoReplyEmailAddress: '',
  autoReplySenderName: '',
  autoReplySubject: '',
  isTextMail: false,
  textMailBody: `お問い合わせいただきありがとうございました。
  ２〜３営業日以内に担当者よりご連絡させていただきます。
  引き続きどうぞよろしくお願いします。`,
  isHtmlMail: false,
  isVerifyMailDomain: false,
  mailDomain: '',
  txtRecord: '',
  receiveEmailField: '',
  useCustomSMTP: false,
  smtpHost: '',
  smtpPort: 25,
  smtpUsername: '',
  smtpPassword: '',
  smtpFromEmail: '',
};

export const FORM_EMBED_APP_SETTING_INIT: FormEmbedAppSetting = {
  isEnableGA4Setting: false,
  gA4TrackingID: '',
  isEnableGoogleAdsSetting: false,
  globalSiteTag: '',
  eventSnippet: '',
  isLinkageYahoo: false,
  conversionMeasurementTags: '',
};

export const FORM_SCHEDULE_SETTING_INIT: FormScheduleSetting = {
  displayTextBeforePublicForm: 'まもなく受付開始いたします。もうしばらくおまちください。',
  displayTextAfterPublicForm: '受付は終了しました。たくさんの投稿ありがとうございました。',
  displayTextHiddenForm: 'フォームが見つかりませんでした。フォームURLが誤っているか、フォームが非公開または削除された可能性があります。',
  hideHiddenText: false,
  maximumNumberFormsReceived: null,
};

export const FORM_GENERAL_SETTING_INIT: GeneralSetting = {
  oGPImage: '',
  linkageDomain: '',
  isDisplaySearchEngine: true,
  receivedDataSaveFlag: true,
  isSettingReCAPTCHA: true,
  isSettingPrivacyPolicy: false,
  isDisplayTermsUse: false,
  isCombineIntoOneCheckbox: false,
  isDisplaySignUp: false,
  isDisplaySignUpSample: false,
  termsUse: '',
  policyLink: '',
  contactPerson: [],
  whitelistedDomain: [],
  captchaKey: '',
};

export interface FormTemplateModePresent {
  screenShot?: string;
  bgColor: string;
  color: string;
  buttonColor: string;
  borderColor?: string;
}

export const FormTemplateModePresets: {
  [key in FormTemplateModePresentType]: FormTemplateModePresent;
} = {
  [FormTemplateModePresentType.WHITE]: {
    screenShot: whiteImage,
    bgColor: 'transparent',
    color: '#555',
    buttonColor: '#555',
    borderColor: '#484848',
  },
  [FormTemplateModePresentType.BLACK]: {
    screenShot: blackImage,
    bgColor: '#000',
    color: '#FFF',
    buttonColor: '#444',
    borderColor: '#777',
  },
  [FormTemplateModePresentType.BLUE]: {
    screenShot: blueImage,
    bgColor: 'transparent',
    color: '#555',
    buttonColor: '#5ebcd7',
    borderColor: '#4ab3d2',
  },
  [FormTemplateModePresentType.GREEN]: {
    screenShot: greenImage,
    bgColor: 'transparent',
    color: '#555',
    buttonColor: '#9dad31',
    borderColor: '#8b992b',
  },
  [FormTemplateModePresentType.NAVY]: {
    screenShot: navyImage,
    bgColor: 'transparent',
    color: '#555',
    buttonColor: '#2e5075',
    borderColor: '#274363',
  },
  [FormTemplateModePresentType.PURPLE]: {
    screenShot: purpleImage,
    bgColor: 'transparent',
    color: '#555',
    buttonColor: '#6a478c',
    borderColor: '#5d3e7b',
  },
  [FormTemplateModePresentType.RED]: {
    screenShot: redImage,
    bgColor: 'transparent',
    color: '#555',
    buttonColor: '#b11527',
    borderColor: '#9a1222',
  },
  [FormTemplateModePresentType.YELLOW]: {
    screenShot: yellowImage,
    bgColor: 'transparent',
    color: '#555',
    buttonColor: '#fbb446',
    borderColor: '#faaa2d',
  },
  [FormTemplateModePresentType.BASIC]: {
    screenShot: basicImage,
    bgColor: 'transparent',
    color: '#333',
    buttonColor: '#5cb85c',
    borderColor: '#4cae4c',
  },
};

export const CONTACT_FORM_TEMPLATE_CONTAINER: FormContainerType = {
  id: '',
  display: 'left',
  controlName: FormControlNames.STEP_CONTAINER,
  itemType: FormItemTypes.CONTAINER,
  displayText: 'ワークフローステップ',
  heading: 'お問い合わせフォーム',
  description:
    'お問い合わせありがとうございます。 <br><br>ご返信までに３〜４営業日いただいております。<br> あらかじめご了承ください。<br>※ お客さまの入力情報はSSL暗号化通信により安全に送信されます。',
};

export const CONTACT_FORM_TEMPLATE_CHILDREN: FormElementChildrenType[] = [
  {
    id: '',
    controlName: FormControlNames.INPUT_TEXTFIELD,
    displayText: 'お名前',
    placeholder: 'お名前',
    description: '',
    labelName: 'お名前',
    itemType: FormItemTypes.CONTROL,
    dataType: FormTextDataTypes.TEXT,
    icon: null,
    required: true,
    category: FormCategory.TEXT_ELEMENT,
    containerId: '',
    characterType: 'text',
    index: 0,
  },
  {
    id: '',
    controlName: FormControlNames.EMAIL,
    displayText: 'テキスト',
    placeholder: 'メールアドレス',
    description: '',
    labelName: 'メールアドレス',
    itemType: FormItemTypes.CONTROL,
    icon: AlternateEmailIcon,
    required: true,
    requiredEmailConfirm: false,
    category: FormCategory.PERSONAL_ELEMENT,
    containerId: '',
    index: 1,
  },
  {
    id: '',
    controlName: FormControlNames.INPUT_MULTILINE,
    displayText: '段落テキスト',
    description: '',
    placeholder: 'お問い合わせ内容 ',
    labelName: 'お問い合わせ内容 ',
    rows: 4,
    itemType: FormItemTypes.CONTROL,
    icon: null,
    required: true,
    category: FormCategory.TEXT_ELEMENT,
    containerId: '',
    index: 2,
  },
];

export const QUESTIONNAIRE_FORM_TEMPLATE_CONTAINER: FormContainerType = {
  id: '',
  display: 'left',
  heading: 'クイックアンケート',
  itemType: FormItemTypes.CONTAINER,
  controlName: FormControlNames.STEP_CONTAINER,
  description:
    '当社製品をご利用いただき、誠にありがとうございます。<br>サービスの向上を目指し、お客さまアンケートを実施しております。ご協力をお願い申し上げます。<br><br>■ アンケート実施期間<br>20XX年XX月XX日 〜 20XX年XX月XX日<br><br>■ お問い合わせ先<br>製品サポート窓口&nbsp;&nbsp;&nbsp;&nbsp;担当◯◯◯◯<br><br>■問い合わせ<br>メール：<EMAIL><br>電話&nbsp;&nbsp;&nbsp;&nbsp;：00-0000-0000<br><br>【個人情報の使用目的について】<br>ご記入いただいた個人情報は以下の目的でのみ使用し、お客様の同意なく第三者に提供することはございません。<br>● 統計データの作成および今後の製品やサービスの開発のため<br>● 統計データの作業を外部に委託する場合は、秘密保持契約を締結し、お客様の個人情報を厳密に保護します。<br>● 粗品やアンケート以外の発送のために、配送業者に住所・氏名・電話番号を開示する場合があります。<br><br>※ お客さまの入力情報はSSL暗号化通信により安全に送信されます。',
  displayText: 'ワークフローステップ',
};

export const QUESTIONNAIRE_FORM_TEMPLATE_CHILDREN: FormElementChildrenType[] = [
  {
    id: '',
    icon: null,
    items: [
      {
        id: '',
        label: 'テレビ',
        value: 'テレビ',
      },
      {
        id: '',
        label: '新聞',
        value: '新聞',
      },
      {
        id: '',
        label: 'ラジオ',
        value: 'ラジオ',
      },
      {
        id: '',
        label: '雑誌',
        value: '雑誌',
      },
      {
        id: '',
        label: '企業のホームページ',
        value: '企業のホームページ',
      },
      {
        id: '',
        label: 'クチコミサイト・価格比較サイト',
        value: 'クチコミサイト・価格比較サイト',
      },
      {
        id: '',
        label: '検索エンジン',
        value: '検索エンジン',
      },
      {
        id: '',
        label: 'ダイレクトメール・チラシ',
        value: 'ダイレクトメール・チラシ',
      },
      {
        id: '',
        label: 'インターネット広告',
        value: 'インターネット広告',
      },
      {
        id: '',
        label: 'メールマガジン',
        value: 'メールマガジン',
      },
      {
        id: '',
        label: 'SNS・ブログ',
        value: 'SNS・ブログ',
      },
      {
        id: '',
        label: '家族・友人・知人の紹介',
        value: '家族・友人・知人の紹介',
      },
      {
        id: '',
        label: '店頭で実物を見て',
        value: '店頭で実物を見て',
      },
      {
        id: '',
        label: '店員の説明',
        value: '店員の説明',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    itemType: FormItemTypes.CONTROL,
    required: true,
    labelName: 'Q1.当社の製品を知ったきっかけを教えてください。  ',
    containerId: '',
    controlName: FormControlNames.CHECKLIST,
    description: '',
    displayMode: 'horizontal',
    displayText: 'チェックボックス',
    displayOtherOption: true,
  },
  {
    id: '',
    icon: null,
    index: 1,
    items: [
      {
        id: '',
        label: 'メーカー・ブランドが好きだから',
        value: 'メーカー・ブランドが好きだから',
      },
      {
        id: '',
        label: 'デザインが好きだから',
        value: 'デザインが好きだから',
      },
      {
        id: '',
        label: '性能が良いから',
        value: '性能が良いから',
      },
      {
        id: '',
        label: '話題だから',
        value: '話題だから',
      },
      {
        id: '',
        label: 'ネットの評判・クチコミがよいから',
        value: 'ネットの評判・クチコミがよいから',
      },
      {
        id: '',
        label: '身近な人からの評判がよいから',
        value: '身近な人からの評判がよいから',
      },
      {
        id: '',
        label: 'サイズが丁度良いから',
        value: 'サイズが丁度良いから',
      },
      {
        id: '',
        label: '広告が手頃だから',
        value: '広告が手頃だから',
      },
      {
        id: '',
        label: '価格が手頃だから',
        value: '価格が手頃だから',
      },
      {
        id: '',
        label: '広告が魅力的だから',
        value: '広告が魅力的だから',
      },
      {
        id: '',
        label: '店頭で目立っていたから',
        value: '店頭で目立っていたから',
      },
      {
        id: '',
        label: '店頭で薦められたから',
        value: '店頭で薦められたから',
      },
      {
        id: '',
        label: '使ってみて良かったから',
        value: '使ってみて良かったから',
      },
      {
        id: '',
        label: 'アフターサービスが充実しているから',
        value: 'アフターサービスが充実しているから',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    itemType: FormItemTypes.CONTROL,
    required: true,
    labelName: 'Q2.購入した理由を教えてください。（複数回答可）  ',
    containerId: '',
    controlName: FormControlNames.CHECKLIST,
    description: '',
    displayText: 'チェックボックス',
    displayOtherOption: true,
    displayMode: 'horizontal',
  },
  {
    id: '',
    icon: null,
    index: 1,
    items: [
      {
        id: '',
        label: '大変満足',
        value: '大変満足',
      },
      {
        id: '',
        label: '満足',
        value: '満足',
      },
      {
        id: '',
        label: 'どちらともいえない',
        value: 'どちらともいえない',
      },
      {
        id: '',
        label: 'やや不満',
        value: 'やや不満',
      },
      {
        id: '',
        label: '不満',
        value: '不満',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    itemType: FormItemTypes.CONTROL,
    required: false,
    labelName: 'Q3.製品について、どれくらい満足していますか？',
    containerId: '',
    controlName: FormControlNames.RADIO,
    description: '',
    displayText: 'ラジオボタン',
    displayOtherOption: true,
    displayMode: 'horizontal',
  },
  {
    id: '',
    icon: null,
    index: 3,
    items: [
      {
        id: '',
        label: '性能・機能',
        value: '性能・機能',
      },
      {
        id: '',
        label: '価格',
        value: '価格',
      },
      {
        id: '',
        label: '使いやすさ',
        value: '使いやすさ',
      },
      {
        id: '',
        label: 'デザイン性',
        value: 'デザイン性',
      },
      {
        id: '',
        label: '信頼性',
        value: '信頼性',
      },
      {
        id: '',
        label: 'アフターサービス',
        value: 'アフターサービス',
      },
      {
        id: '',
        label: '特に無い',
        value: '特に無い',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    itemType: FormItemTypes.CONTROL,
    required: true,
    labelName: 'Q4.他社製品と比べて、当製品が優れていると思うものすべて選択してください。（複数選択可） ',
    containerId: '',
    controlName: FormControlNames.CHECKLIST,
    description: '',
    displayText: 'チェックボックス',
    displayMode: 'horizontal',
  },
  {
    id: '',
    icon: null,
    index: 2,
    category: FormCategory.TEXT_ELEMENT,
    dataType: FormTextDataTypes.TEXT,
    itemType: FormItemTypes.CONTROL,
    required: false,
    labelName: 'Q5.製品に対して、ご意見・ご要望がございましたら、ご自由にお書きください。',
    containerId: '',
    controlName: FormControlNames.INPUT_TEXTFIELD,
    description: '',
    displayText: 'テキスト',
    placeholder: 'Q5.製品に対して、ご意見・ご要望がございましたら、ご自由にお書きください。',
    characterType: 'text',
  },
  {
    id: '',
    icon: null,
    index: 4,
    items: [
      {
        id: '',
        label: '男性',
        value: '男性',
      },
      {
        id: '',
        label: '女性',
        value: '女性',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    itemType: FormItemTypes.CONTROL,
    required: false,
    labelName: '性別',
    containerId: '',
    controlName: FormControlNames.RADIO,
    description: '',
    displayText: 'ラジオボタン',
    displayMode: 'horizontal',
  },
  {
    id: '',
    icon: null,
    index: 5,
    items: [
      {
        id: '',
        label: '10代以下',
        value: '10代以下',
      },
      {
        id: '',
        label: '20代',
        value: '20代',
      },
      {
        id: '',
        label: '30代',
        value: '30代',
      },
      {
        id: '',
        label: '40代',
        value: '40代',
      },
      {
        id: '',
        label: '50代',
        value: '50代',
      },
      {
        id: '',
        label: '60代',
        value: '60代',
      },
      {
        id: '',
        label: '70代以上',
        value: '70代以上',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    itemType: FormItemTypes.CONTROL,
    required: true,
    labelName: '年齢',
    containerId: '',
    controlName: FormControlNames.DROPDOWN,
    description: '',
    displayText: 'プルダウン',
  },
  {
    id: '',
    icon: null,
    index: 6,
    items: [
      {
        id: '',
        label: '会社員・役員',
        value: '会社員・役員',
      },
      {
        id: '',
        label: '自営業',
        value: '自営業',
      },
      {
        id: '',
        label: '公務員',
        value: '公務員',
      },
      {
        id: '',
        label: '学生',
        value: '学生',
      },
      {
        id: '',
        label: '専業主婦',
        value: '専業主婦',
      },
      {
        id: '',
        label: 'パート・アルバイト',
        value: 'パート・アルバイト',
      },
      {
        id: '',
        label: '無職',
        value: '無職',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    itemType: FormItemTypes.CONTROL,
    required: false,
    labelName: 'ご職業',
    containerId: '',
    controlName: FormControlNames.DROPDOWN,
    description: '',
    displayText: 'プルダウン',
  },
  {
    id: '',
    controlName: FormControlNames.FULL_NAME,
    displayText: '氏名',
    placeholder: 'お名前',
    fullNamePlaceholder: {
      full: 'お名前',
      fullPronunciation: 'フリガナ',
      first: '名',
      last: '姓',
      firstPronunciation: 'メイ',
      lastPronunciation: 'セイ',
    },
    description: '',
    labelName: '氏名',
    itemType: FormItemTypes.CONTROL,
    dataType: FormTextDataTypes.TEXT,
    icon: null,
    required: true,
    category: FormCategory.PERSONAL_ELEMENT,
    containerId: '',
    characterType: 'text',
    index: 7,
  },
  {
    id: '',
    controlName: FormControlNames.EMAIL,
    displayText: 'メールアドレス',
    placeholder: 'メールアドレス',
    description: '',
    labelName: 'メールアドレス',
    rows: 4,
    itemType: FormItemTypes.CONTROL,
    icon: AlternateEmailIcon,
    required: true,
    requiredEmailConfirm: false,
    category: FormCategory.PERSONAL_ELEMENT,
    containerId: '',
    index: 8,
  },
];

export const APPLICATION_FORM_TEMPLATE_CONTAINER: FormContainerType = {
  id: '',
  display: 'left',
  heading: 'セミナーお申し込みフォーム',
  itemType: FormItemTypes.CONTAINER,
  controlName: FormControlNames.STEP_CONTAINER,
  description: `◯◯が開催する無料セミナーのお申し込みを受付しております。<br><br>参加をご希望される方は下記のフォームからお申し込みください。<br>イベント・セミナーお申し込み専用フォームに入力いただきました個人情報は下記の目的以外には使用しません。<br>・イベントやセミナーのお申し込みに対して受付のご連絡を差し上げること<br>・該当する製品<br>・サービスの関連情報をご案内すること<br>
お問い合わせは下記までお願いします。<br>電話：00-0000-0000<br>メール：<EMAIL><br><br>※ お客さまの入力情報はSSL暗号化通信により安全に送信されます。`,
  displayText: 'ワークフローステップ',
};

export const APPLICATION_FORM_TEMPLATE_CHILDREN: FormElementChildrenType[] = [
  {
    id: '',
    icon: null,
    items: [
      {
        id: '',
        label: 'セミナーA〈1月1日13時〜15時〉@東京',
        value: 'セミナーA〈1月1日13時〜15時〉@東京',
      },
      {
        id: '',
        label: 'セミナーB〈2月1日13時〜15時〉@名古屋',
        value: 'セミナーB〈2月1日13時〜15時〉@名古屋',
      },
      {
        id: '',
        label: 'セミナーC〈3月1日13時〜15時〉@大阪',
        value: 'セミナーC〈3月1日13時〜15時〉@大阪',
      },
      {
        id: '',
        label: 'セミナーD〈4月1日13時〜15時〉@福岡',
        value: 'セミナーD〈4月1日13時〜15時〉@福岡',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    itemType: FormItemTypes.CONTROL,
    required: true,
    labelName: 'ご希望のセミナー',
    containerId: '',
    controlName: FormControlNames.RADIO,
    description: '',
    displayMode: 'horizontal',
    displayText: 'ラジオボタン',
    index: 1,
  },
  {
    id: '',
    controlName: FormControlNames.DROPDOWN,
    displayText: 'プルダウン',
    description: '',
    labelName: '参加人数',
    itemType: FormItemTypes.CONTROL,
    icon: null,
    required: true,
    items: [
      {
        id: '',
        label: '1人',
        value: '1人',
      },
      {
        id: '',
        label: '2人',
        value: '2人',
      },
      {
        id: '',
        label: '3人',
        value: '3人',
      },
      {
        id: '',
        label: '4人',
        value: '4人',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    containerId: '',
    index: 2,
  },
  {
    id: '',
    controlName: FormControlNames.INPUT_TEXTFIELD,
    displayText: '御社名',
    placeholder: '御社名',
    description: '',
    labelName: '御社名',
    itemType: FormItemTypes.CONTROL,
    dataType: 'text',
    icon: null,
    required: false,
    category: FormCategory.TEXT_ELEMENT,
    containerId: '',
    characterType: 'text',
    index: 3,
  },
  {
    id: '',
    controlName: FormControlNames.INPUT_TEXTFIELD,
    displayText: '部署名',
    placeholder: '部署名',
    description: '',
    labelName: '部署名',
    itemType: FormItemTypes.CONTROL,
    dataType: 'text',
    icon: null,
    required: false,
    category: FormCategory.TEXT_ELEMENT,
    containerId: '',
    characterType: 'text',
    index: 4,
  },
  {
    id: '',
    controlName: FormControlNames.FULL_NAME,
    displayText: '氏名',
    placeholder: 'お名前',
    fullNamePlaceholder: {
      full: 'お名前',
      fullPronunciation: 'フリガナ',
      first: '名',
      last: '姓',
      firstPronunciation: 'メイ',
      lastPronunciation: 'セイ',
    },
    description: '',
    labelName: 'ご担当者様',
    itemType: FormItemTypes.CONTROL,
    dataType: 'text',
    icon: null,
    required: true,
    category: FormCategory.PERSONAL_ELEMENT,
    containerId: '',
    characterType: 'text',
    isUseFurigana: true,
    index: 5,
  },
  {
    id: '',
    controlName: FormControlNames.EMAIL,
    displayText: 'メールアドレス',
    placeholder: 'メールアドレス',
    description: '',
    labelName: 'メールアドレス',
    itemType: FormItemTypes.CONTROL,
    dataType: 'text',
    icon: AlternateEmailIcon,
    required: true,
    requiredEmailConfirm: false,
    category: FormCategory.PERSONAL_ELEMENT,
    containerId: '',
    index: 6,
  },
  {
    id: '',
    controlName: FormControlNames.ADDRESS,
    displayText: '住所',
    description: '',
    placeholder: '',
    labelName: 'ご住所',
    itemType: FormItemTypes.CONTROL,
    icon: null,
    required: false,
    category: 'PERSONAL_ELEMENT',
    containerId: '',
    level: 0,
    index: 7,
  },
  {
    id: '',
    controlName: FormControlNames.INPUT_TEXTFIELD,
    displayText: 'テキスト',
    placeholder: 'お電話番号 ',
    description: '',
    labelName: 'お電話番号 ',
    itemType: FormItemTypes.CONTROL,
    dataType: 'text',
    icon: null,
    required: false,
    category: FormCategory.TEXT_ELEMENT,
    containerId: '',
    characterType: 'text',
    index: 8,
  },
  {
    id: '',
    controlName: FormControlNames.RADIO,
    displayText: 'ラジオボタン',
    description: '',
    labelName: '当セミナーを何で知りましたか？  ',
    itemType: FormItemTypes.CONTROL,
    icon: null,
    required: true,
    items: [
      {
        id: '',
        label: 'ホームページ',
        value: 'ホームページ',
      },
      {
        id: '',
        label: 'メールマガジン',
        value: 'メールマガジン',
      },
      {
        id: '',
        label: '弊社スタッフからの紹介',
        value: '弊社スタッフからの紹介',
      },
      {
        id: '',
        label: '知人からの紹介',
        value: '知人からの紹介',
      },
      {
        id: '',
        label: 'ネット検索',
        value: 'ネット検索',
      },
      {
        id: '',
        label: 'Twitter / Facebook',
        value: 'Twitter / Facebook',
      },
    ],
    category: FormCategory.CHOICE_ELEMENT,
    containerId: '',
    displayOtherOption: true,
    displayMode: 'horizontal',
    index: 9,
  },
  {
    id: '',
    controlName: FormControlNames.INPUT_MULTILINE,
    displayText: 'テキスト',
    placeholder: 'ご質問などありましたらご記入ください。 ',
    description: '',
    labelName: 'ご質問などありましたらご記入ください。 ',
    itemType: FormItemTypes.CONTROL,
    dataType: 'text',
    icon: null,
    required: false,
    category: FormCategory.TEXT_ELEMENT,
    containerId: '',
    characterType: 'text',
    index: 10,
    rows: 4,
  },
];

export const RESERVATION_APPLICATION_FORM_TEMPLATE_CONTAINER: FormContainerType = {
  id: '',
  display: 'left',
  heading: 'ご予約受付フォーム',
  itemType: FormItemTypes.CONTAINER,
  controlName: FormControlNames.STEP_CONTAINER,
  description: `フォームを通じてのご予約を承っております。<br>お電話でのご予約は00-0000-0000までお問い合わせください。<br><br>※ご予約いただいてから3営業日以内に、予約の確定または日程調整のご連絡を差し上げます。<br>&nbsp;&nbsp;&nbsp;&nbsp;予約確定までは、正式なご予約とはなりませんのでご了承ください。<br>※ご希望の日時が満席の場合、別の日時をご提案させていただくことがございますので、予めご了承ください。<br><br>※ お客さまの入力情報はSSL暗号化通信により安全に送信されます。`,
  displayText: 'ワークフローステップ',
};

export const RESERVATION_APPLICATION_FORM_TEMPLATE_CHILDREN: FormElementChildrenType[] = [
  {
    id: '',
    icon: null,
    items: [
      {
        label: 'サービスA',
        value: 'サービスA',
      },
      {
        label: 'サービスB',
        value: 'サービスB',
      },
      {
        label: 'サービスC',
        value: 'サービスC',
      },
    ],
    category: 'CHOICE_ELEMENT',
    itemType: 'control',
    required: true,
    labelName: 'ご希望のサービス',
    containerId: '',
    controlName: FormControlNames.CHECKLIST,
    description: 'いまこの暗い巨きな石の建物のなかで考えていると、みんなむかし',
    displayText: 'チェックボックス',
    displayMode: 'horizontal',
  },
  {
    id: '',
    icon: null,
    category: 'DATE_ELEMENT',
    dataType: 'dateTime',
    itemType: 'control',
    required: true,
    dateLimit: 'none',
    labelName: 'ご予約希望日時 （第一希望）',
    minuteStep: 1,
    containerId: '',
    controlName: FormControlNames.DATE,
    description: '',
    displayText: '日時',
    showMinuteStep: true,
  },
  {
    id: '',
    icon: null,
    category: 'DATE_ELEMENT',
    dataType: 'dateTime',
    itemType: 'control',
    required: false,
    dateLimit: 'none',
    labelName: 'ご予約希望日時（第二希望）',
    minuteStep: 1,
    containerId: '',
    controlName: FormControlNames.DATE,
    description: '',
    displayText: '日時',
    showMinuteStep: true,
  },
  {
    id: '',
    icon: null,
    category: 'DATE_ELEMENT',
    dataType: 'dateTime',
    itemType: 'control',
    required: false,
    dateLimit: 'none',
    labelName: 'ご予約希望日時（第三希望）',
    minuteStep: 1,
    containerId: '',
    controlName: FormControlNames.DATE,
    description: '',
    displayText: '日時',
    showMinuteStep: true,
  },
  {
    id: '',
    icon: null,
    rows: 4,
    category: 'PERSONAL_ELEMENT',
    itemType: 'control',
    required: true,
    labelName: 'お名前',
    isAutoFill: true,
    containerId: '',
    controlName: FormControlNames.FULL_NAME,
    description: '',
    displayText: 'お名前',
    placeholder: 'お名前',
    fullNamePlaceholder: {
      full: 'お名前',
      fullPronunciation: 'フリガナ',
      first: '名',
      last: '姓',
      firstPronunciation: 'メイ',
      lastPronunciation: 'セイ',
    },
    autoFillType: 'hiragana',
    isUseFurigana: true,
  },
  {
    id: '',
    icon: null,
    rows: 4,
    category: 'PERSONAL_ELEMENT',
    itemType: 'control',
    required: true,
    labelName: 'お電話番号',
    containerId: '',
    controlName: FormControlNames.PHONE,
    description: '',
    displayText: '電話番号',
    placeholder: 'お電話番号',
  },
  {
    id: '',
    icon: AlternateEmailIcon,
    category: FormCategory.PERSONAL_ELEMENT,
    itemType: FormItemTypes.CONTROL,
    required: true,
    labelName: 'メールアドレス',
    containerId: '',
    controlName: FormControlNames.EMAIL,
    description: '',
    displayText: 'メールアドレス',
    placeholder: 'メールアドレス',
    requiredEmailConfirm: false,
  },
  {
    id: '',
    icon: null,
    items: [
      {
        label: '電話でのご連絡',
        value: '電話でのご連絡',
      },
      {
        label: 'メールでのご連絡',
        value: 'メールでのご連絡',
      },
    ],
    category: 'CHOICE_ELEMENT',
    itemType: 'control',
    required: true,
    labelName: 'ご連絡方法',
    containerId: '',
    controlName: FormControlNames.RADIO,
    description: '',
    displayMode: 'horizontal',
    displayText: 'ラジオボタン',
  },
  {
    id: '',
    icon: null,
    rows: 4,
    category: 'TEXT_ELEMENT',
    itemType: 'control',
    required: false,
    labelName: 'ご質問等がありましたら、ご記入ください。',
    containerId: '',
    controlName: FormControlNames.INPUT_MULTILINE,
    description: '',
    displayText: '段落テキスト',
    placeholder: 'ご質問等がありましたら、ご記入ください。',
  },
];

export enum FormTemplateType {
  EMPTY_FORM = 'EMPTY_FORM',
  SIMPLE_CONTACT_FORM = 'SIMPLE_CONTACT_FORM',
  QUICK_QUESTIONNAIRE = 'QUICK_QUESTIONNAIRE',
  APPLICATION_FORM = 'APPLICATION_FORM',
  RESERVATION_APPLICATION_FORM = 'RESERVATION_APPLICATION_FORM',
}

export const FormTemplateTypeText: { [key in FormTemplateType]: string } = {
  [FormTemplateType.EMPTY_FORM]: '空白のフォーム',
  [FormTemplateType.SIMPLE_CONTACT_FORM]: 'お問い合わせフォーム',
  [FormTemplateType.QUICK_QUESTIONNAIRE]: 'クイックアンケート',
  [FormTemplateType.APPLICATION_FORM]: '申込フォーム',
  [FormTemplateType.RESERVATION_APPLICATION_FORM]: 'ご予約受付フォーム',
};

export const FormTemplateTypeContent: { [key in FormTemplateType]: TemplateType } = {
  [FormTemplateType.EMPTY_FORM]: {
    name: FormTemplateTypeText[FormTemplateType.EMPTY_FORM],
    formElements: [{ container: { ...FormContainerList[0], id: generateID() }, children: [] }] as FormElement[],
    formGeneralSetting: FORM_GENERAL_SETTING_INIT,
    formColorSetting: FORM_COLOR_SETTING_INIT,
    formMailSetting: FORM_MAIL_SETTING_INIT,
    formScheduleSetting: FORM_SCHEDULE_SETTING_INIT,
    formEmbedAppSetting: FORM_EMBED_APP_SETTING_INIT,
    publishHistory: [] as FormHistoryType[],
    status: FormStatus.DRAFT,
    mode: NewFormMode.MANUAL,
  },
  [FormTemplateType.SIMPLE_CONTACT_FORM]: {
    name: FormTemplateTypeText[FormTemplateType.SIMPLE_CONTACT_FORM],
    formElements: [
      {
        container: { ...CONTACT_FORM_TEMPLATE_CONTAINER, id: generateID() },
        children: CONTACT_FORM_TEMPLATE_CHILDREN,
      },
    ] as FormElement[],
    formGeneralSetting: FORM_GENERAL_SETTING_INIT,
    formColorSetting: FORM_COLOR_SETTING_INIT,
    formMailSetting: FORM_MAIL_SETTING_INIT,
    formScheduleSetting: FORM_SCHEDULE_SETTING_INIT,
    formEmbedAppSetting: FORM_EMBED_APP_SETTING_INIT,
    publishHistory: [] as FormHistoryType[],
    status: FormStatus.DRAFT,
    mode: NewFormMode.MANUAL,
  },
  [FormTemplateType.QUICK_QUESTIONNAIRE]: {
    name: FormTemplateTypeText[FormTemplateType.QUICK_QUESTIONNAIRE],
    formElements: [
      { container: { ...QUESTIONNAIRE_FORM_TEMPLATE_CONTAINER, id: generateID() }, children: QUESTIONNAIRE_FORM_TEMPLATE_CHILDREN },
    ] as FormElement[],
    formGeneralSetting: FORM_GENERAL_SETTING_INIT,
    formColorSetting: FORM_COLOR_SETTING_INIT,
    formMailSetting: FORM_MAIL_SETTING_INIT,
    formScheduleSetting: FORM_SCHEDULE_SETTING_INIT,
    formEmbedAppSetting: FORM_EMBED_APP_SETTING_INIT,
    publishHistory: [] as FormHistoryType[],
    status: FormStatus.DRAFT,
    mode: NewFormMode.MANUAL,
  },
  [FormTemplateType.APPLICATION_FORM]: {
    name: FormTemplateTypeText[FormTemplateType.APPLICATION_FORM],
    formElements: [
      { container: { ...APPLICATION_FORM_TEMPLATE_CONTAINER, id: generateID() }, children: APPLICATION_FORM_TEMPLATE_CHILDREN },
    ] as FormElement[],
    formGeneralSetting: FORM_GENERAL_SETTING_INIT,
    formColorSetting: FORM_COLOR_SETTING_INIT,
    formMailSetting: FORM_MAIL_SETTING_INIT,
    formScheduleSetting: FORM_SCHEDULE_SETTING_INIT,
    formEmbedAppSetting: FORM_EMBED_APP_SETTING_INIT,
    publishHistory: [] as FormHistoryType[],
    status: FormStatus.DRAFT,
    mode: NewFormMode.MANUAL,
  },
  [FormTemplateType.RESERVATION_APPLICATION_FORM]: {
    name: FormTemplateTypeText[FormTemplateType.RESERVATION_APPLICATION_FORM],
    formElements: [
      {
        container: { ...RESERVATION_APPLICATION_FORM_TEMPLATE_CONTAINER, id: generateID() },
        children: RESERVATION_APPLICATION_FORM_TEMPLATE_CHILDREN,
      },
    ] as FormElement[],
    formGeneralSetting: FORM_GENERAL_SETTING_INIT as GeneralSetting,
    formColorSetting: FORM_COLOR_SETTING_INIT as FormColorSetting,
    formMailSetting: FORM_MAIL_SETTING_INIT as FormMailSetting,
    formScheduleSetting: FORM_SCHEDULE_SETTING_INIT as FormScheduleSetting,
    formEmbedAppSetting: FORM_EMBED_APP_SETTING_INIT as FormEmbedAppSetting,
    publishHistory: [] as FormHistoryType[],
    status: FormStatus.DRAFT,
    mode: NewFormMode.MANUAL,
  },
};

export const FormReportRangeDate = {
  lastMonth: '1M',
  lastHalfYear: '6M',
  lastYear: '1Y',
  all: 'ALL',
};

export const SettingFlag = {
  enabled: '有効',
  disabled: '無効',
};

export const OptionAfterSubmitForm = {
  displayMessage: 'display_message',
  specifiedUrl: 'specified_url',
};

export const ThemeColorSettingOptions = {
  STORE: 'store_theme_mode',
  TEMPLATE: 'template_mode',
  CUSTOM: 'custom_mode',
};

export const FormTextAnimationSetting = {
  no_animation: 'アニメーションなし',
  label_top: 'ラベルトップ',
  line_color: 'ラインカラー',
  focus_in: 'フォーカスイン',
};

export const FormButtonAnimationSetting = {
  no_animation: 'アニメーションなし',
  spread: 'スプレッド',
  close_down: 'クローズダウン',
  square: '3Dスクエア',
};

export enum FormInputAnimationTypes {
  NO_ANIMATION = 'no_animation',
  LABEL_TOP = 'label_top',
  LINE_COLOR = 'line_color',
  FOCUS_IN = 'focus_in',
}

export enum FormButtonAnimationTypes {
  NO_ANIMATION = 'no_animation',
  SPREAD = 'spread',
  CLOSE_DOWN = 'close_down',
  SQUARE = 'square',
}
