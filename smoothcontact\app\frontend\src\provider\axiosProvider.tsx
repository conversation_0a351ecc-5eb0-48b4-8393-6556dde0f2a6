import { renewToken } from '@/services/account.service';
import Axios, { AxiosInstance } from 'axios';
import { template } from 'lodash';
import { createContext, useContext, useMemo } from 'react';
import { useLoading } from './loadingProvider';
import { getAppAccessToken, getAppRefreshToken, removeAppAuthTokens, setAppAuthTokens } from '@/utils/helper';
import { createSearchParams, useLocation, useNavigate } from 'react-router-dom';

export const AxiosContext = createContext<AxiosInstance>(Axios.create());

export function AxiosProvider({ children }: React.PropsWithChildren<unknown>) {
  const navigate = useNavigate();
  const { setLoading } = useLoading();
  const location = useLocation();
  const redirectUrl = encodeURIComponent(location.pathname + location.search);

  const refreshToken = getAppRefreshToken();
  const axios = useMemo(() => {
    const axios = Axios.create({
      headers: {
        'Content-Type': 'application/json',
      },
      baseURL: import.meta.env.VITE_API_ENDPOINT,
    });

    axios.interceptors.request.use(
      (config) => {
        setLoading(true);
        if (config.path) {
          config.url = template(config.url)(config.path);
          delete config.path;
        }

        const customConfig = config;
        const accessToken = getAppAccessToken();
        if (accessToken) {
          customConfig.headers.Authorization = `Bearer ${accessToken}`;
        }

        return customConfig;
      },
      (error) => {
        setLoading(false);

        return Promise.reject(error);
      }
    );

    axios.interceptors.response.use(
      (response) => {
        setTimeout(() => {
          setLoading(false);
        }, 500);

        return response;
      },
      async (response) => {
        setLoading(false);
        const originConfig = response.config;

        if (Axios.isCancel(response)) {
          return;
        }

        const responseData = response?.response?.data;

        if (responseData.statusCode === 404 || (responseData.statusCode === 403 && responseData.messageCode !== 'TOKEN_EXPIRED')) {
          navigate('/404');

          throw response;
        }

        if (responseData.statusCode === 500) {
          navigate('/500');

          throw response;
        }

        if (responseData.statusCode === 401) {
          removeAppAuthTokens();
          navigate({
            pathname: '/login',
            search: createSearchParams({
              redirect: redirectUrl,
              reason: 'err401',
            }).toString(),
          });

          throw response;
        }

        if (responseData.messageCode === 'TOKEN_EXPIRED') {
          if (originConfig.hasRenewToken) {
            originConfig.hasRenewToken = false;
            removeAppAuthTokens();
            navigate({
              pathname: '/login',
              search: createSearchParams({
                redirect: redirectUrl,
                reason: 'tokenExpired',
              }).toString(),
            });

            throw response;
          }

          originConfig.hasRenewToken = true;
          try {
            const result = await axios({ ...renewToken, data: { refreshToken } });
            const accessToken = result?.data?.data?.accessToken;

            if (accessToken) {
              setAppAuthTokens(accessToken, refreshToken);
              originConfig.headers.Authorization = `Bearer ${accessToken}`;
              originConfig.hasRenewToken = false;

              return axios(originConfig);
            }

            originConfig.hasRenewToken = false;
            removeAppAuthTokens();
          } catch (error) {
            originConfig.hasRenewToken = false;
            removeAppAuthTokens();
          }
          navigate({
            pathname: '/login',
            search: createSearchParams({
              redirect: redirectUrl,
              reason: 'cannotRenewToken',
            }).toString(),
          });
          throw response;
        }

        setLoading(false);

        throw response;
      }
    );

    return axios;
  }, []);

  return <AxiosContext.Provider value={axios}>{children}</AxiosContext.Provider>;
}

export function useAxiosInstance() {
  return useContext(AxiosContext);
}
