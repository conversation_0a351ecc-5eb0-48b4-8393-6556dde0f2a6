import { Box, FormControl, FormControlLabel, FormLabel, Radio, RadioGroup, TextField, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { FormikValues } from 'formik';
import { FC } from 'react';

interface TextFieldProperties {
  formHandler: FormikValues;
}

const TextFieldProperty: FC<TextFieldProperties> = (props) => {
  const { formHandler } = props;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
      <Typography variant="body2">テキスト設定</Typography>
      <FormControl>
        <FormLabel id="characterType-label">入力タイプ</FormLabel>
        <RadioGroup name="characterType" {...formHandler.register('characterType')} value={formHandler?.values?.characterType ?? 'text'}>
          <FormControlLabel value="text" control={<Radio value="text" />} label="文字列" />
          <FormControlLabel value="number" control={<Radio value="number" />} label="数字" />
          <FormControlLabel value="alphanumeric" control={<Radio value="alphanumeric" />} label="英数字" />
        </RadioGroup>
      </FormControl>
      {(formHandler?.values?.characterType === 'number' || formHandler?.values?.characterType === 'alphanumeric') && (
        <FormControl>
          <FormLabel sx={{ pb: 1 }}>入力制限</FormLabel>
          <Stack direction="row" spacing={0.5} alignItems={'center'}>
            <TextField
              inputProps={{ min: 1 }}
              type="number"
              sx={{ maxWidth: '80px' }}
              variant="outlined"
              size="small"
              error={!!formHandler?.errors?.min}
              helperText={!!formHandler?.errors?.min && '最小値を入力してください'}
              {...formHandler.register('min')}
              value={formHandler?.values?.min ?? ''}
              onKeyDown={(e) => {
                if (e.key === 'e' || e.key === 'E' || e.key === '-' || e.key === '+') {
                  e.preventDefault();
                }
              }}
              FormHelperTextProps={{
                sx: {
                  position: 'absolute',
                  textWrap: 'nowrap',
                  bottom: '-30px',
                  left: 0,
                  margin: 0,
                },
              }}
            />
            <Typography variant="body2">〜</Typography>
            <TextField
              inputProps={{ min: 1 }}
              type="number"
              sx={{ maxWidth: '80px' }}
              variant="outlined"
              size="small"
              error={!!formHandler?.errors?.max}
              helperText={!!formHandler?.errors?.max && '最小値を入力してください'}
              {...formHandler.register('max')}
              value={formHandler?.values?.max ?? ''}
              onKeyDown={(e) => {
                if (e.key === 'e' || e.key === 'E' || e.key === '-' || e.key === '+') {
                  e.preventDefault();
                }
              }}
              FormHelperTextProps={{
                sx: {
                  position: 'absolute',
                  textWrap: 'nowrap',
                  bottom: '-30px',
                  left: 0,
                  margin: 0,
                },
              }}
            />
            <Typography variant="body2">文字以内</Typography>
          </Stack>
        </FormControl>
      )}
    </Box>
  );
};

export default TextFieldProperty;
