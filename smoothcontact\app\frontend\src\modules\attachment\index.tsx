import * as React from 'react';
import useLogic from '@/modules/attachment/useLogic';
import { CssBaseline, Typography } from '@mui/material';
import Container from '@mui/system/Container';
import Box from '@mui/system/Box';
import Stack from '@mui/system/Stack';

export const AttachmentModule: React.FC = () => {
  const { loading, attachment } = useLogic();

  return (
    <Container component="main" maxWidth="xs">
      <CssBaseline />
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
        gap={1}
      >
        <Stack textAlign={'center'}>
          <Typography variant="h2" gutterBottom>
            {loading ? 'リダイレクト中...' : '添付ファイルを開いています...'}
          </Typography>
          {attachment && attachment.url && (
            <Typography variant="body2" gutterBottom>
              お使いのブラウザが自動的にリダイレクトしない場合は、<a href={attachment.url}>[こちらをクリックしてください]</a>。
            </Typography>
          )}
        </Stack>
      </Box>
    </Container>
  );
};
