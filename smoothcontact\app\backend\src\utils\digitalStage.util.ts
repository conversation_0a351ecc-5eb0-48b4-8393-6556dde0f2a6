import axios, { AxiosResponse } from 'axios';
import * as qs from 'qs';

import { logger } from '@/core/logger/index.logger';

import { XmlParseUtil } from './xmlParse.util';

export const ROOT_XML_TAG = 'mypage-rpc';

export const EXPIRATION_ALERT_DAYS = 15;

export const PRO_COURSE = [
  'プロコース',
  'プロコース［年間一括］',
  'ビジネスコース',
  'ビジネスコース［年間一括］',
  'ビジネスコース［年払い］',
  'プロコース［ライセンス］',
  'ビジネスコース［ライセンス］',
  '基本コース',
  '基本コース［年払い］',
  'エントリーコース',
];

export enum LoginType {
  DEGITALSTAGEID = 'DEGITALSTAGEID',
  FACEBOOK = 'FACEBOOK',
  LINKEDIN = 'LINKEDIN',
  OEM = 'OEM',
  PUBLIC = 'PUBLIC',
  SC_ACCOUNT = 'SC_ACCOUNT',
  AI_TEMP = 'AI_TEMP',
  SHOPIFY = 'SHOPIFY',
}

export enum Course {
  FREE = 'FREE',
  PRO = 'PRO',
  ENTERPRISE = 'ENTERPRISE',
}

export const CourseValue: { [key in Course]: number } = {
  [Course.FREE]: 1,
  [Course.PRO]: 2,
  [Course.ENTERPRISE]: 3,
};

export const AUTO_UPDATE = 'update';
export const CHANGE_DS_ID = 'changeid';
export const DELETE_FORM = 'deleteform';

export class DigitalStageUtil {
  static async getDigitalStageIdResponse(id: string, password: string) {
    const url = process.env.MYPAGE_AUTH_URL;

    const params = qs.stringify({
      u: id,
      p: password,
    });

    const config = {
      headers: {
        'User-Agent': 'Smooth Contact DigitalStage ID Regist',
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-BiND-API-Control': 'X-BiND-API-Control',
      },
    };

    try {
      const response = await axios.post(url, params, config);

      return response;
    } catch (error) {
      logger.error('getDigitalStageIdResponse', error);
      throw new Error(`getDigitalStageIdResponse: ${error.message}`);
    }
  }

  static async getDigitalStageInfo(id: string, password: string): Promise<any> {
    try {
      const httpResponse: AxiosResponse = await this.getDigitalStageIdResponse(id, password);
      const objectResult = await XmlParseUtil.parse(httpResponse?.data, ROOT_XML_TAG);

      if (objectResult?.status !== 'success') {
        return null;
      }

      return objectResult?.data;
    } catch (error) {
      logger.error('getDigitalStageInfo', error);
    }

    return null;
  }

  static async getUserRegistrationInformationResponse(email: string): Promise<AxiosResponse<any>> {
    const url = process.env.MYPAGE_SCLINK_URL;
    const params = qs.stringify({
      u: email,
    });

    const config = {
      headers: {
        'User-Agent': 'Smooth Contact User Key Regist',
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-BiND-API-Control': 'X-BiND-API-Control',
      },
    };

    try {
      const response = await axios.post(url, params, config);

      return response;
    } catch (error) {
      logger.error('getUserRegistrationInformationResponse', error);
      throw new Error(`Error: ${error.message}`);
    }
  }

  static async getUserCourseResponse(userKey: string): Promise<AxiosResponse<any>> {
    const url = `${process.env.WEBLIFE_COURSE_API_URL}?user_key=${userKey}`;

    const config = {
      headers: {
        'User-Agent': 'Smooth Contact User Course GET',
        'X-BiND-API-Control': 'X-BiND-API-Control',
      },
    };
    try {
      const response = await axios.get(url, config);

      return response;
    } catch (error) {
      logger.error('getUserCourseResponse', error);
      throw new Error(`Error: ${error.message}`);
    }
  }

  static async registScLink(email: string): Promise<Record<string, string>> {
    const userResult: Record<string, any> = {};

    try {
      const httpResponse: AxiosResponse = await this.getUserRegistrationInformationResponse(email);
      const objectResult = await XmlParseUtil.parse(httpResponse?.data, ROOT_XML_TAG);

      if (objectResult?.status === 'success') {
        userResult['USER_KEY'] = objectResult?.data?.userId ?? '';
        userResult['MAIL_ADDRESS'] = objectResult?.data?.mailAddress ?? '';
        userResult['USER_NAME'] = objectResult?.data?.userName ?? '';
        userResult['EXPIRATION_DATE'] = objectResult?.smoothcontact?.expirationDate ?? '';
        userResult['AVAILABLE'] = objectResult?.smoothcontact?.available === 'true';
      }
    } catch (error) {
      logger.error('registScLink', error);
    }

    return userResult;
  }

  static isAboveProCourse(course: number): boolean {
    return course == CourseValue[CourseValue.PRO];
  }

  static isAboveEnterpriseCourse(course: number): boolean {
    return course == CourseValue[Course.ENTERPRISE];
  }

  static async getWlsCldCourse(userKey: string | null): Promise<boolean> {
    let result = false;

    if (!userKey || userKey.trim() === '') {
      return false;
    }

    try {
      const httpResponse = await this.getUserCourseResponse(userKey);

      if (httpResponse?.status !== 200 || !httpResponse?.data) {
        return false;
      }

      result = this.validateWlsCldCourse(userKey, httpResponse?.data);
    } catch (error) {
      logger.error('getWlsCldCourse', error);
    }

    return result;
  }

  static validateWlsCldCourse(userKey: string, data: any): boolean {
    let ret = false;

    const serialCourse = data[userKey];
    if (Array.isArray(serialCourse)) {
      for (const item of serialCourse) {
        const productName = item.product_name;

        if (productName && productName.trim() !== '') {
          // TODO: Handle enterprise course
          // As of 2022/03, enterprise settings are specific to SmoothContact and not controlled here
          if (PRO_COURSE.includes(productName)) {
            ret = true;
            break;
          }
        }
      }
    }

    return ret;
  }
}
