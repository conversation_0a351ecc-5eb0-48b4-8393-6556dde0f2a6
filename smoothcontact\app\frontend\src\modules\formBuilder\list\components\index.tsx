import ApplicationImage from '@/assets/templates/template_form/application_form.png';
import QuickQuestionnaireImage from '@/assets/templates/template_form/quick_questionnaire_form.png';
import ReservationImage from '@/assets/templates/template_form/reservation_form.png';
import ContactFormImage from '@/assets/templates/template_form/simple_contact_form.png';
import SCCard from '@/components/common/SCCard';
import SCCollapseCard from '@/components/common/SCCollapseCard';
import SCIconButton from '@/components/common/SCIconButton';
import SCSimpleCard from '@/components/common/SCSimpleCard';
import SCDataTable, { SCTableColumnProps } from '@/components/customTable';
import ChangeFormTypeModal from '@/components/FormBuilder/common/ChangeFormTypeModal';
import FormBuilderMenus from '@/components/menu';
import FormDetailDrawer from '@/components/menu/FormDrawerComponent';
import FormSharingModal from '@/components/menu/FormSharingModalComponent';
import theme from '@/styles/theme/themeConfig';
import { FormStatus, FormType } from '@/types/FormTemplateTypes';
import { formatDate, ISO } from '@/utils/dateTime';
import { FormAction, FormTemplateType, FormTemplateTypeContent } from '@/utils/formBuilderUtils';
import AddIcon from '@mui/icons-material/Add';
import CodeIcon from '@mui/icons-material/Code';
import ContentPasteGoIcon from '@mui/icons-material/ContentPasteGo';
import DynamicFormIcon from '@mui/icons-material/DynamicForm';
import EditIcon from '@mui/icons-material/Edit';
import FilterListIcon from '@mui/icons-material/FilterList';
import FilterListOffIcon from '@mui/icons-material/FilterListOff';
import LeaderboardIcon from '@mui/icons-material/Leaderboard';
import ShareIcon from '@mui/icons-material/Share';
import { Button, Grid, IconButton, TableContainer, Tooltip, Typography } from '@mui/material';
import { Box, Container, Stack } from '@mui/system';
import { sum } from 'lodash';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import { FormBuilderFilterRequestDto } from '../dto/request.dto';
import { FilterStatusLabels, FilterStatusOptions, useFormBuilderList } from '../store/FormBuilderListProvider';
import FilterFormBuilderModal from './filterModal';
import FormBuilderTour from './formBuilderTour';
import { ScheduleModal } from './scheduleModal';
import StatusButton from './statusButton';

const FormBuilderListComponent = () => {
  const {
    data,
    statistics,
    pagination,
    profile,
    openDrawer,
    sharingModal,
    filterModal,
    scheduleModal,
    changeFormTypeModal,
    selectedRow,
    isFromBindUp,
    isFromOem,
    saveForm,
    createEmptyForm,
    duplicateForm,
    openConfirmDeleteForm,
    handleOpenDetailModal,
    handleOpenShareModal,
    setFilterModal,
    setScheduleModal,
    setChangeFormTypeModal,
    handleChangePage,
    handleChangeRowsPerPage,
    handleSort,
    handleFilter,
    getAllForms,
    setSelectedRow,
    order,
    orderBy,
    filter,
    setFilter,
    runTour,
    handleTourCallback,
    applyToBindUp,
  } = useFormBuilderList();

  const navigate = useNavigate();
  const { t } = useTranslation();
  const checklistItems = [
    '現在のフォームは無効化されます。',
    '関連CMSに変更内容を通知してください。',
    'Google AdsおよびYahooに関連する機能が変更されます。',
  ];

  const isShowApplyButton = useCallback(
    (row: any) => {
      if (!isFromBindUp) {
        return false;
      }

      return row?.mode === null || row?.mode === FormType.MANUAL;
    },
    [isFromBindUp]
  );

  const applyButtonText = useMemo(() => {
    return isFromOem ? 'サイトで使う' : 'BiNDで使う';
  }, [isFromOem]);

  const columns = useMemo<SCTableColumnProps[]>(() => {
    return [
      {
        id: 'id',
        title: '#',
        width: 80,
      },
      {
        id: 'name',
        title: 'フォーム名',
        sortable: true,
        render: (row: any) => {
          return (
            <Stack direction="row" justifyItems={'center'} alignItems={'center'} gap={2}>
              <Tooltip
                key={`tooltip-form-type-${row.key}`}
                title={row?.mode !== FormType.HTML ? '標準モード' : 'HTMLモード'}
                slotProps={{
                  popper: {
                    modifiers: [
                      {
                        name: 'offset',
                        options: {
                          offset: [0, -5],
                        },
                      },
                    ],
                  },
                }}
              >
                {row?.mode !== FormType.HTML ? <DynamicFormIcon color="primary" /> : <CodeIcon color="primary" />}
              </Tooltip>
              <Link style={{ textDecoration: 'none', color: theme.palette.primary.dark }} to={`/form-builder/edit/${row.extId}`}>
                <Typography sx={{ wordBreak: 'break-all' }}>{row.name}</Typography>
              </Link>
            </Stack>
          );
        },
      },
      {
        id: 'status',
        title: 'ステータス',
        sortable: true,
        width: 130,
        render: (row: any) => {
          return <StatusButton className="step-handle-form-status" item={row} />;
        },
      },
      {
        id: 'updatedDate',
        title: '更新日',
        sortable: true,
        width: 130,
        render: (row: any) => {
          return formatDate(row.updatedAt, ISO.DATE_TIME);
        },
      },
      {
        id: 'view',
        disablePadding: false,
        title: '回答数',
        sortable: true,
        width: 100,
        render: (row) => {
          return <Typography variant="body1">{row.submissionCount}</Typography>;
        },
      },
      {
        id: 'action',
        title: '',
        width: 150,
        render: (row: any) => {
          return (
            <Stack direction="row" justifyItems={'center'}>
              {isShowApplyButton(row) && (
                <Tooltip
                  key={`tooltip-apply-bind-${row.key}`}
                  title={applyButtonText}
                  slotProps={{
                    popper: {
                      modifiers: [
                        {
                          name: 'offset',
                          options: {
                            offset: [0, -5],
                          },
                        },
                      ],
                    },
                  }}
                >
                  <IconButton onClick={() => applyToBindUp(row.id)}>
                    <ContentPasteGoIcon fontSize="inherit" />
                  </IconButton>
                </Tooltip>
              )}
              <Tooltip
                key={`tooltip-edit-${row.key}`}
                title={'編集'}
                slotProps={{
                  popper: {
                    modifiers: [
                      {
                        name: 'offset',
                        options: {
                          offset: [0, -5],
                        },
                      },
                    ],
                  },
                }}
              >
                <IconButton onClick={() => navigate(`/form-builder/edit/${row.extId}`)}>
                  <EditIcon fontSize="inherit" />
                </IconButton>
              </Tooltip>
              <Tooltip
                key={`tooltip-report-${row.key}`}
                title={'レポート'}
                slotProps={{
                  popper: {
                    modifiers: [
                      {
                        name: 'offset',
                        options: {
                          offset: [0, -5],
                        },
                      },
                    ],
                  },
                }}
              >
                <IconButton onClick={() => navigate(`/form-builder/report/${row.extId}`)}>
                  <LeaderboardIcon fontSize="inherit" />
                </IconButton>
              </Tooltip>
              <Tooltip
                key={`tooltip-share-${row.key}`}
                title={'共有'}
                slotProps={{
                  popper: {
                    modifiers: [
                      {
                        name: 'offset',
                        options: {
                          offset: [0, -5],
                        },
                      },
                    ],
                  },
                }}
              >
                <IconButton onClick={() => handleOpenShareModal(row, true)}>
                  <ShareIcon fontSize="inherit" />
                </IconButton>
              </Tooltip>
              <FormBuilderMenus
                className="step-handle-form"
                extId={row.extId}
                key={row.extId}
                menus={[
                  {
                    title: row?.mode !== FormType.HTML ? '＜開発者向け＞コード形式への変更' : '＜標準モード＞デザイン編集への変更',
                    type: FormAction.FORM_TYPE,
                    item: row,
                    onClick: () => {
                      setSelectedRow(row);
                      setChangeFormTypeModal(true);
                    },
                    isActive: true,
                    hidden: true,
                  },
                  {
                    title: 'フォームの詳細',
                    type: FormAction.DETAIL,
                    item: row,
                    onClick: (item: any) => handleOpenDetailModal(item, true),
                    isActive: true,
                  },
                  { title: 'フォームの複製', type: FormAction.DUPLICATE, item: row, onClick: (row) => duplicateForm(row.extId), isActive: true },
                  {
                    title: 'フォームの削除',
                    type: FormAction.DELETE,
                    item: row,
                    onClick: () => openConfirmDeleteForm(row),
                    isActive: true,
                    warningAction: true,
                  },
                ]}
              />
            </Stack>
          );
        },
      },
    ];
  }, [isFromBindUp]);

  const renderFormTemplateList = () => {
    return Object.values(FormTemplateType)?.map?.((value, index) => {
      const formTemplate = FormTemplateTypeContent[value];
      const thumbnail = {
        [FormTemplateType.EMPTY_FORM]: ContactFormImage,
        [FormTemplateType.SIMPLE_CONTACT_FORM]: ContactFormImage,
        [FormTemplateType.QUICK_QUESTIONNAIRE]: QuickQuestionnaireImage,
        [FormTemplateType.APPLICATION_FORM]: ApplicationImage,
        [FormTemplateType.RESERVATION_APPLICATION_FORM]: ReservationImage,
      };

      return (
        <Grid key={index} item xs={3}>
          {value === FormTemplateType.EMPTY_FORM ? (
            <SCCard className="step-create-form" title={formTemplate.name} icon={AddIcon} cardStyle="dashed" onClick={() => createEmptyForm(value)} />
          ) : (
            <SCCard className="step-create-from-template" title={formTemplate.name} onClick={() => createEmptyForm(value)} image={thumbnail[value]} />
          )}
        </Grid>
      );
    });
  };

  const renderToolBar = () => {
    return (
      <Stack direction="row" justifyContent={'space-between'} gap={4}>
        <Stack direction={'row'}>
          <Button variant="text" color="secondary" onClick={() => handleFilter(FilterStatusOptions.ALL)}>
            <Typography fontWeight={!filter?.status?.length || filter?.status?.includes(FilterStatusOptions.ALL) ? 'bold' : 'none'}>
              {FilterStatusLabels[FilterStatusOptions.ALL]}（
              {sum([statistics.draftCount, statistics.expiredCount, statistics.publishedCount, statistics.prePublishedCount])}）
            </Typography>
          </Button>
          <Button variant="text" color="secondary" onClick={() => handleFilter(FilterStatusOptions.PUBLISHED)}>
            <Typography fontWeight={filter?.status?.includes(FilterStatusOptions.PUBLISHED) ? 'bold' : 'none'}>
              {FilterStatusLabels[FilterStatusOptions.PUBLISHED]} ({statistics.publishedCount}）
            </Typography>
          </Button>
          <Button variant="text" color="secondary" onClick={() => handleFilter(FilterStatusOptions.PREPUBLISHED)}>
            <Typography fontWeight={filter?.status?.includes(FilterStatusOptions.PREPUBLISHED) ? 'bold' : 'none'}>
              {FilterStatusLabels[FilterStatusOptions.PREPUBLISHED]} ({statistics.prePublishedCount}）
            </Typography>
          </Button>
          <Button variant="text" color="secondary" onClick={() => handleFilter(FilterStatusOptions.DRAFT)}>
            <Typography fontWeight={filter?.status?.includes(FilterStatusOptions.DRAFT) ? 'bold' : 'none'}>
              {FilterStatusLabels[FilterStatusOptions.DRAFT]} ({statistics.draftCount}）
            </Typography>
          </Button>
          <Button variant="text" color="secondary" onClick={() => handleFilter(FilterStatusOptions.EXPIRED)}>
            <Typography fontWeight={filter?.status?.includes(FilterStatusOptions.EXPIRED) ? 'bold' : 'none'}>
              {FilterStatusLabels[FilterStatusOptions.EXPIRED]}（{statistics.expiredCount}）
            </Typography>
          </Button>
        </Stack>
        <SCIconButton color="secondary" onClick={() => setFilterModal(true)}>
          {filter?.status || filter?.from || filter?.to ? <FilterListIcon /> : <FilterListOffIcon />}
        </SCIconButton>
      </Stack>
    );
  };

  return (
    <Container>
      <Box sx={{ py: 2, pb: '36px' }}>
        <SCCollapseCard title={t('default')} icon={AddIcon}>
          <Grid container spacing={2} marginY={1} columns={15}>
            {renderFormTemplateList()}
          </Grid>
        </SCCollapseCard>
      </Box>

      {!data && (
        <SCSimpleCard>
          <Stack direction="column" justifyContent="center" alignItems="center" spacing={2} p={3}>
            <Typography variant="h6" fontWeight="bold">
              フォームはまだありません
            </Typography>
            <Typography variant="body2">空白のフォームかテンプレートをクリックして、新しいフォームを作成します</Typography>
          </Stack>
        </SCSimpleCard>
      )}

      {data && (
        <Stack spacing={4}>
          {!isFromOem && (
            <Typography variant="h6" sx={{ fontWeight: '500', lineHeight: '32px', letterSpacing: '0.15px', fontSize: '20px' }}>
              {profile?.name ?? '山田太郎'}さん、こんにちは！
            </Typography>
          )}
          <SCSimpleCard>
            <TableContainer>
              <SCDataTable
                columns={columns}
                data={data}
                pagination={pagination}
                handleChangePage={handleChangePage}
                handleChangeRowsPerPage={handleChangeRowsPerPage}
                handleSort={handleSort}
                order={order}
                orderBy={orderBy}
                renderToolBar={() => renderToolBar()}
                renderEmpty={() => {
                  return (
                    <Stack direction="column" justifyContent="center" alignItems="center" spacing={2} p={3}>
                      <Typography variant="h3" fontWeight="bold">
                        フォームはまだありません
                      </Typography>
                      <Typography variant="body2">空白のフォームかテンプレートをクリックして、新しいフォームを作成します</Typography>
                    </Stack>
                  );
                }}
              ></SCDataTable>
            </TableContainer>
          </SCSimpleCard>
        </Stack>
      )}

      {openDrawer && <FormDetailDrawer open={openDrawer} onClose={() => handleOpenDetailModal(null, false)} item={selectedRow} />}
      {sharingModal && <FormSharingModal open={sharingModal} onClose={() => handleOpenShareModal(null, false)} item={selectedRow} />}
      {filterModal && (
        <FilterFormBuilderModal
          filter={filter ?? []}
          open={filterModal}
          onClose={() => setFilterModal(false)}
          onSearch={(currentFilter: FormBuilderFilterRequestDto) => {
            setFilter((pre: any) => ({ ...pre, status: currentFilter?.status ?? undefined }));
            getAllForms(currentFilter);
            setFilterModal(false);
          }}
        />
      )}
      {scheduleModal && (
        <ScheduleModal
          open={scheduleModal}
          onClose={() => setScheduleModal(false)}
          onSubmit={(value) => {
            const newSchedule = {
              releaseStartDate: value?.releaseStartDate ?? selectedRow.releaseStartDate,
              releaseEndDate: value?.releaseEndDate ?? selectedRow.releaseEndDa,
            };

            saveForm({
              ...selectedRow,
              ...newSchedule,
              formScheduleSetting: {
                ...selectedRow.formScheduleSetting,
                ...newSchedule,
              },
              status: FormStatus.PUBLISHED,
            });
          }}
          item={selectedRow}
        />
      )}
      {changeFormTypeModal && (
        <ChangeFormTypeModal
          isOpen={changeFormTypeModal}
          onClose={() => setChangeFormTypeModal(!changeFormTypeModal)}
          onSubmit={() => {
            saveForm({ ...selectedRow, mode: selectedRow?.mode !== FormType.HTML ? FormType.HTML : FormType.MANUAL });
          }}
          checklistItems={checklistItems}
          title={''}
        />
      )}
      <FormBuilderTour runTour={runTour} handleTourCallback={handleTourCallback} />
    </Container>
  );
};

export default FormBuilderListComponent;
