import SCTextEditor from '@/components/common/SCTextEditor';
import { Box, Divider, FormControl, FormControlLabel, FormLabel, Radio, RadioGroup, Stack, TextField, Typography } from '@mui/material';
import { FormikValues } from 'formik';

interface ContainerPropertyProps {
  form: FormikValues;
}

const ContainerProperty = ({ form }: ContainerPropertyProps) => {
  return (
    <Box className="main-form">
      <form style={{ minWidth: '100%' }}>
        <Stack direction="column" spacing={2}>
          <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
            <Typography variant="body2">項目タイトル</Typography>
            <TextField
              name="heading"
              variant="outlined"
              helperText={form.errors.heading}
              error={!!form.errors.heading}
              {...form.register('heading')}
              value={form.values.heading ?? ''}
              inputProps={{ maxLength: 256 }}
            />
          </Box>
          <Divider />
          <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
            <Typography variant="body2">項目説明文</Typography>
            <SCTextEditor
              key={form?.values?.id}
              id={form?.values?.id}
              initialValue={form?.values.description ?? ''}
              onContentChange={(html: string) => form?.setFieldValue('description', html)}
            />
          </Box>
          <Divider />
          <FormControl>
            <FormLabel>ヘッダーの表示位置</FormLabel>
            <RadioGroup name="display" {...form.register('display')} value={form?.values?.display ?? 'none'}>
              <FormControlLabel value="none" control={<Radio value="none" />} label="なし" />
              <FormControlLabel value="left" control={<Radio value="left" />} label="左寄せ" />
              <FormControlLabel value="center" control={<Radio value="center" />} label="中央寄せ" />
              <FormControlLabel value="right" control={<Radio value="right" />} label="右寄せ" />
            </RadioGroup>
          </FormControl>
        </Stack>
      </form>
    </Box>
  );
};

export default ContainerProperty;
