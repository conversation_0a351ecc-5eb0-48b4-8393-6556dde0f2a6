import { Course, JAPAN_POSTAL_CODE_LINK } from '@/common/constants';
import SCIconButton from '@/components/common/SCIconButton';
import SCSimpleCard from '@/components/common/SCSimpleCard';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { useAppSelector } from '@/store/hook';
import { FormElementChildrenType } from '@/types/FormTemplateTypes';
import { ISO } from '@/utils/dateTime';
import { FormControlNames } from '@/utils/formBuilderUtils';
import { KeyboardArrowDown } from '@mui/icons-material';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DeleteIcon from '@mui/icons-material/Delete';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import EmailIcon from '@mui/icons-material/Email';
import ForkRightIcon from '@mui/icons-material/ForkRight';
import MailOutlineIcon from '@mui/icons-material/MailOutline';
import {
  Box,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  IconButton,
  InputAdornment,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import styled from '@mui/system/styled';
import { DateRangeIcon, DateTimeField } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateField } from '@mui/x-date-pickers/DateField';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { TimeField } from '@mui/x-date-pickers/TimeField';
import React from 'react';
import { Draggable } from 'react-beautiful-dnd';
import { Link } from 'react-router-dom';
import FormElementFullName from './FormElementFullName';

const renderElement = (item: FormElementChildrenType) => {
  const CustomTypography = styled(Typography)``;

  switch (item.controlName) {
    case FormControlNames.INPUT_TEXTFIELD:

    case FormControlNames.PHONE:

    case FormControlNames.EMAIL:
      if (item?.verifyEmail) {
        return (
          <>
            <TextField type={item.dataType} id={item?.id} fullWidth placeholder={item.placeholder} variant="outlined" />
            <TextField
              type={item.dataType}
              id={'verify-email' + item?.id}
              fullWidth
              placeholder={item?.verifyEmailPlaceholder}
              variant="outlined"
              sx={{ mt: 2 }}
            />
          </>
        );
      }

      return <TextField id={item?.id} type={item.dataType} fullWidth placeholder={item.placeholder} variant="outlined" />;

    case FormControlNames.FULL_NAME:
      return (
        <FormElementFullName
          classInput={''}
          classLabel={''}
          name={item.id}
          placeholder={item.placeholder}
          fullNamePlaceholder={item.fullNamePlaceholder}
          isError={false}
          value={''}
          onlyOneName={item.isReduceFullName}
          hasPronunciation={item.isUseFurigana}
          onChange={() => {}}
          required={item.required}
          isSubmitting={false}
          inputAnimation={''}
        />
      );

    case FormControlNames.INPUT_MULTILINE:
      return (
        <TextField
          type={item.dataType}
          fullWidth={true}
          multiline={true}
          minRows={item.rows ?? 4}
          placeholder={item.placeholder}
          variant="outlined"
        />
      );

    case FormControlNames.COMMENT:
      return <Typography variant="body2"></Typography>;

    case FormControlNames.DATE:
      return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <Stack spacing={3} direction="row">
            {item.dataType === 'dateTime' && (
              <DateTimeField
                format={ISO.DATE_TIME}
                InputProps={{
                  disabled: true,
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton>
                        <DateRangeIcon />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            )}
            {item.dataType === 'date' && (
              <DateField
                format={ISO.DATE}
                InputProps={{
                  disabled: true,
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton>
                        <DateRangeIcon />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            )}
            {item.dataType === 'time' && (
              <TimeField
                format={ISO.TIME}
                InputProps={{
                  disabled: true,
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton>
                        <AccessTimeIcon />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            )}
          </Stack>
        </LocalizationProvider>
      );

    case FormControlNames.ADDRESS:
      return (
        <Stack direction="column" justifyContent="stretch" alignItems="center" spacing={1}>
          <FormControl fullWidth variant="outlined">
            <Typography variant="body2" sx={{ marginBottom: '10px' }}>
              郵便番号
            </Typography>

            {item?.oneFieldPostcode && (
              <Box width={300} sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }} gap={2}>
                <TextField value="" inputProps={{ 'aria-label': 'weight' }} />
                {item?.displayPostCodeLink && (
                  <Link to={JAPAN_POSTAL_CODE_LINK} target="_blank" rel="noreferrer" style={{ flexShrink: 0 }}>
                    郵便番号を検索
                  </Link>
                )}
              </Box>
            )}

            {!item?.oneFieldPostcode && (
              <Box width={300} sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }} gap={2}>
                <TextField style={{ width: '100px' }} value="" inputProps={{ 'aria-label': 'weight' }} />
                <TextField style={{ width: '100px' }} value="" inputProps={{ 'aria-label': 'weight' }} />
                {item?.displayPostCodeLink && (
                  <Link to={JAPAN_POSTAL_CODE_LINK} target="_blank" rel="noreferrer" style={{ flexShrink: 0 }}>
                    郵便番号を検索
                  </Link>
                )}
              </Box>
            )}

            <FormHelperText>例）1234567</FormHelperText>
          </FormControl>
          <FormControl fullWidth variant="outlined">
            <Typography variant="body1" sx={{ marginBottom: '10px' }}>
              都道府県
            </Typography>
            <TextField value="" inputProps={{ 'aria-label': 'weight' }} />
            <FormHelperText>選択してください</FormHelperText>
          </FormControl>
          <FormControl fullWidth variant="outlined">
            <Typography variant="body1" sx={{ marginBottom: '10px' }}>
              市区町村
            </Typography>
            <TextField value="" inputProps={{ 'aria-label': 'weight' }} />
            <FormHelperText>例）〇〇市〇〇区</FormHelperText>
          </FormControl>
          <FormControl fullWidth variant="outlined">
            <Typography variant="body1" sx={{ marginBottom: '10px' }}>
              町名
            </Typography>
            <TextField value="" inputProps={{ 'aria-label': 'weight' }} />
            <FormHelperText>例）〇〇町</FormHelperText>
          </FormControl>
          <FormControl fullWidth variant="outlined">
            <Typography variant="body1" sx={{ marginBottom: '10px' }}>
              番地等
            </Typography>
            <TextField value="" inputProps={{ 'aria-label': 'weight' }} />
            <FormHelperText>例）1-1-1</FormHelperText>
          </FormControl>
          <FormControl fullWidth variant="outlined">
            <Typography variant="body1" sx={{ marginBottom: '10px' }}>
              建物名
            </Typography>
            <TextField value="" inputProps={{ 'aria-label': 'weight' }} />
            <FormHelperText>建物名は必要に応じてご入力ください。</FormHelperText>
            <FormHelperText>例）〇〇マンション101</FormHelperText>
          </FormControl>
        </Stack>
      );

    case FormControlNames.BIRTHDAY:
      return (
        <Stack direction="row" spacing={2}>
          <Box display="flex" alignItems="center" gap={1}>
            <Select IconComponent={() => <KeyboardArrowDown />} size="small" variant="outlined" value={0}>
              <MenuItem key={0} value={0}>
                ----
              </MenuItem>
            </Select>
            <CustomTypography variant="body1">年</CustomTypography>
          </Box>
          <Box display="flex" alignItems="center" gap={1}>
            <Select IconComponent={() => <KeyboardArrowDown />} size="small" variant="outlined" value={0}>
              <MenuItem key={0} value={0}>
                --
              </MenuItem>
            </Select>
            <CustomTypography variant="body1">月</CustomTypography>
          </Box>
          <Box display="flex" alignItems="center" gap={1}>
            <Select IconComponent={() => <KeyboardArrowDown />} size="small" variant="outlined" value={0}>
              <MenuItem key={0} value={0}>
                --
              </MenuItem>
            </Select>
            <CustomTypography variant="body1">日</CustomTypography>
          </Box>
        </Stack>
      );

    case FormControlNames.CHECKLIST:
      return (
        <FormGroup>
          <Box
            sx={{
              display: item.displayMode !== 'horizontal' ? 'flex' : 'block',
              flexDirection: item.displayMode !== 'horizontal' ? 'column' : 'row',
            }}
          >
            {item?.items?.length > 0 &&
              item.items?.map?.((i) => <FormControlLabel key={i.value} control={<Checkbox checked={false} readOnly />} label={i.label} />)}
          </Box>
          {item?.displayOtherOption && (
            <FormControlLabel
              sx={{ display: 'flex' }}
              key="other"
              control={<Checkbox checked={false} readOnly />}
              label={
                <Box display="flex" alignItems="center">
                  <span>その他</span>
                  <TextField sx={{ ml: 1 }} size="small" value={''} />
                </Box>
              }
            />
          )}
        </FormGroup>
      );

    case FormControlNames.RADIO:
      return (
        <>
          <RadioGroup name={item.controlName + item.id}>
            <Box
              sx={{
                display: item.displayMode !== 'horizontal' ? 'flex' : 'block',
                flexDirection: item.displayMode !== 'horizontal' ? 'column' : 'row',
              }}
            >
              {item?.items?.length > 0 &&
                item.items?.map?.((i) => <FormControlLabel value={i.value} key={i.value} control={<Radio checked={false} />} label={i.label} />)}
            </Box>
            {item?.displayOtherOption && (
              <FormControlLabel
                sx={{ display: 'flex' }}
                key="other"
                control={<Radio checked={false} />}
                label={
                  <Box display="flex" alignItems="center">
                    <span>その他</span>
                    <TextField sx={{ ml: 1 }} size="small" value={''} />
                  </Box>
                }
              />
            )}
          </RadioGroup>
        </>
      );

    case FormControlNames.DROPDOWN:
      return (
        <FormControl style={{ minWidth: '100%' }}>
          <Select
            IconComponent={() => <KeyboardArrowDown />}
            disabled={true}
            variant="outlined"
            value={item.items.length > 0 ? item.items[0].value : ''}
          >
            {item.items.length > 0 &&
              item.items?.map?.((i) => (
                <MenuItem key={i.value} value={i.value}>
                  {i.label}
                </MenuItem>
              ))}
          </Select>
        </FormControl>
      );

    case FormControlNames.FILE_UPLOAD:
      const UploadButton = styled('button')(({ theme }) => ({
        color: theme.palette.grey[400],
        backgroundColor: theme.palette.grey[100],
        padding: '5px 10px',
        borderRadius: '4px',
        border: `1px solid ${theme.palette.divider}`,
        cursor: 'pointer',
      }));

      return (
        <Stack direction="row" alignItems="center" spacing={1}>
          <UploadButton>ファイルを選択</UploadButton>
          <span>選択されていません</span>
        </Stack>
      );
  }
};

interface ControlViewComponentProps {
  item: FormElementChildrenType;
  containerId: string;
  index: any;
  moveCard?: (id: string, to: number) => void;
  findCard?: (id: string) => { index: number };
  handleDeleteElement: (controlName: string, elementId: string, containerId?: string, selectedItemId?: string) => void;
}

function ControlViewComponent({ item, containerId, index, handleDeleteElement }: ControlViewComponentProps) {
  const { error, selectedControl, selectedSwitchContact, selectControl, setSelectedParentControl, duplicateControl, setSelectedSwitchContact } =
    useFormBuilder();

  const { profile } = useAppSelector((state) => ({
    profile: state.app.profile,
  }));

  const handleDuplicateControl: React.MouseEventHandler<HTMLSpanElement> = (event) => {
    if (error) {
      return;
    }

    duplicateControl(item.id, containerId);
    if (event.stopPropagation) event.stopPropagation();
  };

  const handleConditionControl: React.MouseEventHandler<HTMLSpanElement> = (event) => {
    if (error) {
      return;
    }

    if (event.stopPropagation) event.stopPropagation();

    setSelectedParentControl(item);
  };

  const handleSwitchContact: React.MouseEventHandler<HTMLSpanElement> = (event) => {
    if (error) {
      return;
    }

    if (event.stopPropagation) event.stopPropagation();

    selectControl(null);
    setSelectedSwitchContact(item);
  };

  const selectedBorder = '2px dotted #92939A';
  const nonSelectedBorder = '1px solid rgba(0,0,0,0.1)';

  return (
    <>
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        className={item?.level ? 'wl-border' : ''}
        style={{ marginLeft: item?.level > 0 ? `${item?.level * 20}px` : '0px' }}
      >
        <Draggable key={`form_draggable_${index}`} draggableId={`form_draggable_${index}`} index={index}>
          {(provided, snapshot) => (
            <SCSimpleCard
              onClick={() => selectControl(item)}
              sx={{
                alignItems: 'stretch',
                justifyContent: 'flex-end',
                width: '100%',
                opacity: snapshot?.isDragging ? 0.5 : 1,
                border: snapshot?.isDragging || item?.id === selectedControl?.id ? selectedBorder : nonSelectedBorder,
              }}
              ref={provided.innerRef}
              {...provided.draggableProps}
            >
              <Box {...provided.dragHandleProps}>
                <Stack direction="row" justifyContent="space-between" sx={{ cursor: 'pointer' }}>
                  <Typography sx={{ wordBreak: 'break-all' }} variant="body2">
                    {item.labelName + (item.required ? ' *' : '')}
                  </Typography>
                  <Stack direction="row" justifyContent="flex-end" alignItems="center" gap={1}>
                    {!item?.parentId && (
                      <SCIconButton size="small" onClick={handleDuplicateControl} title="複製" disabled={error}>
                        <ContentCopyIcon fontSize="inherit" />
                      </SCIconButton>
                    )}
                    {[FormControlNames.CHECKLIST, FormControlNames.RADIO, FormControlNames.DROPDOWN].includes(item?.controlName) && (
                      <>
                        <SCIconButton size="small" onClick={handleConditionControl} title="条件分岐を追加" disabled={error}>
                          <ForkRightIcon fontSize="inherit" />
                        </SCIconButton>
                        {profile?.course === Course.ENTERPRISE && (
                          <SCIconButton size="small" onClick={handleSwitchContact} title="個別送信先を追加" disabled={error}>
                            {selectedSwitchContact && item?.id === selectedSwitchContact?.id ? (
                              <EmailIcon fontSize="inherit" />
                            ) : (
                              <MailOutlineIcon
                                color={item?.switchContacts?.some?.((contact: any) => contact?.enabled === true) ? 'primary' : 'inherit'}
                                fontSize="inherit"
                              />
                            )}
                          </SCIconButton>
                        )}
                      </>
                    )}
                    <Divider orientation="vertical" flexItem />
                    <Box style={{ cursor: 'grab' }}>
                      <SCIconButton size="small" title="Drag">
                        <DragIndicatorIcon sx={{ cursor: 'grab' }} fontSize="inherit" />
                      </SCIconButton>
                    </Box>
                    <SCIconButton size="small" onClick={() => handleDeleteElement(item?.controlName, item?.id, containerId)} title="削除">
                      <DeleteIcon fontSize="inherit" />
                    </SCIconButton>
                  </Stack>
                </Stack>
                {!item?.isBottomDescription && item?.description !== '' && (
                  <Box sx={{ marginTop: '10px' }}>
                    <div dangerouslySetInnerHTML={{ __html: item?.description }}></div>
                  </Box>
                )}
                <Box sx={{ marginTop: '10px' }}>{renderElement(item)}</Box>
                {item?.isBottomDescription && item?.description !== '' && (
                  <Box sx={{ marginTop: '10px' }}>
                    <div dangerouslySetInnerHTML={{ __html: item?.description }}></div>
                  </Box>
                )}
              </Box>
            </SCSimpleCard>
          )}
        </Draggable>
      </Stack>
    </>
  );
}

export default ControlViewComponent;
