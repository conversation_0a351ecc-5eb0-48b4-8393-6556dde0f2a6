import React, { useEffect, useRef, useState } from 'react';
import { FormHelperText } from '@mui/material';
import { styled } from '@mui/system';

const LabelTopInput = styled('div')<{ borderColor: string; marginTop: string; fontSize: string }>`
  position: relative;
  margin-top: ${(props) => props.marginTop || '2.5rem'};
  width: 100%;

  input,
  textarea {
    width: 100%;
    border-width: 1px;
    border-style: solid;
    border-color: ${(props) => props.borderColor};
    border-left: 0;
    border-right: 0;
    background-color: transparent;
    font-size: ${(props) => props.fontSize || '14px'};
  }

  input {
    height: 46px;
    padding: 0;
  }

  textarea {
    height: auto;
    resize: none;
    line-height: 150%;
  }

  label {
    cursor: text;
  }

  input + label {
    position: absolute;
    bottom: 13px;
    left: 0;
    transition: all 0.3s ease;
    font-size: ${(props) => props.fontSize || '14px'};
  }

  input:focus,
  textarea:focus {
    outline: none;
    border-left: none;
    border-right: none;
  }

  input:focus {
    margin-bottom: -5px;
  }

  input:focus + label {
    transform: translateY(-150%);
    transition: all 0.3s ease;
    padding-top: 5px;
  }

  textarea:focus + label {
    transform: translateY(-200%);
    transition: all 0.3s ease;
    padding-top: 20px;
  }
`;

interface LabelTopInputComponentProps {
  name?: string;
  type?: string;
  label?: string;
  value?: any;
  onChange?: (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  className?: string;
  error?: boolean;
  helperText?: string;
  multiline?: boolean;
  minRows?: number;
  placeholder?: string;
  borderColor?: string;
  backgroundColor?: string;
  marginTop?: string;
  inputProps?: any;
  borderRadius?: string;
  fontSize?: string;
  color?: string;
}

const LabelTopInputComponent: React.FC<LabelTopInputComponentProps> = ({
  className,
  error,
  helperText,
  multiline,
  minRows,
  borderColor,
  marginTop,
  ...commonInputProps
}) => {
  const [labelStyle, setLabelStyle] = useState({});
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const labelRef = useRef<HTMLLabelElement>(null);

  useEffect(() => {
    const shouldLabelFloat = multiline || commonInputProps.value;

    if (shouldLabelFloat) {
      if (multiline && textareaRef.current && labelRef.current) {
        const textareaHeight = textareaRef.current.offsetHeight;
        const labelHeight = labelRef.current.offsetHeight;
        const topPosition = textareaHeight / 2 - labelHeight / 2;

        setLabelStyle({
          position: 'absolute',
          top: `${topPosition}px`,
          left: 0,
        });
      } else if (commonInputProps.value && labelRef.current) {
        // Adjust the label style for non-multiline inputs with a value
        setLabelStyle({
          transform: 'translateY(-200%)',
          transition: 'all 0.3s ease',
        });
      }
    } else {
      // Reset the label style if there's no value
      setLabelStyle({});
    }
  }, [multiline, textareaRef.current?.offsetHeight, commonInputProps.value]);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { inputProps, label, backgroundColor, borderRadius, placeholder, fontSize, color, ...newCommonProps } = commonInputProps;
  const inputId = `${className}-${commonInputProps.name}`;

  return (
    <>
      <LabelTopInput className={className} borderColor={borderColor} marginTop={marginTop} fontSize={fontSize}>
        {multiline ? (
          <textarea id={inputId} ref={textareaRef} rows={minRows} {...newCommonProps} style={{ color }} />
        ) : (
          <input id={inputId} {...newCommonProps} />
        )}
        <label ref={labelRef} style={labelStyle} htmlFor={inputId}>
          {label ?? placeholder}
        </label>
      </LabelTopInput>
      {error && <FormHelperText error={error}>{helperText}</FormHelperText>}
    </>
  );
};

export default LabelTopInputComponent;
