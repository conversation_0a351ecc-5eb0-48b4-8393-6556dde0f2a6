import { Injectable } from '@nestjs/common';
import { v4 as uuid } from 'uuid';

import { RootService } from '@/core/services/root.service';
import { AWSS3 } from '@/libs/AWSS3';

@Injectable()
export class UploadService extends RootService {
  constructor(private awsS3: AWSS3) {
    super();
  }

  async uploadFile(extId: string, file: Express.Multer.File, newFileName?: string): Promise<Record<string, any>> {
    try {
      const uploadDir = `${extId}/${uuid()}`;
      this.awsS3.setUploadDir(uploadDir);
      this.awsS3.setUploadFile(file);
      newFileName && this.awsS3.setUploadFileName(newFileName);

      return await this.awsS3.temporaryUpload();
    } catch (error) {
      return this.response({ message: 'アップロードに失敗しました。' });
    }
  }

  async uploadFileWithCustomDir(dir: string, newFileName: string, file: Express.Multer.File): Promise<Record<string, any>> {
    try {
      this.awsS3.setUploadDir(dir);
      this.awsS3.setUploadFile(file);
      this.awsS3.setUploadFileName(newFileName);

      return await this.awsS3.temporaryUpload();
    } catch (error) {
      return this.response({ message: 'アップロードに失敗しました。' });
    }
  }

  async confirmFile(uploadKey: string): Promise<Record<string, any>> {
    try {
      return this.awsS3.confirmAndMoveFile(uploadKey);
    } catch (error) {
      return this.response({ message: 'ファイルを確認できません' });
    }
  }

  async deleteFile(uploadKey: string): Promise<Record<string, any>> {
    try {
      return this.awsS3.deleteFile(uploadKey);
    } catch (error) {
      return this.response({ message: 'ファイルを削除できません' });
    }
  }
}
