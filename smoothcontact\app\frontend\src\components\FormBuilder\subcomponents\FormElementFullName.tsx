import React, { FC, useEffect } from 'react';
import { FormItemValue, ItemFullNameFullValue, ItemFullNameValue } from '@/types/FormTemplateTypes';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import ResponsiveStack from '@/components/common/ResponsiveStack';
import { useTranslation } from 'react-i18next';
import useTypingKana from '@/hooks/useTypingKana';
import Stack from '@mui/material/Stack';
import InputFactory from './FormColorSetting/AnimationCustom/text/InputFactoryComponent';
import { FormInputAnimationTypes } from '@/utils/formBuilderUtils';

export const DEFAULT_SEPARATOR = ' ';

interface FormElementFullNameProps<T> {
  name?: string;
  label?: string;
  value: FormItemValue<T>;
  classInput?: string;
  classLabel?: string;
  onlyOneName?: boolean;
  required?: boolean;
  hasPronunciation?: boolean;
  placeholder?: string;
  fullNamePlaceholder?: {
    full: string;
    last: string;
    first: string;
    fullPronunciation: string;
    lastPronunciation: string;
    firstPronunciation: string;
  };
  isError?: boolean;
  helperText?: string;
  isSubmitting?: boolean;
  inputAnimation: string;
  isAutoFill?: boolean;
  autoFillType?: 'hiragana' | 'katakana';
  onChange?: (value: FormItemValue<T>, isValid?: boolean) => void;
  entryFormSetting?: any;
}

const InputFullName: FC<FormElementFullNameProps<ItemFullNameValue>> = ({
  value,
  label,
  classLabel,
  classInput,
  required,
  isSubmitting,
  hasPronunciation,
  inputAnimation,
  fullNamePlaceholder,
  isAutoFill,
  autoFillType,
  onChange,
  entryFormSetting,
}) => {
  const inputValue = value as ItemFullNameValue;
  const { t } = useTranslation();
  const { kana, setKana, onCompositionStart, onCompositionUpdate, onCompositionEnd, onKeyUp } = useTypingKana({
    enabled: isAutoFill,
    useKatakana: autoFillType === 'katakana',
  });

  const validationSchema = Yup.object().shape({
    name: required ? Yup.string().required() : Yup.string(),
    pronunciation: required && hasPronunciation ? Yup.string().required() : Yup.string(),
  });
  const form = useFormik<ItemFullNameValue>({
    initialValues: {
      name: inputValue.name || '',
      pronunciation: inputValue.pronunciation || '',
    },
    validationSchema,
    onSubmit: () => {},
  });

  useEffect(() => {
    if (isSubmitting) {
      form.setTouched({
        name: true,
        pronunciation: true,
      });
      form.validateForm();
    }
  }, [isSubmitting]);

  useEffect(() => {
    if (form.dirty) {
      onChange && onChange(form.values, form.isValid);
    }
  }, [form.isValid, form.values]);

  useEffect(() => {
    if (kana) {
      form.setFieldValue('pronunciation', kana);
    }
  }, [kana]);

  return (
    <>
      <ResponsiveStack direction="row" mobileDirection="column" spacing={1} alignItems="center">
        <Box width={160}>
          <Typography className={classLabel}>氏名</Typography>
        </Box>
        <Box className="input-container" flexGrow={1}>
          <InputFactory
            name="name"
            className={classInput}
            inputAnimation={inputAnimation}
            placeholder={fullNamePlaceholder?.full}
            value={form.values?.name}
            error={Boolean(form.touched?.name && form.errors?.name)}
            helperText={
              form.touched?.name &&
              t(form.errors?.name, {
                field: `${label} (氏名)`,
              })
            }
            onChange={(event) => {
              if (event.target.value === '') {
                setKana('');
                form.setFieldValue('pronunciation', '');
              }

              form.handleChange(event);
            }}
            onBlur={form.handleBlur}
            onCompositionStart={onCompositionStart}
            onCompositionUpdate={onCompositionUpdate}
            onCompositionEnd={onCompositionEnd}
            onKeyUp={onKeyUp}
            borderColor={entryFormSetting?.borderColor}
            backgroundColor={entryFormSetting?.bgColor}
            borderRadius={entryFormSetting?.borderRadius}
            fontSize={entryFormSetting?.fontSize}
          />
        </Box>
      </ResponsiveStack>
      {hasPronunciation && (
        <ResponsiveStack
          sx={{
            mt: inputAnimation === FormInputAnimationTypes.LABEL_TOP ? '35px' : '15px',
          }}
          direction="row"
          mobileDirection="column"
          spacing={1}
          alignItems="center"
        >
          <Box width={160}>
            <Typography className={classLabel}>フリガナ</Typography>
          </Box>
          <Box className="input-container" flexGrow={1}>
            <InputFactory
              name="pronunciation"
              className={classInput}
              inputAnimation={inputAnimation}
              placeholder={fullNamePlaceholder?.fullPronunciation}
              value={form.values?.pronunciation}
              error={Boolean(form.touched?.pronunciation && form.errors?.pronunciation)}
              helperText={
                form.touched?.pronunciation &&
                t(form.errors?.pronunciation, {
                  field: `${label} (フリガナ)`,
                })
              }
              onChange={(event) => {
                form.handleChange(event);
              }}
              borderColor={entryFormSetting?.borderColor}
              backgroundColor={entryFormSetting?.bgColor}
              borderRadius={entryFormSetting?.borderRadius}
              fontSize={entryFormSetting?.fontSize}
            />
          </Box>
        </ResponsiveStack>
      )}
    </>
  );
};

const InputFullNameFull: FC<FormElementFullNameProps<ItemFullNameFullValue>> = ({
  value,
  classLabel,
  classInput,
  required,
  isSubmitting,
  hasPronunciation,
  inputAnimation,
  fullNamePlaceholder,
  isAutoFill,
  autoFillType,
  onChange,
  entryFormSetting,
}) => {
  const inputValue = value as ItemFullNameFullValue;
  const { t } = useTranslation();
  const {
    kana: lastNameKana,
    setKana: setLastNameKana,
    onCompositionStart: onLastNameCompositionStart,
    onCompositionUpdate: onLastNameCompositionUpdate,
    onCompositionEnd: onLastNameCompositionEnd,
    onKeyUp: onLastNameKeyUp,
  } = useTypingKana({
    enabled: isAutoFill,
    useKatakana: autoFillType === 'katakana',
  });
  const {
    kana: firstNameKana,
    setKana: setFirstNameKana,
    onCompositionStart: onFirstNameCompositionStart,
    onCompositionUpdate: onFirstNameCompositionUpdate,
    onCompositionEnd: onFirstNameCompositionEnd,
    onKeyUp: onFirstNameKeyUp,
  } = useTypingKana({
    enabled: isAutoFill,
    useKatakana: autoFillType === 'katakana',
  });

  const validationSchema = Yup.object().shape({
    lastName: required ? Yup.string().required() : Yup.string(),
    firstName: required ? Yup.string().required() : Yup.string(),
    lastNamePronunciation: required && hasPronunciation ? Yup.string().required() : Yup.string(),
    firstNamePronunciation: required && hasPronunciation ? Yup.string().required() : Yup.string(),
  });
  const form = useFormik<ItemFullNameFullValue>({
    initialValues: {
      lastName: inputValue?.lastName || '',
      firstName: inputValue?.firstName || '',
      lastNamePronunciation: inputValue?.lastNamePronunciation || '',
      firstNamePronunciation: inputValue?.firstNamePronunciation || '',
    },
    validationSchema,
    onSubmit: () => {},
  });

  useEffect(() => {
    if (isSubmitting) {
      form.setTouched({
        lastName: true,
        firstName: true,
        lastNamePronunciation: true,
        firstNamePronunciation: true,
      });
      form.validateForm();
    }
  }, [isSubmitting]);

  useEffect(() => {
    if (form.dirty) {
      onChange && onChange(form.values, form.isValid);
    }
  }, [form.isValid, form.values]);

  useEffect(() => {
    if (lastNameKana) {
      form.setFieldValue('lastNamePronunciation', lastNameKana);
    }
  }, [lastNameKana]);

  useEffect(() => {
    if (firstNameKana) {
      form.setFieldValue('firstNamePronunciation', firstNameKana);
    }
  }, [firstNameKana]);

  return (
    <>
      <ResponsiveStack direction="row" mobileDirection="column" spacing={1} alignItems="center">
        <Box width={160}>
          <Typography className={classLabel}>氏名</Typography>
        </Box>
        <Box className="input-container" flexGrow={1}>
          <Stack direction="row" spacing={3}>
            <Box width={'100%'}>
              <InputFactory
                name="lastName"
                className={classInput}
                inputAnimation={inputAnimation}
                placeholder={fullNamePlaceholder?.last}
                value={form.values?.lastName}
                error={Boolean(form.errors?.lastName)}
                helperText={form.errors?.lastName && t(form.errors?.lastName, { field: fullNamePlaceholder?.last ?? '苗字' })}
                onChange={(e) => {
                  form.handleChange(e);
                }}
                onBlur={form.handleBlur}
                onCompositionStart={onLastNameCompositionStart}
                onCompositionUpdate={onLastNameCompositionUpdate}
                onCompositionEnd={onLastNameCompositionEnd}
                onKeyUp={(e: any) => {
                  if (['Backspace', 'Delete'].includes(e.code) && e.target.value === '') {
                    setLastNameKana('');
                    form.setFieldValue('lastNamePronunciation', '');
                  }

                  onLastNameKeyUp(e);
                }}
                borderColor={entryFormSetting?.borderColor}
                backgroundColor={entryFormSetting?.bgColor}
                borderRadius={entryFormSetting?.borderRadius}
                fontSize={entryFormSetting?.fontSize}
              />
            </Box>
            <Box width={'100%'}>
              <InputFactory
                name="firstName"
                className={classInput}
                inputAnimation={inputAnimation}
                placeholder={fullNamePlaceholder?.first}
                value={form.values?.firstName}
                error={Boolean(form.errors?.firstName)}
                helperText={form.errors?.firstName && t(form.errors?.firstName, { field: fullNamePlaceholder?.first ?? '名前' })}
                onChange={(e) => {
                  form.handleChange(e);
                }}
                onBlur={form.handleBlur}
                onCompositionStart={onFirstNameCompositionStart}
                onCompositionUpdate={onFirstNameCompositionUpdate}
                onCompositionEnd={onFirstNameCompositionEnd}
                onKeyUp={(e: any) => {
                  if (['Backspace', 'Delete'].includes(e.code) && e.target.value === '') {
                    setFirstNameKana('');
                    form.setFieldValue('firstNamePronunciation', '');
                  }

                  onFirstNameKeyUp(e);
                }}
                borderColor={entryFormSetting?.borderColor}
                backgroundColor={entryFormSetting?.bgColor}
                borderRadius={entryFormSetting?.borderRadius}
                fontSize={entryFormSetting?.fontSize}
              />
            </Box>
          </Stack>
        </Box>
      </ResponsiveStack>
      {hasPronunciation && (
        <ResponsiveStack
          sx={{
            mt: inputAnimation === FormInputAnimationTypes.LABEL_TOP ? '35px' : '15px',
          }}
          direction="row"
          mobileDirection="column"
          spacing={1}
          alignItems="center"
        >
          <Box width={160}>
            <Typography className={classLabel}>フリガナ</Typography>
          </Box>
          <Box className="input-container" flexGrow={1}>
            <Stack direction="row" spacing={3}>
              <Box width={'100%'}>
                <InputFactory
                  name="lastNamePronunciation"
                  className={classInput}
                  inputAnimation={inputAnimation}
                  placeholder={fullNamePlaceholder?.lastPronunciation}
                  value={form.values?.lastNamePronunciation}
                  error={Boolean(form.touched?.lastNamePronunciation && form.errors?.lastNamePronunciation)}
                  helperText={
                    form.touched?.lastNamePronunciation &&
                    t(form.errors?.lastNamePronunciation, {
                      field: `${fullNamePlaceholder?.lastPronunciation ?? '苗字'} (フリガナ)`,
                    })
                  }
                  onChange={(event) => {
                    form.handleChange(event);
                  }}
                  borderColor={entryFormSetting?.borderColor}
                  backgroundColor={entryFormSetting?.bgColor}
                  borderRadius={entryFormSetting?.borderRadius}
                  fontSize={entryFormSetting?.fontSize}
                />
              </Box>
              <Box width={'100%'}>
                <InputFactory
                  name="firstNamePronunciation"
                  className={classInput}
                  inputAnimation={inputAnimation}
                  placeholder={fullNamePlaceholder?.firstPronunciation}
                  value={form.values?.firstNamePronunciation}
                  error={Boolean(form.touched?.firstNamePronunciation && form.errors?.firstNamePronunciation)}
                  helperText={
                    form.touched?.firstNamePronunciation &&
                    t(form.errors?.firstNamePronunciation, {
                      field: `${fullNamePlaceholder?.firstPronunciation ?? '名前'} (フリガナ)`,
                    })
                  }
                  onChange={(event) => {
                    form.handleChange(event);
                  }}
                  borderColor={entryFormSetting?.borderColor}
                  backgroundColor={entryFormSetting?.bgColor}
                  borderRadius={entryFormSetting?.borderRadius}
                  fontSize={entryFormSetting?.fontSize}
                />
              </Box>
            </Stack>
          </Box>
        </ResponsiveStack>
      )}
    </>
  );
};

const FormElementFullName: FC<FormElementFullNameProps<any>> = (props) => {
  if (props.onlyOneName) {
    return <InputFullName {...props} />;
  }

  return <InputFullNameFull {...props} />;
};

export default FormElementFullName;
