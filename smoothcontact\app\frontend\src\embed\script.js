const APP_URL = import.meta.env.VITE_APP_URL;
class SmoothContactEmbed {
  constructor() {
    this.iframes = [];
    this.initialize();
    this.setupMessageListener();
  }

  initialize() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', this.renderEmbeds.bind(this), false);
    } else {
      this.renderEmbeds();
    }
  }

  renderEmbeds() {
    Array.prototype.forEach.call(document.querySelectorAll('.sc-embed'), (element) => {
      if (element.dataset.rendered === 'true' && element.childElementCount !== 0) {
        return;
      }

      const formId = element.dataset.scForm;
      if (!formId) {
        console.error('data-sc-form is not set.');

        return;
      }

      if (!/^[a-zA-Z0-9]+$/.test(formId)) {
        console.error('data-sc-form is invalid: ' + formId);

        return;
      }

      const iframeSrc = `${APP_URL}/front/output/${formId}`;
      const iframe = this.createIframe(iframeSrc, element.dataset.scRedirect === 'true');

      iframe.onload = function () {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        iframeDoc.body.style.backgroundColor = 'transparent';
      };

      element.appendChild(iframe);
      this.iframes.push(iframe);
      element.dataset.rendered = 'true';
    });
  }

  createIframe(src, redirect) {
    const iframe = document.createElement('iframe');
    iframe.setAttribute('src', src);
    iframe.setAttribute('frameborder', 'no');
    iframe.setAttribute('referrerpolicy', 'origin-when-cross-origin');
    iframe.setAttribute('loading', 'lazy');
    iframe.setAttribute('allowtransparency', 'true');
    iframe.style.backgroundColor = 'transparent';
    iframe.style.width = '100%';
    iframe.style.minHeight = '50px';

    if (redirect) {
      iframe.addEventListener('load', () => {
        iframe.contentWindow.postMessage({ scRedirect: true }, '*');
      });
    }

    return iframe;
  }

  setupMessageListener() {
    window.addEventListener(
      'message',
      async (event) => {
        let message;
        try {
          message = JSON.parse(event.data);
        } catch (e) {
          return;
        }

        if (message.action === 'scrollTop') {
          this.iframes.forEach((iframe) => {
            iframe.scrollIntoView(true);
          });
        }

        if (message.action === 'redirect' && message.url) {
          window.location.assign(message.url);

          return;
        }

        if (message.action === 'google_ads') {
          if (typeof window.gtag === 'undefined') {
            await this.loadScript('https://www.googletagmanager.com/gtag/js?id=' + message.id);
          }

          if (message.id) {
            window.gtag('config', message.id);
          }

          if (message.script) {
            this.execScript(message.script);
          }

          return;
        }

        if (message.action === 'yahoo_ads') {
          if (typeof window.ytag === 'undefined') {
            await this.loadScript('https://s.yimg.jp/images/listing/tool/cv/ytag.js');
            window.yjDataLayer = window.yjDataLayer || [];

            // eslint-disable-next-line no-inner-declarations
            window.ytag = function () {
              window.yjDataLayer.push(arguments);
            };
            // eslint-disable-next-line no-undef
            ytag({ type: 'ycl_cookie' });
          }

          if (message.script) {
            this.execScript(message.script, true);
          }
        }

        if (message.height) {
          this.iframes.forEach((iframe) => {
            if (iframe.getAttribute('src').indexOf(message.friendlyKey) >= 0 && event.source === iframe.contentWindow) {
              if (message.page === 'thanks' && iframe.style.height !== message.height + 'px') {
                window.scrollTo(0, iframe.offsetTop);
              }

              iframe.style.display = 'block';
              iframe.style.height = message.height + 'px';
            }
          });
        }
      },
      false
    );
  }

  loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.async = true;
      script.type = 'text/javascript';
      script.onload = () => {
        resolve();
      };
      script.onerror = () => {
        console.log('Failed to load script', src);
        reject();
      };
      script.src = src;
      document.head.appendChild(script);
    });
  }

  execScript(script, isAsync = false) {
    // script maybe have attributes like <script async defer src="https://www.googletagmanager.com/gtag/js?id=AW-123456789"></script>
    const eventSnippet = script.replace(/<script.*?>|<\/script>/g, '');
    const scriptEl = document.createElement('script');
    if (isAsync) {
      scriptEl.async = true;
    }

    scriptEl.text = eventSnippet;
    document.body.appendChild(scriptEl);
    // remove script tag after exec
    setTimeout(() => {
      document.body.removeChild(scriptEl);
    }, 10000);
  }
}

new SmoothContactEmbed();
