import TextFieldsIcon from '@mui/icons-material/TextFields';
import { Button, FormHelperText, InputAdornment, List, ListItem, ListItemText, TextField, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { FormikValues } from 'formik';
import { isArray } from 'lodash';
import { FC, useState } from 'react';

interface FieldDisplayProps {
  form: FormikValues;
}

const FieldDisplay: FC<FieldDisplayProps> = ({ form }) => {
  const [field, setField] = useState('');

  return (
    <>
      <Typography variant="body1" fontSize={12} color="text.secondary">
        フォームフィールドの表示
      </Typography>
      <FormHelperText>フォームフィールドの表示設定</FormHelperText>
      <Stack direction="row" alignItems={'baseline'} justifyContent={'space-around'} spacing={1}>
        <TextField
          size="small"
          fullWidth
          value={field}
          onChange={(e) => {
            setField(e.target.value);
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <TextFieldsIcon />
              </InputAdornment>
            ),
          }}
        />
        <Button
          disabled={!field.length}
          onClick={() => {
            const newFieldDisplays = isArray(form?.values?.htmlFieldDisplays)
              ? form?.values?.htmlFieldDisplays
              : form?.values?.htmlFieldDisplays
                ? [form?.values?.htmlFieldDisplays]
                : [];

            form?.setFieldValue('htmlFieldDisplays', [...newFieldDisplays, field]);
            setField('');
          }}
          variant="contained"
          color="primary"
        >
          追加
        </Button>
      </Stack>
      <List style={{ margin: 0 }} dense={true}>
        {form?.values?.htmlFieldDisplays?.map?.((domain: any, index: any) => (
          <ListItem
            disableGutters
            key={index}
            secondaryAction={
              <Button
                size="small"
                variant="outlined"
                color="warning"
                onClick={() => {
                  const newFieldDisplays = form?.values?.htmlFieldDisplays.filter((value: any) => value !== domain);
                  form?.setFieldValue('htmlFieldDisplays', newFieldDisplays);
                }}
              >
                解除する
              </Button>
            }
          >
            <ListItemText primary={`${domain}`} />
          </ListItem>
        ))}
      </List>
    </>
  );
};

export default FieldDisplay;
