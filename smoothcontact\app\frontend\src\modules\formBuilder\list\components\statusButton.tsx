import SCChip from '@/components/common/SCChip';
import { FormStatus, FormStatusLabel } from '@/types/FormTemplateTypes';
import { getRealStatus, getStatusColor } from '@/utils/helper';
import CheckIcon from '@mui/icons-material/Check';
import { FormHelperText, ListItemIcon, Stack, Typography } from '@mui/material';
import Menu, { MenuProps } from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { alpha, styled } from '@mui/material/styles';
import dayjs from 'dayjs';
import * as React from 'react';
import { useFormBuilderList } from '../store/FormBuilderListProvider';

const StyledMenu = styled((props: MenuProps) => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: 'bottom',
      horizontal: 'right',
    }}
    transformOrigin={{
      vertical: 'top',
      horizontal: 'right',
    }}
    {...props}
  />
))(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    minWidth: 180,
    color: theme.palette.mode === 'light' ? 'rgb(55, 65, 81)' : theme.palette.grey[300],
    boxShadow:
      'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
    '& .MuiMenu-list': {
      padding: '4px 0',
    },
    '& .MuiMenuItem-root': {
      '& .MuiSvgIcon-root': {
        fontSize: 18,
        color: theme.palette.text.secondary,
        marginRight: 0,
      },
      '&:active': {
        backgroundColor: alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),
      },
      '& .MuiListItemIcon-root': {
        justifyContent: 'center',
      },
    },
  },
}));

export interface StatusButtonProps {
  item: any;
  className?: string;
}

export default function StatusButton({ item, className }: StatusButtonProps) {
  const { saveForm, setOpenModal, setScheduleModal, setSelectedRow } = useFormBuilderList();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleChangeStatus = (status: FormStatus) => {
    switch (status) {
      case FormStatus.DRAFT:
        saveForm({ ...item, status: FormStatus.DRAFT });
        break;

      case FormStatus.PUBLISHED:
        saveForm({ ...item, status: FormStatus.PUBLISHED, releaseStartDate: dayjs().toISOString(), releaseEndDate: null });
        break;

      case FormStatus.CLOSED:
        const releaseEndDate = dayjs().subtract(5, 'second').toISOString();
        const formScheduleSetting = { ...item.formScheduleSetting, releaseEndDate, releaseStartDate: null };
        saveForm({ ...item, status: FormStatus.PUBLISHED, formScheduleSetting, releaseEndDate, releaseStartDate: null });
        break;

      default:
        break;
    }
  };

  const handleStatusChangeConfirming = (status: FormStatus | null) => {
    if (status === null) {
      setAnchorEl(null);

      return;
    }

    if (status !== FormStatus.PREPUBLISHED) {
      const title =
        status === FormStatus.DRAFT
          ? 'フォームを非公開にしますか？'
          : status === FormStatus.PUBLISHED
            ? 'フォームを公開してもよろしいですか？'
            : 'フォームの公開を終了してもよろしいですか？';

      const subtitle =
        status === FormStatus.DRAFT
          ? '公開後の再編集は集計結果に影響が出るため、推奨しておりません。また、ユーザーはURLにアクセスできなくなります'
          : status === FormStatus.PUBLISHED
            ? ''
            : 'ユーザーはURLにアクセスできますが、再度公開を開始するまでは投稿することはできません';
      const confirmText = status === FormStatus.DRAFT ? '非公開にする' : status === FormStatus.PUBLISHED ? '公開する' : '公開を終了する';

      setOpenModal({
        open: true,
        title,
        subtitle,
        onConfirm: async () => {
          handleChangeStatus(status);
        },
        cancelText: 'キャンセル',
        confirmText,
        width: 400,
      });
    } else {
      setSelectedRow(item);
      setScheduleModal(true);
    }

    setAnchorEl(null);
  };

  const realStatus = getRealStatus(
    item?.status,
    item?.releaseStartDate,
    item?.releaseEndDate,
    Number(item?.submissionCount ?? 0) >= Number(item?.formScheduleSetting?.maximumNumberFormsReceived ?? 9999999999)
  );

  const statusColor = getStatusColor(realStatus);

  return (
    <>
      <SCChip
        className={className ?? ''}
        key={item.id}
        onClick={handleClick}
        label={`${(FormStatusLabel as { [key: number]: string })[realStatus]}`}
        color={statusColor}
        size="small"
      />
      <StyledMenu anchorEl={anchorEl} open={open} onClose={() => handleStatusChangeConfirming(null)}>
        <MenuItem onClick={() => realStatus !== FormStatus.DRAFT && handleStatusChangeConfirming(FormStatus.DRAFT)} disableRipple sx={{ pr: 0 }}>
          <Stack>
            <Typography>{FormStatusLabel[FormStatus.DRAFT]}</Typography>
            <FormHelperText>
              編集中はこのステースを維持して
              <br />
              ください
            </FormHelperText>
          </Stack>
          {realStatus === FormStatus.DRAFT && (
            <ListItemIcon>
              <CheckIcon />
            </ListItemIcon>
          )}
        </MenuItem>
        <MenuItem
          onClick={() => realStatus !== FormStatus.PREPUBLISHED && handleStatusChangeConfirming(FormStatus.PREPUBLISHED)}
          disableRipple
          sx={{ pr: 0 }}
        >
          <Stack>
            <Typography>公開予約</Typography>
            <FormHelperText>
              フォームのURLが生成され、公開
              <br />
              時間まで投稿を制限できます
            </FormHelperText>
          </Stack>
          {realStatus === FormStatus.PREPUBLISHED && (
            <ListItemIcon>
              <CheckIcon />
            </ListItemIcon>
          )}
        </MenuItem>
        <MenuItem
          onClick={() => realStatus !== FormStatus.PUBLISHED && handleStatusChangeConfirming(FormStatus.PUBLISHED)}
          disableRipple
          sx={{ pr: 0 }}
        >
          <Stack>
            <Typography>{FormStatusLabel[FormStatus.PUBLISHED]}</Typography>
            <FormHelperText>
              フォームのURLが生成され投稿が
              <br />
              できるようになります
            </FormHelperText>
          </Stack>
          {realStatus === FormStatus.PUBLISHED && (
            <ListItemIcon>
              <CheckIcon />
            </ListItemIcon>
          )}
        </MenuItem>
        <MenuItem onClick={() => realStatus !== FormStatus.CLOSED && handleStatusChangeConfirming(FormStatus.CLOSED)} disableRipple sx={{ pr: 0 }}>
          <Stack>
            <Typography>公開終了</Typography>
            <FormHelperText>
              終了すると対象ページが開けなく
              <br />
              なります
            </FormHelperText>
          </Stack>
          {realStatus === FormStatus.CLOSED && (
            <ListItemIcon>
              <CheckIcon />
            </ListItemIcon>
          )}
        </MenuItem>
      </StyledMenu>
    </>
  );
}
