import { forwardRef, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as fs from 'fs';
import * as path from 'path';

import { AWSS3Module } from '@/libs/AWSS3';

import { FormBuilderModule } from '../form-builder/form-builder.module';
import { MfaModule } from '../mfa/mfa.module';
import { UploadModule } from '../upload/upload.module';
import { AccountController } from './account.controller';
import { AccountService } from './account.service';
import { AccountCleanupTask } from './accountCleanup.task';
import { AccountEntity } from './entities/account.entity';

@Module({
  imports: [
    ThrottlerModule.forRoot(),
    JwtModule.register({
      global: true,
      privateKey: fs.readFileSync(path.resolve('./configs/shaKey/private.key')),
      publicKey: fs.readFileSync(path.resolve('./configs/shaKey/public.key')),
      signOptions: { algorithm: 'RS256' },
    }),
    TypeOrmModule.forFeature([AccountEntity]),
    UploadModule,
    AWSS3Module,
    forwardRef(() => MfaModule),
    forwardRef(() => FormBuilderModule),
    ScheduleModule.forRoot(),
  ],
  controllers: [AccountController],
  providers: [AccountService, AccountCleanupTask],
  exports: [AccountService],
})
export class AccountModule {}
