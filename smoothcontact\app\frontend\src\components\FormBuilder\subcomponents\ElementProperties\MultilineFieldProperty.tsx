import { FormElementChildrenType } from '@/types/FormTemplateTypes';
import { Box, FormControl, FormLabel, TextField, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { FC } from 'react';

interface TextFieldProperties {
  editControlProperties: (updatedItem: FormElementChildrenType) => any;
  selectedControl: FormElementChildrenType;
  formHandler: any;
}

const MultilineFieldProperty: FC<TextFieldProperties> = (props) => {
  const { formHandler } = props;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
      <Typography variant="body2">段落テキスト設定</Typography>
      <FormControl>
        <FormLabel sx={{ pb: 1 }}>段数</FormLabel>
        <TextField
          type="number"
          variant="outlined"
          size="small"
          inputProps={{ min: 1, max: 10 }}
          error={!!formHandler?.errors?.rows}
          helperText={!!formHandler?.errors?.rows && '1から10までの数字を入力してください。'}
          {...formHandler.register('rows')}
          value={formHandler?.values?.rows ?? ''}
          onKeyDown={(e) => {
            if (e.key === 'e' || e.key === 'E' || e.key === '-' || e.key === '+') {
              e.preventDefault();
            }
          }}
        />
      </FormControl>
      <FormControl>
        <FormLabel sx={{ pb: 1 }}>入力制限</FormLabel>
        <Stack direction="row" spacing={0.5} alignItems={'center'}>
          <TextField
            inputProps={{ min: 1 }}
            type="number"
            sx={{ maxWidth: '80px' }}
            variant="outlined"
            size="small"
            error={!!formHandler?.errors?.min}
            helperText={!!formHandler?.errors?.min && '最小値を入力してください'}
            {...formHandler.register('min')}
            value={formHandler?.values?.min ?? ''}
            onKeyDown={(e) => {
              if (e.key === 'e' || e.key === 'E' || e.key === '-' || e.key === '+') {
                e.preventDefault();
              }
            }}
          />
          <Typography variant="body2">〜</Typography>
          <TextField
            inputProps={{ min: 1 }}
            type="number"
            sx={{ maxWidth: '80px' }}
            variant="outlined"
            size="small"
            error={!!formHandler?.errors?.max}
            helperText={!!formHandler?.errors?.max && '最小値を入力してください'}
            {...formHandler.register('max')}
            value={formHandler?.values?.max ?? ''}
            onKeyDown={(e) => {
              if (e.key === 'e' || e.key === 'E' || e.key === '-' || e.key === '+') {
                e.preventDefault();
              }
            }}
          />
          <Typography variant="body2">文字以内</Typography>
        </Stack>
      </FormControl>
    </Box>
  );
};

export default MultilineFieldProperty;
