import useAxios, { ApiResponse } from '@/hooks/useAxios';
import { useToast } from '@/provider/toastProvider';
import { getRequestConfig, updateFormRequestConfig } from '@/services/form-builder.service';
import { TemplateType } from '@/types/FormTemplateTypes';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

export default function useLogic() {
  const { toast } = useToast();
  const { id: extId } = useParams();
  const [data, setData] = useState<TemplateType>(null);
  const { apiCaller, loading } = useAxios();
  const { t } = useTranslation();

  useEffect(() => {
    const link1 = document.createElement('link');
    link1.rel = 'stylesheet';
    link1.href = 'https://fonts.fontplus.dev/v1/css/jOW3USBm';
    document.head.appendChild(link1);

    const link2 = document.createElement('link');
    link2.rel = 'stylesheet';
    link2.href = 'https://fonts.fontplus.dev/v1/css/1YLRAG6A';
    document.head.appendChild(link2);

    const link3 = document.createElement('link');
    link3.rel = 'stylesheet';
    link3.href = 'https://fonts.fontplus.dev/v1/css/t8EwrwXo';
    document.head.appendChild(link3);

    // Cleanup: Remove the link tag when the component is unmounted
    return () => {
      document.head.removeChild(link1);
      document.head.removeChild(link2);
      document.head.removeChild(link3);
    };
  }, []);

  const get = async (extId: string) => {
    const { data, success, error }: ApiResponse<any> = await apiCaller(getRequestConfig(extId));

    if (!success) {
      toast({
        isError: true,
        message: error,
      });

      return;
    }

    setData(data);
  };

  const save = async (saveRequest: any, message?: string) => {
    const { data, success, error }: ApiResponse<any> = await apiCaller(updateFormRequestConfig(saveRequest));

    if (!success) {
      toast({
        isError: true,
        message: error ?? t('save_failed'),
      });

      return false;
    }

    setData(data);

    toast({
      isError: false,
      message: message ?? t('save_success'),
    });

    return true;
  };

  useEffect(() => {
    if (!extId) {
      return;
    }

    get(extId);
  }, [extId]);

  return {
    get,
    save,
    loading,
    data,
  };
}
