import React, { createContext, useContext } from 'react';
interface EmbedState {}

export const EmbedContext = createContext<EmbedState>({});

export const EmbedProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <EmbedContext.Provider
      value={{
        shareData: true,
      }}
    >
      {children}
    </EmbedContext.Provider>
  );
};

export const useEmbed = () => {
  const context = useContext(EmbedContext);
  if (!context) {
    throw new Error('useEmbed must be used within an EmbedProvider');
  }

  return context;
};
