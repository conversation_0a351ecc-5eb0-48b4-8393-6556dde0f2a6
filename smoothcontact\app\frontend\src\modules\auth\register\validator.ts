import { isEmailHalfSize } from '@/utils/validate';
import * as Yup from 'yup';

const EMAIL_REGEX = /^(?!.*\.\.@)(?!.*\.\.)[+\w\-_][+\w\-._]*@[\w\-._]+\.[A-Za-z]+$/;

export const validationSchema = Yup.object().shape({
  firstName: Yup.string().required('ファーストネームが必須'),
  lastName: Yup.string().required('ラストネームが必須'),
  email: Yup.string()
    .required('メールアドレスが必須')
    .email('メールアドレスの形式ではありません。')
    .max(50, 'メールアドレスは半角英数字記号、50文字以内で入力してください。')
    .test('is-email-halfSize', 'メールアドレスは半角英数字記号、50文字以内で入力してください。', isEmailHalfSize)
    .matches(EMAIL_REGEX, 'メールアドレスの形式ではありません。'),
  pwd: Yup.string()
    .required('パスワードが必須')
    .min(6, 'パスワードは6文字以上で入力してください')
    .max(16, 'パスワードは16文字以内で入力してください')
    .matches(/^[a-zA-Z0-9_-]*$/, 'パスワードに使える文字は半角のアルファベット、"_" ＆ "-"記号'),
  rePwd: Yup.string()
    .required('パスワードの確認が必須')
    .oneOf([Yup.ref('pwd'), null], 'パスワードが一致する必要がある'),
  privacyPolicy: Yup.boolean().oneOf([true], 'プライバシーポリシーに同意してください'),
  termsOfUse: Yup.boolean().oneOf([true], '利用規約に同意してください'),
});
