import { FC } from 'react';
import { FormikValues } from 'formik';
import { Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import SCInputTag from '@/components/common/SCInputTag';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';

interface ContactPersonProps {
  form: FormikValues;
}

const ContactPerson: FC<ContactPersonProps> = ({ form }) => {
  const { t } = useTranslation();

  return (
    <>
      <Typography variant="body1" fontSize={12} color="text.secondary" sx={{ pl: 2 }}>
        {t('form_builder.contact_person.label')}
      </Typography>
      <Typography fontSize={12} color="text.secondary">
        {t('form_builder.contact_person.description')}
      </Typography>

      <SCInputTag name="contactPerson" form={form} IconComponent={AccountCircleIcon} />
    </>
  );
};

export default ContactPerson;
