import React, { FC, useEffect, useRef, useState } from 'react';
import { Button, CircularProgress, Typography } from '@mui/material';
import { useParams } from 'react-router-dom';
import CloseIcon from '@mui/icons-material/Close';
import useAxios from '@/hooks/useAxios';
import { useToast } from '@/provider/toastProvider';
import { useTranslation } from 'react-i18next';

export interface UploadFileValue {
  fileName: string;
  key: string;
}

interface UploadFileComponentProps {
  previewUrl?: string;
  onChangeFile?: (value: UploadFileValue) => void;
  isResetTrigger?: boolean;
}

const UploadFileComponent: FC<UploadFileComponentProps> = ({ previewUrl, onChangeFile, isResetTrigger }) => {
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [showButton, setShowButton] = useState(true);
  const { apiCaller, loading } = useAxios<any>();
  const { id: extId } = useParams<{ id: string }>();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { t } = useTranslation();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];

    if (!selectedFile) {
      return;
    }

    if (!isAcceptedFileType(selectedFile)) {
      toast({
        isError: true,
        message: t('invalid_file_type'),
      });

      return;
    }

    // Maximum file size allowed is 10MB
    if (selectedFile.size > 10485760) {
      toast({
        isError: true,
        message: t('file_too_large'),
      });

      return;
    }

    setFile(selectedFile);

    const reader = new FileReader();

    reader.onloadend = () => {
      setPreview(reader.result as string);
    };
    reader.readAsDataURL(selectedFile);
  };

  const isAcceptedFileType = (file: File) => {
    const acceptedFileTypes = ['image/jpeg', 'image/jpg', 'image/png'];

    return acceptedFileTypes.includes(file.type);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
    setShowButton(true);
  };

  const handleRemoveClick = () => {
    setFile(null);
    setPreview(null);
    setShowButton(false);
    onChangeFile({ fileName: '', key: '' });

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  useEffect(() => {
    const uploadFile = async () => {
      if (!file) {
        return;
      }

      try {
        const data = new FormData();
        data.append('file', file);
        const result = await apiCaller({
          url: `/api/form-builder/${extId}/upload-og-image`,
          method: 'POST',
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          data,
        });

        if (!result.success) {
          toast({
            isError: true,
            message: t('upload_failed'),
          });
        }

        onChangeFile && onChangeFile(result.data);
      } catch (e) {
        toast({
          isError: true,
          message: t('unspecified_error'),
        });
      }
    };

    uploadFile();
  }, [file]);

  useEffect(() => {
    if (isResetTrigger) {
      setPreview(previewUrl);
    }

    if (previewUrl && !file) {
      setPreview(previewUrl);
      setShowButton(true);

      return;
    }

    if (!previewUrl && file) {
      handleRemoveClick();

      return;
    }
  }, [previewUrl, isResetTrigger]);

  return (
    <div
      style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', borderRadius: '10px', border: '1px dotted #ccc', padding: '16px' }}
    >
      <div style={{ textAlign: 'center' }}>
        <label htmlFor="fileInput" style={{ display: 'flex', alignItems: 'center' }}>
          <input ref={fileInputRef} type="file" style={{ display: 'none' }} onChange={handleFileChange} />
        </label>
        {!file && !preview && (
          <>
            <Button
              variant="contained"
              onClick={handleUploadClick}
              sx={{ textTransform: 'none', backgroundColor: 'white', border: '1px solid #E6E6E6', mb: 2 }}
            >
              Add files
            </Button>
            <Typography display="block" variant="caption" fontSize={13} color="text.secondary">
              Accepts .jpg, and .png
            </Typography>
          </>
        )}
      </div>

      {preview && (
        <div style={{ position: 'relative', maxWidth: '100%' }}>
          {showButton && (
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                borderRadius: '50%',
                width: '40px',
                height: '40px',
              }}
            >
              <Button
                onClick={handleRemoveClick}
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  color: 'black',
                }}
                disabled={loading}
              >
                {loading ? <CircularProgress /> : <CloseIcon />}
              </Button>
            </div>
          )}
          <img
            src={preview}
            alt="OGP Preview"
            style={{
              maxWidth: '100%',
              height: 'auto',
            }}
          />
        </div>
      )}
    </div>
  );
};

export default UploadFileComponent;
