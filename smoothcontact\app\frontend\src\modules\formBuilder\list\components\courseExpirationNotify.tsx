import * as React from 'react';
import { Box, Stack } from '@mui/system';
import CloseIcon from '@mui/icons-material/Close';
import { Button, Card, CardActions, CardContent, IconButton, Typography } from '@mui/material';

export interface CourseExpirationNotifyProps {
  course: string;
  expirationDate: string;
}

export default function CourseExpirationNotify({ course, expirationDate }: CourseExpirationNotifyProps) {
  const courseNames: { [key: string]: string } = {
    '1': 'Free Course',
    '2': 'Pro-Course',
    '3': 'Enterprise-Course',
  };

  // only display when expiration date is within 15 days
  const now = new Date();
  const expiration = new Date(expirationDate);
  const diffTime = Math.abs(expiration.getTime() - now.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays > 15) {
    return null;
  }

  // Get the year, month, and day
  const year = expiration.getFullYear();
  const month = expiration.getMonth() + 1; // Months are 0-based, so add 1
  const day = expiration.getDate();

  // Format the date with the kanji characters
  const formattedDate = `${year}年${month}月${day}日`;

  return (
    <Box sx={{ py: 2, pb: '36px' }}>
      <Stack direction="column" justifyContent="center" alignItems="center" spacing={2} p={3}>
        <Card sx={{ minWidth: 275, position: 'relative', backgroundColor: 'rgb(155 159 163 / 40%);' }}>
          <IconButton sx={{ position: 'absolute', top: 8, right: 8 }} onClick={() => console.log('Remove Clicked')}>
            <CloseIcon />
          </IconButton>

          <CardContent>
            <Typography variant="h3" color="error">
              Your contract course will be expired soon
            </Typography>
            <Typography sx={{ color: 'text.secondary', mt: 1.5, mb: 1.5 }}>
              Your current contract {courseNames[course]} will be expired on {formattedDate}
            </Typography>
            <Typography variant="body2">
              For maintaining form data, please update the expiration date by purchasing a serial number. If the expiration date has expired, the
              contract course will be automatically switched to the free course, in this case various functions will be disabled, or limited. Please
              update your course contract as soon as possible.
            </Typography>
          </CardContent>
          <CardActions sx={{ justifyContent: 'center', width: '100%' }}>
            <Button size="small" variant="contained" color="success">
              Purchase a serial number
            </Button>
          </CardActions>
        </Card>
      </Stack>
    </Box>
  );
}
