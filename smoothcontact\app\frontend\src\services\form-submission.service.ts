import { FormSubmissionFilterRequestDto } from '@/modules/formBuilder/submission/dto/request.dto';
import { AxiosRequestConfig } from 'axios';

export const getByExtIdRequestConfig = (extId: string, params: FormSubmissionFilterRequestDto): AxiosRequestConfig => ({
  url: `/api/form-submission/${extId}`,
  method: 'get',
  params,
});

export const getReportByExtIdRequestConfig = (extId: string): AxiosRequestConfig => ({
  url: `/api/form-submission/${extId}/report`,
  method: 'get',
});

export const downloadCsvSubmissionRequestConfig = (extId: string, isCombine: boolean): AxiosRequestConfig => ({
  url: `/api/form-submission/${extId}/csv/download`,
  method: 'get',
  params: { isCombine },
  responseType: 'blob',
});

export const downloadCsvSubmissionStatictisRequestConfig = (extId: string): AxiosRequestConfig => ({
  url: `/api/form-submission/${extId}/csv/statictis`,
  method: 'get',
  responseType: 'blob',
});

export const getCrossTabulationSelectionsRequestConfig = (
  extId: string,
  targetTabulationId: string,
  crossTabulationId: string
): AxiosRequestConfig => ({
  url: `/api/form-submission/${extId}/cross-tabulation`,
  method: 'get',
  params: { targetTabulationId, crossTabulationId },
});
