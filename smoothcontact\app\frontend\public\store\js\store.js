// store.js V2
document.write('<form method="post" name="storeForm" action="https://cart0.shopserve.jp/dsstore.zk/cart.php">');
document.write('<input type="hidden" name="ITM" value="">');
document.write('<input type="hidden" name="OPT1" value="">');
document.write('<input type="hidden" name="OPT2" value="">');
document.write('</form>');

// cart
function doBuy(itemNo,opt1,opt2) {
	var ssform = document.forms['storeForm'];
	ssform.ITM.value = itemNo;
	if (!opt1) opt1 = '';
	ssform['OPT1'].value = opt1;
	if (!opt2) opt2 = '';
	ssform['OPT2'].value = opt2;
	
	if (typeof(pageTracker) != 'undefined') pageTracker._linkByPost(ssform);
	
	ssform.submit();
}
