import SCColorPicker from '@/components/common/SCColorPicker';
import SCFontSelector from '@/components/common/SCFontSelector';
import { KeyboardArrowDown } from '@mui/icons-material';
import { FormControl, Grid, MenuItem, Select, Stack, TextField, Typography } from '@mui/material';
import { FC } from 'react';
import { CustomModeComponentProps } from './CustomModeComponent';

const LabelsCustomComponent: FC<CustomModeComponentProps> = (props) => {
  const { form, webFonts } = props;

  return (
    <>
      <Typography variant="body2">ラベル</Typography>
      <Stack direction="row" spacing={2}>
        <TextField
          label="文字サイズ"
          variant="outlined"
          type="number"
          {...form.register('labelSettings.fontSize')}
          value={form?.values?.labelSettings?.fontSize}
          error={!!form?.errors?.labelSettings?.fontSize}
          helperText={form?.errors?.labelSettings?.fontSize ? '整数を半角で入力してください' : ''}
        />
        <Select
          IconComponent={() => <KeyboardArrowDown />}
          {...form.register('labelSettings.fontSizeUnit')}
          value={form?.values?.labelSettings?.fontSizeUnit}
          displayEmpty
        >
          <MenuItem value={'px'}>px</MenuItem>
          <MenuItem value={'rem'}>rem</MenuItem>
        </Select>
      </Stack>
      <Grid container alignItems="center">
        <Grid item xs={4} container justifyContent="flex-end">
          <Typography variant="body2">文字色</Typography>
        </Grid>
        <Grid item xs={6} sx={{ pl: 2 }}>
          <SCColorPicker name="labelSettings.color" color={form?.values?.labelSettings?.color} form={form} />
        </Grid>
      </Grid>
      <FormControl fullWidth>
        <SCFontSelector
          name={'labelSettings.fontFamily'}
          fontFamily={form?.values?.labelSettings?.fontFamily ?? ''}
          fontName={form?.values?.labelSettings?.fontName ?? form?.values?.labelSettings?.fontFamily ?? ''}
          source={webFonts}
          onFontChange={(font) => {
            form?.setFieldValue('labelSettings.fontFamily', `${font?.fontFamily}`);
            form?.setFieldValue('labelSettings.fontName', `${font?.fontName}`);
          }}
        />
      </FormControl>
    </>
  );
};

export default LabelsCustomComponent;
