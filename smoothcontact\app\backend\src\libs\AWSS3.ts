import { CopyObjectCommand, DeleteObjectCommand, GetObjectCommand, PutO<PERSON>Command, S3Client } from '@aws-sdk/client-s3';
import { S3ClientConfig } from '@aws-sdk/client-s3/dist-types/S3Client';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Injectable, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createReadStream, unlinkSync } from 'fs';
import { join } from 'path';
import * as process from 'process';

import { formatDate } from '@/utils/helpers';
import { logger } from '@/core/logger/index.logger';

const TEMPORARY_DIR = 'temporary';

@Injectable()
export class AWSS3 {
  private s3: S3Client;
  private s3DefaultBucket: string;
  private uploadFile: Express.Multer.File | null = null;
  private uploadDir: string = '';
  private uploadKey: string = '';
  private uploadFileName: string = '';

  constructor(private readonly configService: ConfigService) {
    const awsRegion = this.configService.get('AWS_S3_REGION');

    if (!awsRegion) {
      throw new Error('AWS_S3_REGION is not defined');
    }

    const s3Config: S3ClientConfig = {
      region: awsRegion,
      credentials:
        this.configService.get('AWS_ACCESS_KEY') && this.configService.get('AWS_SECRET_KEY')
          ? {
              accessKeyId: this.configService.get('AWS_ACCESS_KEY'),
              secretAccessKey: this.configService.get('AWS_SECRET_KEY'),
            }
          : undefined,
    };

    this.s3 = new S3Client(s3Config);

    this.s3DefaultBucket = this.configService.get('AWS_S3_BUCKET_NAME') || '';
  }

  setUploadDir(dir: string) {
    this.uploadDir = dir;
  }

  setUploadFile(file: Express.Multer.File) {
    this.uploadFile = file;
  }

  setUploadFileName(fileName: string) {
    this.uploadFileName = fileName;
  }

  async upload(keepFileTemp = true, isTemporary = true) {
    const file = this.uploadFile;
    if (!file) {
      throw new Error('File is not set');
    }

    const fileTemp = createReadStream(join(process.cwd(), file.path));
    const prefix = isTemporary ? TEMPORARY_DIR + '/' : '';
    const fileExtension = this.getFileExtension(file.originalname);
    const originalName = file.originalname.split('.').slice(0, -1).join('.');
    const fileName = this.uploadFileName
      ? this.uploadFileName + '.' + fileExtension
      : Buffer.from(`${originalName}-${formatDate(new Date(), 'YYYY.MM.DD_HH.mm.ss')}`, 'latin1').toString('utf8') + '.' + fileExtension;
    this.uploadKey = `${this.uploadDir}/${fileName}`;

    const command = new PutObjectCommand({
      Bucket: this.s3DefaultBucket,
      Key: `${prefix}${this.uploadKey}`,
      Body: fileTemp,
      ContentType: file.mimetype,
      ContentDisposition: 'inline',
    });

    try {
      await this.s3.send(command);
      if (!keepFileTemp) {
        unlinkSync(join(process.cwd(), file.path));
      }

      return {
        fileName: Buffer.from(file.originalname, 'latin1').toString('utf8'),
        key: this.uploadKey,
        url: await this.getSignedUrl(`${prefix}${this.uploadKey}`),
      };
    } catch (e) {
      logger.error('upload error', e);
      throw new Error('Failed to upload file');
    }
  }

  async temporaryUpload() {
    return this.upload(false, true);
  }

  async confirmAndMoveFile(uploadKey: string) {
    if (!uploadKey) {
      return;
    }

    const temporaryFileKey = `${TEMPORARY_DIR}/${uploadKey}`;
    const copyCommand = new CopyObjectCommand({
      Bucket: this.s3DefaultBucket,
      CopySource: encodeURI(`${this.s3DefaultBucket}/${temporaryFileKey}`),
      Key: uploadKey,
    });

    const deleteCommand = new DeleteObjectCommand({
      Bucket: this.s3DefaultBucket,
      Key: temporaryFileKey,
    });

    try {
      await this.s3.send(copyCommand);
      await this.s3.send(deleteCommand);

      return {
        key: uploadKey,
        url: await this.getSignedUrl(uploadKey),
        publicUrl: this.getPublicUrl(uploadKey),
      };
    } catch (e) {
      logger.error('confirmAndMoveFile error', e);
      throw new Error('Failed to copy file');
    }
  }

  async deleteFile(uploadKey: string) {
    if (!uploadKey) {
      throw new Error('Upload key is not set');
    }

    const command = new DeleteObjectCommand({
      Bucket: this.s3DefaultBucket,
      Key: uploadKey,
    });

    try {
      await this.s3.send(command);

      return {
        key: uploadKey,
      };
    } catch (e) {
      logger.error('deleteFile error', e);
      throw new Error('Failed to delete file');
    }
  }

  async getSignedUrl(fileKey: string, expires: number = 900) {
    const command = new GetObjectCommand({ Bucket: this.s3DefaultBucket, Key: fileKey });

    return await getSignedUrl(this.s3, command, { expiresIn: expires });
  }

  getFileExtension = (fileName: string): string => {
    const lastDotIndex = fileName.lastIndexOf('.');

    if (lastDotIndex === -1 || lastDotIndex === 0 || lastDotIndex === fileName.length - 1) {
      return '';
    }

    return fileName.slice(lastDotIndex + 1);
  };

  getPublicUrl = (fileName: string): string => {
    return encodeURI(`${this.getS3BaseUrl()}/${fileName}`);
  };

  getS3BaseUrl = () => {
    return this.configService.get('AWS_S3_BUCKET_URL') || '';
  };
}

@Module({
  imports: [],
  controllers: [],
  providers: [AWSS3],
  exports: [AWSS3],
})
export class AWSS3Module {}
