import { Expose } from 'class-transformer';

import { MessageCode } from '@/common/constant';
import { assignDataToInstance } from '@/common/helper';

export interface IDataError {
  errors?: any;
  [key: string]: any;
}

export type ExecuteReturnType<T, DefinedType> = DefinedType extends undefined ? T : DefinedType;

export class APIResponseBase<T> {
  statusCode?: number;

  messageCode?: MessageCode;

  message?: string;

  messageErrors?: Partial<Record<keyof T, string>>;

  data?: T | null;

  success?: boolean;

  constructor(data?: Partial<APIResponseBase<T>>) {
    assignDataToInstance(data, this);
  }
}

export class PaginationMetaData {
  page?: number;

  perPage?: number;

  total?: number;

  constructor(data?: Partial<PaginationMetaData>) {
    assignDataToInstance(data, this);
  }
}

export class Pagination<T> {
  @Expose()
  items: T[] = [];

  @Expose()
  pagination?: PaginationMetaData = new PaginationMetaData({ page: 1, perPage: 10, total: 0 });
}

export class ListItem<T> {
  items: T[];
}

export class APIResponseApplyBiND {
  status: boolean;
  url: string;
}
