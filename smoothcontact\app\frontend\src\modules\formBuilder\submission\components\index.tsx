import SCBreadcrumbs from '@/components/common/SCBreadcrumbs';
import SCChip from '@/components/common/SCChip';
import SCIconButton from '@/components/common/SCIconButton';
import SCSimpleCard from '@/components/common/SCSimpleCard';
import SCDataTable, { SCTableColumnProps } from '@/components/customTable';
import { formatDate } from '@/utils/dateTime';
import FilterListOffIcon from '@mui/icons-material/FilterListOff';
import { TableContainer, Typography } from '@mui/material';
import CircularProgress from '@mui/material/CircularProgress';
import { Box, Container, Stack } from '@mui/system';
import { useMemo } from 'react';
import { useFormBuilderList } from '../store/FormSubmissionListProvider';
import SubmissionFilterModal from './filterModal';

export enum SubmissionStatus {
  NOT_YET_SUPPORT = 'NOT_YET_SUPPORT',
  WAITING_REPLY = 'WAITING_REPLY',
  DEVELOPMENT_SUPPORT_REQUIRED = 'DEVELOPMENT_SUPPORT_REQUIRED',
  COMPLETE = 'COMPLETE',
}

export enum SubmissionStatusValue {
  NOT_YET_SUPPORT = 0,
  WAITING_REPLY = 1,
  DEVELOPMENT_SUPPORT_REQUIRED = 2,
  COMPLETE = 3,
}

export const enum FilterStatusOptions {
  ALL = 'ALL',
  NOT_YET_SUPPORT = 'NOT_YET_SUPPORT',
  WAITING_REPLY = 'WAITING_REPLY',
  DEVELOPMENT_SUPPORT_REQUIRED = 'DEVELOPMENT_SUPPORT_REQUIRED',
  COMPLETE = 'COMPLETE',
}

export const SubmissionStatusLabel: { [key in FilterStatusOptions]: string } = {
  [FilterStatusOptions.ALL]: 'すべて',
  [FilterStatusOptions.NOT_YET_SUPPORT]: '未対応',
  [FilterStatusOptions.WAITING_REPLY]: '返信待ち',
  [FilterStatusOptions.DEVELOPMENT_SUPPORT_REQUIRED]: '開発対応が必要',
  [FilterStatusOptions.COMPLETE]: '対応完了',
};

const FormSubmissionListComponent = () => {
  const {
    data,
    pagination,
    filterModal,
    setFilterModal,
    handleChangePage,
    handleChangeRowsPerPage,
    handleSort,
    getAllSubmissions: getAllForms,
    order,
    orderBy,
    filter,
    setFilter,
  } = useFormBuilderList();

  const columns = useMemo<SCTableColumnProps[]>(() => {
    return [
      {
        id: 'id',
        title: '#',
        width: '30px',
      },
      {
        id: 'name',
        title: '件名',
        sortable: true,
        render: (row: any) => {
          return <Typography>{row?.formBuilder?.formElements?.[0]?.container?.heading}</Typography>;
        },
      },
      {
        id: 'status',
        title: 'ステータス',
        sortable: true,
        width: '120px',
        render: (row: any) => {
          const statusLabel: string = SubmissionStatusLabel[FilterStatusOptions.NOT_YET_SUPPORT];

          switch (row?.status) {
            case SubmissionStatusValue.WAITING_REPLY:
              return SubmissionStatusLabel[FilterStatusOptions.WAITING_REPLY];

            case SubmissionStatusValue.DEVELOPMENT_SUPPORT_REQUIRED:
              return SubmissionStatusLabel[FilterStatusOptions.DEVELOPMENT_SUPPORT_REQUIRED];

            case SubmissionStatusValue.COMPLETE:
              return SubmissionStatusLabel[FilterStatusOptions.COMPLETE];

            default:
          }

          const getStatusColor = () => {
            switch (row?.status) {
              case SubmissionStatusValue.WAITING_REPLY:
                return 'success';

              case SubmissionStatusValue.DEVELOPMENT_SUPPORT_REQUIRED:
                return 'warning';

              case SubmissionStatusValue.COMPLETE:
                return 'default';

              default:
                return 'info';
            }
          };

          return <SCChip key={row?.id} label={`${statusLabel}`} color={getStatusColor()} size="small" />;
        },
      },
      {
        id: 'updatedAt',
        title: '更新日',
        width: '120px',
        render: (row: any) => {
          return formatDate(row.updatedAt);
        },
      },
      {
        id: 'sender',
        width: '180px',
        title: '送信者',
        render: (row: any) => {
          const receiveEmailField = row?.formBuilder?.formMailSetting?.receiveEmailField;

          if (!receiveEmailField) {
            return <Typography></Typography>;
          }

          const receiveEmails = row?.formValues?.filter((value: any) => value?.id === receiveEmailField);

          return <Typography>{receiveEmails?.[0]?.value ?? ''}</Typography>;
        },
      },
      // {
      //   id: 'receiver',
      //   title: '担当者',
      //   render: () => {
      //     return '';
      //     // return <SCChip key={row?.id} size="small" color="default" label={'山田太郎'} avatar={<Avatar />}></SCChip>;
      //   },
      // },
      // {
      //   id: 'tag',
      //   title: 'タグ',
      //   render: () => {
      //     return ''; // TODO: Implement tag
      //   },
      // },
    ];
  }, []);

  return (
    <Container>
      <Box sx={{ py: 2 }}>
        <Stack direction="column" gap={3}>
          {!data && (
            <Box display="flex" justifyContent="center" alignItems="center" style={{ height: '98vh' }}>
              <CircularProgress />
            </Box>
          )}
          <SCBreadcrumbs
            items={[
              {
                child: (
                  <Typography sx={{ cursor: 'pointer' }} color="secondary">
                    問い合わせ管理
                  </Typography>
                ),
              },
            ]}
          />
          <Typography variant="h6" fontWeight="bold">
            問い合わせ管理
          </Typography>
          {data && (
            <Stack spacing={4}>
              <SCSimpleCard>
                <TableContainer>
                  <SCDataTable
                    columns={columns}
                    data={data}
                    pagination={pagination}
                    handleChangePage={handleChangePage}
                    handleChangeRowsPerPage={handleChangeRowsPerPage}
                    handleSort={handleSort}
                    order={order}
                    orderBy={orderBy}
                    renderToolBar={() => {
                      return (
                        <Stack direction="row" justifyContent={'flex-end'} gap={4}>
                          <SCIconButton color="secondary" onClick={() => setFilterModal(true)}>
                            <FilterListOffIcon />
                          </SCIconButton>
                        </Stack>
                      );
                    }}
                  ></SCDataTable>
                </TableContainer>
              </SCSimpleCard>
            </Stack>
          )}

          {filterModal && (
            <SubmissionFilterModal
              filter={filter ?? []}
              open={filterModal}
              setFilter={setFilter}
              onSearch={(currentFilter: any) => {
                setFilter(currentFilter);
                getAllForms(currentFilter);
                setFilterModal(false);
              }}
            />
          )}
        </Stack>
      </Box>
    </Container>
  );
};

export default FormSubmissionListComponent;
