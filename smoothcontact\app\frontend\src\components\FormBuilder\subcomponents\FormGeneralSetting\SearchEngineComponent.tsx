import { FC } from 'react';
import { FormikValues } from 'formik';
import { useTranslation } from 'react-i18next';
import SettingItem from './SettingItemComponent';
import { Box, Checkbox, FormControlLabel } from '@mui/material';

interface SearchEngineProps {
  form: FormikValues;
}

const SearchEngine: FC<SearchEngineProps> = ({ form }) => {
  const { t } = useTranslation();

  const isDisplaySearchEngine = form?.values?.isDisplaySearchEngine ?? true;

  return (
    <Box>
      <SettingItem label={t('form_builder.search_engine.label')} isEnable={isDisplaySearchEngine} />
      <FormControlLabel
        control={
          <Checkbox
            checked={isDisplaySearchEngine}
            name="isDisplaySearchEngine"
            {...form.register('isDisplaySearchEngine', { nameOfValueProps: 'checked' })}
          />
        }
        label={t('form_builder.search_engine.checkbox_label')}
      />
    </Box>
  );
};

export default SearchEngine;
