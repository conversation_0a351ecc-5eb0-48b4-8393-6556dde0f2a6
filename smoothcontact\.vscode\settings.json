{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "search.exclude": {
    "**/.git": true,
    "**/node_modules": true,
    "**/dist": true,
    "**/logs": true,
    "**/yarn.lock": true,
    // "apps/admin": true,
    // "apps/customer": true,
    "**/package-lock.json": true
  },
  "cSpell.ignoreRegExpList": [
    "persistor",
    "msfilter",
    "boxicons",
    "miui",
    "uuidv4",
    "nprogress",
    "htmls",
    "simplebar",
    "Eles",
    "Formik",
    "antd",
    "clsx",
    "dont",
    "ant"
  ],
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": false
  },
  "editor.quickSuggestionsDelay": 10,
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    // "apps/admin": true,
    // "apps/customer": true,
    "**/.DS_Store": true
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.tabSize": 2,
  "eslint.validate": ["javascript", "javascriptreact", "html", "typescriptreact"],
  "javascript.suggestionActions.enabled": false,
  "i18n-ally.localesPaths": ["locales"],
  "i18n-ally.sourceLanguage": "jp",
  "i18n-ally.keystyle": "nested",
  "i18n-ally.displayLanguage": "jp",
  "i18n-ally.pathMatcher": "{locale}/{namespaces}.json",
  "i18n-ally.enabledFrameworks": ["i18next", "react"],
  "i18n-ally.namespace": true,
  "i18n-ally.defaultNamespace": "common",
  "workspace.isHidden": true,
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "cSpell.words": ["Aidstart", "DEGITALSTAGEID", "regist", "Statictis"],
  "[sql]": {
    "editor.defaultFormatter": "cweijan.vscode-database-client2"
  }
}
