import SCColorPicker from '@/components/common/SCColorPicker';
import { FormColorSetting } from '@/types/FormTemplateTypes';
import { Divider, Grid, Stack, Typography } from '@mui/material';
import { FormikValues } from 'formik';
import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import AnimationCustomComponent from './AnimationCustomComponent';
import ButtonCustomComponent from './ButtonCustomComponent';
import ChoiceCustomComponent from './ChoiceCustomComponent';
import DescriptionCustomComponent from './DescriptionCustomComponent';
import EntryFormCustomComponent from './EntryFormCustomComponent';
import GeneralCustomComponent from './GeneralCustomComponent';
import LabelsCustomComponent from './LabelsCustomComponent';
import TitleCustomComponent from './TitleCustomComponent';

export interface CustomModeComponentProps {
  editColorSetting?: (colorSetting: FormColorSetting) => void;
  colorSetting?: FormColorSetting;
  form: FormikValues;
  webFonts?: any;
}

const CustomModeComponent: FC<CustomModeComponentProps> = (props) => {
  const { t } = useTranslation();
  const { form, webFonts } = props;

  return (
    <Stack spacing={2}>
      <Grid container alignItems="center">
        <Grid item xs={6} container justifyContent="flex-end">
          <Typography variant="body2">{t('form_builder.color_setting.custom_mode.preview_background_color')}</Typography>
        </Grid>
        <Grid item xs={4} sx={{ pl: 2 }}>
          <SCColorPicker name="bgColor" color={form?.values?.bgColor} form={form} />
        </Grid>
      </Grid>
      <Divider />
      <GeneralCustomComponent form={props.form} webFonts={webFonts} />
      <Divider />
      <ChoiceCustomComponent form={props.form} />
      <Divider />
      <TitleCustomComponent form={props.form} webFonts={webFonts} />
      <Divider />
      <LabelsCustomComponent form={props.form} webFonts={webFonts} />
      <Divider />
      <DescriptionCustomComponent form={props.form} webFonts={webFonts} />
      <Divider />
      <EntryFormCustomComponent form={props.form} webFonts={webFonts} />
      <Divider />
      <ButtonCustomComponent form={props.form} webFonts={webFonts} />
      <Divider />
      <AnimationCustomComponent form={props.form} />
    </Stack>
  );
};

export default CustomModeComponent;
