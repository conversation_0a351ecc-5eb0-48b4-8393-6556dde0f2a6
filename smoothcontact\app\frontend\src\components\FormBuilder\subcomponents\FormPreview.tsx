import SCSimpleCardFitHeight from '@/components/common/SCSimpleCardFitHeight';
import FormConfirm from '@/components/FormBuilder/subcomponents/FormConfirm';
import FormDisplayMessage from '@/components/FormBuilder/subcomponents/FormDisplayMessage';
import FormStandalone from '@/components/FormBuilder/subcomponents/FormStandalone';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormColorSetting, SideBarType } from '@/types/FormTemplateTypes';
import { FORM_COLOR_SETTING_INIT, FormTemplateModePresentType, FormTemplateModePresets } from '@/utils/formBuilderUtils';
import { displayFontFamily } from '@/utils/helper';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import { Typography } from '@mui/material';
import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import React, { useEffect, useMemo, useState } from 'react';
import { Scrollbars } from 'react-custom-scrollbars';
import { makeStyles } from 'tss-react/mui';

interface FormPreviewProps {
  screenType: string;
  showPreview: boolean;
}

enum TabValue {
  INPUT = 'input',
  CONFIRM = 'confirm',
  THANKYOU = 'thankyou',
  BEFORE_PUBLIC = 'before_public',
  AFTER_PUBLIC = 'after_public',
  HIDDEN_FORM = 'hidden_form',
}

const getTabsBySideBarType = (sideBarType: SideBarType) => {
  switch (sideBarType) {
    case SideBarType.SCHEDULE:
      return [
        {
          value: TabValue.BEFORE_PUBLIC,
          label: '公開前',
        },
        {
          value: TabValue.AFTER_PUBLIC,
          label: '公開終了後',
        },
        {
          value: TabValue.HIDDEN_FORM,
          label: '非公開',
        },
      ];

    default:
      return [
        {
          value: TabValue.INPUT,
          label: '入力画面',
        },
        {
          value: TabValue.CONFIRM,
          label: '確認画面',
        },
        {
          value: TabValue.THANKYOU,
          label: '送信完了画面',
        },
      ];
  }
};

const useStyles = makeStyles()((theme) => ({
  tab: {
    '.MuiTab-root': {
      '&.Mui-selected': {
        color: theme.palette.primary.dark,
      },
    },
    '.MuiTabs-indicator': {
      backgroundColor: theme.palette.primary.dark,
    },
  },
}));

const FormPreview: React.FC<FormPreviewProps> = ({ screenType, showPreview }) => {
  const {
    formGeneralSetting,
    formMailSetting,
    formScheduleSetting,
    sideBarType,
    error,
    isFormChanged,
    selectedTemplate,
    setSideBarType,
    setOpenModal,
    resetSelectedTemplate,
  } = useFormBuilder();

  const [cardHeight, setCardHeight] = useState(0);
  const { classes } = useStyles();
  const [currentTab, setCurrentTab] = useState(TabValue.INPUT);
  const tabs = getTabsBySideBarType(sideBarType);

  useEffect(() => {
    switch (sideBarType) {
      case SideBarType.SCHEDULE:
        setCurrentTab(TabValue.BEFORE_PUBLIC);
        break;

      case SideBarType.MAIL:
        setCurrentTab(TabValue.THANKYOU);
        break;

      default:
        setCurrentTab(currentTab ?? TabValue.INPUT);
    }
  }, [sideBarType]);

  const handleChange = (event: React.SyntheticEvent, newValue: TabValue) => {
    if (isFormChanged || error) {
      setOpenModal?.({
        open: true,
        title: 'このページから移動しますか？',
        subtitle: '行った変更が保存されない可能性があります',
        confirmText: 'このページから移動する',
        cancelText: 'このページに留まる',
        onConfirm: () => {
          resetSelectedTemplate();

          if (newValue == TabValue.THANKYOU) {
            setSideBarType(SideBarType.MAIL);
          }

          setCurrentTab(newValue);
        },
        type: 'warning',
      });

      return;
    }

    if (newValue == TabValue.THANKYOU) {
      setSideBarType(SideBarType.MAIL);
    }

    if (newValue == TabValue.INPUT || newValue == TabValue.CONFIRM) {
      setSideBarType(SideBarType.COLOR);
    }

    setCurrentTab(newValue);
  };

  const formLayoutComponents = useMemo(() => {
    return selectedTemplate.formElements;
  }, [selectedTemplate.formElements]);

  const formTemplateModePreset = useMemo(() => {
    return FormTemplateModePresets[selectedTemplate?.formColorSetting?.templateModeColor ?? FormTemplateModePresentType.BASIC];
  }, [selectedTemplate?.formColorSetting?.templateModeColor]);

  const isBlackTemplate = useMemo(() => {
    return selectedTemplate?.formColorSetting?.templateModeColor === FormTemplateModePresentType.BLACK;
  }, [selectedTemplate?.formColorSetting?.templateModeColor]);

  const currentFormColorSetting =
    selectedTemplate?.formColorSetting?.optionMode === 'template_mode'
      ? ({
          ...FORM_COLOR_SETTING_INIT,
          optionMode: 'template_mode',
          layoutMode: selectedTemplate?.formColorSetting?.layoutMode,
          templateModeColor: selectedTemplate?.formColorSetting?.templateModeColor,
          bgColor: formTemplateModePreset?.bgColor,
          choiceSettings: {
            color: selectedTemplate?.formColorSetting?.choiceSettings?.color ?? '#333',
          },
          titleSettings: {
            ...FORM_COLOR_SETTING_INIT?.titleSettings,
            color: formTemplateModePreset?.buttonColor,
          },
          buttonSettings: {
            ...FORM_COLOR_SETTING_INIT?.buttonSettings,
            bgColor: formTemplateModePreset?.buttonColor,
            borderColor: formTemplateModePreset?.borderColor,
          },
          labelSettings: {
            ...FORM_COLOR_SETTING_INIT?.labelSettings,
            color: isBlackTemplate ? '#FFF' : formTemplateModePreset?.color,
          },
          descriptionSettings: {
            ...FORM_COLOR_SETTING_INIT?.descriptionSettings,
            color: isBlackTemplate ? '#FFF' : formTemplateModePreset?.color,
          },
          generalSettings: {
            ...FORM_COLOR_SETTING_INIT?.generalSettings,
            color: isBlackTemplate ? '#FFF' : formTemplateModePreset?.color,
          },
          entryFormSettings: {
            ...FORM_COLOR_SETTING_INIT?.entryFormSettings,
            color: isBlackTemplate ? '#555' : formTemplateModePreset?.color,
          },
        } as FormColorSetting)
      : selectedTemplate?.formColorSetting;

  const renderTab = tabs?.map?.((tab) => <Tab key={`tab-${tab.value}`} label={tab.label} value={tab.value} />);

  const renderTabPanel = tabs.map((tab) => {
    switch (tab.value) {
      case TabValue.INPUT:
        return (
          <TabPanel key={`tab-panel-${tab.value}`} value={TabValue.INPUT} sx={{ p: 0 }}>
            <FormStandalone
              screenType={screenType}
              formElements={formLayoutComponents}
              generalSetting={formGeneralSetting}
              colorSetting={currentFormColorSetting}
              isPreview={true}
              onSubmit={() => {}}
            />
          </TabPanel>
        );

      case TabValue.CONFIRM:
        return (
          <TabPanel key={`tab-panel-${tab.value}`} value={TabValue.CONFIRM} sx={{ p: 0 }}>
            <FormConfirm formLayoutComponents={formLayoutComponents} formColorSetting={currentFormColorSetting} />
          </TabPanel>
        );

      case TabValue.THANKYOU:
        return (
          <TabPanel key={`tab-panel-${tab.value}`} value={TabValue.THANKYOU} sx={{ p: 0 }}>
            <FormDisplayMessage formElements={formLayoutComponents} colorSetting={currentFormColorSetting} message={formMailSetting.message} />
          </TabPanel>
        );

      case TabValue.BEFORE_PUBLIC:
        return (
          <TabPanel key={`tab-panel-${tab.value}`} value={TabValue.BEFORE_PUBLIC} sx={{ p: 0 }}>
            <FormDisplayMessage
              formElements={formLayoutComponents}
              colorSetting={currentFormColorSetting}
              message={formScheduleSetting.displayTextBeforePublicForm}
            />
          </TabPanel>
        );

      case TabValue.AFTER_PUBLIC:
        return (
          <TabPanel key={`tab-panel-${tab.value}`} value={TabValue.AFTER_PUBLIC} sx={{ p: 0 }}>
            <FormDisplayMessage
              formElements={formLayoutComponents}
              colorSetting={currentFormColorSetting}
              message={formScheduleSetting.displayTextAfterPublicForm}
            />
          </TabPanel>
        );

      case TabValue.HIDDEN_FORM:
        const formattedMessage = formScheduleSetting?.displayTextHiddenForm?.replace(/\n/g, '<br>') ?? '';

        if (formScheduleSetting?.hideHiddenText) {
          return null;
        }

        return (
          <TabPanel key={`tab-panel-${tab.value}`} value={TabValue.HIDDEN_FORM} sx={{ p: 0 }}>
            <Box textAlign="center" p={2}>
              <Typography
                variant="body2"
                dangerouslySetInnerHTML={{
                  __html: formattedMessage ?? '',
                }}
              ></Typography>
            </Box>
          </TabPanel>
        );
    }
  });

  if (!showPreview) {
    return null;
  }

  return (
    <>
      <TabContext value={tabs?.map?.((item) => item.value).includes(currentTab) ? currentTab : tabs?.[0]?.value}>
        <Box sx={{ mb: 2 }}>
          <TabList onChange={handleChange} className={classes.tab}>
            {renderTab}
          </TabList>
        </Box>
        <SCSimpleCardFitHeight
          sx={{
            backgroundColor: currentFormColorSetting?.bgColor ?? 'aliceblue',
            fontSize: `${currentFormColorSetting?.generalSettings?.fontSize}${currentFormColorSetting?.generalSettings?.fontSizeUnit}`,
            fontFamily: displayFontFamily(currentFormColorSetting?.generalSettings?.fontFamily),
            color: currentFormColorSetting?.generalSettings?.color,
            paddingTop: '40px',
            paddingBottom: '40px',
          }}
          offsetBottom={22}
          onContentHeightChange={(height) => setCardHeight(height)}
        >
          <Scrollbars style={{ height: cardHeight - 50 }}>{renderTabPanel}</Scrollbars>
        </SCSimpleCardFitHeight>
      </TabContext>
    </>
  );
};

export default FormPreview;
