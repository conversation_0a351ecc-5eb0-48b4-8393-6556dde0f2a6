import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { FormControlNames, FormType, SwitchContact } from '@/modules/form-builder/common/common';
import { FormBuilderEntity } from '@/modules/form-builder/entities/form-builder.entity';
import { FormBuilderService } from '@/modules/form-builder/form-builder.service';
import { MailService } from '@/modules/mail/mail.service';
import { FormSubmissionEntity } from '@/modules/submission/entities/form-submission.entity';
import { StringUtil } from '@/utils/string.util';

import { SubmissionService } from './submission.service';

@Injectable()
export class SubmissionSwitchContactHandler {
  private formBuilder: FormBuilderEntity | null = null;
  private formSubmission: FormSubmissionEntity | null = null;

  constructor(
    @Inject(forwardRef(() => FormBuilderService))
    protected formBuilderService: FormBuilderService,
    protected mailService: MailService,
    protected configService: ConfigService,
    protected submissionService: SubmissionService,
  ) {}

  public setFormBuilder(formBuilder: FormBuilderEntity) {
    this.formBuilder = formBuilder;

    return this;
  }

  public setFormSubmission(formSubmission: FormSubmissionEntity) {
    this.formSubmission = formSubmission;

    return this;
  }

  public async handle() {
    if (!this.formBuilder || !this.formSubmission) {
      return;
    }

    const matchedSwitchContacts = this.getMatchedSwitchContacts();
    if (matchedSwitchContacts.length === 0) {
      return;
    }

    this.setupMailTransporter();

    const mailRecipient =
      this?.formBuilder?.mode !== FormType.HTML
        ? this.formSubmission.formValues.filter((item) => item.controlName === FormControlNames.EMAIL)?.map((item) => item.value)
        : [];
    const mailFromAddress = this?.formBuilder?.formMailSetting.useCustomSMTP
      ? this?.formBuilder?.formMailSetting.smtpFromEmail
      : this.configService.get('SYSTEM_SENDER');
    const submissionHtml = this.submissionService.buildMailMessageHtml(
      this.formSubmission.formValues,
      true,
      this.formBuilder?.formElements?.[0]?.children,
      true,
    );

    for (const contact of matchedSwitchContacts) {
      await this.sendMail(contact);
      await this.sendAutoReplyMail(contact, mailRecipient, mailFromAddress, submissionHtml);
    }
  }

  private getMatchedSwitchContacts(): SwitchContact[] {
    const elementChildren = this.formBuilder?.formElements?.[0]?.children || [];
    const submissionValues = this.formSubmission?.formValues || [];

    const switchContactElements = elementChildren.filter((element) => {
      return element?.switchContacts?.length > 0;
    });

    return switchContactElements.reduce((enabledContacts, element) => {
      const submissionValue = submissionValues.find((s) => s.id === element.id)?.value;

      if (submissionValue) {
        const enabledContactsForElement = element.switchContacts.filter(
          (contact) => contact.enabled && (Array.isArray(submissionValue) ? submissionValue.includes(contact.id) : submissionValue === contact.id),
        );

        enabledContacts.push(...enabledContactsForElement.map((contact) => contact.contact));
      }

      return enabledContacts;
    }, []);
  }

  private setupMailTransporter() {
    const formMailSetting = this.formBuilder?.formMailSetting || null;
    if (!formMailSetting) {
      return;
    }

    if (formMailSetting.useCustomSMTP) {
      this.mailService.addTransporter('custom', {
        host: formMailSetting.smtpHost || '',
        port: formMailSetting.smtpPort || 25,
        secure: true,
        auth: {
          user: formMailSetting.smtpUsername || '',
          pass: formMailSetting.smtpPassword || '',
        },
        from: {
          name: formMailSetting.autoReplySenderName || '',
          address: formMailSetting.smtpFromEmail || '',
        },
      });
    }
  }

  private async sendMail(switchContact: SwitchContact) {
    const formMailSetting = this.formBuilder?.formMailSetting || null;
    if (!formMailSetting) {
      return;
    }

    await this.mailService.sendMail({
      transporterName: formMailSetting.useCustomSMTP ? 'custom' : 'default',
      to: switchContact.email,
      sender: switchContact.sender,
      subject: switchContact.title,
      html: StringUtil.convertNewlinesToHtmlBreaks(switchContact.content),
    });
  }

  private async sendAutoReplyMail(switchContact: SwitchContact, mailRecipient: string[], mailFromAddress: string, submissionHtml: string) {
    const formMailSetting = this.formBuilder?.formMailSetting || null;

    if (!switchContact?.isAutoReply) {
      return;
    }

    await this.mailService.sendMail({
      transporterName: formMailSetting.useCustomSMTP ? 'custom' : 'default',
      to: mailRecipient,
      replyTo: switchContact.autoReplyEmailAddress,
      inReplyTo: switchContact.autoReplyEmailAddress,
      sender: {
        name: switchContact.autoReplySenderName,
        address: mailFromAddress,
      },
      from: {
        name: switchContact.autoReplySenderName,
        address: mailFromAddress,
      },
      subject: switchContact?.autoReplySubject || `${this.formBuilder.name}自動返信`,
      text: switchContact.autoReplyBody,
      html: switchContact.autoReplyBody.replace(/(?:\r\n|\r|\n)/g, '<br>') + submissionHtml,
    });
  }
}
