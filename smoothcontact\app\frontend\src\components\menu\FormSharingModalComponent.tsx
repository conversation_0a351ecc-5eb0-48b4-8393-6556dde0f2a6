import { SideBarType, TemplateType } from '@/types/FormTemplateTypes';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CodeIcon from '@mui/icons-material/Code';
import LinkIcon from '@mui/icons-material/Link';
import Timeline from '@mui/lab/Timeline';
import TimelineConnector from '@mui/lab/TimelineConnector';
import TimelineContent from '@mui/lab/TimelineContent';
import TimelineDot from '@mui/lab/TimelineDot';
import TimelineItem, { timelineItemClasses } from '@mui/lab/TimelineItem';
import TimelineSeparator from '@mui/lab/TimelineSeparator';
import { Box, Button, Stack, TextField, Typography } from '@mui/material';
import React, { useRef } from 'react';
import AceEditor from 'react-ace';
import 'ace-builds/src-noconflict/mode-html';
import { Link, useNavigate } from 'react-router-dom';
import SCModal from '../common/SCModal';

interface FormSharingModalProps {
  open: boolean;
  onClose: () => void;
  item: TemplateType;
}

const FormSharingModal: React.FC<FormSharingModalProps> = ({ open, onClose, item }) => {
  const shareLinkTextFieldRef = useRef<HTMLInputElement>(null);
  const textFieldRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const handleCopyToClipboard = (textFieldRef: React.MutableRefObject<HTMLInputElement>) => {
    const textToCopy = textFieldRef.current.value;
    navigator.clipboard.writeText(textToCopy);
  };

  const editorFormContentShare = () => {
    return (
      <>
        <Box>
          <Typography variant="h6" sx={{ mt: 1, mb: 1, fontSize: '14px', fontWeight: '700' }}>
            フォームのリンクをシェア
          </Typography>
          <Typography variant="body2">リンクを知っている全員がアクセスできます</Typography>
          <TextField
            fullWidth
            disabled
            variant="outlined"
            sx={{
              mt: 1,
              mb: 1,
              '& .MuiInputBase-input': {
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
            }}
            inputRef={shareLinkTextFieldRef}
            inputProps={{ style: { fontSize: 13 } }}
            value={`${import.meta.env.VITE_APP_URL}/front/output/${item?.extId}`}
          />
          <Button
            variant="outlined"
            color="secondary"
            size="small"
            onClick={() => handleCopyToClipboard(shareLinkTextFieldRef)}
            startIcon={<LinkIcon />}
            sx={{ fontSize: '13px', fontWeight: '400' }}
          >
            URLをコピー
          </Button>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              onClick={() => navigate(`/form-builder/edit/${item?.extId}?type=${SideBarType.GENERAL}`)}
              endIcon={<ArrowForwardIcon sx={{ fontSize: '14px', pl: '0px', color: '#24CBD4' }} />}
              sx={{ fontSize: '12px', fontWeight: '400' }}
            >
              独自ドメインを設定するには
            </Button>
          </Box>
        </Box>
        <Box sx={{ mt: 2, mb: 3 }}>
          <Typography variant="h6" sx={{ mt: 1, mb: 1, fontSize: '14px', fontWeight: '700' }}>
            サイトに埋め込む
          </Typography>
          <Typography variant="body2" sx={{ pr: '10px' }}>
            以下のコードをコピーし、あなたのウェブサイトの表示させたい箇所へ貼り付けてください
          </Typography>
          <TextField
            fullWidth
            variant="outlined"
            sx={{ mt: 1, mb: 1 }}
            multiline
            rows={4}
            inputProps={{ style: { fontSize: 13 } }}
            inputRef={textFieldRef}
            value={`<script src="${import.meta.env.VITE_APP_URL}/embed.js"></script>\n<div class="sc-embed" data-sc-form="${item?.extId}" data-sc-redirect="true"></div>`}
          />
          <Button
            variant="outlined"
            color="secondary"
            size="small"
            onClick={() => handleCopyToClipboard(textFieldRef)}
            startIcon={<CodeIcon />}
            sx={{ fontSize: '13px', fontWeight: '400' }}
          >
            コードをコピー
          </Button>
        </Box>
      </>
    );
  };

  const htmlFormContentShare = () => {
    return (
      <>
        <Timeline
          sx={{
            [`& .${timelineItemClasses.root}:before`]: {
              flex: 0,
              padding: 0,
            },
          }}
        >
          <TimelineItem>
            <TimelineSeparator>
              <TimelineDot color="info">
                <CodeIcon />
              </TimelineDot>
              <TimelineConnector />
            </TimelineSeparator>
            <TimelineContent>
              <Stack direction="column" gap={1} mt={1}>
                <Typography variant="h6">フォームのHTMLタグを作成する</Typography>
                <Typography>1. フォームタグを作成し、入力フィールドを追加してください。各入力には一意のname属性が必要です。</Typography>
                <Typography>2. 送信ボタンのタグを追加してください。</Typography>
                <AceEditor
                  style={{ width: '100%' }}
                  readOnly={true}
                  mode="html"
                  width="100%"
                  height="280px"
                  onLoad={() => {}}
                  onChange={() => {}}
                  fontSize={14}
                  showGutter={true}
                  value={`<!-- クラス、属性を変更しない -->
<form class="sc-html-embed" data-sc-form="${item?.extId}">
  <!-- ↓ フィールドを追加/変更することができます。 -->
  <div data-sc-show-thank-you-message></div>
  <div>
    <label>ユーザー名*</label>
    <input name="ユーザー名" type="text" data-sc-type="input" data-sc-required />
    <div style="color: red" data-sc-show-if-error="ユーザー名">ユーザー名 is required</div>
  </div>
  <div>
    <label>メール*</label>
    <input name="メール" value="" type="text" data-sc-type="email" data-sc-required />
    <div style="color: red" data-sc-show-if-error="メール">メール is required</div>
  </div>
  <div>
    <label>Eメール確認*</label>
    <input name="Eメール確認" value="" data-sc-type="email_confirmation" data-sc-confirmed-name="メール" />
    <div style="color: red" data-sc-show-if-error="Eメール確認">Eメール確認 not match</div>
  </div>
  <button type="submit" data-sc-error-text="Some required fields are missing." data-sc-submitting-text="Submitting…">
    Submit
  </button>
</form>`}
                  setOptions={{
                    showLineNumbers: false,
                    highlightActiveLine: false,
                    tabSize: 2,
                  }}
                />
                <Typography>
                  💡
                  <Link target="_blank" to="/form-guide">
                    Code Guide
                  </Link>
                  で多くのバリデーションユーティリティが利用できます。
                </Typography>
              </Stack>
            </TimelineContent>
          </TimelineItem>
          <TimelineItem>
            <TimelineSeparator>
              <TimelineDot color="success">
                <LinkIcon />
              </TimelineDot>
            </TimelineSeparator>
            <TimelineContent>
              <Stack direction="column" gap={1} mt={1}>
                <Typography variant={'h6'} component="span">
                  scriptタグを設定
                </Typography>
                <Typography>以下のscriptタグをコピーして、headタグ内に貼り付けてください。</Typography>
                <TextField
                  disabled={true}
                  value={`<script src="${import.meta.env.VITE_APP_URL}/html-embed.js" charset="UTF-8"></script>`}
                  fullWidth
                />
              </Stack>
            </TimelineContent>
          </TimelineItem>
        </Timeline>
      </>
    );
  };

  return (
    <SCModal width={520} isOpen={open} onClose={onClose} title="リンクをシェア/サイトに埋め込む" secondaryActionColor="primary">
      {item?.mode === 'dev' ? htmlFormContentShare() : editorFormContentShare()}
    </SCModal>
  );
};

export default FormSharingModal;
