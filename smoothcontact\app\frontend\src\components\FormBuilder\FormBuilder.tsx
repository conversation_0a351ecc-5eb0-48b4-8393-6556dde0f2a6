import SCBoxFitHeight from '@/components/common/SCBoxFitHeight';
import useFadeAnimate from '@/hooks/useFadeAnimate';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { SideBarType } from '@/types/FormTemplateTypes';
import { useTheme } from '@mui/material';
import { Box, Stack } from '@mui/system';
import React from 'react';
import { Scrollbars } from 'react-custom-scrollbars';
import { makeStyles } from 'tss-react/mui';
import SCBlockWithOption from '../common/SCBlockWithOption';
import SCModal from '../common/SCModal';
import FormSharingModal from '../menu/FormSharingModalComponent';
import CustomBreadCrumb from './CustomBreadcrumb';
import FormActionButton from './FormAction';
import LeftSidebar from './LeftSidebar';
import EditEmbedAppSettingComponent from './subcomponents/EditEmbedAppSettingComponent';
import EditGeneralSettingComponent from './subcomponents/EditGeneralSettingComponent';
import EditMailSettingComponent from './subcomponents/EditMailSettingComponent';
import EditPropertiesComponent from './subcomponents/EditPropertiesComponent';
import EditScheduleSettingComponent from './subcomponents/EditScheduleSettingComponent';
import EditSwitchContactComponent from './subcomponents/EditSwitchContactComponent';
import EditColorSettingComponent from './subcomponents/FormColorSetting/EditColorSettingComponent';
import FormDrag from './subcomponents/FormDrag';
import FormHtml from './subcomponents/FormHtml';
import FormPreview from './subcomponents/FormPreview';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const useStyles = makeStyles()((_theme) => ({
  blockSticky: {
    marginLeft: 0,
    width: '280px',
  },
}));

const FormBuilder: React.FC = () => {
  const {
    setSelectedParentControl,
    setSharingModal,
    getSideBarTitle,
    selectedTemplate,
    selectedParentControl,
    sideBarType: sideBarTypeBase,
    sharingModal,
    selectedSwitchContact,
    isHtmlForm,
  } = useFormBuilder();

  const { classes } = useStyles();

  const ANIMATION_DURATION = 300;
  const { classes: classAnimation, valueAnimated: sideBarType } = useFadeAnimate<SideBarType>(ANIMATION_DURATION, sideBarTypeBase, sideBarTypeBase);
  const [blockHeight, setBlockHeight] = React.useState(0);

  const showPreview = sideBarTypeBase !== SideBarType.ELEMENT;
  const theme = useTheme();

  const handleContentHeightChange = (height: number) => {
    setBlockHeight(height);
  };

  if (!selectedTemplate) {
    return;
  }

  return (
    <>
      <Stack direction="row" justifyContent="space-between" alignItems="start" spacing={0} gap={2}>
        <Box flexGrow={1}>
          <Stack
            sx={{ position: 'sticky', top: 0, boxShadow: `2px 2px 4px 0px #9090900F`, borderRadius: '0 0 10px 10px' }}
            zIndex={999}
            bgcolor={theme.palette.background.default}
            p={1}
            mb={1}
            mt={-1}
            direction={{ md: 'column', lg: 'row' }}
            justifyContent="space-between"
            alignItems={{ md: 'flex-start', lg: 'center' }}
            gap={2}
          >
            <CustomBreadCrumb />
            <FormActionButton />
          </Stack>
          {!isHtmlForm && sideBarTypeBase === SideBarType.ELEMENT && <FormDrag />}
          {!isHtmlForm && <FormPreview screenType="pc" showPreview={showPreview} />}
          {isHtmlForm && <FormHtml extId={selectedTemplate?.extId} />}
        </Box>
        <SCBoxFitHeight offsetBottom={50} className={classes.blockSticky} onContentHeightChange={handleContentHeightChange}>
          <SCBlockWithOption title={getSideBarTitle()}>
            <Scrollbars className={classAnimation} style={{ height: blockHeight }} autoHide>
              <Box sx={{ p: 2 }}>
                {!isHtmlForm && sideBarType === SideBarType.ELEMENT && !selectedSwitchContact && <EditPropertiesComponent />}
                {!isHtmlForm && sideBarType === SideBarType.ELEMENT && !!selectedSwitchContact && <EditSwitchContactComponent />}
                {sideBarType === SideBarType.GENERAL && <EditGeneralSettingComponent />}
                {!isHtmlForm && sideBarType === SideBarType.COLOR && <EditColorSettingComponent />}
                {sideBarType === SideBarType.MAIL && <EditMailSettingComponent />}
                {sideBarType === SideBarType.SCHEDULE && <EditScheduleSettingComponent />}
                {!isHtmlForm && sideBarType === SideBarType.EMBED_APP && <EditEmbedAppSettingComponent />}
              </Box>
            </Scrollbars>
          </SCBlockWithOption>
        </SCBoxFitHeight>
      </Stack>

      <SCModal width={800} isOpen={!!selectedParentControl} onClose={() => setSelectedParentControl(null)}>
        <LeftSidebar allowDrag={false} />
      </SCModal>

      <FormSharingModal open={sharingModal} onClose={() => setSharingModal(false)} item={selectedTemplate} />
    </>
  );
};

export default FormBuilder;
