import SCSimpleCardFitHeight from '@/components/common/SCSimpleCardFitHeight';
import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormElementChildrenType } from '@/types/FormTemplateTypes';
import { FormControlList, FormControlNames, FormItemTypes } from '@/utils/formBuilderUtils';
import { generateID } from '@/utils/helper';
import { Box, Button, Stack } from '@mui/material';
import React from 'react';
import { DragDropContext, Droppable } from 'react-beautiful-dnd';
import { Scrollbars } from 'react-custom-scrollbars';
import { makeStyles } from 'tss-react/mui';
import LeftSidebar from '../../LeftSidebar';
import DropContainerComponent from '../DropContainerComponent';

const useStyles = makeStyles()((theme) => ({
  leftSidebar: {
    '& .MuiStack-root': {
      paddingTop: theme.spacing(1),
      position: 'sticky',
      top: '53px',
      zIndex: 900,
    },
    [theme.breakpoints.down(1440)]: {
      width: '170px',
    },
    maxWidth: '260px',
    minWidth: '180px',
    fontSize: '11px',
  },
}));

const FormDrag = () => {
  const { classes } = useStyles();
  const [cardHeight, setCardHeight] = React.useState(0);
  const { selectedTemplate, handleItemAdded, moveControl, selectControl, selectedControl } = useFormBuilder();
  const formColorSetting = selectedTemplate?.formColorSetting;
  const handleDragEnd = (data: any) => {
    const { draggableId, source, destination } = data;
    const containerId = selectedTemplate?.formElements?.[0]?.container?.id;

    if (!destination) {
      return;
    }

    if (source.droppableId === destination.droppableId && destination.index === source.index) {
      return;
    }

    if (source && destination) {
      if (source.droppableId === 'controls_droppable') {
        const sourceClone = FormControlList;
        const selectedItem = sourceClone.find((control) => control.controlName === draggableId);

        handleItemAdded(selectedItem as FormElementChildrenType, containerId, destination.index, true);
      }

      if (source.droppableId === 'form_droppable') {
        if (source.index !== destination.index) {
          const item = selectedTemplate?.formElements?.[0]?.children?.[source.index];

          moveControl(item, source.index, destination.index, containerId);
        }
      }
    }
  };

  const onDragEnd = (data: any) => {
    handleDragEnd(data);
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Box display="flex" gap={2}>
        <Box className={classes.leftSidebar}>
          <LeftSidebar allowDrag={true} />
        </Box>
        <Box flexGrow={1}>
          <Stack direction="column" justifyContent="flex-start" alignItems="stretch" spacing={2}>
            <SCSimpleCardFitHeight offsetBottom={22} onContentHeightChange={(height) => setCardHeight(height)}>
              <Scrollbars style={{ height: cardHeight - 50 }}>
                <Droppable droppableId="form_droppable" type="controls">
                  {(provided) => (
                    <Box {...provided.droppableProps} ref={provided.innerRef}>
                      {selectedTemplate?.formElements &&
                        selectedTemplate?.formElements?.map?.((layout, ind) => {
                          return (
                            <DropContainerComponent
                              key={layout?.container?.id}
                              index={ind}
                              layout={layout?.container}
                              childrenComponents={layout?.children}
                              accept={FormItemTypes.CONTROL}
                            />
                          );
                        })}
                      {provided.placeholder}
                    </Box>
                  )}
                </Droppable>
                {selectedTemplate && (
                  <Box
                    onClick={() =>
                      selectControl({
                        id: generateID(),
                        controlName: FormControlNames.SUBMIT,
                        confirmText: formColorSetting?.buttonSettings?.confirmText ?? '確認',
                        submitText: formColorSetting?.buttonSettings?.submitText ?? '送信',
                        itemType: FormItemTypes.BUTTON,
                      })
                    }
                    sx={{
                      display: 'flex',
                      alignItems: 'stretch',
                      justifyContent: 'center',
                      mt: '30px',
                      cursor: 'pointer',
                      border: selectedControl?.controlName === FormControlNames.SUBMIT ? '2px dotted #92939A' : 'none',
                      borderRadius: '10px',
                      padding: '10px',
                      ':hover': {
                        border: '2px dotted #92939A',
                      },
                      mb: 1,
                    }}
                  >
                    <Button variant="outlined" color="secondary" fullWidth size="large">
                      {selectedTemplate?.formColorSetting?.buttonSettings?.confirmText ?? '確認'}
                    </Button>
                  </Box>
                )}
              </Scrollbars>
            </SCSimpleCardFitHeight>
          </Stack>
        </Box>
      </Box>
    </DragDropContext>
  );
};

export default FormDrag;
