import { Box, Button } from '@mui/material';
import { FormikValues } from 'formik';
import { FC } from 'react';
import SettingItem from './FormGeneralSetting/SettingItemComponent';

interface DomainLinkageProps {
  form: FormikValues;
}

const DomainLinkage: FC<DomainLinkageProps> = ({ form }) => {
  return (
    <>
      <Box>
        <SettingItem
          label="独自ドメインの連携設定"
          description="公開URLに独自ドメインを使用するかを選択します"
          isEnable={!!form?.values?.linkageDomain}
        />
        <Button variant="outlined" color="secondary" sx={{ mt: 1, ml: 3 }} size={'small'}>
          {form?.values?.linkageDomain ? '設定を変更' : '独自ドメインを設定'}
        </Button>
      </Box>
    </>
  );
};

export default DomainLinkage;
