/*
 * Copyright (c) SOFTBANK TECHNOLOGY CORP. All rights reserved.
 *
 * @project:	FONTPLUS
 * @version:	1.0
 * @see:		allowed to refer
 *					1. openjs(http://www.openjs.com/)
 *						Copyright (c) 2006-2007, <PERSON><PERSON>
 *
 * 					2. vxjs(http://code.google.com/p/vxjs/)
 */
FontPlus_97d4bf1d6ad5a1d3ee4828c3dea3b807 = (function () {
  var FontPlusTools = (function () {
    var B8 = {
      d: document,
      z: 0,
      o: 10,
      u: false,
      req: null,
      s: null,
      aa: 0,
      llt: 0,
      t: null,
      pm: null,
      cm: 300,
      X: function (M, f, d, B) {
        B8.req = new (window.XDomainRequest || window.ActiveXObject || XMLHttpRequest)('Microsoft.XMLHTTP');
        B8.req.open(d ? 'POST' : 'GET', M, 1);
        try {
          B8.req.designate_id = B;
          B8.req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
          B8.req.onreadystatechange = function () {
            B8.req.readyState > 3 && f && f(B8.req.responseText, B8.req);
          };
        } catch (e) {
          B8.req.onerror = function () {};
          B8.req.onprogress = function () {};
          B8.req.onload = function () {
            f && f(B8.req.responseText, B8.req);
          };
        }
        B8.req.send(d);
      },
      I: function (M, d, f) {
        var B = M.toLowerCase();
        for (f = d.length; f-- && d[f].toLowerCase() != B; );

        return f;
      },
      E: function (d, f, B, M) {
        if (d.attachEvent ? (M ? d.detachEvent('on' + f, d[f + B]) : 1) : M ? d.removeEventListener(f, B, 0) : d.addEventListener(f, B, 0)) {
          d['e' + f + B] = B;
          d[f + B] = function () {
            d['e' + f + B](window.event);
          };
          d.attachEvent('on' + f, d[f + B]);
        }
      },
      R: function (f, d, B) {
        if ('\v' != 'v' && !window.attachEvent) {
          if (d.n == 'O') setTimeout(f, B);
          else B8.E(B8.d, 'DOMContentLoaded', f);
        } else if (d.n == 'O') {
          if (d.v < 11.6) window.addEventListener('load', f, false);
          else setTimeout(f, B);
        } else if (B8.d.all) B8.E(window, 'load', f);
        else setTimeout(f, 1000);
      },
      TM: function (d) {
        return d.replace(/^\s+|\s+$/g, '');
      },
      ST: function (d, f) {
        return d.currentStyle ? d.currentStyle[f] : B8.d.defaultView ? B8.d.defaultView.getComputedStyle(d, null).getPropertyValue(f) : null;
      },
      BR: function () {
        var f = ['Edge', 'MSIE', 'Firefox', 'Chrome', 'Safari', 'Opera', 'Twitter for iPhone'],
          agent = navigator.userAgent,
          name,
          ver,
          mode,
          n,
          d,
          os,
          ext,
          fd = 'fontplus-unused';
        name = ver = mode = n = '';
        ext = 'w';
        for (d in f) {
          if (!f.hasOwnProperty(d)) continue;

          if (agent.indexOf(f[d]) != -1) {
            name = f[d];
            n = f[d].substr(0, 1);
            if (name == 'Opera' && agent.match(/; Opera (Mobi|Tablet)\/(.*) Version\/([0-9\.]+)/)) {
              name += ' Mobile';
              ver = RegExp.$3;
              n = 'T';
            } else if (agent.match(/(iPad|iPhone); (.*) OS ([0-9_]+) like /)) {
              name += ' iOS';
              ver = RegExp.$3.replace(/_/g, '.');
              n = 'I';
              os = 'iOS';
            } else if (agent.match(/Android ([0-9\.]+)/)) {
              name = os = 'Android';
              ver = RegExp.$1;
              n = 'A';
              if (agent.match(/Chrome\/([0-9\.]+)/)) {
                name = 'Chrome';
                ver = RegExp.$1;
              }
            } else if (agent.match(/Version\/([0-9\.]+)/)) ver = RegExp.$1;
            else if (agent.match(RegExp(name + '[/ ]([0-9.]+)'))) ver = RegExp.$1;

            break;
          }
        }
        if (!name) {
          if (agent.indexOf('Trident') != -1) {
            if (agent.match(/rv:([\d\.]+)/)) {
              name = 'MSIE';
              n = 'M';
              ver = RegExp.$1;
            }
          } else {
            name = 'Unknown:' + agent;
            ver = '99';
            n = 'U';
          }
        }

        if (!os) {
          if (agent.match(/Windows Phone/)) os = 'Windows Phone';
          else if (agent.match(/Windows/)) {
            if (agent.match(/NT 5.(1|2; (Win|WOW)64)/)) {
              os = 'WindowsXP';
            } else {
              os = 'Windows';
            }
          } else if (agent.match(/Macintosh/)) os = 'Macintosh';
          else if (agent.match(/Linux/)) os = 'Linux';
          else os = 'Unknown:' + agent;
        }

        if (n == 'M') {
          mode = B8.d.documentMode ? B8.d.documentMode : 5;
          if (parseInt(ver) < 9) fd = 'Courier New';
        } else if (n == 'O') fd = 'Courier New';
        else if (n == 'T') fd = 'Droid Sans';

        var B = ver.split('.');
        for (var d = 0; d < B.length; d++) {
          B[d] = parseInt(B[d]);
        }
        if (name == 'MSIE' || name == 'IEMobile') {
          if (B[0] <= 8) {
            ext = 'e';
          }
        } else if (name == 'Firefox') {
          if (B[0] == 3 && B[1] == 5) {
            ext = 'o';
          }
        } else if (name == 'Opera') {
          if (B[0] >= 10) {
            ext = 'o';
            if (B[0] >= 12 || (B[0] == 11 && B[1] >= 10)) {
              ext = 'w';
            }
          }
        } else if (name == 'Opera Mobile') {
          if (B[0] > 9 || (B[0] == 9 && B[1] >= 7)) {
            ext = 'o';
          }
        } else if (name == 'Safari') {
          if (B[0] == 3 && B[1] >= 1) {
            ext = 't';
          } else if (B[0] >= 9) {
            ext = 'o';
          } else if (B[0] > 5 || (B[0] == 5 && B[1] >= 1)) {
            ext = 'w';
          } else if (B[0] >= 4) {
            ext = 'o';
          }
        } else if (name == 'Safari iOS') {
          if (B[0] < 4 || (B[0] == 4 && B[1] < 1)) {
            name = 'Unknown:' + agent;
            ver = '99';
            n = 'U';
          } else if (B[0] < 5) {
            ext = 't';
          }
        } else if (name == 'Android') {
          ext = 'o';
        }

        return { name: name, ver: ver, mode: mode, os: os, fd: fd, n: n, v: parseFloat(ver), ext: ext };
      },
      CR: function () {
        return (B8.d.characterSet || B8.d.charset).toUpperCase();
      },
      AB: function (f) {
        B8.g = false;
        if (B8.req) B8.req.abort();

        if (f) f();
        else {
          if (window.console && window.console.log) {
            console.log('FONTPLUS Timeout abort javascript');
          } else {
            var B = B8.TN(B8.d, 'HEAD')[0] || B8.TN(B8.d, 'BODY')[0];
            var d = B8.d.createElement('script');
            d.src =
              (B8.d.location.protocol == 'https:' ? 'https://' + B8.s[0] : 'http://' + B8.s[1]) +
              '/accessor/fpabort?lurl=' +
              encodeURIComponent(B8.d.location.href) +
              '&' +
              new Date().getTime().toString();
            d.type = 'text/javascript';
            B.appendChild(d);
          }
        }
      },
      CN: function (f) {
        var d = [f],
          i,
          c;
        c = f.childNodes;
        for (i = c.length; i--; ) {
          if (c[i].tagName == undefined) continue;

          d = d.concat(B8.CN(c[i]));
        }

        return d;
      },
      TN: function (d, B) {
        var f = [],
          i = 0,
          a = B === '*',
          n = d.firstChild,
          o;
        while ((d = n)) {
          if (a ? d.nodeType === 1 : (B8.l ? d.nodeName : d.nodeName.toUpperCase()) === B) f[i++] = d;

          n = d.firstChild || d.nextSibling;
          while (!n && (d = d.parentNode)) n = d.nextSibling;
        }

        return f;
      },
      QS: function (B, d, f) {
        d = d ? d : B8.d;
        if (B == '*' || B.match(/^[a-zA-Z]+$/)) f = B8.TN(d, B8.l ? B.toUpperCase() : B.toLowerCase());
        else if (B.match(/^#([a-zA-Z0-9_-]+)$/)) f = [d.getElementById(RegExp.$1)];
        else if (typeof jQuery != 'undefined') {
          f = jQuery(B);
        } else if (d.querySelectorAll) f = d.querySelectorAll(B);
        else f = B8.CS(B);

        return f;
      },
      CS: function (bq) {
        var B4 = [];
        if (!document.getElementsByTagName) return B4;

        bq = bq.replace(/\s*([^\w])\s*/g, '$1');
        var B5 = bq.split(',');
        var f2 = function (B, bi) {
          if (!bi) bi = '*';

          var X = [];
          for (var d = 0, len = B.length; (con = B[d]), d < len; d++) {
            var M;
            if (bi == '*') M = con.all ? con.all : con.getElementsByTagName('*');
            else M = con.getElementsByTagName(bi);

            for (var f = 0, leng = M.length; f < leng; f++) X.push(M[f]);
          }

          return X;
        };
        COMMA: for (var f3 = 0, len1 = B5.length; (selector = B5[f3]), f3 < len1; f3++) {
          var bt = new Array(document);
          var f5 = selector.split(' ');
          SPACE: for (var f6 = 0, len2 = f5.length; (element = f5[f6]), f6 < len2; f6++) {
            var f8 = element.indexOf('[');
            var B3 = element.indexOf(']');
            var B2 = element.indexOf('#');
            if (B2 + 1 && !(B2 > f8 && B2 < B3)) {
              var B1 = element.split('#');
              var B6 = B1[0];
              var f4 = B1[1];
              var f0 = document.getElementById(f4);
              if (!f0 || (B6 && f0.nodeName.toLowerCase() != B6)) {
                continue COMMA;
              }

              bt = new Array(f0);
              continue SPACE;
            }

            B2 = element.indexOf('.');
            if (B2 + 1 && !(B2 > f8 && B2 < B3)) {
              var B1 = element.split('.');
              var B6 = B1[0];
              var bs = B1[1];
              var f1 = f2(bt, B6);
              bt = [];
              if (bs == 'fontplus_target_tags') {
                for (var f7 = 0, len = f1.length; (fnd = f1[f7]), f7 < len; f7++) {
                  if (fnd.className) {
                    if (fnd.className.match) {
                      if (fnd.className.match(new RegExp('(^|s|\x20)' + bs + '(s|\x20|$)'))) bt.push(fnd);
                    } else if (fnd.className.baseVal.match) {
                      if (fnd.className.baseVal.match(new RegExp('(^|s|\x20)' + bs + '(s|\x20|$)'))) bt.push(fnd);
                    }
                  }
                }
              } else {
                for (var f7 = 0, len = f1.length; (fnd = f1[f7]), f7 < len; f7++) {
                  if (fnd.className) {
                    if (fnd.className.match) {
                      if (fnd.className.match(new RegExp('(^|s)' + bs + '(s|$)'))) bt.push(fnd);
                    } else if (fnd.className.baseVal.match) {
                      if (fnd.className.baseVal.match(new RegExp('(^|s)' + bs + '(s|$)'))) bt.push(fnd);
                    }
                  }
                }
              }

              continue SPACE;
            }

            if (element.indexOf('[') + 1) {
              if (element.match(/^(\w*)\[(\w+)([=~\|\^\$\*]?)=?['"]?([^\]'"]*)['"]?\]$/)) {
                var B6 = RegExp.$1;
                var br = RegExp.$2;
                var B0 = RegExp.$3;
                var B7 = RegExp.$4;
              }

              var f1 = f2(bt, B6);
              bt = [];
              for (var f7 = 0, len = f1.length; (fnd = f1[f7]), f7 < len; f7++) {
                if (B0 == '=' && fnd.getAttribute(br) != B7) continue;

                if (B0 == '~' && !fnd.getAttribute(br).match(new RegExp('(^|\\s)' + B7 + '(\\s|$)'))) continue;

                if (B0 == '|' && !fnd.getAttribute(br).match(new RegExp('^' + B7 + '-?'))) continue;

                if (B0 == '^' && fnd.getAttribute(br).indexOf(B7) != 0) continue;

                if (B0 == '$' && fnd.getAttribute(br).lastIndexOf(B7) != fnd.getAttribute(br).length - B7.length) continue;

                if (B0 == '*' && !(fnd.getAttribute(br).indexOf(B7) + 1)) continue;
                else if (!fnd.getAttribute(br)) continue;

                bt.push(fnd);
              }
              continue SPACE;
            }

            var f1 = f2(bt, element);
            bt = f1;
          }
          for (var f9 = 0, len = bt.length; f9 < len; f9++) B4.push(bt[f9]);
        }

        return B4;
      },
      DF: function () {
        if (B8.b.os == 'Windows' || B8.b.os == 'WindowsXP') return ['MS PGothic', 'Arial'];
        else if (B8.b.os == 'Macintosh') return ['Hiragino Kaku Gothic Pro', 'Helvetica'];
        else if (B8.b.os == 'Linux') return ['IPAPGothic', 'Century'];
        else if (B8.b.os == 'iOS' || B8.b.os.substr(0, 7) == 'Unknown') return ['HiraKakuProN-W3', 'Helvetica'];
        else if (B8.b.os == 'Android') return ['Droid Sans'];
        else if (B8.b.os == 'Windows Phone') return ['Meiryo UI', 'Arial'];
      },
      CM: function (X, bq, bi) {
        var f = B8.DF(),
          bi = '_cmp_elm_' + bi + '_',
          j,
          e,
          b = B8.TN(B8.d, 'BODY')[0],
          t = bq.replace(/["'&><]/g, function (d) {
            return '&#' + d.charCodeAt(0) + ';';
          });
        for (j = 2; j--; ) {
          e = B8.d.createElement('span');
          e.setAttribute('id', bi + j);
          var B = B8.d.createElement('span');
          if (B.style.setProperty) {
            B.style.setProperty('font-family', (j == 0 ? "'" + X + "'," : '') + "'fpbf_ac93d8111'", 'important');
          } else {
            B.style.cssText = 'font-family: ' + (j == 0 ? "'" + X + "'," : '') + "'fpbf_ac93d8111' !important;";
          }

          B.innerHTML = t;
          e.appendChild(B);
          for (var M = 0; M < f.length; M++) {
            B = B8.d.createElement('span');
            if (B.style.setProperty) {
              B.style.setProperty('font-family', (j == 0 ? "'" + X + "'," : '') + f[M], 'important');
            } else {
              B.style.cssText = 'font-family: ' + (j == 0 ? "'" + X + "'," : '') + f[M] + ' !important;';
            }

            B.innerHTML = t;
            e.appendChild(B);
          }
          e.style.fontSize = B8.cm + 'px';
          e.style.visibility = 'hidden';
          e.style.position = 'absolute';
          e.style.whiteSpace = 'nowrap';
          e.style.lineHeight = 'normal';
          e.style.top = '-500px';
          b.appendChild(e);
        }
      },
      CD: function (f) {
        var d,
          r,
          e0,
          e1,
          t,
          b = B8.TN(B8.d, 'BODY')[0];
        for (d = f.length; d--; ) {
          t = typeof f[d];
          if (t == 'string' || t == 'number') {
            r = '_cmp_elm_' + f[d] + '_';
            e0 = B8.d.getElementById(r + '0');
            e1 = B8.d.getElementById(r + '1');
            if (!e0 || !e1) continue;

            b.removeChild(e0);
            b.removeChild(e1);
          } else {
            b.removeChild(f[d]);
          }
        }
      },
      DC: function (d) {},
      WT: function (bi, X, bq, M) {
        M = M == null ? B8.o * 700 : M;
        X = typeof X == 'function' ? X : B8.DC;
        bq = bq ? [] : bq;
        if (M <= 0) {
          B8.g = false;
          X({ code: bi.length, msg: 'timeout', time: -1 });
          B8.CD(bi);
          setTimeout(function () {
            B8.AB();
          }, 0);

          return;
        }

        setTimeout(
          function () {
            for (var d = bi.length, z; d--; ) {
              var f = '_cmp_elm_' + bi[d] + '_',
                e0 = B8.d.getElementById(f + '0'),
                e1 = B8.d.getElementById(f + '1');
              if (M == B8.o * 700 && (B8.b.n == 'C' || B8.b.n == 'S')) {
                continue;
              }

              if (!e0 || !e1) continue;

              if (e0.offsetWidth != e1.offsetWidth || e0.offsetHeight != e1.offsetHeight || B8.u) {
                z = B8.ST(e0, B8.TC('z-index'));
                if (z != 'auto') {
                  bq[d] = z;
                }

                bi.splice(d, 1);
                B8.CD([e0, e1]);
              }
            }
            if (!bi.length) {
              B8.g = false;
              var B = +new Date() - B8.m;
              if (!bq) {
                X({ code: 0, time: B });
              } else {
                X({ code: 0, size: bq, time: B });
              }
            } else {
              B8.WT(bi, X, bq, M - 500);
            }
          },
          B8.u ? 3000 : 500
        );
      },
      TC: function (d) {
        if ((B8.b.n == 'M' && (B8.b.v <= 8 || B8.b.mode <= 8)) || B8.b.n == 'O' || B8.b.n == 'T')
          return d.replace(/-./g, function (m) {
            return m.charAt(1).toUpperCase();
          });

        return d;
      },
      b: null,
      l: document.createElement('p').tagName == 'P',
      g: false,
      m: null,
    };

    return B8;
  })();
  var FontPlusAccessor = (function () {
    var f5 = FontPlusTools,
      use_fonts = [],
      oth_fonts = [],
      nicknames = [],
      liststyletypes = [],
      liststyletypecnts = [],
      str = [],
      par = f5.TN(f5.d, 'HEAD')[0] || f5.TN(f5.d, 'BODY')[0],
      irregular = null,
      tid = null,
      ifrms = [],
      lclist = [],
      use_rogo = false,
      proxy = false,
      desig = 0,
      selhist = [],
      selidx = 0,
      completefunc = null,
      bgimg = {},
      font_ext = false,
      font_ext_hist = [],
      font_ext_reload = false,
      options = { selector: '*', complete: false, callbacks: {}, timeoutfunc: false, sync: true, size: false };
    f5.b = f5.BR();
    var f4 = {
      init: function () {
        if (!B0()) return;

        if (!M0()) return;

        B5(0);
        X9();
        f5.o = f4.timeout;
        f5.u = false;
        f5.s = f4.server;
        f5.aa = f4.aa;
        f5.llt = f4.llt;
        f5.t = f4.t;
        f5.pm = f4.pm;
        f5.cm = f4.cm;
        if (f5.llt !== 1) {
          if ((f5.b.n == 'F' && f5.b.v <= 12) || f5.b.n == 'O' || f5.b.n == 'T' || f5.b.n == 'U') {
            f5.llt = 1;
          } else if (f5.b.n == 'M' || f5.b.n == 'I' || f5.b.n == 'A') {
            var d;
            if (f5.b.n == 'M') {
              if (f5.b.v < 9 || bi2()) {
                d = 2001;
              } else {
                d = 4096;
              }
            } else {
              d = 8207;
            }

            if (f5.llt < 1 || d < f5.llt) {
              f5.llt = d;
            }
          }
        }

        f5.R(
          function () {
            B5(1);
            if (!options.sync) return;

            f5.g = true;
            if (f4.nolist == 1 && irregular == 1) {
              f8();
              setTimeout(function () {
                f4.ready();
              }, 2000);
            } else f4.ready();
          },
          f5.b,
          f4.plusf.length
        );
      },
      ready: function () {
        f5.m = +new Date();
        font_ext = f5.b.n == 'M' && f5.b.v <= 8 && parseInt(f5.b.mode) <= 8 ? true : false;
        tid = setTimeout(function () {
          f5.AB();
        }, f4.timeout * 1000);
        X4();
        X5(f5.d, 1);
        if (B2()) {
          bi1(true);
          X1();
        } else {
          bi1(false);
          irregular || proxy ? X8() : X7();
        }

        B5(2);
      },
      receiveOther: function (bs) {
        var B = f5.d.getElementById('fontplus_jsonp'),
          css;
        if (B) B.parentNode.removeChild(B);

        if (f5.z) return;

        B = 'fontplus_' + bs.tagid;
        css = f5.d.getElementById(B);
        if (css && f5.b.n == 'M') {
          css.parentNode.removeChild(css);
          css = null;
        }

        if (!css) {
          css = f5.d.createElement('style');
          css.setAttribute('type', 'text/css');
          css.setAttribute('rel', 'stylesheet');
          css.setAttribute('id', B);
          par.appendChild(css);
        }

        if (irregular == 2 && css.styleSheet) {
          css.styleSheet.cssText = bs.data;
        } else if (irregular == 2 && css.sheet) {
          css.sheet.cssText = bs.data;
        } else {
          css.innerHTML = bs.data;
        }

        for (var X = ifrms.length; X--; ) {
          var d = ifrms[X].contentDocument || ifrms[X].contentWindow.document;
          M9(d);
          M1(d, f5.TN(d, 'HEAD')[0] || f5.TN(d, 'BODY')[0], bs.data);
        }
        if (f4.rl && f4.rl === 2) {
          X2(null);
        }

        clearTimeout(tid);
        X3(bs.families);
        if (typeof sessionStorage != 'undefined' && irregular !== 1 && f5.b.n != 'U') {
          var bi = '';
          for (var X = 0; X < use_fonts.length; X++) {
            bi += use_fonts[X] + (nicknames[X] ? nicknames[X] : '0') + str[X];
          }
          try {
            sessionStorage.setItem(bi, bs.data);
          } catch (e) {}
        } else if (f5.b.n == 'M' && f5.b.v == 7) {
          var f = f5.d.createElement('input');
          f.type = 'hidden';
          f.style.behavior = "url('#default#userData')";
          f5.d.body.appendChild(f);
          if (typeof f.load != 'undefined') {
            var br = 'fpNum',
              charSaveKey = 'fpChar',
              cssSaveKey = 'fpCss',
              idxKey = 'fp';
            var bt = '';
            for (var X = 0; X < use_fonts.length; X++) {
              bt += use_fonts[X] + (nicknames[X] ? nicknames[X] : '0') + str[X];
            }
            f.load(br);
            var M = f.getAttribute('key');
            var bq = f.getAttribute('max');
            if (M == f4.condition) {
              f.setAttribute('max', bq + 1);
              f.save(br);
              f.load(charSaveKey);
              f.setAttribute(idxKey + bq, bt);
              f.save(charSaveKey);
              f.load(cssSaveKey);
              f.setAttribute(idxKey + bq, bs.ie_data);
              f.save(cssSaveKey);
            } else {
              f.setAttribute('key', f4.condition);
              f.setAttribute('max', 1);
              f.save(br);
              f.load(charSaveKey);
              if (M != null) {
                for (var X = 0; X < bq; X++) {
                  if (f.getAttribute(idxKey + X) != null) f.removeAttribute(idxKey + X);
                }
              }

              f.setAttribute(idxKey + '0', bt);
              f.save(charSaveKey);
              f.load(cssSaveKey);
              if (M != null) {
                for (var X = 0; X < bq; X++) {
                  if (f.getAttribute(idxKey + X) != null) f.removeAttribute(idxKey + X);
                }
              }

              f.setAttribute(idxKey + '0', bs.ie_data);
              f.save(cssSaveKey);
            }
          }

          f5.d.body.removeChild(f);
        }
      },
      setting: function (d) {
        options = d;
      },
      reload: function (f) {
        if (f5.g) {
          setTimeout(f4.reload, 500);

          return;
        }

        (f5.g = true),
          (font_ext_reload = f5.b.n == 'M' && f5.b.v <= 8 && !f ? true : false),
          (use_fonts = []),
          (oth_fonts = []),
          (nicknames = []),
          (liststyletypes = []),
          (liststyletypecnts = []),
          (str = []),
          (ifrms = []),
          (lclist = []);
        if (f) {
          var d, elm;
          for (d = selhist.length; d--; ) {
            elm = f5.d.getElementById(selhist[d].id);
            if (elm) elm.parentNode.removeChild(elm);
          }
          (selhist = []), (selidx = 0);
        }

        options.sync = true;
        if (irregular === 1) {
          f8();
          setTimeout(function () {
            f4.ready();
          }, 1000);
        } else {
          f4.ready();
        }
      },
      designate_load: function (d, f, M) {
        if (f5.g) {
          setTimeout(function () {
            f4.designate_load(d, f, M);
          }, 500);

          return;
        }

        f5.g = true;
        f5.m = +new Date();
        tid = setTimeout(function () {
          f5.AB(options.timeoutfunc);
        }, f4.timeout * 1000);
        var B,
          _id = M ? M : d[0].nickname;
        (use_fonts = []), (lclist = []), (nicknames = []), (liststyletypes = []), (liststyletypecnts = []), (str = []);
        for (var B = d.length; B--; ) {
          use_fonts.push(d[B].fontname);
          nicknames.push(d[B].nickname);
          str.push(d[B].text);
        }
        options.callbacks[_id] = { func: f };
        if (irregular === null) if (!B0()) return;

        if (B2()) {
          bi1(true);
          X1(_id);
        } else {
          bi1(false);
          irregular || proxy ? X8(_id) : X7(_id);
        }
      },
      setFonts: function (d) {
        f4.plusf = d;
      },
      isloading: function () {
        return f5.g;
      },
    };
    var B2 = function () {
      if (f5.llt < 1) {
        return true;
      } else if (f5.llt < 2) {
        return false;
      } else {
        var B = use_fonts.length,
          paramurl = bi6('reqf');
        paramurl += '/con=' + encodeURIComponent(f4.condition);
        paramurl += '&cha=' + f5.CR();
        paramurl += '&dmode=' + f5.b.mode;
        if (f5.aa) paramurl += '&aa=1';

        var X = new RegExp("　| |'|\r|\n|\t", 'g');
        for (var f = 0; f < B; f++) {
          if (!str[f].length) continue;

          var d = str[f].replace(X, '');
          var M = null,
            orignalFont = null,
            modeString = null;
          try {
            d = encodeURIComponent(
              bi5(d.match(/[\uD800-\uDBFF][\uDC00-\uDFFF]|[^\uD800-\uDFFF]/g) || [])
                .sort()
                .join('')
            );
          } catch (e) {
            d = encodeURIComponent(d);
          }
          d = bi3(unescape(d));
          if (f5.t != null) {
            M = '&t=' + f5.t;
          } else if (f5.pm) {
            M = '&pm=1';
          }

          var bi = B8(use_fonts[f]);
          if (bi) {
            orignalFont = bi['org'];
            M = '&' + bi['mode'];
            if (bi['value']) {
              M = M + '=' + bi['value'];
            } else {
              M = M + '=1';
            }
          } else {
            orignalFont = use_fonts[f];
          }

          var bq =
            paramurl +
            (M ? M : '') +
            '&fa=' +
            encodeURIComponent(orignalFont) +
            '&lst=' +
            (liststyletypes[f] ? encodeURIComponent(liststyletypes[f]) : '0') +
            '&lsc=' +
            (liststyletypecnts[f] ? liststyletypecnts[f] : '0') +
            '&s=' +
            d;
          if (f5.llt <= bq.length) {
            return false;
          }
        }
      }

      return true;
    };
    var X1 = function (bi) {
      if (!str.length) {
        M6();

        return;
      }

      desig = bi ? bi : 0;
      var bq = use_fonts.length,
        logParam = [],
        paramurl = bi6('reqf');
      paramurl += '/con=' + encodeURIComponent(f4.condition);
      paramurl += '&cha=' + f5.CR();
      paramurl += '&dmode=' + f5.b.mode;
      if (f5.aa) paramurl += '&aa=1';

      var f0 = '';
      var B = '';
      var M = '';
      if (f4.exttype) {
        switch (f4.exttype) {
          case 'woff':

          case 'woff_by_ttf':
            M = "') format('woff";
            break;

          case 'otf':
            M = "') format('opentype";
            break;

          case 'ttf':
            M = "') format('truetype";
            break;

          case 'svg':
            M = "#FontPlus') format('svg";
            break;
        }
      }

      for (var X = 0; X < bq; X++) {
        if (!str[X].length) continue;

        var f = bi3(unescape(encodeURIComponent(str[X]).replace('*', '%2A').replace('!', '%21').replace('~', '%7E')));
        var br = null,
          orignalFont = null,
          modeString = null;
        if (f5.t != null) {
          br = '&t=' + f5.t;
        } else if (f5.pm) {
          br = '&pm=1';
        }

        var bs = B8(use_fonts[X]);
        if (bs) {
          orignalFont = bs['org'];
          br = '&' + bs['mode'];
          if (bs['value']) {
            br = br + '=' + bs['value'];
          } else {
            br = br + '=1';
          }
        } else {
          orignalFont = use_fonts[X];
        }

        var bt =
          paramurl +
          (br ? br : '') +
          '&fa=' +
          bi3(unescape(encodeURIComponent(orignalFont))) +
          '&lst=' +
          (liststyletypes[X] ? encodeURIComponent(liststyletypes[X]) : '0') +
          '&lsc=' +
          (liststyletypecnts[X] ? liststyletypecnts[X] : '0') +
          '&s=' +
          f;
        f0 += "@font-face { font-family: '" + (nicknames[X] ? nicknames[X] : use_fonts[X]) + "'; font-weight: Bold; src: url('" + bt + M + "'); }\n";
        f5.CM(bi || font_ext ? nicknames[X] : use_fonts[X], str[X].substr(0, 2), bi ? bi : X);
        lclist.push(bi ? bi : X);
        if (f4.rl && f4.rl === 1) {
          logParam['fonts[' + X + '][fa]'] = bi3(unescape(encodeURIComponent(use_fonts[X])));
          logParam['fonts[' + X + '][nn]'] = nicknames[X] ? encodeURIComponent(nicknames[X]) : '0';
          logParam['fonts[' + X + '][lst]'] = liststyletypes[X] ? encodeURIComponent(liststyletypes[X]) : '0';
          logParam['fonts[' + X + '][lsc]'] = liststyletypecnts[X] ? liststyletypecnts[X] : '0';
          logParam['fonts[' + X + '][s]'] = f;
        }

        if (B != '') {
          B += ',';
        }

        B += use_fonts[X];
      }
      if (font_ext_reload) {
        f6();
      }

      var d = desig ? desig : selhist[selidx].id,
        css = f5.d.getElementById(d);
      if (css && f5.b.n == 'M') {
        css.parentNode.removeChild(css);
        css = null;
      }

      if (!css) {
        css = f5.d.createElement('style');
        css.setAttribute('type', 'text/css');
        css.setAttribute('rel', 'stylesheet');
        css.setAttribute('id', d);
        par.appendChild(css);
      }

      if (f5.b.n == 'M' && (f5.b.v < 9 || f5.b.mode < 9) && css.styleSheet) css.styleSheet.cssText = f0;
      else css.innerHTML = f0;

      X2(logParam);
      if (lclist.length) {
        f5.WT(lclist, desig ? options.callbacks[desig]['func'] : completefunc, options.size, B9());
      }

      clearTimeout(tid);
      X3(B);
      M4();
    };
    var X0 = function (B) {
      var f0 = use_fonts.length,
        keyStr = '',
        familiesStr = '';
      var f = null;
      if (typeof sessionStorage != 'undefined' && f5.b.n != 'U') {
        var br = sessionStorage.getItem(f4.condition);
        if (br) {
          for (var bq = 0; bq < f0; bq++) {
            keyStr += use_fonts[bq] + (nicknames[bq] ? nicknames[bq] : '0') + str[bq];
            if (familiesStr != '') {
              familiesStr += ',';
            }

            familiesStr += use_fonts[bq];
          }
          f = sessionStorage.getItem(keyStr);
        } else {
          try {
            sessionStorage.clear();
            sessionStorage.setItem(f4.condition, '1');
          } catch (e) {}
        }
      } else if (f5.b.n == 'M' && f5.b.v == 7) {
        var M = f5.d.createElement('input');
        M.type = 'hidden';
        M.style.behavior = "url('#default#userData')";
        f5.d.body.appendChild(M);
        if (typeof M.load != 'undefined') {
          var f2 = 'fpNum',
            charSaveKey = 'fpChar',
            cssSaveKey = 'fpCss',
            idxKey = 'fp';
          M.load(f2);
          var bi = M.getAttribute('key');
          var f1 = M.getAttribute('max');
          if (bi == f4.condition && f1 != null) {
            for (var bq = 0; bq < f0; bq++) {
              keyStr += use_fonts[bq] + (nicknames[bq] ? nicknames[bq] : '0') + str[bq];
              if (familiesStr != '') {
                familiesStr += ',';
              }

              familiesStr += use_fonts[bq];
            }
            M.load(charSaveKey);
            for (var bq = 0; bq < f1; bq++) {
              var f3 = M.getAttribute(idxKey + bq);
              if (f3 != null && f3 == keyStr) {
                M.load(cssSaveKey);
                f = M.getAttribute(idxKey + bq);
                break;
              }
            }
          }
        }

        f5.d.body.removeChild(M);
      }

      if (f) {
        for (var bq = 0; bq < f0; bq++) {
          f5.CM(B || font_ext ? nicknames[bq] : use_fonts[bq], str[bq].substr(0, 2), B ? B : bq);
          lclist.push(B ? B : bq);
        }
        if (font_ext_reload) {
          f6();
        }

        if (f.length > 4 && f.substring(0, 4) == 'http') {
          var bs = f5.d.getElementById(B ? B : 'fontplus_link');
          if (bs) {
            bs.parentNode.removeChild(bs);
          }

          bs = f5.d.createElement('link');
          bs.setAttribute('type', 'text/css');
          bs.setAttribute('rel', 'stylesheet');
          bs.setAttribute('charset', 'utf-8');
          bs.setAttribute('id', B ? B : 'fontplus_link');
          bs.setAttribute('href', f);
          bs.setAttribute('media', 'screen');
          par.appendChild(bs);
        } else {
          if (font_ext) {
            for (var bq = 0; bq < f0; bq++) {
              f = f.replace(use_fonts[bq], nicknames[bq]);
            }
          }

          var X = B ? B : selhist[selidx].id,
            css = f5.d.getElementById(X);
          if (css && f5.b.n == 'M') {
            css.parentNode.removeChild(css);
            css = null;
          }

          if (!css) {
            css = f5.d.createElement('style');
            css.setAttribute('type', 'text/css');
            css.setAttribute('rel', 'stylesheet');
            css.setAttribute('id', X);
            par.appendChild(css);
          }

          if (css.styleSheet) {
            css.styleSheet.cssText = f;
          } else {
            css.innerHTML = f;
          }

          for (var bq = ifrms.length; bq--; ) {
            var d = ifrms[bq].contentDocument || ifrms[bq].contentWindow.document;
            M9(d);
            M1(d, f5.TN(d, 'HEAD')[0] || f5.TN(d, 'BODY')[0], f);
          }
        }

        if (lclist.length) {
          f5.WT(lclist, B ? options.callbacks[B]['func'] : completefunc, options.size, B9());
        }

        clearTimeout(tid);
        X3(familiesStr);
        M4();
        var bt = [];
        if (f4.rl && f4.rl === 1) {
          for (var bq = 0; bq < f0; bq++) {
            bt['fonts[' + bq + '][fa]'] = bi3(unescape(encodeURIComponent(use_fonts[bq])));
            bt['fonts[' + bq + '][nn]'] = nicknames[bq] ? encodeURIComponent(nicknames[bq]) : '0';
            bt['fonts[' + bq + '][lst]'] = liststyletypes[bq] ? encodeURIComponent(liststyletypes[bq]) : '0';
            bt['fonts[' + bq + '][lsc]'] = liststyletypecnts[bq] ? liststyletypecnts[bq] : '0';
            bt['fonts[' + bq + '][s]'] = bi3(unescape(encodeURIComponent(str[bq])));
          }
        }

        X2(bt);

        return true;
      }

      return false;
    };
    var X2 = function (bi) {
      if (!f4.rl) return;

      if (bi) {
        var d = 0,
          frame,
          form,
          specid,
          onload;
        bi['con'] = f4.condition;
        bi['cha'] = f5.CR();
        bi['dmode'] = f5.b.mode;
        bi['tm'] = new Date().getTime().toString();
        bi['size'] = options.size ? 1 : 0;
        if (f5.aa) bi['aa'] = 1;

        if (f5.t != null) {
          bi['t'] = f5.t;
        } else if (f5.pm) {
          bi['pm'] = 1;
        }

        form = f5.d.createElement('form');
        specid = Math.floor(Math.random() * 10000) + new Date().getTime().toString();
        form.action = bi6('reql');
        form.method = 'POST';
        form.id = 'form_' + specid;
        f5.d.body.appendChild(form);
        for (var br in bi) {
          if (!bi.hasOwnProperty(br)) continue;

          var X = f5.d.createElement('input');
          X.type = 'hidden';
          X.name = br;
          X.value = bi[br];
          form.appendChild(X);
        }
        frame = f5.d.createElement('iframe');
        frame.name = form.target = 'fontplus_iframe_' + specid;
        frame.style.display = 'none';
        frame.id = 'frame_' + specid;
        if (bi2() && f5.b.n == 'M' && (f5.b.v < 7 || parseInt(f5.b.mode) < 7)) frame.src = bi6('reload');
        else frame.src = 'about:blank';

        onload = frame.onload = function () {
          if (d == 0) {
            form.submit();
          } else if (d == 1) {
            form.parentNode.removeChild(form);
            setTimeout(function () {
              frame.parentNode.removeChild(frame);
            }, 0);
          }

          d++;
        };
        if (document.all) {
          frame.onreadystatechange = function () {
            if (this.readyState == 'complete') {
              frame.contentWindow.name = frame.name;
              onload();
            }
          };
        }

        f5.d.body.appendChild(frame);
      } else {
        var bq = use_fonts.length;
        var bs = 'c=' + encodeURIComponent(f4.ukey);
        bs += '&ca=0';
        bs += '&dmode=' + (f5.b.mode ? f5.b.mode : '0');
        if (f5.aa) bs += '&aa=1';

        if (f5.t != null) {
          bs += '&t=' + f5.t;
        } else if (f5.pm) {
          bs += '&pm=1';
        }

        if (f4.exttype) {
          bs += '&type=' + (f4.exttype == 'woff_by_ttf' ? 'woff' : f4.exttype);
        }

        var f = '';
        for (var B = 0; B < use_fonts.length; B++) {
          if (!str[B].length) continue;

          f += '&fn[' + B + ']=' + encodeURIComponent(encodeURIComponent(use_fonts[B]));
          f += '&nn[' + B + ']=' + (nicknames[B] ? encodeURIComponent(encodeURIComponent(nicknames[B])) : '0');
          f += '&gl[' + B + ']=' + str[B].length;
        }
        if (f != '') {
          var M = document.createElement('img');
          M.setAttribute('src', f4.lsv + bs + f + '&tm=' + Math.floor(Math.random() * 10000) + new Date().getTime().toString());
          M.setAttribute('width', 0);
          M.setAttribute('height', 0);
          M.style.display = 'none';
          M.onerror = function () {
            M.parentNode.removeChild(M);
          };
          M.onload = function () {
            M.parentNode.removeChild(M);
          };
          f5.d.body.appendChild(M);
        }
      }
    };
    var X4 = function () {
      var d;
      selidx = -1;
      for (d = selhist.length; d--; ) {
        if (selhist[d].selector == options.selector) {
          selidx = d;
          break;
        }
      }
      if (selidx < 0) {
        selidx = selhist.length;
        selhist.push({ selector: options.selector, id: 'fontplus_css_' + selidx });
      }
    };
    var X5 = function (d, B) {
      if (!B) {
        var X = f5.TN(d, 'SCRIPT');
        for (var M = X.length; M--; ) {
          if (X[M].src.match(/\/accessor\/script\/fontplus.js\?/)) {
            B = 1;
            break;
          }
        }
        if (!B) return B;
      }

      var f = f5.QS(options.selector, d),
        M,
        e = [];
      if (options.selector != '*') {
        for (var M = f.length; M--; ) e = e.concat(f5.CN(f[M]));
      } else {
        e = f;
      }

      X6(e);
      if (options.selector == '*') B4(d);

      return B;
    };
    var X6 = function (d) {
      var f, k;
      for (k = d.length; k--; ) {
        if ((f = B7(d[k])) == null) continue;

        B6(d[k], f);
      }
    };
    var bi1 = function (d) {
      var M;
      if (d) {
        M = new RegExp("　| |'|\r|\n|\t", 'g');
      } else {
        M = new RegExp('　|\r|\n|\t', 'g');
      }

      for (var f = use_fonts.length; f--; ) {
        var B = str[f].replace(M, '');
        str[f] = bi5(B.match(/[\uD800-\uDBFF][\uDC00-\uDFFF]|[^\uD800-\uDFFF]/g) || [])
          .sort()
          .join('');
        try {
          encodeURIComponent(str[f]);
        } catch (e) {
          str[f] = B;
        }
        if (f5.b.n == 'M' && str[f].indexOf("'") >= 0) {
          str[f] = str[f].replace("'", '') + "'";
        }

        if (f5.b.n == 'M' && str[f].indexOf('.') >= 0) {
          str[f] = str[f].replace('.', '') + '.';
        }
      }
    };
    var bi5 = function (d) {
      var B = {};
      var M = [];
      var f, value;
      for (f = 0; f < d.length; f++) {
        value = d[f];
        if (!(value in B)) {
          B[value] = true;
          M.push(value);
        }
      }

      return M;
    };
    var B4 = function (d) {
      for (var f = d.styleSheets.length; f--; ) bi0(d.styleSheets[f]);
    };
    var bi0 = function (X) {
      var M, sc, st, act, p_cls;
      try {
        M = X.cssRules ? X.cssRules : X.rules;
      } catch (e) {
        M = null;
      }
      if (!M) return;

      for (var d = M.length; d--; ) {
        try {
          sc = M[d].styleSheet;
        } catch (e) {
          sc = null;
        }
        if (sc) bi0(sc);

        if (!M[d].selectorText) continue;

        var B = M[d].selectorText.match(/(:link|:visited|:hover|:active)/gi);
        if (B && B.length && M[d].style.fontFamily.length) {
          act = M[d].selectorText.replace(/:link|:visited|:hover|:active/gi, '');
          p_cls = f5.CS(act);
          for (var f = p_cls.length; f--; ) {
            if ((st = B7(p_cls[f])) == null || f5.TM(st) == f5.TM(M[d].style.fontFamily)) {
              continue;
            }

            B6(p_cls[f], M[d].style.fontFamily);
          }
        }
      }
    };
    var B7 = function (d) {
      return f5.ST(d, f5.TC('font-family'));
    };
    var B0 = function () {
      var d = f5.b.n,
        v = f5.b.v;
      irregular = 0;
      if (d == 'O' || d == 'T') irregular = 1;
      else if (d == 'M' && v < 8) irregular = 2;
      else if (d == 'S' && v < 5) irregular = 3;

      if (
        (d == 'M' && v >= 6) ||
        (d == 'F' && v >= 3.5) ||
        (d == 'C' && v >= 2) ||
        (d == 'O' && v >= 10) ||
        (d == 'T' && v >= 9.7) ||
        (d == 'S' && v >= 3.1) ||
        (d == 'A' && v >= 2.2) ||
        d == 'I' ||
        d == 'U' ||
        d == 'E'
      )
        return true;

      return false;
    };
    var B6 = function (d, br, bq) {
      var B = br.replace(/\\/g, '').replace(/'/g, '').replace(/"/g, '').split(','),
        idx,
        c,
        n,
        nst = false,
        fn,
        orignalFont;
      var f;
      for (var M = 0; M < B.length; M++) {
        B[M] = f5.TM(B[M]);
        var bi = B8(B[M]);
        if (bi) {
          orignalFont = bi['org'];
        } else {
          orignalFont = B[M];
        }

        if (f5.I(B[M], oth_fonts) >= 0) {
          continue;
        }

        if ((idx = f5.I(B[M], use_fonts)) < 0) {
          if ((c = M2(B[M])) >= 0) {
            if ((idx = f5.I(font_ext_hist[c].org, use_fonts)) < 0) {
              idx = use_fonts.length;
              use_fonts.push(font_ext_hist[c].org);
            }
          } else if ((c = f5.I(orignalFont, f4.plusf)) >= 0) {
            idx = use_fonts.length;
            use_fonts.push(B[M]);
          }
        }

        if (idx < 0) {
          oth_fonts.push(B[M]);
          continue;
        }

        f = bi4(d);
        if (!f) {
          if (str[idx] == null) {
            use_fonts.splice(idx, 1);
          }

          return;
        }

        if (str[idx] == null) str[idx] = '';

        str[idx] += f;
        if (d.nodeName.toLowerCase() == 'ol') {
          var bs = f5.ST(d, f5.TC('list-style-type')).toLowerCase();
          if (bs && bs != 'none' && bs != 'disc' && bs != 'circle' && bs != 'square') {
            var X = d.getElementsByTagName('li').length;
            if (!liststyletypes[idx]) {
              liststyletypes[idx] = bs;
              liststyletypecnts[idx] = X;
            } else {
              var f1 = liststyletypes[idx].toString().split('_');
              var bt = f5.I(bs, f1);
              if (bt < 0) {
                liststyletypes[idx] += '_' + bs;
                liststyletypecnts[idx] += '_' + X;
              } else {
                var f0 = liststyletypecnts[idx].toString().split('_');
                if (f0[bt] < X) {
                  f0[bt] = X;
                  liststyletypecnts[idx] = f0.join('_');
                }
              }
            }
          }
        }

        if (font_ext) {
          if (!nst) nst = br.replace(/'/g, '').replace(/"/g, '');

          if (nicknames[idx] == null) nicknames[idx] = 'FP-' + M7(16);

          ecp_fa = B[M].replace(/\+/g, '\\+')
            .replace(/\^/g, '\\^')
            .replace(/\$/g, '\\$')
            .replace(/\*/g, '\\*')
            .replace(/\?/g, '\\?')
            .replace(/\./g, '\\.');
          nst = nst.replace(new RegExp(ecp_fa), nicknames[idx]);
        }
      }
      if (nst) {
        if (font_ext_reload) d.setAttribute('data-fp-tmp-nst', nst);
        else d.style.fontFamily = nst;
      }
    };
    var bi4 = function (d, f) {
      var B = f5.l ? d.tagName : d.tagName.toUpperCase(),
        s = '',
        i;
      if (B == 'SCRIPT' || B == 'HEAD' || B == 'TITLE' || B == 'STYLE' || B == 'HTML' || B == 'META') {
        return s;
      }

      if (d.value && B != 'LI' && B != 'SELECT') {
        s = d.value;
      } else if (d.childNodes.length) {
        if (f5.b.n == 'M' && (f5.b.v < 9 || f5.b.mode < 9)) {
          s += B3(d, 1);
          if (B == 'TR') {
            for (i = d.cells.length; i--; ) s += d.cells[i].innerText;
          }
        } else s += M5(d);
      } else {
        return s;
      }

      B5(3, d);

      return f5.TM(s);
    };
    var M5 = function (d, f) {
      if (d.nodeValue) f = d.nodeValue;
      else if (d.textContent) f = d.textContent;
      else if (d.innerText && (f5.l ? d.tagName : d.tagName.toUpperCase()) == 'A') f = d.innerText;
      else f = '';

      return f;
    };
    var B3 = function (d, B) {
      var f,
        s = '';
      for (f = d.childNodes.length; f--; ) {
        if (options.selector != '*' && d.childNodes[f].childNodes.length) s += B3(d.childNodes[f], B + 1);

        s += M5(d.childNodes[f]);
      }

      return s;
    };
    var bi2 = function () {
      return typeof f5.d.location.protocol == 'unknown' || f5.d.location.protocol == 'https:' ? 1 : 0;
    };
    var bi6 = function (d) {
      return (bi2() ? 'https://' + f4.server[0] : 'http://' + f4.server[1]) + '/accessor' + (d ? '/' + d : '/mkfont');
    };
    var X7 = function (f) {
      if (!str.length) {
        M6();

        return;
      }

      desig = f ? f : 0;
      if (X0(desig) === true) {
        return;
      }

      var B = use_fonts.length;
      var M = 'condition=' + encodeURIComponent(f4.condition);
      M += '&charset=' + f5.CR();
      M += '&browser[name]=' + f5.b.name;
      if (f5.b.name.substr(0, 7) == 'Unknown') M += '&browser[ver]=' + f5.b.name;
      else M += '&browser[ver]=' + f5.b.ver;

      M += '&browser[mode]=' + f5.b.mode;
      M += '&browser[os]=' + f5.b.os;
      M += '&location=' + f5.d.location.href;
      M += '&ssl=' + bi2();
      M += '&frame=1';
      M += '&tagid=' + (f ? f : 'css_' + selidx);
      M += '&tm=' + new Date().getTime().toString();
      M += '&size=' + (options.size ? 1 : 0);
      if (f5.aa) M += '&aa=1';

      if (f5.t != null) {
        M += '&t=' + f5.t;
      } else if (f5.pm) {
        M += '&pm=1';
      }

      for (var d = 0; d < B; d++) {
        if (!str[d].length) continue;

        M += '&fonts[' + d + '][family]=' + bi3(unescape(encodeURIComponent(use_fonts[d])));
        M += '&fonts[' + d + '][nickname]=' + (nicknames[d] ? nicknames[d] : '0');
        M += '&fonts[' + d + '][lst]=' + (liststyletypes[d] ? liststyletypes[d] : '0');
        M += '&fonts[' + d + '][lsc]=' + (liststyletypecnts[d] ? liststyletypecnts[d] : '0');
        M += '&fonts[' + d + '][str]=' + bi3(unescape(encodeURIComponent(str[d])));
        f5.CM(f || font_ext ? nicknames[d] : use_fonts[d], str[d].substr(0, 2), f ? f : d);
        lclist.push(f ? f : d);
      }
      f5.X(bi6(), M8, M, f);
    };
    var M8 = function (d, bi) {
      if (f5.z) return;

      if (d) {
        if (font_ext_reload) {
          f6();
        }

        var f = d.split('|');
        var B = desig ? desig : selhist[selidx].id,
          css = f5.d.getElementById(B);
        if (css && f5.b.n == 'M') {
          css.parentNode.removeChild(css);
          css = null;
        }

        if (!css) {
          css = f5.d.createElement('style');
          css.setAttribute('type', 'text/css');
          css.setAttribute('rel', 'stylesheet');
          css.setAttribute('id', B);
          par.appendChild(css);
        }

        if (css.styleSheet) {
          css.styleSheet.cssText = f[0];
        } else {
          css.innerHTML = f[0];
        }

        if (lclist.length) {
          f5.WT(lclist, desig ? options.callbacks[desig]['func'] : completefunc, options.size, B9());
        }

        clearTimeout(tid);
        X3(f[1]);
        if (f4.rl && f4.rl == 2) {
          X2(null);
        }

        if (typeof sessionStorage != 'undefined' && f5.b.n != 'U') {
          var X = '';
          for (var M = 0; M < use_fonts.length; M++) {
            X += use_fonts[M] + (nicknames[M] ? nicknames[M] : '0') + str[M];
          }
          try {
            if (font_ext && f[2]) {
              sessionStorage.setItem(X, f[2]);
            } else {
              sessionStorage.setItem(X, f[0]);
            }
          } catch (e) {}
        }

        M4();
      } else if (bi.status != 200 && (f5.b.n == 'F' || f5.b.n == 'S')) {
        proxy = true;
        f5.CD(lclist);
        lclist = [];
        X8(bi.designate_id);
      }
    };
    var f7 = function (f, B) {
      var M = f5.d.getElementById(B ? B : 'fontplus_link');
      if (M) {
        M.parentNode.removeChild(M);
      }

      M = f5.d.createElement('link');
      M.setAttribute('type', 'text/css');
      M.setAttribute('rel', 'stylesheet');
      M.setAttribute('charset', 'utf-8');
      M.setAttribute('id', B ? B : 'fontplus_link');
      var d = '';
      if (f4.box) d = '&box=' + encodeURIComponent(f4.box);

      M.setAttribute('href', bi6('stylesheet') + '/' + f + '?' + encodeURIComponent(f4.condition) + d);
      M.setAttribute('media', 'screen');
      par.appendChild(M);

      return M;
    };
    var B1 = function (f) {
      var d = f5.d.createElement('script');
      d.setAttribute('id', 'fontplus_jsonp');
      d.charset = 'utf-8';
      d.src = bi6('jsonp') + '/' + f + '/receiveOther?' + encodeURIComponent(f4.condition);
      par.appendChild(d);
    };
    var M6 = function () {
      f5.g = false;
      clearTimeout(tid);
      if (completefunc) completefunc({ code: 0 });
    };
    var X8 = function (bi) {
      if (f5.z) return;

      if (!str.length) {
        M6();

        return;
      }

      var br = use_fonts.length,
        param = {},
        cnt = 0,
        frame,
        form,
        specid,
        onload;
      desig = bi ? bi : 0;
      if (irregular !== 1 && X0(desig) === true) {
        return;
      }

      param['condition'] = f4.condition;
      param['charset'] = f5.CR();
      param['browser[name]'] = f5.b.name;
      if (f5.b.name.substr(0, 7) == 'Unknown') param['browser[ver]'] = f5.b.name;
      else param['browser[ver]'] = f5.b.ver;

      param['browser[mode]'] = f5.b.mode;
      param['browser[os]'] = f5.b.os;
      param['location'] = f5.d.location.href;
      param['ssl'] = bi2();
      param['tagid'] = bi ? bi : 'css_' + selidx;
      param['frame'] = f5.b.n == 'M' && f5.b.v == 6 ? ifrms.length + 1 : f5.b.n == 'S' && f5.b.v < 4 ? 2 : 1;
      param['tm'] = new Date().getTime().toString();
      param['size'] = options.size ? 1 : 0;
      if (f5.aa) param['aa'] = 1;

      if (f5.t != null) {
        param['t'] = f5.t;
      } else if (f5.pm) {
        param['pm'] = 1;
      }

      var M = '';
      for (var X = 0; X < br; X++) {
        if (!str[X].length) continue;

        param['fonts[' + X + '][family]'] = bi3(unescape(encodeURIComponent(use_fonts[X])));
        param['fonts[' + X + '][nickname]'] = nicknames[X] ? nicknames[X] : '0';
        param['fonts[' + X + '][lst]'] = liststyletypes[X] ? liststyletypes[X] : '0';
        param['fonts[' + X + '][lsc]'] = liststyletypecnts[X] ? liststyletypecnts[X] : '0';
        param['fonts[' + X + '][str]'] = bi3(unescape(encodeURIComponent(str[X])));
        f5.CM(bi || font_ext ? nicknames[X] : use_fonts[X], str[X].substr(0, 2), bi ? bi : X);
        lclist.push(bi ? bi : X);
        if (M != '') {
          M += ',';
        }

        M += use_fonts[X];
      }
      form = f5.d.createElement('form');
      specid = Math.floor(Math.random() * 10000) + new Date().getTime().toString();
      form.action = bi6() + '/' + specid;
      form.method = 'POST';
      form.id = 'form_' + specid;
      f5.d.body.appendChild(form);
      for (var bs in param) {
        if (!param.hasOwnProperty(bs)) continue;

        var bq = f5.d.createElement('input');
        bq.type = 'hidden';
        bq.name = bs;
        bq.value = param[bs];
        form.appendChild(bq);
      }
      frame = f5.d.createElement('iframe');
      frame.name = form.target = 'fontplus_iframe_' + specid;
      frame.style.display = 'none';
      frame.id = 'frame_' + specid;
      if (bi2() && f5.b.n == 'M' && (f5.b.v < 7 || parseInt(f5.b.mode) < 7)) frame.src = bi6('reload');
      else frame.src = 'about:blank';

      onload = frame.onload = function () {
        if (cnt == 0) {
          form.submit();
        } else if (cnt == 1) {
          form.parentNode.removeChild(form);
          frame.parentNode.removeChild(frame);
          M9(f5.d);
          if (font_ext_reload) {
            f6();
          }

          if (f5.b.n == 'F' || f5.b.n == 'S') {
            var B = f7(specid + '.css', bi ? bi : selhist[selidx].id);
            clearTimeout(tid);
            if (typeof sessionStorage != 'undefined') {
              var f = '';
              for (var d = 0; d < use_fonts.length; d++) {
                f += use_fonts[d] + (nicknames[d] ? nicknames[d] : '0') + str[d];
              }
              try {
                sessionStorage.setItem(f, B.href);
              } catch (e) {}
            }
          } else {
            B1(specid);
          }

          if (lclist.length) {
            f5.WT(lclist, desig ? options.callbacks[desig]['func'] : completefunc, options.size, B9());
          }

          X3(M);
          M4();
        }

        cnt++;
      };
      if (document.all) {
        frame.onreadystatechange = function () {
          if (this.readyState == 'complete') {
            frame.contentWindow.name = frame.name;
            onload();
          }
        };
      }

      f5.d.body.appendChild(frame);
    };
    var M0 = function () {
      if (irregular === 0) return true;

      if (irregular === 1) {
        if (!f4.nolist) f7('allfonts.css');
      }

      M1(f5.d, par);

      return true;
    };
    var M1 = function (f, B, M) {
      var d = f.getElementById('fontplus_style');
      if (!d) {
        d = f5.d.createElement('style');
        d.setAttribute('type', 'text/css');
        d.setAttribute('id', 'fontplus_style');
      }

      if (M) {
        if (irregular == 2 && d.styleSheet) {
          d.styleSheet.cssText = M;
        } else if (irregular == 2 && d.sheet) {
          d.sheet.cssText = M;
        } else {
          d.innerHTML = M;
        }
      }

      B.appendChild(d);
    };
    var f8 = function () {
      if (f4.nolist) {
        var d = f5.d.createElement('style'),
          s = '',
          i;
        d.setAttribute('type', 'text/css');
        d.setAttribute('id', 'fontplus_link');
        s = "@charset 'utf-8';\n";
        for (i = f4.plusf.length; i--; ) {
          s +=
            "@font-face { font-family: '" +
            f4.plusf[i] +
            "'; font-weight: Bold; src: url('" +
            bi6('download') +
            '/default.' +
            f4.condition +
            ".ttf') format('truetype'); }\n";
        }
        s +=
          "@font-face { font-family: 'FONTPLUS_DUMMY'; font-weight: Bold; src: url('" +
          bi6('download') +
          '/default.' +
          f4.condition +
          ".ttf') format('truetype'); }\n";
        if (irregular == 2 && d.styleSheet) {
          d.styleSheet.cssText = s;
        } else if (irregular == 2 && d.sheet) {
          d.sheet.cssText = s;
        } else {
          d.innerHTML = s;
        }

        par.appendChild(d);
      } else f7('allfonts.css');
    };
    var M9 = function (d) {
      var f = d.getElementById('fontplus_link');
      if (f) f.parentNode.removeChild(f);
    };
    var B9 = function () {
      return f4.timeout * 1000 - (+new Date() - f5.m);
    };
    var B5 = function (bq, bi) {
      if (bq == 0 && f4.delay > 0) {
        var X = f5.d.createElement('style'),
          s,
          i;
        X.setAttribute('type', 'text/css');
        X.setAttribute('id', 'fontplus_delay_css');
        s = '.fontplus_target_tags {visibility:hidden;} body {visibility:hidden}';
        if (f5.b.n == 'M' && (f5.b.v < 9 || f5.b.mode < 9) && X.styleSheet) {
          X.styleSheet.cssText = s;
        } else if (f5.b.n == 'M' && (f5.b.v < 9 || f5.b.mode < 9) && X.sheet) {
          X.sheet.cssText = s;
        } else {
          X.innerHTML = s;
        }

        par.appendChild(X);
      } else if (bq == 1) {
        if (f4.delay > 0) {
          bgimg.type = false;
          bgimg.src = f5.ST(f5.TN(f5.d, 'BODY')[0], f5.TC('background-image'));
          if (!bgimg.type && bgimg.src != 'none') bgimg.type = 'BODY';

          if (bgimg.type) {
            f5.TN(f5.d, bgimg.type)[0].style.backgroundImage = 'none';
          }

          completefunc = function (B) {
            var d = f5.QS('.fontplus_target_tags', document);
            for (var f = d.length; f--; ) {
              d[f].style.visibility = '';
              d[f].className = d[f].className.replace(/\s*fontplus_target_tags/, '');
            }
            f5.d.body.style.visibility = 'visible';
            if (f4.delay == 2) {
              if (bgimg.type) f5.TN(f5.d, bgimg.type)[0].style.backgroundImage = bgimg.src;
            }

            if (options.complete) options.complete(B);
          };
        } else {
          completefunc = function (d) {
            if (options.complete) options.complete(d);
          };
        }
      } else if (bq == 2 && f4.delay == 1) {
        f5.d.body.style.visibility = 'visible';
        if (bgimg.type) f5.TN(f5.d, bgimg.type)[0].style.backgroundImage = bgimg.src;
      } else if (bq == 3 && f4.delay > 0) {
        if (bi.className.match) {
          if (bi.style.visibility != 'hidden' && !bi.className.match(/\s*fontplus_target_tags/)) {
            var M = bi.className;
            M += (M.length > 0 ? ' ' : '') + 'fontplus_target_tags';
            bi.className = M;
          }
        } else if (bi.className.baseVal.match) {
          if (bi.style.visibility != 'hidden' && !bi.className.baseVal.match(/\s*fontplus_target_tags/)) {
            var M = bi.className;
            M += (M.length > 0 ? ' ' : '') + 'fontplus_target_tags';
            bi.className = M;
          }
        }
      }
    };
    var X9 = function () {
      var B = 'http' + (bi2() ? 's' : '') + f4.bfurl;
      var f = f4.bfnms;
      var X = "@font-face { font-family: 'fpbf_ac93d8111'; src: url('" + B;
      if (f5.b.n != 'U') {
        var d = f['w'] + "') format('woff";
        if (f5.b.ext) {
          switch (f5.b.ext) {
            case 'o':
              d = f['o'] + "') format('opentype";
              break;

            case 't':
              d = f['t'] + "') format('truetype";
              break;

            case 'e':
              d = f['e'];
              break;
          }
        }

        X += d + "'); }";
      } else {
        X +=
          f['w'] +
          "') format('woff'), url('" +
          B +
          f['o'] +
          "') format('opentype'), url('" +
          B +
          f['t'] +
          "') format('truetype'); src: url('" +
          B +
          f['e'] +
          "')\\9; }";
      }

      var M = f5.d.createElement('style');
      M.setAttribute('type', 'text/css');
      M.setAttribute('id', 'fpbf_ac93d8111_css');
      if (f5.b.n == 'M' && (f5.b.v < 9 || f5.b.mode < 9) && M.styleSheet) {
        par.appendChild(M);
        M.styleSheet.cssText = X;
      } else {
        M.innerHTML = X;
        par.appendChild(M);
      }
    };
    var M7 = function (f) {
      var d = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
        rand = '',
        i,
        n = d.length;
      for (i = f; i--; ) rand += d.charAt(Math.floor(Math.random() * n));

      return rand;
    };
    var f6 = function () {
      if (!font_ext_reload) return -1;

      var d = f5.QS(options.selector, f5.d);
      for (var f = d.length; f--; ) {
        if (d[f].getAttribute('data-fp-tmp-nst')) {
          d[f].style.fontFamily = d[f].getAttribute('data-fp-tmp-nst');
          d[f].removeAttribute('data-fp-tmp-nst');
        }
      }
      font_ext_reload = false;
    };
    var M4 = function () {
      if (!font_ext) return -1;

      font_ext_hist = [];
      for (var d = 0; d < use_fonts.length; d++) {
        font_ext_hist.push({ org: use_fonts[d], ext: nicknames[d] });
      }
      font_ext = false;
    };
    var M2 = function (d, f) {
      if (!font_ext) return -1;

      var B = d.toLowerCase();
      for (f = font_ext_hist.length; f-- && B != font_ext_hist[f].ext.toLowerCase(); );

      return f;
    };
    var X3 = function (B) {
      if (!f4.trial) return;

      var f = 2,
        rsp = 2,
        img = f5.d.createElement('img'),
        s,
        fs = B.replace(/,/g, '\r\n');
      img.setAttribute('src', 'http' + (bi2() ? 's:' : '') + '://' + f4.server[0] + '/img/common/banner.gif');
      img.setAttribute('id', 'fontplus-trial-banner');
      img.setAttribute('class', 'fontplus-trial-banner');
      img.setAttribute('title', fs);
      s = img.style;
      s.position = 'fixed';
      s.bottom = f + 'px';
      s.right = rsp + 'px';
      s.cursor = 'pointer';
      s.zIndex = 2147483647;
      s.textDecoration = 'none';
      s.verticalAlign = 'baseline';
      s.outline = s.padding = s.margin = s.border = 0;
      if ((f5.b.n == 'M' && (f5.b.v < 7 || parseInt(f5.b.mode) < 7)) || f5.b.n == 'I') {
        other_scroll = function () {
          var d, cw, st, sl;
          if (f5.b.n == 'I') {
            cw = window.innerWidth;
            d = window.innerHeight;
            st = window.scrollY;
            sl = window.scrollX;
          } else {
            d = f5.d.documentElement.clientHeight || f5.d.body.clientHeight || f5.d.clientHeight || 0;
            cw = f5.d.documentElement.clientWidth || f5.d.body.clientWidth || f5.d.clientWidth || 0;
            st = f5.d.documentElement.scrollTop || f5.d.body.scrollTop || f5.d.scrollTop || 0;
            sl = f5.d.documentElement.scrollLeft || f5.d.body.scrollLeft || f5.d.scrollLeft || 0;
          }

          s.top = d + st - img.height - rsp + 'px';
          s.left = cw + sl - img.width - f + 'px';
        };
        s.position = 'absolute';
        window.onscroll = window.onresize = other_scroll;
      }

      f5.TN(f5.d, 'BODY')[0].appendChild(img);
      f5.E(img, 'click', function () {
        window.open('http://' + f4.server[0] + '/');
      });
    };
    var M3 = function (d) {
      if (String(d).match(/\{pm\}$|---pm$/)) {
        return 1;
      } else if (String(d).match(/\{t=[0-9]+\}$|---t[0-9]+$/)) {
        return 2;
      } else {
        return 0;
      }
    };
    var B8 = function (d) {
      var f = M3(d),
        orignalFont = null,
        value = null,
        proportionData = new Object();
      if (f == 1) {
        orignalFont = String(d).replace(/\{pm\}$|---pm$/, '');
        f = 'pm';
        value = null;
      } else if (f == 2) {
        orignalFont = String(d).replace(/\{t=[0-9]+\}$|---t[0-9]+$/, '');
        f = 't';
        var B = String(d).match(/\{t=[0-9]+\}$|---t[0-9]+$/);
        value = String(B).match(/[0-9]+/);
      }

      if (orignalFont) {
        proportionData['org'] = orignalFont;
        if (f) proportionData['mode'] = f;

        if (value) proportionData['value'] = value;

        return proportionData;
      }

      return null;
    };
    var bi3 = function (br) {
      var B = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
        'a',
        'b',
        'c',
        'd',
        'e',
        'f',
        'g',
        'h',
        'i',
        'j',
        'k',
        'l',
        'm',
        'n',
        'o',
        'p',
        'q',
        'r',
        's',
        't',
        'u',
        'v',
        'w',
        'x',
        'y',
        'z',
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9',
        String.fromCharCode(43),
        String.fromCharCode(47),
      ];
      var f = '';
      var bq = br.length;
      var bi = 0;
      var d = 0;
      var M = 0;
      while (M < bq) {
        d = br.charCodeAt(M);
        if (d > 0xff) {
          return null;
        }

        f += B[d >> 2];
        bi = (d & 0x03) << 4;
        M++;
        if (M >= bq) {
          break;
        }

        d = br.charCodeAt(M);
        if (d > 0xff) {
          return null;
        }

        f += B[bi | (d >> 4)];
        bi = (d & 0x0f) << 2;
        M++;
        if (M >= bq) {
          break;
        }

        d = br.charCodeAt(M);
        if (d > 0xff) {
          return null;
        }

        f += B[bi | (d >> 6)];
        f += B[d & 0x3f];
        M++;
      }
      var X = bq % 3;
      if (X) {
        f += B[bi];
      }

      if (X == 1) {
        f += '==';
      } else if (X == 2) {
        f += '=';
      }

      return f9(f);
    };
    var f9 = function (d) {
      return d.replace(/\+/g, '-').replace(/\//g, '_').replace(/\=+$/, '');
    };

    return f4;
  })();
  FontPlusAccessor.condition = 'fp_gBaf4X~siMM=';
  FontPlusAccessor.plusf = [
    'FOT-Stick Std B',
    'FOT-Stick Std B Regular',
    'FOT-\u30b9\u30c6\u30c3\u30ad Std',
    'FOT-\u30b9\u30c6\u30c3\u30ad Std B',
    'FOT-\u30b9\u30c6\u30c3\u30ad Std Regular',
    'StickStd-B',
    'FOT-UDKakugoC80 Pro B',
    'FOT-UDKakugoC80 Pro B Regular',
    'FOT-UD\u89d2\u30b4C80 Pro B',
    'FOT-UD\u89d2\u30b4C80 Pro B Regular',
    'UDKakugoC80Pro-B',
    'FOT-UDKakugoC80 Pro DB',
    'FOT-UDKakugoC80 Pro DB Regular',
    'FOT-UD\u89d2\u30b4C80 Pro DB',
    'FOT-UD\u89d2\u30b4C80 Pro DB Regular',
    'UDKakugoC80Pro-DB',
    'FOT-UDKakugoC80 Pro M',
    'FOT-UDKakugoC80 Pro M Regular',
    'FOT-UD\u89d2\u30b4C80 Pro M',
    'FOT-UD\u89d2\u30b4C80 Pro M Regular',
    'UDKakugoC80Pro-M',
    'FOT-UDKakugoC80 Pro R',
    'FOT-UDKakugoC80 Pro R Regular',
    'FOT-UD\u89d2\u30b4C80 Pro R',
    'FOT-UD\u89d2\u30b4C80 Pro R Regular',
    'UDKakugoC80Pro-R',
    'FOT-UDKakugoC80 Pro L',
    'FOT-UDKakugoC80 Pro L Regular',
    'FOT-UD\u89d2\u30b4C80 Pro L',
    'FOT-UD\u89d2\u30b4C80 Pro L Regular',
    'UDKakugoC80Pro-L',
    'FOT-UDKakugoC70 Pro B',
    'FOT-UDKakugoC70 Pro B Regular',
    'FOT-UD\u89d2\u30b4C70 Pro B',
    'FOT-UD\u89d2\u30b4C70 Pro B Regular',
    'UDKakugoC70Pro-B',
    'FOT-UDKakugoC70 Pro DB',
    'FOT-UDKakugoC70 Pro DB Regular',
    'FOT-UD\u89d2\u30b4C70 Pro DB',
    'FOT-UD\u89d2\u30b4C70 Pro DB Regular',
    'UDKakugoC70Pro-DB',
    'FOT-UDKakugoC70 Pro M',
    'FOT-UDKakugoC70 Pro M Regular',
    'FOT-UD\u89d2\u30b4C70 Pro M',
    'FOT-UD\u89d2\u30b4C70 Pro M Regular',
    'UDKakugoC70Pro-M',
    'FOT-UDKakugoC70 Pro R',
    'FOT-UDKakugoC70 Pro R Regular',
    'FOT-UD\u89d2\u30b4C70 Pro R',
    'FOT-UD\u89d2\u30b4C70 Pro R Regular',
    'UDKakugoC70Pro-R',
    'FOT-UDKakugoC70 Pro L',
    'FOT-UDKakugoC70 Pro L Regular',
    'FOT-UD\u89d2\u30b4C70 Pro L',
    'FOT-UD\u89d2\u30b4C70 Pro L Regular',
    'UDKakugoC70Pro-L',
    'FOT-UDKakugoC60 Pro B',
    'FOT-UDKakugoC60 Pro B Regular',
    'FOT-UD\u89d2\u30b4C60 Pro B',
    'FOT-UD\u89d2\u30b4C60 Pro B Regular',
    'UDKakugoC60Pro-B',
    'FOT-UDKakugoC60 Pro DB',
    'FOT-UDKakugoC60 Pro DB Regular',
    'FOT-UD\u89d2\u30b4C60 Pro DB',
    'FOT-UD\u89d2\u30b4C60 Pro DB Regular',
    'UDKakugoC60Pro-DB',
    'FOT-UDKakugoC60 Pro M',
    'FOT-UDKakugoC60 Pro M Regular',
    'FOT-UD\u89d2\u30b4C60 Pro M',
    'FOT-UD\u89d2\u30b4C60 Pro M Regular',
    'UDKakugoC60Pro-M',
    'FOT-UDKakugoC60 Pro R',
    'FOT-UDKakugoC60 Pro R Regular',
    'FOT-UD\u89d2\u30b4C60 Pro R',
    'FOT-UD\u89d2\u30b4C60 Pro R Regular',
    'UDKakugoC60Pro-R',
    'FOT-UDKakugoC60 Pro L',
    'FOT-UDKakugoC60 Pro L Regular',
    'FOT-UD\u89d2\u30b4C60 Pro L',
    'FOT-UD\u89d2\u30b4C60 Pro L Regular',
    'UDKakugoC60Pro-L',
    'FOT-NewCinemaA Std D',
    'FOT-NewCinemaA Std D Regular',
    'FOT-\u30cb\u30e5\u30fc\u30b7\u30cd\u30deA Std D',
    'FOT-\u30cb\u30e5\u30fc\u30b7\u30cd\u30deA Std D Regular',
    'NewCinemaAStd-D',
    'FOT-NewCinemaB Std D',
    'FOT-NewCinemaB Std D Regular',
    'FOT-\u30cb\u30e5\u30fc\u30b7\u30cd\u30deB Std D',
    'FOT-\u30cb\u30e5\u30fc\u30b7\u30cd\u30deB Std D Regular',
    'NewCinemaBStd-D',
    'AokaneStd-EB',
    'FOT-Aokane Std EB',
    'FOT-Aokane Std EB Regular',
    'FOT-\u3042\u304a\u304b\u306d Std EB',
    'FOT-\u3042\u304a\u304b\u306d Std EB Regular',
    'BabyPopStd-EB',
    'FOT-BabyPop Std EB',
    'FOT-BabyPop Std EB Regular',
    'FOT-\u30d9\u30d3\u30dd\u30c3\u30d7 Std EB',
    'FOT-\u30d9\u30d3\u30dd\u30c3\u30d7 Std EB Regular',
    'FOT-TsukuAntiqueLMin Std L',
    'FOT-TsukuAntiqueLMin Std L Regular',
    'FOT-\u7b51\u7d2b\u30a2\u30f3\u30c6\u30a3\u30fc\u30afL\u660e\u671d Std L',
    'FOT-\u7b51\u7d2b\u30a2\u30f3\u30c6\u30a3\u30fc\u30afL\u660e\u671d Std L Regular',
    'TsukuAntiqueLMinStd-L',
    'FOT-TsukuAntiqueSMin Std L',
    'FOT-TsukuAntiqueSMin Std L Regular',
    'FOT-\u7b51\u7d2b\u30a2\u30f3\u30c6\u30a3\u30fc\u30afS\u660e\u671d Std L',
    'FOT-\u7b51\u7d2b\u30a2\u30f3\u30c6\u30a3\u30fc\u30afS\u660e\u671d Std L Regular',
    'TsukuAntiqueSMinStd-L',
    'FOT-TsukuBMin Pr6 L',
    'FOT-TsukuBMin Pr6 L Regular',
    'FOT-\u7b51\u7d2bB\u660e\u671d Pr6 L',
    'FOT-\u7b51\u7d2bB\u660e\u671d Pr6 L Regular',
    'TsukuBMinPr6-L',
    'FOT-TsukuBMin Pr6N L',
    'FOT-TsukuBMin Pr6N L Regular',
    'FOT-\u7b51\u7d2bB\u660e\u671d Pr6N L',
    'FOT-\u7b51\u7d2bB\u660e\u671d Pr6N L Regular',
    'TsukuBMinPr6N-L',
    'FOT-UDKakugo_Large Pro B',
    'FOT-UDKakugo_Large Pro B Regular',
    'FOT-UD\u89d2\u30b4_\u30e9\u30fc\u30b8 Pro B',
    'FOT-UD\u89d2\u30b4_\u30e9\u30fc\u30b8 Pro B Regular',
    'UDKakugo_LargePro-B',
    'FOT-UDKakugo_Large Pro DB',
    'FOT-UDKakugo_Large Pro DB Regular',
    'FOT-UD\u89d2\u30b4_\u30e9\u30fc\u30b8 Pro DB',
    'FOT-UD\u89d2\u30b4_\u30e9\u30fc\u30b8 Pro DB Regular',
    'UDKakugo_LargePro-DB',
    'FOT-UDKakugo_Large Pro L',
    'FOT-UDKakugo_Large Pro L Regular',
    'FOT-UD\u89d2\u30b4_\u30e9\u30fc\u30b8 Pro L',
    'FOT-UD\u89d2\u30b4_\u30e9\u30fc\u30b8 Pro L Regular',
    'UDKakugo_LargePro-L',
    'FOT-UDKakugo_Large Pro M',
    'FOT-UDKakugo_Large Pro M Regular',
    'FOT-UD\u89d2\u30b4_\u30e9\u30fc\u30b8 Pro M',
    'FOT-UD\u89d2\u30b4_\u30e9\u30fc\u30b8 Pro M Regular',
    'UDKakugo_LargePro-M',
    'FOT-UDKakugo_Large Pro R',
    'FOT-UDKakugo_Large Pro R Regular',
    'FOT-UD\u89d2\u30b4_\u30e9\u30fc\u30b8 Pro R',
    'FOT-UD\u89d2\u30b4_\u30e9\u30fc\u30b8 Pro R Regular',
    'UDKakugo_LargePro-R',
    'FOT-UDMarugo_Large Pro B',
    'FOT-UDMarugo_Large Pro B Regular',
    'FOT-UD\u4e38\u30b4_\u30e9\u30fc\u30b8 Pro B',
    'FOT-UD\u4e38\u30b4_\u30e9\u30fc\u30b8 Pro B Regular',
    'UDMarugo_LargePro-B',
    'FOT-UDMarugo_Large Pro DB',
    'FOT-UDMarugo_Large Pro DB Regular',
    'FOT-UD\u4e38\u30b4_\u30e9\u30fc\u30b8 Pro DB',
    'FOT-UD\u4e38\u30b4_\u30e9\u30fc\u30b8 Pro DB Regular',
    'UDMarugo_LargePro-DB',
    'FOT-UDMarugo_Large Pro L',
    'FOT-UDMarugo_Large Pro L Regular',
    'FOT-UD\u4e38\u30b4_\u30e9\u30fc\u30b8 Pro L',
    'FOT-UD\u4e38\u30b4_\u30e9\u30fc\u30b8 Pro L Regular',
    'UDMarugo_LargePro-L',
    'FOT-UDMarugo_Large Pro M',
    'FOT-UDMarugo_Large Pro M Regular',
    'FOT-UD\u4e38\u30b4_\u30e9\u30fc\u30b8 Pro M',
    'FOT-UD\u4e38\u30b4_\u30e9\u30fc\u30b8 Pro M Regular',
    'UDMarugo_LargePro-M',
    'FOT-UDMincho Pro B',
    'FOT-UDMincho Pro B Regular',
    'FOT-UD\u660e\u671d Pro B',
    'FOT-UD\u660e\u671d Pro B Regular',
    'UDMinchoPro-B',
    'FOT-UDMincho Pro DB',
    'FOT-UDMincho Pro DB Regular',
    'FOT-UD\u660e\u671d Pro DB',
    'FOT-UD\u660e\u671d Pro DB Regular',
    'UDMinchoPro-DB',
    'FOT-UDMincho Pro L',
    'FOT-UDMincho Pro L Regular',
    'FOT-UD\u660e\u671d Pro L',
    'FOT-UD\u660e\u671d Pro L Regular',
    'UDMinchoPro-L',
    'FOT-UDMincho Pro M',
    'FOT-UDMincho Pro M Regular',
    'FOT-UD\u660e\u671d Pro M',
    'FOT-UD\u660e\u671d Pro M Regular',
    'UDMinchoPro-M',
    'FOT-KokinEdo Std EB',
    'FOT-KokinEdo Std EB Regular',
    'FOT-\u53e4\u4eca\u6c5f\u6238 Std EB',
    'FOT-\u53e4\u4eca\u6c5f\u6238 Std EB Regular',
    'KokinEdoStd-EB',
    'FOT-KokinHige Std EB',
    'FOT-KokinHige Std EB Regular',
    'FOT-\u53e4\u4eca\u9aed Std EB',
    'FOT-\u53e4\u4eca\u9aed Std EB Regular',
    'KokinHigeStd-EB',
    'FOT-Kakurei Std M',
    'FOT-Kakurei Std M Regular',
    'FOT-\u89d2\u96b7 Std M',
    'FOT-\u89d2\u96b7 Std M Regular',
    'KakureiStd-M',
    'FOT-Kakurei Std L',
    'FOT-Kakurei Std L Regular',
    'FOT-\u89d2\u96b7 Std L',
    'FOT-\u89d2\u96b7 Std L Regular',
    'KakureiStd-L',
    'FOT-Kakurei Std EB',
    'FOT-Kakurei Std EB Regular',
    'FOT-\u89d2\u96b7 Std EB',
    'FOT-\u89d2\u96b7 Std EB Regular',
    'KakureiStd-EB',
    'FOT-Hourei Std EB',
    'FOT-Hourei Std EB Regular',
    'FOT-\u8c4a\u96b7 Std EB',
    'FOT-\u8c4a\u96b7 Std EB Regular',
    'HoureiStd-EB',
    'AnitoStd-Relief',
    'FOT-Anito Std Relief',
    'FOT-Anito Std Relief Regular',
    'FOT-\u30a2\u30cb\u30c8 Std Relief',
    'FOT-\u30a2\u30cb\u30c8 Std Relief Regular',
    'AnitoStd-Inline',
    'FOT-Anito Std Inline',
    'FOT-Anito Std Inline Regular',
    'FOT-\u30a2\u30cb\u30c8 Std Inline',
    'FOT-\u30a2\u30cb\u30c8 Std Inline Regular',
    'AnitoStd-M',
    'FOT-Anito Std M',
    'FOT-Anito Std M Regular',
    'FOT-\u30a2\u30cb\u30c8 Std M',
    'FOT-\u30a2\u30cb\u30c8 Std M Regular',
    'AnitoStd-L',
    'FOT-Anito Std L',
    'FOT-Anito Std L Regular',
    'FOT-\u30a2\u30cb\u30c8 Std L',
    'FOT-\u30a2\u30cb\u30c8 Std L Regular',
    'FOT-TsukuOldGothic Std B',
    'FOT-TsukuOldGothic Std B Regular',
    'FOT-\u7b51\u7d2b\u30aa\u30fc\u30eb\u30c9\u30b4\u30b7\u30c3\u30af Std B',
    'FOT-\u7b51\u7d2b\u30aa\u30fc\u30eb\u30c9\u30b4\u30b7\u30c3\u30af Std B Regular',
    'TsukuOldGothicStd-B',
    'FOT-TsukuCOldMin Pr6N R',
    'FOT-TsukuCOldMin Pr6N R Regular',
    'FOT-\u7b51\u7d2bC\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N R',
    'FOT-\u7b51\u7d2bC\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N R Regular',
    'TsukuCOldMinPr6N-R',
    'FOT-TsukuCOldMin Pr6 R',
    'FOT-TsukuCOldMin Pr6 R Regular',
    'FOT-\u7b51\u7d2bC\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 R',
    'FOT-\u7b51\u7d2bC\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 R Regular',
    'TsukuCOldMinPr6-R',
    'FOT-Skip Std M',
    'FOT-Skip Std M Regular',
    'FOT-\u30b9\u30ad\u30c3\u30d7 Std M',
    'FOT-\u30b9\u30ad\u30c3\u30d7 Std M Regular',
    'SkipStd-M',
    'FOT-Skip Std L',
    'FOT-Skip Std L Regular',
    'FOT-\u30b9\u30ad\u30c3\u30d7 Std L',
    'FOT-\u30b9\u30ad\u30c3\u30d7 Std L Regular',
    'SkipStd-L',
    'FOT-Skip Std E',
    'FOT-Skip Std E Regular',
    'FOT-\u30b9\u30ad\u30c3\u30d7 Std E',
    'FOT-\u30b9\u30ad\u30c3\u30d7 Std E Regular',
    'SkipStd-E',
    'FOT-Skip Std D',
    'FOT-Skip Std D Regular',
    'FOT-\u30b9\u30ad\u30c3\u30d7 Std D',
    'FOT-\u30b9\u30ad\u30c3\u30d7 Std D Regular',
    'SkipStd-D',
    'FOT-arc Std R',
    'FOT-arc Std R Regular',
    'FOT-\u30a2\u30fc\u30af Std R',
    'FOT-\u30a2\u30fc\u30af Std R Regular',
    'arcStd-R',
    'F+UD-NewRodin B',
    'F+UD-NewRodin\u3000B',
    'F+UD-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 B',
    'F+UD-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3\u3000B',
    'F+UD-NewRodin DB',
    'F+UD-NewRodin\u3000DB',
    'F+UD-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 DB',
    'F+UD-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3\u3000DB',
    'F+UD-NewRodin M',
    'F+UD-NewRodin\u3000M',
    'F+UD-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 M',
    'F+UD-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3\u3000M',
    'F+UD-TsukuMin D',
    'F+UD-TsukuMin\u3000D',
    'F+UD-\u7b51\u7d2b\u660e\u671d D',
    'F+UD-\u7b51\u7d2b\u660e\u671d\u3000D',
    'TsukuMin-D',
    'F+UD-TsukuMin E',
    'F+UD-TsukuMin\u3000E',
    'F+UD-\u7b51\u7d2b\u660e\u671d E',
    'F+UD-\u7b51\u7d2b\u660e\u671d\u3000E',
    'TsukuMin-E',
    'F+UD-TsukuMin R',
    'F+UD-TsukuMin\u3000R',
    'F+UD-\u7b51\u7d2b\u660e\u671d R',
    'F+UD-\u7b51\u7d2b\u660e\u671d\u3000R',
    'F+UD-Seurat B',
    'F+UD-Seurat\u3000B',
    'F+UD-\u30b9\u30fc\u30e9 B',
    'F+UD-\u30b9\u30fc\u30e9\u3000B',
    'F+UD-Seurat DB',
    'F+UD-Seurat\u3000DB',
    'F+UD-\u30b9\u30fc\u30e9 DB',
    'F+UD-\u30b9\u30fc\u30e9\u3000DB',
    'F+UD-Seurat M',
    'F+UD-Seurat\u3000M',
    'F+UD-\u30b9\u30fc\u30e9 M',
    'F+UD-\u30b9\u30fc\u30e9\u3000M',
    'FOT-Yuruka Std UB',
    'FOT-\u30e6\u30fc\u30eb\u30ab Std UB',
    'YurukaStd-UB',
    'FOT-TsukuAOldMin Pr6 E',
    'FOT-TsukuAOldMin Pr6 E Regular',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 E',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 E Regular',
    'TsukuAOldMinPr6-E',
    'FOT-TsukuBOldMin Pr6N R',
    'FOT-TsukuBOldMin Pr6N R Regular',
    'FOT-\u7b51\u7d2bB\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N R',
    'FOT-\u7b51\u7d2bB\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N R Regular',
    'TsukuBOldMinPr6N-R',
    'FOT-TsukuBOldMin Pr6 R',
    'FOT-TsukuBOldMin Pr6 R Regular',
    'FOT-\u7b51\u7d2bB\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 R',
    'FOT-\u7b51\u7d2bB\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 R Regular',
    'TsukuBOldMinPr6-R',
    'FOT-TsukuAOldMin Pr6N E',
    'FOT-TsukuAOldMin Pr6N E Regular',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N E',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N E Regular',
    'TsukuAOldMinPr6N-E',
    'FOT-TsukuAOldMin Pr6N B',
    'FOT-TsukuAOldMin Pr6N B Regular',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N B',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N B Regular',
    'TsukuAOldMinPr6N-B',
    'FOT-TsukuAOldMin Pr6N D',
    'FOT-TsukuAOldMin Pr6N D Regular',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N D',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N D Regular',
    'TsukuAOldMinPr6N-D',
    'FOT-TsukuAOldMin Pr6N M',
    'FOT-TsukuAOldMin Pr6N M Regular',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N M',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N M Regular',
    'TsukuAOldMinPr6N-M',
    'FOT-TsukuAOldMin Pr6N R',
    'FOT-TsukuAOldMin Pr6N R Regular',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N R',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N R Regular',
    'TsukuAOldMinPr6N-R',
    'FOT-TsukuAOldMin Pr6N L',
    'FOT-TsukuAOldMin Pr6N L Regular',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N L',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6N L Regular',
    'TsukuAOldMinPr6N-L',
    'FOT-TsukuAOldMin Pr6 B',
    'FOT-TsukuAOldMin Pr6 B Regular',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 B',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 B Regular',
    'TsukuAOldMinPr6-B',
    'FOT-TsukuAOldMin Pr6 D',
    'FOT-TsukuAOldMin Pr6 D Regular',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 D',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 D Regular',
    'TsukuAOldMinPr6-D',
    'FOT-TsukuAOldMin Pr6 M',
    'FOT-TsukuAOldMin Pr6 M Regular',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 M',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 M Regular',
    'TsukuAOldMinPr6-M',
    'FOT-TsukuAOldMin Pr6 R',
    'FOT-TsukuAOldMin Pr6 R Regular',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 R',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 R Regular',
    'TsukuAOldMinPr6-R',
    'FOT-TsukuAOldMin Pr6 L',
    'FOT-TsukuAOldMin Pr6 L Regular',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 L',
    'FOT-\u7b51\u7d2bA\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pr6 L Regular',
    'TsukuAOldMinPr6-L',
    'FOT-TsukuOldMin Pro R',
    'FOT-TsukuOldMin Pro R Regular',
    'FOT-\u7b51\u7d2b\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pro R',
    'FOT-\u7b51\u7d2b\u30aa\u30fc\u30eb\u30c9\u660e\u671d Pro R Regular',
    'TsukuOldMinPro-R',
    'FOT-Seurat ProN  EB',
    'FOT-Seurat ProN  EB Regular',
    'FOT-\u30b9\u30fc\u30e9 ProN EB',
    'FOT-\u30b9\u30fc\u30e9 ProN EB Regular',
    'SeuratProN-EB',
    'FOT-TsukuGo Pro U',
    'FOT-TsukuGo Pro U Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro U',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro U Regular',
    'TsukuGoPro-U',
    'FOT-TsukuBRdGothic Std B',
    'FOT-TsukuBRdGothic Std B Regular',
    'FOT-\u7b51\u7d2bB\u4e38\u30b4\u30b7\u30c3\u30af Std B',
    'FOT-\u7b51\u7d2bB\u4e38\u30b4\u30b7\u30c3\u30af Std B Regular',
    'TsukuBRdGothicStd-B',
    'FOT-TsukuBMDMin Std E',
    'FOT-TsukuBMDMin Std E Regular',
    'FOT-\u7b51\u7d2bB\u898b\u51fa\u30df\u30f3 Std E',
    'FOT-\u7b51\u7d2bB\u898b\u51fa\u30df\u30f3 Std E Regular',
    'TsukuBMDMinStd-E',
    'FOT-TsukuARdGothic Std B',
    'FOT-TsukuARdGothic Std B Regular',
    'FOT-\u7b51\u7d2bA\u4e38\u30b4\u30b7\u30c3\u30af Std B',
    'FOT-\u7b51\u7d2bA\u4e38\u30b4\u30b7\u30c3\u30af Std B Regular',
    'TsukuARdGothicStd-B',
    'FOT-TsukuARdGothic Std E',
    'FOT-TsukuARdGothic Std E Regular',
    'FOT-\u7b51\u7d2bA\u4e38\u30b4\u30b7\u30c3\u30af Std E',
    'FOT-\u7b51\u7d2bA\u4e38\u30b4\u30b7\u30c3\u30af Std E Regular',
    'TsukuARdGothicStd-E',
    'FOT-TsukuARdGothic Std D',
    'FOT-TsukuARdGothic Std D Regular',
    'FOT-\u7b51\u7d2bA\u4e38\u30b4\u30b7\u30c3\u30af Std D',
    'FOT-\u7b51\u7d2bA\u4e38\u30b4\u30b7\u30c3\u30af Std D Regular',
    'TsukuARdGothicStd-D',
    'FOT-TsukuARdGothic Std M',
    'FOT-TsukuARdGothic Std M Regular',
    'FOT-\u7b51\u7d2bA\u4e38\u30b4\u30b7\u30c3\u30af Std M',
    'FOT-\u7b51\u7d2bA\u4e38\u30b4\u30b7\u30c3\u30af Std M Regular',
    'TsukuARdGothicStd-M',
    'FOT-TsukuARdGothic Std R',
    'FOT-TsukuARdGothic Std R Regular',
    'FOT-\u7b51\u7d2bA\u4e38\u30b4\u30b7\u30c3\u30af Std R',
    'FOT-\u7b51\u7d2bA\u4e38\u30b4\u30b7\u30c3\u30af Std R Regular',
    'TsukuARdGothicStd-R',
    'FOT-TsukuARdGothic Std L',
    'FOT-TsukuARdGothic Std L Regular',
    'FOT-\u7b51\u7d2bA\u4e38\u30b4\u30b7\u30c3\u30af Std L',
    'FOT-\u7b51\u7d2bA\u4e38\u30b4\u30b7\u30c3\u30af Std L Regular',
    'TsukuARdGothicStd-L',
    'FOT-TsukuAMDMin Std E',
    'FOT-TsukuAMDMin Std E Regular',
    'FOT-\u7b51\u7d2bA\u898b\u51fa\u30df\u30f3 Std E',
    'FOT-\u7b51\u7d2bA\u898b\u51fa\u30df\u30f3 Std E Regular',
    'TsukuAMDMinStd-E',
    'FOT-Skip Std B',
    'FOT-Skip Std B Regular',
    'FOT-\u30b9\u30ad\u30c3\u30d7 Std B',
    'FOT-\u30b9\u30ad\u30c3\u30d7 Std B Regular',
    'SkipStd-B',
    'FOT-OedKtr Std E',
    'FOT-OedKtr Std E Regular',
    'FOT-\u5927\u6c5f\u6238\u52d8\u4ead\u6d41 Std E',
    'FOT-\u5927\u6c5f\u6238\u52d8\u4ead\u6d41 Std E Regular',
    'OedKtrStd-E',
    'FOT-ModeMinB Std B',
    'FOT-ModeMinB Std B Regular',
    'FOT-\u30e2\u30fc\u30c9\u660e\u671dB Std B',
    'FOT-\u30e2\u30fc\u30c9\u660e\u671dB Std B Regular',
    'ModeMinBStd-B',
    'FOT-ModeMinA Std B',
    'FOT-ModeMinA Std B Regular',
    'FOT-\u30e2\u30fc\u30c9\u660e\u671dA Std B',
    'FOT-\u30e2\u30fc\u30c9\u660e\u671dA Std B Regular',
    'ModeMinAStd-B',
    'FOT-ManyoSosho Std E',
    'FOT-ManyoSosho Std E Regular',
    'FOT-\u4e07\u8449\u8349\u66f8 Std E',
    'FOT-\u4e07\u8449\u8349\u66f8 Std E Regular',
    'ManyoSoshoStd-E',
    'FOT-ManyoKoinLarge Std B',
    'FOT-ManyoKoinLarge Std B Regular',
    'FOT-\u4e07\u8449\u53e4\u5370\u30e9\u30fc\u30b8 Std B',
    'FOT-\u4e07\u8449\u53e4\u5370\u30e9\u30fc\u30b8 Std B Regular',
    'ManyoKoinLargeStd-B',
    'FOT-ManyoKoin Std B',
    'FOT-ManyoKoin Std B Regular',
    'FOT-\u4e07\u8449\u53e4\u5370 Std B',
    'FOT-\u4e07\u8449\u53e4\u5370 Std B Regular',
    'ManyoKoinStd-B',
    'FOT-ManyoGyosho Std E',
    'FOT-ManyoGyosho Std E Regular',
    'FOT-\u4e07\u8449\u884c\u66f8 Std E',
    'FOT-\u4e07\u8449\u884c\u66f8 Std E Regular',
    'ManyoGyoshoStd-E',
    'FOT-Humming Std E',
    'FOT-Humming Std E Regular',
    'FOT-\u30cf\u30df\u30f3\u30b0 Std E',
    'FOT-\u30cf\u30df\u30f3\u30b0 Std E Regular',
    'HummingStd-E',
    'FOT-Humming Std D',
    'FOT-Humming Std D Regular',
    'FOT-\u30cf\u30df\u30f3\u30b0 Std D',
    'FOT-\u30cf\u30df\u30f3\u30b0 Std D Regular',
    'HummingStd-D',
    'FOT-Humming Std M',
    'FOT-Humming Std M Regular',
    'FOT-\u30cf\u30df\u30f3\u30b0 Std M',
    'FOT-\u30cf\u30df\u30f3\u30b0 Std M Regular',
    'HummingStd-M',
    'FOT-Humming Std L',
    'FOT-Humming Std L Regular',
    'FOT-\u30cf\u30df\u30f3\u30b0 Std L',
    'FOT-\u30cf\u30df\u30f3\u30b0 Std L Regular',
    'HummingStd-L',
    'FOT-Humming Std B',
    'FOT-Humming Std B Regular',
    'FOT-\u30cf\u30df\u30f3\u30b0 Std B',
    'FOT-\u30cf\u30df\u30f3\u30b0 Std B Regular',
    'HummingStd-B',
    'AnticCezannePro-M',
    'FOT-AnticCezanne Pro M',
    'FOT-AnticCezanne Pro M Regular',
    'FOT-\u30a2\u30f3\u30c1\u30c3\u30af\u30bb\u30b6\u30f3\u30cc Pro M',
    'FOT-\u30a2\u30f3\u30c1\u30c3\u30af\u30bb\u30b6\u30f3\u30cc Pro M Regular',
    'AnticCezannePro-DB',
    'FOT-AnticCezanne Pro DB',
    'FOT-AnticCezanne Pro DB Regular',
    'FOT-\u30a2\u30f3\u30c1\u30c3\u30af\u30bb\u30b6\u30f3\u30cc Pro DB',
    'FOT-\u30a2\u30f3\u30c1\u30c3\u30af\u30bb\u30b6\u30f3\u30cc Pro DB Regular',
    'FOT-TsukuMin Pr5N D',
    'FOT-TsukuMin Pr5N D Regular',
    'FOT-TsukuMin Pr6N D',
    'FOT-TsukuMin Pr6N D Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N D',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N D Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6N D',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6N D Regular',
    'TsukuMinPr5N-D',
    'TsukuMinPr6N-D',
    'FOT-TsukuMin Pr5N M',
    'FOT-TsukuMin Pr5N M Regular',
    'FOT-TsukuMin Pr6N M',
    'FOT-TsukuMin Pr6N M Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N M',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N M Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6N M',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6N M Regular',
    'TsukuMinPr5N-M',
    'TsukuMinPr6N-M',
    'FOT-TsukuMin Pr5N RB',
    'FOT-TsukuMin Pr5N RB Regular',
    'FOT-TsukuMin Pr6N RB',
    'FOT-TsukuMin Pr6N RB Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N RB',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N RB Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6N RB',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6N RB Regular',
    'TsukuMinPr5N-RB',
    'TsukuMinPr6N-RB',
    'FOT-TsukuMin Pr5N R',
    'FOT-TsukuMin Pr5N R Regular',
    'FOT-TsukuMin Pr6N R',
    'FOT-TsukuMin Pr6N R Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N R',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N R Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6N R',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6N R Regular',
    'TsukuMinPr5N-R',
    'TsukuMinPr6N-R',
    'FOT-TsukuMin Pr5N LB',
    'FOT-TsukuMin Pr5N LB Regular',
    'FOT-TsukuMin Pr6N LB',
    'FOT-TsukuMin Pr6N LB Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N LB',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N LB Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6N LB',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6N LB Regular',
    'TsukuMinPr5N-LB',
    'TsukuMinPr6N-LB',
    'FOT-TsukuMin Pr5N L',
    'FOT-TsukuMin Pr5N L Regular',
    'FOT-TsukuMin Pr6N L',
    'FOT-TsukuMin Pr6N L Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N L',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N L Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6N L',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6N L Regular',
    'TsukuMinPr5N-L',
    'TsukuMinPr6N-L',
    'FOT-TsukuMin Pr5 D',
    'FOT-TsukuMin Pr5 D Regular',
    'FOT-TsukuMin Pr6 D',
    'FOT-TsukuMin Pr6 D Regular',
    'FOT-TsukuMin Pro D',
    'FOT-TsukuMin Pro D Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 D',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 D Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6 D',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6 D Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro D',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro D Regular',
    'TsukuMinPr5-D',
    'TsukuMinPr6-D',
    'TsukuMinPro-D',
    'FOT-TsukuMin Pr5 M',
    'FOT-TsukuMin Pr5 M Regular',
    'FOT-TsukuMin Pr6 M',
    'FOT-TsukuMin Pr6 M Regular',
    'FOT-TsukuMin Pro M',
    'FOT-TsukuMin Pro M Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 M',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 M Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6 M',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6 M Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro M',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro M Regular',
    'TsukuMinPr5-M',
    'TsukuMinPr6-M',
    'TsukuMinPro-M',
    'FOT-TsukuMin Pr5 RB',
    'FOT-TsukuMin Pr5 RB Regular',
    'FOT-TsukuMin Pr6 RB',
    'FOT-TsukuMin Pr6 RB Regular',
    'FOT-TsukuMin Pro RB',
    'FOT-TsukuMin Pro RB Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 RB',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 RB Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6 RB',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6 RB Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro RB',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro RB Regular',
    'TsukuMinPr5-RB',
    'TsukuMinPr6-RB',
    'TsukuMinPro-RB',
    'FOT-TsukuMin Pr5 R',
    'FOT-TsukuMin Pr5 R Regular',
    'FOT-TsukuMin Pr6 R',
    'FOT-TsukuMin Pr6 R Regular',
    'FOT-TsukuMin Pro R',
    'FOT-TsukuMin Pro R Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 R',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 R Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6 R',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6 R Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro R',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro R Regular',
    'TsukuMinPr5-R',
    'TsukuMinPr6-R',
    'TsukuMinPro-R',
    'FOT-TsukuMin Pr5 LB',
    'FOT-TsukuMin Pr5 LB Regular',
    'FOT-TsukuMin Pr6 LB',
    'FOT-TsukuMin Pr6 LB Regular',
    'FOT-TsukuMin Pro LB',
    'FOT-TsukuMin Pro LB Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 LB',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 LB Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6 LB',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6 LB Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro LB',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro LB Regular',
    'TsukuMinPr5-LB',
    'TsukuMinPr6-LB',
    'TsukuMinPro-LB',
    'FOT-TsukuMin Pr5 L',
    'FOT-TsukuMin Pr5 L Regular',
    'FOT-TsukuMin Pr6 L',
    'FOT-TsukuMin Pr6 L Regular',
    'FOT-TsukuMin Pro L',
    'FOT-TsukuMin Pro L Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 L',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 L Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6 L',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr6 L Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro L',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro L Regular',
    'TsukuMinPr5-L',
    'TsukuMinPr6-L',
    'TsukuMinPro-L',
    'FOT-TsukuBRdGothic Std E',
    'FOT-TsukuBRdGothic Std E Regular',
    'FOT-\u7b51\u7d2bB\u4e38\u30b4\u30b7\u30c3\u30af Std E',
    'FOT-\u7b51\u7d2bB\u4e38\u30b4\u30b7\u30c3\u30af Std E Regular',
    'TsukuBRdGothicStd-E',
    'FOT-TsukuBRdGothic Std D',
    'FOT-TsukuBRdGothic Std D Regular',
    'FOT-\u7b51\u7d2bB\u4e38\u30b4\u30b7\u30c3\u30af Std D',
    'FOT-\u7b51\u7d2bB\u4e38\u30b4\u30b7\u30c3\u30af Std D Regular',
    'TsukuBRdGothicStd-D',
    'FOT-TsukuBRdGothic Std M',
    'FOT-TsukuBRdGothic Std M Regular',
    'FOT-\u7b51\u7d2bB\u4e38\u30b4\u30b7\u30c3\u30af Std M',
    'FOT-\u7b51\u7d2bB\u4e38\u30b4\u30b7\u30c3\u30af Std M Regular',
    'TsukuBRdGothicStd-M',
    'FOT-TsukuBRdGothic Std R',
    'FOT-TsukuBRdGothic Std R Regular',
    'FOT-\u7b51\u7d2bB\u4e38\u30b4\u30b7\u30c3\u30af Std R',
    'FOT-\u7b51\u7d2bB\u4e38\u30b4\u30b7\u30c3\u30af Std R Regular',
    'TsukuBRdGothicStd-R',
    'FOT-TsukuBRdGothic Std L',
    'FOT-TsukuBRdGothic Std L Regular',
    'FOT-\u7b51\u7d2bB\u4e38\u30b4\u30b7\u30c3\u30af Std L',
    'FOT-\u7b51\u7d2bB\u4e38\u30b4\u30b7\u30c3\u30af Std L Regular',
    'TsukuBRdGothicStd-L',
    'FOT-RaglanPunch Std UB',
    'FOT-RaglanPunch Std UB Regular',
    'FOT-\u30e9\u30b0\u30e9\u30f3\u30d1\u30f3\u30c1 Std UB',
    'FOT-\u30e9\u30b0\u30e9\u30f3\u30d1\u30f3\u30c1 Std UB Regular',
    'RaglanPunchStd-UB',
    'FOT-Pearl Std L',
    'FOT-Pearl Std L Regular',
    'FOT-\u30d1\u30fc\u30eb Std L',
    'FOT-\u30d1\u30fc\u30eb Std L Regular',
    'PearlStd-L',
    'FOT-Steelwork Std B',
    'FOT-Steelwork Std B Regular',
    'FOT-\u30b9\u30c6\u30a3\u30fc\u30eb\u30ef\u30fc\u30af Std B',
    'FOT-\u30b9\u30c6\u30a3\u30fc\u30eb\u30ef\u30fc\u30af Std B Regular',
    'SteelworkStd-B',
    'FOT-Slump Std DB',
    'FOT-Slump Std DB Regular',
    'FOT-\u30b9\u30e9\u30f3\u30d7 Std DB',
    'FOT-\u30b9\u30e9\u30f3\u30d7 Std DB Regular',
    'SlumpStd-DB',
    'FOT-ShadowTL Std B',
    'FOT-ShadowTL Std B Regular',
    'FOT-\u30b7\u30e3\u30c9\u30a6TL Std B',
    'FOT-\u30b7\u30e3\u30c9\u30a6TL Std B Regular',
    'ShadowTLStd-B',
    'FOT-Shadow Std B',
    'FOT-Shadow Std B Regular',
    'FOT-\u30b7\u30e3\u30c9\u30a6 Std B',
    'FOT-\u30b7\u30e3\u30c9\u30a6 Std B Regular',
    'ShadowStd-B',
    'FOT-Rowdy Std EB',
    'FOT-Rowdy Std EB Regular',
    'FOT-\u30ed\u30a6\u30c7\u30a3 Std EB',
    'FOT-\u30ed\u30a6\u30c7\u30a3 Std EB Regular',
    'RowdyStd-EB',
    'FOT-RocknRoll Std DB',
    'FOT-RocknRoll Std DB Regular',
    'FOT-\u30ed\u30c3\u30af\u30f3\u30ed\u30fc\u30eb Std DB',
    'FOT-\u30ed\u30c3\u30af\u30f3\u30ed\u30fc\u30eb Std DB Regular',
    'RocknRollStd-DB',
    'FOT-Reggae Std B',
    'FOT-Reggae Std B Regular',
    'FOT-\u30ec\u30b2\u30a8 Std B',
    'FOT-\u30ec\u30b2\u30a8 Std B Regular',
    'ReggaeStd-B',
    'FOT-RampartTL Std EB',
    'FOT-RampartTL Std EB Regular',
    'FOT-\u30e9\u30f3\u30d1\u30fc\u30c8TL Std EB',
    'FOT-\u30e9\u30f3\u30d1\u30fc\u30c8TL Std EB Regular',
    'RampartTLStd-EB',
    'FOT-Rampart Std EB',
    'FOT-Rampart Std EB Regular',
    'FOT-\u30e9\u30f3\u30d1\u30fc\u30c8 Std EB',
    'FOT-\u30e9\u30f3\u30d1\u30fc\u30c8 Std EB Regular',
    'RampartStd-EB',
    'FOT-Railway Std B',
    'FOT-Railway Std B Regular',
    'FOT-\u30ec\u30a4\u30eb\u30a6\u30a7\u30a4 Std B',
    'FOT-\u30ec\u30a4\u30eb\u30a6\u30a7\u30a4 Std B Regular',
    'RailwayStd-B',
    'FOT-Raglan Std UB',
    'FOT-Raglan Std UB Regular',
    'FOT-\u30e9\u30b0\u30e9\u30f3 Std UB',
    'FOT-\u30e9\u30b0\u30e9\u30f3 Std UB Regular',
    'RaglanStd-UB',
    'FOT-PopJoy Std B',
    'FOT-PopJoy Std B Regular',
    'FOT-Pop\u30b8\u30e7\u30a4 Std B',
    'FOT-Pop\u30b8\u30e7\u30a4 Std B Regular',
    'PopJoyStd-B',
    'FOT-PopHappiness Std EB',
    'FOT-PopHappiness Std EB Regular',
    'FOT-Pop\u30cf\u30c3\u30d4\u30cd\u30b9 Std EB',
    'FOT-Pop\u30cf\u30c3\u30d4\u30cd\u30b9 Std EB Regular',
    'PopHappinessStd-EB',
    'FOT-PopFury Std B',
    'FOT-PopFury Std B Regular',
    'FOT-Pop\u30d5\u30e5\u30fc\u30ea Std B',
    'FOT-Pop\u30d5\u30e5\u30fc\u30ea Std B Regular',
    'PopFuryStd-B',
    'FOT-Mystery Std DB',
    'FOT-Mystery Std DB Regular',
    'FOT-\u30df\u30b9\u30c6\u30ea Std DB',
    'FOT-\u30df\u30b9\u30c6\u30ea Std DB Regular',
    'MysteryStd-DB',
    'FOT-Macaroni Std DB',
    'FOT-Macaroni Std DB Regular',
    'FOT-\u30de\u30ab\u30ed\u30cb Std DB',
    'FOT-\u30de\u30ab\u30ed\u30cb Std DB Regular',
    'MacaroniStd-DB',
    'FOT-Lyra Std DB',
    'FOT-Lyra Std DB Regular',
    'FOT-\u30e9\u30a4\u30e9 Std DB',
    'FOT-\u30e9\u30a4\u30e9 Std DB Regular',
    'LyraStd-DB',
    'ComicReggaeStd-B',
    'FOT-ComicReggae Std B',
    'FOT-ComicReggae Std B Regular',
    'FOT-\u30b3\u30df\u30c3\u30af\u30ec\u30b2\u30a8 Std B',
    'FOT-\u30b3\u30df\u30c3\u30af\u30ec\u30b2\u30a8 Std B Regular',
    'ComicMysteryStd-DB',
    'FOT-ComicMystery Std DB',
    'FOT-ComicMystery Std DB Regular',
    'FOT-\u30b3\u30df\u30c3\u30af\u30df\u30b9\u30c6\u30ea Std DB',
    'FOT-\u30b3\u30df\u30c3\u30af\u30df\u30b9\u30c6\u30ea Std DB Regular',
    'CometStd-B',
    'FOT-Comet Std B',
    'FOT-Comet Std B Regular',
    'FOT-\u30b3\u30e1\u30c3\u30c8 Std B',
    'FOT-\u30b3\u30e1\u30c3\u30c8 Std B Regular',
    'ChiaroStd-B',
    'FOT-Chiaro Std B',
    'FOT-Chiaro Std B Regular',
    'FOT-\u30ad\u30a2\u30ed Std B',
    'FOT-\u30ad\u30a2\u30ed Std B Regular',
    'CaratStd-UB',
    'FOT-Carat Std UB',
    'FOT-Carat Std UB Regular',
    'FOT-\u30ab\u30e9\u30c3\u30c8 Std UB',
    'FOT-\u30ab\u30e9\u30c3\u30c8 Std UB Regular',
    'FOT-Kurokane Std EB',
    'FOT-Kurokane Std EB Regular',
    'FOT-\u304f\u308d\u304b\u306d Std EB',
    'FOT-\u304f\u308d\u304b\u306d Std EB Regular',
    'KurokaneStd-EB',
    'BudoStd-L',
    'FOT-Budo Std L',
    'FOT-Budo Std L Regular',
    'FOT-\u3076\u3069\u3046 Std L',
    'FOT-\u3076\u3069\u3046 Std L Regular',
    'AraletStd-DB',
    'FOT-Aralet Std DB',
    'FOT-Aralet Std DB Regular',
    'FOT-\u3042\u3089\u308c Std DB',
    'FOT-\u3042\u3089\u308c Std DB Regular',
    'FOT-SeuratCapie Pro EB',
    'FOT-SeuratCapie Pro EB Regular',
    'FOT-\u30b9\u30fc\u30e9\u30ad\u30e3\u30d4\u30fc Pro EB',
    'FOT-\u30b9\u30fc\u30e9\u30ad\u30e3\u30d4\u30fc Pro EB Regular',
    'SeuratCapiePro-EB',
    'FOT-SeuratCapie Pro B',
    'FOT-SeuratCapie Pro B Regular',
    'FOT-\u30b9\u30fc\u30e9\u30ad\u30e3\u30d4\u30fc Pro B',
    'FOT-\u30b9\u30fc\u30e9\u30ad\u30e3\u30d4\u30fc Pro B Regular',
    'SeuratCapiePro-B',
    'FOT-SeuratCapie Pro DB',
    'FOT-SeuratCapie Pro DB Regular',
    'FOT-\u30b9\u30fc\u30e9\u30ad\u30e3\u30d4\u30fc Pro DB',
    'FOT-\u30b9\u30fc\u30e9\u30ad\u30e3\u30d4\u30fc Pro DB Regular',
    'SeuratCapiePro-DB',
    'FOT-SeuratCapie Pro M',
    'FOT-SeuratCapie Pro M Regular',
    'FOT-\u30b9\u30fc\u30e9\u30ad\u30e3\u30d4\u30fc Pro M',
    'FOT-\u30b9\u30fc\u30e9\u30ad\u30e3\u30d4\u30fc Pro M Regular',
    'SeuratCapiePro-M',
    'FOT-RodinWanpaku Pro UB',
    'FOT-RodinWanpaku Pro UB Regular',
    'FOT-\u30ed\u30c0\u30f3\u308f\u3093\u3071\u304f Pro UB',
    'FOT-\u30ed\u30c0\u30f3\u308f\u3093\u3071\u304f Pro UB Regular',
    'RodinWanpakuPro-UB',
    'FOT-RodinWanpaku Pro EB',
    'FOT-RodinWanpaku Pro EB Regular',
    'FOT-\u30ed\u30c0\u30f3\u308f\u3093\u3071\u304f Pro EB',
    'FOT-\u30ed\u30c0\u30f3\u308f\u3093\u3071\u304f Pro EB Regular',
    'RodinWanpakuPro-EB',
    'FOT-RodinWanpaku Pro B',
    'FOT-RodinWanpaku Pro B Regular',
    'FOT-\u30ed\u30c0\u30f3\u308f\u3093\u3071\u304f Pro B',
    'FOT-\u30ed\u30c0\u30f3\u308f\u3093\u3071\u304f Pro B Regular',
    'RodinWanpakuPro-B',
    'FOT-RodinWanpaku Pro DB',
    'FOT-RodinWanpaku Pro DB Regular',
    'FOT-\u30ed\u30c0\u30f3\u308f\u3093\u3071\u304f Pro DB',
    'FOT-\u30ed\u30c0\u30f3\u308f\u3093\u3071\u304f Pro DB Regular',
    'RodinWanpakuPro-DB',
    'FOT-RodinWanpaku Pro M',
    'FOT-RodinWanpaku Pro M Regular',
    'FOT-\u30ed\u30c0\u30f3\u308f\u3093\u3071\u304f Pro M',
    'FOT-\u30ed\u30c0\u30f3\u308f\u3093\u3071\u304f Pro M Regular',
    'RodinWanpakuPro-M',
    'FOT-RodinWanpaku Pro L',
    'FOT-RodinWanpaku Pro L Regular',
    'FOT-\u30ed\u30c0\u30f3\u308f\u3093\u3071\u304f Pro L',
    'FOT-\u30ed\u30c0\u30f3\u308f\u3093\u3071\u304f Pro L Regular',
    'RodinWanpakuPro-L',
    'FOT-RodinRose Pro EB',
    'FOT-RodinRose Pro EB Regular',
    'FOT-\u30ed\u30c0\u30f3\u30ed\u30fc\u30ba Pro EB',
    'FOT-\u30ed\u30c0\u30f3\u30ed\u30fc\u30ba Pro EB Regular',
    'RodinRosePro-EB',
    'FOT-RodinRose Pro B',
    'FOT-RodinRose Pro B Regular',
    'FOT-\u30ed\u30c0\u30f3\u30ed\u30fc\u30ba Pro B',
    'FOT-\u30ed\u30c0\u30f3\u30ed\u30fc\u30ba Pro B Regular',
    'RodinRosePro-B',
    'FOT-RodinRose Pro DB',
    'FOT-RodinRose Pro DB Regular',
    'FOT-\u30ed\u30c0\u30f3\u30ed\u30fc\u30ba Pro DB',
    'FOT-\u30ed\u30c0\u30f3\u30ed\u30fc\u30ba Pro DB Regular',
    'RodinRosePro-DB',
    'FOT-RodinNTLG Pro UB',
    'FOT-RodinNTLG Pro UB Regular',
    'FOT-\u30ed\u30c0\u30f3NTLG Pro UB',
    'FOT-\u30ed\u30c0\u30f3NTLG Pro UB Regular',
    'RodinNTLGPro-UB',
    'FOT-RodinNTLG Pro EB',
    'FOT-RodinNTLG Pro EB Regular',
    'FOT-\u30ed\u30c0\u30f3NTLG Pro EB',
    'FOT-\u30ed\u30c0\u30f3NTLG Pro EB Regular',
    'RodinNTLGPro-EB',
    'FOT-RodinNTLG Pro B',
    'FOT-RodinNTLG Pro B Regular',
    'FOT-\u30ed\u30c0\u30f3NTLG Pro B',
    'FOT-\u30ed\u30c0\u30f3NTLG Pro B Regular',
    'RodinNTLGPro-B',
    'FOT-RodinNTLG Pro DB',
    'FOT-RodinNTLG Pro DB Regular',
    'FOT-\u30ed\u30c0\u30f3NTLG Pro DB',
    'FOT-\u30ed\u30c0\u30f3NTLG Pro DB  Regular',
    'RodinNTLGPro-DB',
    'FOT-RodinNTLG Pro M',
    'FOT-RodinNTLG Pro M Regular',
    'FOT-\u30ed\u30c0\u30f3NTLG Pro M',
    'FOT-\u30ed\u30c0\u30f3NTLG Pro M Regular',
    'RodinNTLGPro-M',
    'FOT-RodinNTLG Pro L',
    'FOT-RodinNTLG Pro L Regular',
    'FOT-\u30ed\u30c0\u30f3NTLG Pro L',
    'FOT-\u30ed\u30c0\u30f3NTLG Pro L Regular',
    'RodinNTLGPro-L',
    'FOT-RodinMaria Pro EB',
    'FOT-RodinMaria Pro EB Regular',
    'FOT-\u30ed\u30c0\u30f3\u30de\u30ea\u30a2 Pro EB',
    'FOT-\u30ed\u30c0\u30f3\u30de\u30ea\u30a2 Pro EB Regular',
    'RodinMariaPro-EB',
    'FOT-RodinMaria Pro B',
    'FOT-RodinMaria Pro B Regular',
    'FOT-\u30ed\u30c0\u30f3\u30de\u30ea\u30a2 Pro B',
    'FOT-\u30ed\u30c0\u30f3\u30de\u30ea\u30a2 Pro B Regular',
    'RodinMariaPro-B',
    'FOT-RodinMaria Pro DB',
    'FOT-RodinMaria Pro DB Regular',
    'FOT-\u30ed\u30c0\u30f3\u30de\u30ea\u30a2 Pro DB',
    'FOT-\u30ed\u30c0\u30f3\u30de\u30ea\u30a2 Pro DB Regular',
    'RodinMariaPro-DB',
    'FOT-RodinHimawari Pro UB',
    'FOT-RodinHimawari Pro UB Regular',
    'FOT-\u30ed\u30c0\u30f3\u3072\u307e\u308f\u308a Pro UB',
    'FOT-\u30ed\u30c0\u30f3\u3072\u307e\u308f\u308a Pro UB Regular',
    'RodinHimawariPro-UB',
    'FOT-RodinHimawari Pro EB',
    'FOT-RodinHimawari Pro EB Regular',
    'FOT-\u30ed\u30c0\u30f3\u3072\u307e\u308f\u308a Pro EB',
    'FOT-\u30ed\u30c0\u30f3\u3072\u307e\u308f\u308a Pro EB Regular',
    'RodinHimawariPro-EB',
    'FOT-RodinHimawari Pro B',
    'FOT-RodinHimawari Pro B Regular',
    'FOT-\u30ed\u30c0\u30f3\u3072\u307e\u308f\u308a Pro B',
    'FOT-\u30ed\u30c0\u30f3\u3072\u307e\u308f\u308a Pro B Regular',
    'RodinHimawariPro-B',
    'FOT-RodinHimawari Pro DB',
    'FOT-RodinHimawari Pro DB Regular',
    'FOT-\u30ed\u30c0\u30f3\u3072\u307e\u308f\u308a Pro DB',
    'FOT-\u30ed\u30c0\u30f3\u3072\u307e\u308f\u308a Pro DB Regular',
    'RodinHimawariPro-DB',
    'FOT-RodinHimawari Pro M',
    'FOT-RodinHimawari Pro M Regular',
    'FOT-\u30ed\u30c0\u30f3\u3072\u307e\u308f\u308a Pro M',
    'FOT-\u30ed\u30c0\u30f3\u3072\u307e\u308f\u308a Pro M Regular',
    'RodinHimawariPro-M',
    'FOT-RodinHimawari Pro L',
    'FOT-RodinHimawari Pro L Regular',
    'FOT-\u30ed\u30c0\u30f3\u3072\u307e\u308f\u308a Pro L',
    'FOT-\u30ed\u30c0\u30f3\u3072\u307e\u308f\u308a Pro L Regular',
    'RodinHimawariPro-L',
    'FOT-RodinHappy Pro UB',
    'FOT-RodinHappy Pro UB Regular',
    'FOT-\u30ed\u30c0\u30f3\u30cf\u30c3\u30d4\u30fc Pro UB',
    'FOT-\u30ed\u30c0\u30f3\u30cf\u30c3\u30d4\u30fc Pro UB Regular',
    'RodinHappyPro-UB',
    'FOT-RodinHappy Pro EB',
    'FOT-RodinHappy Pro EB Regular',
    'FOT-\u30ed\u30c0\u30f3\u30cf\u30c3\u30d4\u30fc Pro EB',
    'FOT-\u30ed\u30c0\u30f3\u30cf\u30c3\u30d4\u30fc Pro EB Regular',
    'RodinHappyPro-EB',
    'FOT-RodinHappy Pro B',
    'FOT-RodinHappy Pro B Regular',
    'FOT-\u30ed\u30c0\u30f3\u30cf\u30c3\u30d4\u30fc Pro B',
    'FOT-\u30ed\u30c0\u30f3\u30cf\u30c3\u30d4\u30fc Pro B Regular',
    'RodinHappyPro-B',
    'FOT-RodinHappy Pro DB',
    'FOT-RodinHappy Pro DB Regular',
    'FOT-\u30ed\u30c0\u30f3\u30cf\u30c3\u30d4\u30fc Pro DB',
    'FOT-\u30ed\u30c0\u30f3\u30cf\u30c3\u30d4\u30fc Pro DB Regular',
    'RodinHappyPro-DB',
    'FOT-RodinHappy Pro M',
    'FOT-RodinHappy Pro M Regular',
    'FOT-\u30ed\u30c0\u30f3\u30cf\u30c3\u30d4\u30fc Pro M',
    'FOT-\u30ed\u30c0\u30f3\u30cf\u30c3\u30d4\u30fc Pro M Regular',
    'RodinHappyPro-M',
    'FOT-RodinHappy Pro L',
    'FOT-RodinHappy Pro L Regular',
    'FOT-\u30ed\u30c0\u30f3\u30cf\u30c3\u30d4\u30fc Pro L',
    'FOT-\u30ed\u30c0\u30f3\u30cf\u30c3\u30d4\u30fc Pro L Regular',
    'RodinHappyPro-L',
    'FOT-RodinCattleya Pro UB',
    'FOT-RodinCattleya Pro UB Regular',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30c8\u30ec\u30a2 Pro UB',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30c8\u30ec\u30a2 Pro UB Regular',
    'RodinCattleyaPro-UB',
    'FOT-RodinCattleya Pro EB',
    'FOT-RodinCattleya Pro EB Regular',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30c8\u30ec\u30a2 Pro EB',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30c8\u30ec\u30a2 Pro EB Regular',
    'RodinCattleyaPro-EB',
    'FOT-RodinCattleya Pro B',
    'FOT-RodinCattleya Pro B Regular',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30c8\u30ec\u30a2 Pro B',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30c8\u30ec\u30a2 Pro B Regular',
    'RodinCattleyaPro-B',
    'FOT-RodinCattleya Pro DB',
    'FOT-RodinCattleya Pro DB Regular',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30c8\u30ec\u30a2 Pro DB',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30c8\u30ec\u30a2 Pro DB Regular',
    'RodinCattleyaPro-DB',
    'FOT-RodinCattleya Pro M',
    'FOT-RodinCattleya Pro M Regular',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30c8\u30ec\u30a2 Pro M',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30c8\u30ec\u30a2 Pro M Regular',
    'RodinCattleyaPro-M',
    'FOT-RodinCattleya Pro L',
    'FOT-RodinCattleya Pro L Regular',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30c8\u30ec\u30a2 Pro L',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30c8\u30ec\u30a2 Pro L Regular',
    'RodinCattleyaPro-L',
    'FOT-RodinCamille Pro EB',
    'FOT-RodinCamille Pro EB Regular',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30df\u30fc\u30e6 Pro EB',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30df\u30fc\u30e6 Pro EB Regular',
    'RodinCamillePro-EB',
    'FOT-RodinCamille Pro B',
    'FOT-RodinCamille Pro B Regular',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30df\u30fc\u30e6 Pro B',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30df\u30fc\u30e6 Pro B Regular',
    'RodinCamillePro-B',
    'FOT-RodinCamille Pro DB',
    'FOT-RodinCamille Pro DB Regular',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30df\u30fc\u30e6 Pro DB',
    'FOT-\u30ed\u30c0\u30f3\u30ab\u30df\u30fc\u30e6 Pro DB Regular',
    'RodinCamillePro-DB',
    'FOT-RodinBokutoh Pro UB',
    'FOT-RodinBokutoh Pro UB Regular',
    'FOT-\u30ed\u30c0\u30f3\u58a8\u6771 Pro UB',
    'FOT-\u30ed\u30c0\u30f3\u58a8\u6771 Pro UB Regular',
    'RodinBokutohPro-UB',
    'FOT-RodinBokutoh Pro EB',
    'FOT-RodinBokutoh Pro EB Regular',
    'FOT-\u30ed\u30c0\u30f3\u58a8\u6771 Pro EB',
    'FOT-\u30ed\u30c0\u30f3\u58a8\u6771 Pro EB Regular',
    'RodinBokutohPro-EB',
    'FOT-RodinBokutoh Pro B',
    'FOT-RodinBokutoh Pro B Regular',
    'FOT-\u30ed\u30c0\u30f3\u58a8\u6771 Pro B',
    'FOT-\u30ed\u30c0\u30f3\u58a8\u6771 Pro B Regular',
    'RodinBokutohPro-B',
    'FOT-RodinBokutoh Pro DB',
    'FOT-RodinBokutoh Pro DB Regular',
    'FOT-\u30ed\u30c0\u30f3\u58a8\u6771 Pro DB',
    'FOT-\u30ed\u30c0\u30f3\u58a8\u6771 Pro DB Regular',
    'RodinBokutohPro-DB',
    'FOT-RodinBokutoh Pro M',
    'FOT-RodinBokutoh Pro M Regular',
    'FOT-\u30ed\u30c0\u30f3\u58a8\u6771 Pro M',
    'FOT-\u30ed\u30c0\u30f3\u58a8\u6771 Pro M Regular',
    'RodinBokutohPro-M',
    'FOT-RodinBokutoh Pro L',
    'FOT-RodinBokutoh Pro L Regular',
    'FOT-\u30ed\u30c0\u30f3\u58a8\u6771 Pro L',
    'FOT-\u30ed\u30c0\u30f3\u58a8\u6771 Pro L Regular',
    'RodinBokutohPro-L',
    'FOT-MatisseWakaba Pro EB',
    'FOT-MatisseWakaba Pro EB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u308f\u304b\u3070\u3084\u307e\u3068Pro EB',
    'FOT-\u30de\u30c6\u30a3\u30b9\u308f\u304b\u3070\u3084\u307e\u3068Pro EB Regular',
    'MatisseWakabaPro-EB',
    'FOT-MatisseWakaba Pro DB',
    'FOT-MatisseWakaba Pro DB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u308f\u304b\u3070\u3084\u307e\u3068Pro DB',
    'FOT-\u30de\u30c6\u30a3\u30b9\u308f\u304b\u3070\u3084\u307e\u3068Pro DB Regular',
    'MatisseWakabaPro-DB',
    'FOT-MatisseWakaba Pro M',
    'FOT-MatisseWakaba Pro M Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u308f\u304b\u3070\u3084\u307e\u3068Pro M',
    'FOT-\u30de\u30c6\u30a3\u30b9\u308f\u304b\u3070\u3084\u307e\u3068Pro M Regular',
    'MatisseWakabaPro-M',
    'FOT-MatisseV Pro UB',
    'FOT-MatisseV Pro UB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9V Pro UB',
    'FOT-\u30de\u30c6\u30a3\u30b9V Pro UB Regular',
    'MatisseVPro-UB',
    'FOT-MatisseV Pro EB',
    'FOT-MatisseV Pro EB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9V Pro EB',
    'FOT-\u30de\u30c6\u30a3\u30b9V Pro EB Regular',
    'MatisseVPro-EB',
    'FOT-MatisseV Pro B',
    'FOT-MatisseV Pro B Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9V Pro B',
    'FOT-\u30de\u30c6\u30a3\u30b9V Pro B Regular',
    'MatisseVPro-B',
    'FOT-MatisseV Pro DB',
    'FOT-MatisseV Pro DB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9V Pro DB',
    'FOT-\u30de\u30c6\u30a3\u30b9V Pro DB Regular',
    'MatisseVPro-DB',
    'FOT-MatisseV Pro M',
    'FOT-MatisseV Pro M Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9V Pro M',
    'FOT-\u30de\u30c6\u30a3\u30b9V Pro M Regular',
    'MatisseVPro-M',
    'FOT-MatisseV Pro L',
    'FOT-MatisseV Pro L Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9V Pro L',
    'FOT-\u30de\u30c6\u30a3\u30b9V Pro L Regular',
    'MatisseVPro-L',
    'FOT-MatisseMinori Pro EB',
    'FOT-MatisseMinori Pro EB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u307f\u306e\u308a\u3084\u307e\u3068Pro EB',
    'FOT-\u30de\u30c6\u30a3\u30b9\u307f\u306e\u308a\u3084\u307e\u3068Pro EB Regular',
    'MatisseMinoriPro-EB',
    'FOT-MatisseMinori Pro B',
    'FOT-MatisseMinori Pro B Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u307f\u306e\u308a\u3084\u307e\u3068Pro B',
    'FOT-\u30de\u30c6\u30a3\u30b9\u307f\u306e\u308a\u3084\u307e\u3068Pro B Regular',
    'MatisseMinoriPro-B',
    'FOT-MatisseMinori Pro DB',
    'FOT-MatisseMinori Pro DB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u307f\u306e\u308a\u3084\u307e\u3068Pro DB',
    'FOT-\u30de\u30c6\u30a3\u30b9\u307f\u306e\u308a\u3084\u307e\u3068Pro DB Regular',
    'MatisseMinoriPro-DB',
    'FOT-MatisseMinori Pro M',
    'FOT-MatisseMinori Pro M Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u307f\u306e\u308a\u3084\u307e\u3068Pro M',
    'FOT-\u30de\u30c6\u30a3\u30b9\u307f\u306e\u308a\u3084\u307e\u3068Pro M Regular',
    'MatisseMinoriPro-M',
    'FOT-MatisseHatsuhi Pro EB',
    'FOT-MatisseHatsuhi Pro EB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u306f\u3064\u3072\u3084\u307e\u3068Pro EB',
    'FOT-\u30de\u30c6\u30a3\u30b9\u306f\u3064\u3072\u3084\u307e\u3068Pro EB Regular',
    'MatisseHatsuhiPro-EB',
    'FOT-MatisseHatsuhi Pro B',
    'FOT-MatisseHatsuhi Pro B Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u306f\u3064\u3072\u3084\u307e\u3068Pro B',
    'FOT-\u30de\u30c6\u30a3\u30b9\u306f\u3064\u3072\u3084\u307e\u3068Pro B Regular',
    'MatisseHatsuhiPro-B',
    'FOT-MatisseHatsuhi Pro DB',
    'FOT-MatisseHatsuhi Pro DB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u306f\u3064\u3072\u3084\u307e\u3068Pro DB',
    'FOT-\u30de\u30c6\u30a3\u30b9\u306f\u3064\u3072\u3084\u307e\u3068Pro DB Regular',
    'MatisseHatsuhiPro-DB',
    'FOT-MatisseHatsuhi Pro M',
    'FOT-MatisseHatsuhi Pro M Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u306f\u3064\u3072\u3084\u307e\u3068Pro M',
    'FOT-\u30de\u30c6\u30a3\u30b9\u306f\u3064\u3072\u3084\u307e\u3068Pro M Regular',
    'MatisseHatsuhiPro-M',
    'FOT-MatisseEleganto Pro UB',
    'FOT-MatisseEleganto Pro UB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u3048\u308c\u304c\u3093\u3068 Pro UB',
    'FOT-\u30de\u30c6\u30a3\u30b9\u3048\u308c\u304c\u3093\u3068 Pro UB Regular',
    'MatisseElegantoPro-UB',
    'FOT-MatisseEleganto Pro EB',
    'FOT-MatisseEleganto Pro EB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u3048\u308c\u304c\u3093\u3068 Pro EB',
    'FOT-\u30de\u30c6\u30a3\u30b9\u3048\u308c\u304c\u3093\u3068 Pro EB Regular',
    'MatisseElegantoPro-EB',
    'FOT-MatisseEleganto Pro B',
    'FOT-MatisseEleganto Pro B Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u3048\u308c\u304c\u3093\u3068 Pro B',
    'FOT-\u30de\u30c6\u30a3\u30b9\u3048\u308c\u304c\u3093\u3068 Pro B Regular',
    'MatisseElegantoPro-B',
    'FOT-MatisseEleganto Pro DB',
    'FOT-MatisseEleganto Pro DB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u3048\u308c\u304c\u3093\u3068 Pro DB',
    'FOT-\u30de\u30c6\u30a3\u30b9\u3048\u308c\u304c\u3093\u3068 Pro DB Regular',
    'MatisseElegantoPro-DB',
    'FOT-MatisseEleganto Pro M',
    'FOT-MatisseEleganto Pro M Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u3048\u308c\u304c\u3093\u3068 Pro M',
    'FOT-\u30de\u30c6\u30a3\u30b9\u3048\u308c\u304c\u3093\u3068 Pro M Regular',
    'MatisseElegantoPro-M',
    'CezanneBokutohPro-EB',
    'FOT-CezanneBokutoh Pro EB',
    'FOT-CezanneBokutoh Pro EB Regular',
    'FOT-\u30bb\u30b6\u30f3\u30cc\u58a8\u6771 Pro EB',
    'FOT-\u30bb\u30b6\u30f3\u30cc\u58a8\u6771 Pro EB Regular',
    'CezanneBokutohPro-B',
    'FOT-CezanneBokutoh Pro B',
    'FOT-CezanneBokutoh Pro B Regular',
    'FOT-\u30bb\u30b6\u30f3\u30cc\u58a8\u6771 Pro B',
    'FOT-\u30bb\u30b6\u30f3\u30cc\u58a8\u6771 Pro B Regular',
    'CezanneBokutohPro-DB',
    'FOT-CezanneBokutoh Pro DB',
    'FOT-CezanneBokutoh Pro DB Regular',
    'FOT-\u30bb\u30b6\u30f3\u30cc\u58a8\u6771 Pro DB',
    'FOT-\u30bb\u30b6\u30f3\u30cc\u58a8\u6771 Pro DB Regular',
    'CezanneBokutohPro-M',
    'FOT-CezanneBokutoh Pro M',
    'FOT-CezanneBokutoh Pro M Regular',
    'FOT-\u30bb\u30b6\u30f3\u30cc\u58a8\u6771 Pro M',
    'FOT-\u30bb\u30b6\u30f3\u30cc\u58a8\u6771 Pro M Regular',
    'FOT-Utrillo Pro DB',
    'FOT-Utrillo Pro DB Regular',
    'FOT-\u30e6\u30c8\u30ea\u30ed Pro DB',
    'FOT-\u30e6\u30c8\u30ea\u30ed Pro DB Regular',
    'UtrilloPro-DB',
    'FOT-Utrillo Pro M',
    'FOT-Utrillo Pro M Regular',
    'FOT-\u30e6\u30c8\u30ea\u30ed Pro M',
    'FOT-\u30e6\u30c8\u30ea\u30ed Pro M Regular',
    'UtrilloPro-M',
    'FOT-Seurat ProN  UB',
    'FOT-Seurat ProN  UB Regular',
    'FOT-\u30b9\u30fc\u30e9 ProN UB',
    'FOT-\u30b9\u30fc\u30e9 ProN UB Regular',
    'SeuratProN-UB',
    'FOT-Seurat ProN  B',
    'FOT-Seurat ProN  B Regular',
    'FOT-\u30b9\u30fc\u30e9 ProN B',
    'FOT-\u30b9\u30fc\u30e9 ProN B Regular',
    'SeuratProN-B',
    'FOT-Seurat ProN  DB',
    'FOT-Seurat ProN  DB Regular',
    'FOT-\u30b9\u30fc\u30e9 ProN DB',
    'FOT-\u30b9\u30fc\u30e9 ProN DB Regular',
    'SeuratProN-DB',
    'FOT-Seurat ProN  M',
    'FOT-Seurat ProN  M Regular',
    'FOT-\u30b9\u30fc\u30e9 ProN M',
    'FOT-\u30b9\u30fc\u30e9 ProN M Regular',
    'SeuratProN-M',
    'FOT-Seurat ProN  L',
    'FOT-Seurat ProN  L Regular',
    'FOT-\u30b9\u30fc\u30e9 ProN L',
    'FOT-\u30b9\u30fc\u30e9 ProN L Regular',
    'SeuratProN-L',
    'FOT-Seurat Pro UB',
    'FOT-Seurat Pro UB Regular',
    'FOT-\u30b9\u30fc\u30e9 Pro UB',
    'FOT-\u30b9\u30fc\u30e9 Pro UB Regular',
    'SeuratPro-UB',
    'FOT-Seurat Pro EB',
    'FOT-Seurat Pro EB Regular',
    'FOT-\u30b9\u30fc\u30e9 Pro EB',
    'FOT-\u30b9\u30fc\u30e9 Pro EB Regular',
    'SeuratPro-EB',
    'FOT-Seurat Pro B',
    'FOT-Seurat Pro B Regular',
    'FOT-\u30b9\u30fc\u30e9 Pro B',
    'FOT-\u30b9\u30fc\u30e9 Pro B Regular',
    'SeuratPro-B',
    'FOT-Seurat Pro DB',
    'FOT-Seurat Pro DB Regular',
    'FOT-\u30b9\u30fc\u30e9 Pro DB',
    'FOT-\u30b9\u30fc\u30e9 Pro DB Regular',
    'SeuratPro-DB',
    'FOT-Seurat Pro M',
    'FOT-Seurat Pro M Regular',
    'FOT-\u30b9\u30fc\u30e9 Pro M',
    'FOT-\u30b9\u30fc\u30e9 Pro M Regular',
    'SeuratPro-M',
    'FOT-Seurat Pro L',
    'FOT-Seurat Pro L Regular',
    'FOT-\u30b9\u30fc\u30e9 Pro L',
    'FOT-\u30b9\u30fc\u30e9 Pro L Regular',
    'SeuratPro-L',
    'FOT-Rodin ProN  UB',
    'FOT-Rodin ProN  UB Regular',
    'FOT-\u30ed\u30c0\u30f3 ProN UB',
    'FOT-\u30ed\u30c0\u30f3 ProN UB Regular',
    'RodinProN-UB',
    'FOT-Rodin ProN  EB',
    'FOT-Rodin ProN  EB Regular',
    'FOT-\u30ed\u30c0\u30f3 ProN EB',
    'FOT-\u30ed\u30c0\u30f3 ProN EB Regular',
    'RodinProN-EB',
    'FOT-Rodin ProN  B',
    'FOT-Rodin ProN  B Regular',
    'FOT-\u30ed\u30c0\u30f3 ProN B',
    'FOT-\u30ed\u30c0\u30f3 ProN B Regular',
    'RodinProN-B',
    'FOT-Rodin ProN  DB',
    'FOT-Rodin ProN  DB Regular',
    'FOT-\u30ed\u30c0\u30f3 ProN DB',
    'FOT-\u30ed\u30c0\u30f3 ProN DB Regular',
    'RodinProN-DB',
    'FOT-Rodin ProN  M',
    'FOT-Rodin ProN  M Regular',
    'FOT-\u30ed\u30c0\u30f3 ProN M',
    'FOT-\u30ed\u30c0\u30f3 ProN M Regular',
    'RodinProN-M',
    'FOT-Rodin ProN  L',
    'FOT-Rodin ProN  L Regular',
    'FOT-\u30ed\u30c0\u30f3 ProN L',
    'FOT-\u30ed\u30c0\u30f3 ProN L Regular',
    'RodinProN-L',
    'FOT-Rodin Pro UB',
    'FOT-Rodin Pro UB Regular',
    'FOT-\u30ed\u30c0\u30f3 Pro UB',
    'FOT-\u30ed\u30c0\u30f3 Pro UB Regular',
    'RodinPro-UB',
    'FOT-Rodin Pro EB',
    'FOT-Rodin Pro EB Regular',
    'FOT-\u30ed\u30c0\u30f3 Pro EB',
    'FOT-\u30ed\u30c0\u30f3 Pro EB Regular',
    'RodinPro-EB',
    'FOT-Rodin Pro B',
    'FOT-Rodin Pro B Regular',
    'FOT-\u30ed\u30c0\u30f3 Pro B',
    'FOT-\u30ed\u30c0\u30f3 Pro B Regular',
    'RodinPro-B',
    'FOT-Rodin Pro DB',
    'FOT-Rodin Pro DB Regular',
    'FOT-\u30ed\u30c0\u30f3 Pro DB',
    'FOT-\u30ed\u30c0\u30f3 Pro DB Regular',
    'RodinPro-DB',
    'FOT-Rodin Pro M',
    'FOT-Rodin Pro M Regular',
    'FOT-\u30ed\u30c0\u30f3 Pro M',
    'FOT-\u30ed\u30c0\u30f3 Pro M Regular',
    'RodinPro-M',
    'FOT-Rodin Pro L',
    'FOT-Rodin Pro L Regular',
    'FOT-\u30ed\u30c0\u30f3 Pro L',
    'FOT-\u30ed\u30c0\u30f3 Pro L Regular',
    'RodinPro-L',
    'FOT-NewRodin ProN  UB',
    'FOT-NewRodin ProN  UB Regular',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 ProN UB',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 ProN UB Regular',
    'NewRodinProN-UB',
    'FOT-NewRodin ProN  EB',
    'FOT-NewRodin ProN  EB Regular',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 ProN EB',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 ProN EB Regular',
    'NewRodinProN-EB',
    'FOT-NewRodin ProN  B',
    'FOT-NewRodin ProN  B Regular',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 ProN B',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 ProN B Regular',
    'NewRodinProN-B',
    'FOT-NewRodin ProN  DB',
    'FOT-NewRodin ProN  DB Regular',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 ProN DB',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 ProN DB Regular',
    'NewRodinProN-DB',
    'FOT-NewRodin ProN  M',
    'FOT-NewRodin ProN  M Regular',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 ProN M',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 ProN M Regular',
    'NewRodinProN-M',
    'FOT-NewRodin ProN  L',
    'FOT-NewRodin ProN  L Regular',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 ProN L',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 ProN L Regular',
    'NewRodinProN-L',
    'FOT-NewRodin Pro UB',
    'FOT-NewRodin Pro UB Regular',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 Pro UB',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 Pro UB Regular',
    'NewRodinPro-UB',
    'FOT-NewRodin Pro EB',
    'FOT-NewRodin Pro EB Regular',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 Pro EB',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 Pro EB Regular',
    'NewRodinPro-EB',
    'FOT-NewRodin Pro B',
    'FOT-NewRodin Pro B Regular',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 Pro B',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 Pro B Regular',
    'NewRodinPro-B',
    'FOT-NewRodin Pro DB',
    'FOT-NewRodin Pro DB Regular',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 Pro DB',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 Pro DB Regular',
    'NewRodinPro-DB',
    'FOT-NewRodin Pro M',
    'FOT-NewRodin Pro M Regular',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 Pro M',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 Pro M Regular',
    'NewRodinPro-M',
    'FOT-NewRodin Pro L',
    'FOT-NewRodin Pro L Regular',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 Pro L',
    'FOT-\u30cb\u30e5\u30fc\u30ed\u30c0\u30f3 Pro L Regular',
    'NewRodinPro-L',
    'FOT-NewCezanne ProN  EB',
    'FOT-NewCezanne ProN  EB Regular',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc ProN EB',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc ProN EB Regular',
    'NewCezanneProN-EB',
    'FOT-NewCezanne ProN  B',
    'FOT-NewCezanne ProN  B Regular',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc ProN B',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc ProN B Regular',
    'NewCezanneProN-B',
    'FOT-NewCezanne ProN  DB',
    'FOT-NewCezanne ProN  DB Regular',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc ProN DB',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc ProN DB Regular',
    'NewCezanneProN-DB',
    'FOT-NewCezanne ProN  M',
    'FOT-NewCezanne ProN  M Regular',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc ProN M',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc ProN M Regular',
    'NewCezanneProN-M',
    'FOT-NewCezanne Pro EB',
    'FOT-NewCezanne Pro EB Regular',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc Pro EB',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc Pro EB Regular',
    'NewCezannePro-EB',
    'FOT-NewCezanne Pro B',
    'FOT-NewCezanne Pro B Regular',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc Pro B',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc Pro B Regular',
    'NewCezannePro-B',
    'FOT-NewCezanne Pro DB',
    'FOT-NewCezanne Pro DB Regular',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc Pro DB',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc Pro DB Regular',
    'NewCezannePro-DB',
    'FOT-NewCezanne Pro M',
    'FOT-NewCezanne Pro M Regular',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc Pro M',
    'FOT-\u30cb\u30e5\u30fc\u30bb\u30b6\u30f3\u30cc Pro M Regular',
    'NewCezannePro-M',
    'FOT-Matisse ProN  UB',
    'FOT-Matisse ProN  UB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9 ProN UB',
    'FOT-\u30de\u30c6\u30a3\u30b9 ProN UB Regular',
    'MatisseProN-UB',
    'FOT-Matisse ProN  EB',
    'FOT-Matisse ProN  EB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9 ProN EB',
    'FOT-\u30de\u30c6\u30a3\u30b9 ProN EB Regular',
    'MatisseProN-EB',
    'FOT-Matisse ProN  B',
    'FOT-Matisse ProN  B Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9 ProN B',
    'FOT-\u30de\u30c6\u30a3\u30b9 ProN B Regular',
    'MatisseProN-B',
    'FOT-Matisse ProN  DB',
    'FOT-Matisse ProN  DB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9 ProN DB',
    'FOT-\u30de\u30c6\u30a3\u30b9 ProN DB Regular',
    'MatisseProN-DB',
    'FOT-Matisse ProN  M',
    'FOT-Matisse ProN  M Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9 ProN M',
    'FOT-\u30de\u30c6\u30a3\u30b9 ProN M Regular',
    'MatisseProN-M',
    'FOT-Matisse ProN  L',
    'FOT-Matisse ProN  L Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9 ProN L',
    'FOT-\u30de\u30c6\u30a3\u30b9 ProN L Regular',
    'MatisseProN-L',
    'FOT-Matisse Pro UB',
    'FOT-Matisse Pro UB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9 Pro UB',
    'FOT-\u30de\u30c6\u30a3\u30b9 Pro UB Regular',
    'MatissePro-UB',
    'FOT-Matisse Pro EB',
    'FOT-Matisse Pro EB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9 Pro EB',
    'FOT-\u30de\u30c6\u30a3\u30b9 Pro EB Regular',
    'MatissePro-EB',
    'FOT-Matisse Pro DB',
    'FOT-Matisse Pro DB Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9 Pro DB',
    'FOT-\u30de\u30c6\u30a3\u30b9 Pro DB Regular',
    'MatissePro-DB',
    'FOT-Matisse Pro B',
    'FOT-Matisse Pro B Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9 Pro B',
    'FOT-\u30de\u30c6\u30a3\u30b9 Pro B Regular',
    'MatissePro-B',
    'FOT-Matisse Pro L',
    'FOT-Matisse Pro L Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9 Pro L',
    'FOT-\u30de\u30c6\u30a3\u30b9 Pro L Regular',
    'MatissePro-L',
    'FOT-Matisse Pro M',
    'FOT-Matisse Pro M Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9 Pro M',
    'FOT-\u30de\u30c6\u30a3\u30b9 Pro M Regular',
    'MatissePro-M',
    'FOT-Klee Pro DB',
    'FOT-Klee Pro DB Regular',
    'FOT-\u30af\u30ec\u30fc Pro DB',
    'FOT-\u30af\u30ec\u30fc Pro DB Regular',
    'KleePro-DB',
    'FOT-Klee Pro M',
    'FOT-Klee Pro M Regular',
    'FOT-\u30af\u30ec\u30fc Pro M',
    'FOT-\u30af\u30ec\u30fc Pro M Regular',
    'KleePro-M',
    'FOT-Greco Std B',
    'FOT-Greco Std B Regular',
    'FOT-\u30b0\u30ec\u30b3 Std B',
    'FOT-\u30b0\u30ec\u30b3 Std B Regular',
    'GrecoStd-B',
    'FOT-Greco Std DB',
    'FOT-Greco Std DB Regular',
    'FOT-\u30b0\u30ec\u30b3 Std DB',
    'FOT-\u30b0\u30ec\u30b3 Std DB Regular',
    'GrecoStd-DB',
    'FOT-Greco Std M',
    'FOT-Greco Std M Regular',
    'FOT-\u30b0\u30ec\u30b3 Std M',
    'FOT-\u30b0\u30ec\u30b3 Std M Regular',
    'GrecoStd-M',
    'FOT-GMaruGo Pro B',
    'FOT-GMaruGo Pro B Regular',
    'FOT-\u5b66\u53c2\u4e38\u30b4 Pro B',
    'FOT-\u5b66\u53c2\u4e38\u30b4 Pro B Regular',
    'GMaruGoPro-B',
    'FOT-GMaruGo Pro DB',
    'FOT-GMaruGo Pro DB Regular',
    'FOT-\u5b66\u53c2\u4e38\u30b4 Pro DB',
    'FOT-\u5b66\u53c2\u4e38\u30b4 Pro DB Regular',
    'GMaruGoPro-DB',
    'FOT-GMaruGo Pro M',
    'FOT-GMaruGo Pro M Regular',
    'FOT-\u5b66\u53c2\u4e38\u30b4 Pro M',
    'FOT-\u5b66\u53c2\u4e38\u30b4 Pro M Regular',
    'GMaruGoPro-M',
    'CezanneProN-EB',
    'FOT-Cezanne ProN  EB',
    'FOT-Cezanne ProN  EB Regular',
    'FOT-\u30bb\u30b6\u30f3\u30cc ProN EB',
    'FOT-\u30bb\u30b6\u30f3\u30cc ProN EB Regular',
    'CezanneProN-B',
    'FOT-Cezanne ProN  B',
    'FOT-Cezanne ProN  B Regular',
    'FOT-\u30bb\u30b6\u30f3\u30cc ProN B',
    'FOT-\u30bb\u30b6\u30f3\u30cc ProN B Regular',
    'CezanneProN-DB',
    'FOT-Cezanne ProN  DB',
    'FOT-Cezanne ProN  DB Regular',
    'FOT-\u30bb\u30b6\u30f3\u30cc ProN DB',
    'FOT-\u30bb\u30b6\u30f3\u30cc ProN DB Regular',
    'CezanneProN-M',
    'FOT-Cezanne ProN  M',
    'FOT-Cezanne ProN  M Regular',
    'FOT-\u30bb\u30b6\u30f3\u30cc ProN M',
    'FOT-\u30bb\u30b6\u30f3\u30cc ProN M Regular',
    'CezannePro-EB',
    'FOT-Cezanne Pro EB',
    'FOT-Cezanne Pro EB Regular',
    'FOT-\u30bb\u30b6\u30f3\u30cc Pro EB',
    'FOT-\u30bb\u30b6\u30f3\u30cc Pro EB Regular',
    'CezannePro-B',
    'FOT-Cezanne Pro B',
    'FOT-Cezanne Pro B Regular',
    'FOT-\u30bb\u30b6\u30f3\u30cc Pro B',
    'FOT-\u30bb\u30b6\u30f3\u30cc Pro B Regular',
    'CezannePro-DB',
    'FOT-Cezanne Pro DB',
    'FOT-Cezanne Pro DB Regular',
    'FOT-\u30bb\u30b6\u30f3\u30cc Pro DB',
    'FOT-\u30bb\u30b6\u30f3\u30cc Pro DB Regular',
    'CezannePro-M',
    'FOT-Cezanne Pro M',
    'FOT-Cezanne Pro M Regular',
    'FOT-\u30bb\u30b6\u30f3\u30cc Pro M',
    'FOT-\u30bb\u30b6\u30f3\u30cc Pro M Regular',
    'FOT-TsukuGo Pro H',
    'FOT-TsukuGo Pro H Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro H',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro H Regular',
    'TsukuGoPro-H',
    'FOT-TsukuGo Pro E',
    'FOT-TsukuGo Pro E Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro E',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro E Regular',
    'TsukuGoPro-E',
    'FOT-TsukuGo Pro B',
    'FOT-TsukuGo Pro B Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro B',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro B Regular',
    'TsukuGoPro-B',
    'FOT-TelopMin ProN  H',
    'FOT-TelopMin ProN  H Regular',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d ProN H',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d ProN H Regular',
    'TelopMinProN-HV',
    'FOT-TelopMin ProN  E',
    'FOT-TelopMin ProN  E Regular',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d ProN E',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d ProN E Regular',
    'TelopMinProN-E',
    'FOT-TelopMin ProN  D',
    'FOT-TelopMin ProN  D Regular',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d ProN D',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d ProN D Regular',
    'TelopMinProN-D',
    'FOT-TelopMin ProN  B',
    'FOT-TelopMin ProN  B Regular',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d ProN B',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d ProN B Regular',
    'TelopMinProN-B',
    'FOT-TelopMin Pro H',
    'FOT-TelopMin Pro H Regular',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d Pro H',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d Pro H Regular',
    'TelopMinPro-HV',
    'FOT-TelopMin Pro E',
    'FOT-TelopMin Pro E Regular',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d Pro E',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d Pro E Regular',
    'TelopMinPro-E',
    'FOT-TelopMin Pro D',
    'FOT-TelopMin Pro D Regular',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d Pro D',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d Pro D Regular',
    'TelopMinPro-D',
    'FOT-TelopMin Pro B',
    'FOT-TelopMin Pro B Regular',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d Pro B',
    'FOT-\u30c6\u30ed\u30c3\u30d7\u660e\u671d Pro B Regular',
    'TelopMinPro-B',
    'DotGothic16Std-M',
    'FOT-DotGothic16 Std M',
    'FOT-DotGothic16 Std M Regular',
    'FOT-\u30c9\u30c3\u30c8\u30b4\u30b7\u30c3\u30af 16 Std M',
    'FOT-\u30c9\u30c3\u30c8\u30b4\u30b7\u30c3\u30af 16 Std M Regular',
    'DotGothic12Std-M',
    'FOT-DotGothic12 Std M',
    'FOT-DotGothic12 Std M Regular',
    'FOT-\u30c9\u30c3\u30c8\u30b4\u30b7\u30c3\u30af 12 Std M',
    'FOT-\u30c9\u30c3\u30c8\u30b4\u30b7\u30c3\u30af 12 Std M Regular',
    'DotMincho16Std-M',
    'FOT-DotMincho16 Std M',
    'FOT-DotMincho16 Std M Regular',
    'FOT-\u30c9\u30c3\u30c8\u660e\u671d 16 Std M',
    'FOT-\u30c9\u30c3\u30c8\u660e\u671d 16 Std M Regular',
    'DotMincho12Std-M',
    'FOT-DotMincho12 Std M',
    'FOT-DotMincho12 Std M Regular',
    'FOT-\u30c9\u30c3\u30c8\u660e\u671d 12 Std M',
    'FOT-\u30c9\u30c3\u30c8\u660e\u671d 12 Std M Regular',
    'FOT-TsukuMin Pr5N HV',
    'FOT-TsukuMin Pr5N HV Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N H',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N H Regular',
    'TsukuMinPr5N-HV',
    'FOT-TsukuMin Pr5N E',
    'FOT-TsukuMin Pr5N E Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N E',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N E Regular',
    'TsukuMinPr5N-E',
    'FOT-TsukuMin Pr5N B',
    'FOT-TsukuMin Pr5N B Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N B',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5N B Regular',
    'TsukuMinPr5N-B',
    'FOT-TsukuGo Pr5N D',
    'FOT-TsukuGo Pr5N D Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5N D',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5N D Regular',
    'TsukuGoPr5N-D',
    'FOT-TsukuGo Pr5N M',
    'FOT-TsukuGo Pr5N M Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5N M',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5N M Regular',
    'TsukuGoPr5N-M',
    'FOT-TsukuGo Pr5N R',
    'FOT-TsukuGo Pr5N R Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5N R',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5N R Regular',
    'TsukuGoPr5N-R',
    'FOT-TsukuGo Pr5N L',
    'FOT-TsukuGo Pr5N L Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5N L',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5N L Regular',
    'TsukuGoPr5N-L',
    'FOT-TsukuMin Pr5 HV',
    'FOT-TsukuMin Pr5 HV Regular',
    'FOT-TsukuMin Pro H',
    'FOT-TsukuMin Pro H Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 H',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 H Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro H',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro H Regular',
    'TsukuMinPr5-HV',
    'TsukuMinPro-HV',
    'FOT-TsukuMin Pr5 E',
    'FOT-TsukuMin Pr5 E Regular',
    'FOT-TsukuMin Pro E',
    'FOT-TsukuMin Pro E Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 E',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 E Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro E',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro E Regular',
    'TsukuMinPr5-E',
    'TsukuMinPro-E',
    'FOT-TsukuMin Pr5 B',
    'FOT-TsukuMin Pr5 B Regular',
    'FOT-TsukuMin Pro B',
    'FOT-TsukuMin Pro B Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 B',
    'FOT-\u7b51\u7d2b\u660e\u671d Pr5 B Regular',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro B',
    'FOT-\u7b51\u7d2b\u660e\u671d Pro B Regular',
    'TsukuMinPr5-B',
    'TsukuMinPro-B',
    'FOT-TsukuGo Pr5 D',
    'FOT-TsukuGo Pr5 D Regular',
    'FOT-TsukuGo Pro D',
    'FOT-TsukuGo Pro D Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5 D',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5 D Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro D',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro D Regular',
    'TsukuGoPr5-D',
    'TsukuGoPro-D',
    'vultest',
    'FOT-TsukuGo Pr5 M',
    'FOT-TsukuGo Pr5 M Regular',
    'FOT-TsukuGo Pro M',
    'FOT-TsukuGo Pro M Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5 M',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5 M Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro M',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro M Regular',
    'TsukuGoPr5-M',
    'TsukuGoPro-M',
    'FOT-TsukuGo Pr5 R',
    'FOT-TsukuGo Pr5 R Regular',
    'FOT-TsukuGo Pro R',
    'FOT-TsukuGo Pro R Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5 R',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5 R Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro R',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro R Regular',
    'TsukuGoPr5-R',
    'TsukuGoPro-R',
    'FOT-TsukuGo Pr5 L',
    'FOT-TsukuGo Pr5 L Regular',
    'FOT-TsukuGo Pro L',
    'FOT-TsukuGo Pro L Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5 L',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pr5 L Regular',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro L',
    'FOT-\u7b51\u7d2b\u30b4\u30b7\u30c3\u30af Pro L Regular',
    'TsukuGoPr5-L',
    'TsukuGoPro-L',
    'FOT-MatisseWakaba Pro B',
    'FOT-MatisseWakaba Pro B Regular',
    'FOT-\u30de\u30c6\u30a3\u30b9\u308f\u304b\u3070\u3084\u307e\u3068Pro B',
    'FOT-\u30de\u30c6\u30a3\u30b9\u308f\u304b\u3070\u3084\u307e\u3068Pro B Regular',
    'MatisseWakabaPro-B',
    'Yuji-boku',
    '\u4f51\u5b57\uff0f\u6734',
    'Yuji-mai',
    '\u4f51\u5b57\uff0f\u821e',
    'Yuji-syuku',
    '\u4f51\u5b57\uff0f\u8085',
    'MarumaruGothicC&Lr',
    'MarumaruGothicC&Lr Regular',
    '\u4e38\u4e38gothic C&Lr',
    'MarumaruGothicC&Sr',
    'MarumaruGothicC&Sr Regular',
    '\u4e38\u4e38gothic C&Sr',
    'MarumaruGothicB&Lr',
    'MarumaruGothicB&Lr Regular',
    '\u4e38\u4e38gothic B&Lr',
    'MarumaruGothicB&Sr',
    'MarumaruGothicB&Sr Regular',
    '\u4e38\u4e38gothic B&Sr',
    'MarumaruGothicA&Lr',
    'MarumaruGothicA&Lr Regular',
    '\u4e38\u4e38gothic A&Lr',
    'MarumaruGothicA&Sr',
    'MarumaruGothicA&Sr Regular',
    '\u4e38\u4e38gothic A&Sr',
    'Iroha 32sakura',
    'iroha-32sakura',
    'iroha-32sakura Regular',
    'Iroha 31nire',
    'iroha-31nire',
    'iroha-31nire Regular',
    'Iroha 30momiji',
    'iroha-30momiji',
    'iroha-30momiji Regular',
    'Iroha 29ume',
    'iroha-29ume',
    'iroha-29ume Regular',
    'Iroha 28kiri',
    'iroha-28kiri',
    'iroha-28kiri Regular',
    'Iroha 27keyaki',
    'iroha-27keyaki',
    'iroha-27keyaki Regular',
    'Iroha 26tubaki',
    'iroha-26tubaki',
    'iroha-26tubaki Regular',
    'Iroha 25icho',
    'iroha-25icho',
    'iroha-25icho Regular',
    'Iroha 24matu',
    'iroha-24matu',
    'iroha-24matu Regular',
    'Iroha 23kaede',
    'iroha-23kaede',
    'iroha-23kaede Regular',
    'Iroha 22momi',
    'iroha-22momi',
    'iroha-22momi Regular',
    'Iroha 21popura',
    'iroha-21popura',
    'iroha-21popura Regular',
    'Yamamotoan',
    'Yamamotoan Regular',
    '\u5c71\u672c\u5eb5 StdN R',
    'MaruminYoshino',
    'MaruminYoshino Regular',
    '\u4e38\u660eYoshino',
    'MaruminTikuma',
    'MaruminTikuma Regular',
    '\u4e38\u660eTikuma',
    'MaruminShinano',
    'MaruminShinano Regular',
    '\u4e38\u660eShinano',
    'MaruminKiso',
    'MaruminKiso Regular',
    '\u4e38\u660eKiso',
    'MaruminKatura',
    'MaruminKatura Regular',
    '\u4e38\u660eKatura',
    'MaruminFuji',
    'MaruminFuji Regular',
    '\u4e38\u660eFuji',
    'MaruminOld',
    'MaruminOld Regular',
    '\u4e38\u660e\u30aa\u30fc\u30eb\u30c9',
    'HOT-Kakukuzusishu Std B',
    'HOT-Kakukuzusishu Std B Regular',
    'HOT-\u767d\u821f\u89d2\u5d29\u6731\u6587 Std B',
    'HOT-\u767d\u821f\u89d2\u5d29\u6731\u6587 Std B Regular',
    'KakukuzusishuStd-B',
    'HOT-Kakukuzusihaku Std B',
    'HOT-Kakukuzusihaku Std B Regular',
    'HOT-\u767d\u821f\u89d2\u5d29\u767d\u6587 Std B',
    'HOT-\u767d\u821f\u89d2\u5d29\u767d\u6587 Std B Regular',
    'KakukuzusihakuStd-B',
    'HOT-KujotenR Std L',
    'HOT-KujotenR Std L Regular',
    'HOT-\u767d\u821f\u4e5d\u7573\u7bc6\u30e9\u30d5 Std L',
    'HOT-\u767d\u821f\u4e5d\u7573\u7bc6\u30e9\u30d5 Std L Regular',
    'KujotenRStd-L',
    'HOT-Kujotenhoso Std EL',
    'HOT-Kujotenhoso Std EL Regular',
    'HOT-\u767d\u821f\u4e5d\u7573\u7bc6\u7d30 Std EL',
    'HOT-\u767d\u821f\u4e5d\u7573\u7bc6\u7d30 Std EL Regular',
    'KujotenhosoStd-EL',
    'HOT-Tenkoin Std M',
    'HOT-Tenkoin Std M Regular',
    'HOT-\u767d\u821f\u7bc6\u53e4\u5370 Std M',
    'HOT-\u767d\u821f\u7bc6\u53e4\u5370 Std M Regular',
    'TenkoinStd-M',
    'FTenshoStd-D',
    'HOT-FTensho Std D',
    'HOT-FTensho Std D Regular',
    'HOT-\u767d\u821f\u592a\u7bc6\u66f8 Std D',
    'HOT-\u767d\u821f\u592a\u7bc6\u66f8 Std D Regular',
    'HOT-Tensho Std M',
    'HOT-Tensho Std M Regular',
    'HOT-\u767d\u821f\u7bc6\u66f8 Std M',
    'HOT-\u767d\u821f\u7bc6\u66f8 Std M Regular',
    'TenshoStd-M',
    'HOT-HTensho Std L',
    'HOT-HTensho Std L Regular',
    'HOT-\u767d\u821f\u7d30\u7bc6\u66f8 Std L',
    'HOT-\u767d\u821f\u7d30\u7bc6\u66f8 Std L Regular',
    'HTenshoStd-L',
    'HOT-Syoten Std L',
    'HOT-Syoten Std L Regular',
    'HOT-\u767d\u821f\u5c0f\u7bc6 Std L',
    'HOT-\u767d\u821f\u5c0f\u7bc6 Std L Regular',
    'SyotenStd-L',
    'HOT-Koukotsu Std M',
    'HOT-Koukotsu Std M Regular',
    'HOT-\u767d\u821f\u7532\u9aa8 Std M',
    'HOT-\u767d\u821f\u7532\u9aa8 Std M Regular',
    'KoukotsuStd-M',
    'HOT-Ohige115 Std H',
    'HOT-Ohige115 Std H Regular',
    'HOT-\u5927\u9aed115 Std H',
    'HOT-\u5927\u9aed115 Std H Regular',
    'Ohige115Std-H',
    'HOT-Ohige113 Std H',
    'HOT-Ohige113 Std H Regular',
    'HOT-\u5927\u9aed113 Std H',
    'HOT-\u5927\u9aed113 Std H Regular',
    'Ohige113Std-H',
    'HOT-KyoMurasaki Std E',
    'HOT-KyoMurasaki Std E Regular',
    'HOT-\u4eac\u7d2b Std E',
    'HOT-\u4eac\u7d2b Std E Regular',
    'KyoMurasakiStd-E',
    'HOT-Kintoki Std U',
    'HOT-Kintoki Std U Regular',
    'HOT-\u91d1\u6642 Std U',
    'HOT-\u91d1\u6642 Std U Regular',
    'KintokiStd-U',
    'HOT-Kasumi Std U',
    'HOT-Kasumi Std U Regular',
    'HOT-\u82b1\u58a8 Std U',
    'HOT-\u82b1\u58a8 Std U Regular',
    'KasumiStd-U',
    'BukotsuStd-U',
    'HOT-Bukotsu Std U',
    'HOT-Bukotsu Std U Regular',
    'HOT-\u6b66\u9aa8 Std U',
    'HOT-\u6b66\u9aa8 Std U Regular',
    'HOT-Sosho Std R',
    'HOT-Sosho Std R Regular',
    'HOT-\u767d\u821f\u8349\u66f8 Std R',
    'HOT-\u767d\u821f\u8349\u66f8 Std R Regular',
    'SoshoStd-R',
    'FSoshoStd-B',
    'HOT-FSosho Std B',
    'HOT-FSosho Std B Regular',
    'HOT-\u767d\u821f\u592a\u8349\u66f8 Std B',
    'HOT-\u767d\u821f\u592a\u8349\u66f8 Std B Regular',
    'GFSoshoStd-E',
    'HOT-GFSosho Std E',
    'HOT-GFSosho Std E Regular',
    'HOT-\u767d\u821f\u6975\u592a\u8349\u66f8 Std E',
    'HOT-\u767d\u821f\u6975\u592a\u8349\u66f8 Std E Regular',
    'HOT-ReishoR Std R',
    'HOT-ReishoR Std R Regular',
    'HOT-\u767d\u821f\u96b7\u66f8R Std R',
    'HOT-\u767d\u821f\u96b7\u66f8R Std R Regular',
    'ReishoRStd-R',
    'FReishoRStd-B',
    'HOT-FReishoR Std B',
    'HOT-FReishoR Std B Regular',
    'HOT-\u767d\u821f\u592a\u96b7\u66f8R Std B',
    'HOT-\u767d\u821f\u592a\u96b7\u66f8R Std B Regular',
    'FReishoStd-B',
    'HOT-FReisho Std B',
    'HOT-FReisho Std B Regular',
    'HOT-\u767d\u821f\u592a\u96b7\u66f8 Std B',
    'HOT-\u767d\u821f\u592a\u96b7\u66f8 Std B Regular',
    'GFReishoStd-E',
    'HOT-GFReisho Std E',
    'HOT-GFReisho Std E Regular',
    'HOT-\u767d\u821f\u6975\u592a\u96b7\u66f8 Std E',
    'HOT-\u767d\u821f\u6975\u592a\u96b7\u66f8 Std E Regular',
    'HOT-Kointai Std R',
    'HOT-Kointai Std R Regular',
    'HOT-\u767d\u821f\u53e4\u5370\u4f53 Std R',
    'HOT-\u767d\u821f\u53e4\u5370\u4f53 Std R Regular',
    'KointaiStd-R',
    'FKointaiStd-B',
    'HOT-FKointai Std B',
    'HOT-FKointai Std B Regular',
    'HOT-\u767d\u821f\u592a\u53e4\u5370\u4f53 Std B',
    'HOT-\u767d\u821f\u592a\u53e4\u5370\u4f53 Std B Regular',
    'GokuhosoInsotaiStd-L',
    'HOT-GokuhosoInsotai Std L',
    'HOT-GokuhosoInsotai Std L Regular',
    'HOT-\u767d\u821f\u6975\u7d30\u5370\u76f8\u4f53 Std L',
    'HOT-\u767d\u821f\u6975\u7d30\u5370\u76f8\u4f53 Std L Regular',
    'HOT-Insotai Std R',
    'HOT-Insotai Std R Regular',
    'HOT-\u767d\u821f\u5370\u76f8\u4f53 Std R',
    'HOT-\u767d\u821f\u5370\u76f8\u4f53 Std R Regular',
    'InsotaiStd-R',
    'HOT-Ouka Std R',
    'HOT-Ouka Std R Regular',
    'HOT-\u685c\u82b1 Std R',
    'HOT-\u685c\u82b1 Std R Regular',
    'OukaStd-R',
    'HOT-Seigetsu Std R',
    'HOT-Seigetsu Std R Regular',
    'HOT-\u9759\u6708 Std R',
    'HOT-\u9759\u6708 Std R Regular',
    'SeigetsuStd-R',
    'HOT-Karakusa Std R',
    'HOT-Karakusa Std R Regular',
    'HOT-\u5510\u8349 Std R',
    'HOT-\u5510\u8349 Std R Regular',
    'KarakusaStd-R',
    'GeikaisuikoStd-R',
    'HOT-Geikaisuiko Std R',
    'HOT-Geikaisuiko Std R Regular',
    'HOT-\u9be8\u6d77\u9154\u4faf Std R',
    'HOT-\u9be8\u6d77\u9154\u4faf Std R Regular',
    'HOT-Tenshin Std R',
    'HOT-Tenshin Std R Regular',
    'HOT-\u5929\u771f Std R',
    'HOT-\u5929\u771f Std R Regular',
    'TenshinStd-R',
    'HOT-KyoMadoka Std R',
    'HOT-KyoMadoka Std R Regular',
    'HOT-\u4eac\u5186 Std R',
    'HOT-\u4eac\u5186 Std R Regular',
    'KyoMadokaStd-R',
    'HOT-Hakuu Std R',
    'HOT-Hakuu Std R Regular',
    'HOT-\u767d\u96e8 Std R',
    'HOT-\u767d\u96e8 Std R Regular',
    'HakuuStd-R',
    'HOT-Samurai Std R',
    'HOT-Samurai Std R Regular',
    'HOT-\u3055\u3080\u3089\u3044 Std R',
    'HOT-\u3055\u3080\u3089\u3044 Std R Regular',
    'SamuraiStd-R',
    'HOT-KyoMadokaKanalarge Std R',
    'HOT-KyoMadokaKanalarge Std R Regular',
    'HOT-\u4eac\u5186\u304b\u306a\u592a Std R',
    'HOT-\u4eac\u5186\u304b\u306a\u592a Std R Regular',
    'KyoMadokaKanalargeStd-R',
    'HOT-Ninja Std R',
    'HOT-Ninja Std R Regular',
    'HOT-\u5fcd\u8005 Std R',
    'HOT-\u5fcd\u8005 Std R Regular',
    'NinjaStd-R',
    'HOT-Shunpu Std R',
    'HOT-Shunpu Std R Regular',
    'HOT-\u96bc\u98a8 Std R',
    'HOT-\u96bc\u98a8 Std R Regular',
    'ShunpuStd-R',
    'HOT-Mamekichi Std R',
    'HOT-Mamekichi Std R Regular',
    'HOT-\u307e\u3081\u5409 Std R',
    'HOT-\u307e\u3081\u5409 Std R Regular',
    'MamekichiStd-R',
    'HOT-Mameraku Std R',
    'HOT-Mameraku Std R Regular',
    'HOT-\u307e\u3081\u697d Std R',
    'HOT-\u307e\u3081\u697d Std R Regular',
    'MamerakuStd-R',
    'HOT-Mamefuku Std R',
    'HOT-Mamefuku Std R Regular',
    'HOT-\u307e\u3081\u798f Std R',
    'HOT-\u307e\u3081\u798f Std R Regular',
    'MamefukuStd-R',
    'HOT-Konshin Std R',
    'HOT-Konshin Std R Regular',
    'HOT-\u9b42\u5fc3 Std R',
    'HOT-\u9b42\u5fc3 Std R Regular',
    'KonshinStd-R',
    'HOT-Kaisho Std R',
    'HOT-Kaisho Std R Regular',
    'HOT-\u767d\u821f\u6977\u66f8 Std R',
    'HOT-\u767d\u821f\u6977\u66f8 Std R Regular',
    'KaishoStd-R',
    'FKaishoStd-B',
    'HOT-FKaisho Std B',
    'HOT-FKaisho Std B Regular',
    'HOT-\u767d\u821f\u592a\u6977\u66f8 Std B',
    'HOT-\u767d\u821f\u592a\u6977\u66f8 Std B Regular',
    'GFKaishoStd-E',
    'HOT-GFKaisho Std E',
    'HOT-GFKaisho Std E Regular',
    'HOT-\u767d\u821f\u6975\u592a\u6977\u66f8 Std E',
    'HOT-\u767d\u821f\u6975\u592a\u6977\u66f8 Std E Regular',
    'GyoshoStd-R',
    'HOT-Gyosho Std R',
    'HOT-Gyosho Std R Regular',
    'HOT-\u767d\u821f\u884c\u66f8 Std R',
    'HOT-\u767d\u821f\u884c\u66f8 Std R Regular',
    'FGyoshoStd-B',
    'HOT-FGyosho Std B',
    'HOT-FGyosho Std B Regular',
    'HOT-\u767d\u821f\u592a\u884c\u66f8 Std B',
    'HOT-\u767d\u821f\u592a\u884c\u66f8 Std B Regular',
    'GFGyoshoStd-E',
    'HOT-GFGyosho Std E',
    'HOT-GFGyosho Std E Regular',
    'HOT-\u767d\u821f\u6975\u592a\u884c\u66f8 Std E',
    'HOT-\u767d\u821f\u6975\u592a\u884c\u66f8 Std E Regular',
    'Shin StdN 6K',
    'ShinStdN-6K',
    'ShinStdN-6K-83pv-RKSJ-H',
    '\u82af StdN 6K',
    'Shin StdN 24K',
    'ShinStdN-24K',
    'ShinStdN-24K-83pv-RKSJ-H',
    '\u82af StdN 24K',
    'Shin StdN 12K',
    'ShinStdN-12K',
    'ShinStdN-12K-83pv-RKSJ-H',
    '\u82af StdN 12K',
  ];
  FontPlusAccessor.server = ['webfont-pub.weblife.me', 'webfont-pub.weblife.me'];
  FontPlusAccessor.trial = 0;
  FontPlusAccessor.seq = 0;
  FontPlusAccessor.nolist = 0;
  FontPlusAccessor.delay = 0;
  FontPlusAccessor.timeout = 10;
  FontPlusAccessor.aa = 1;
  FontPlusAccessor.llt = 0;
  FontPlusAccessor.t = null;
  FontPlusAccessor.pm = null;
  FontPlusAccessor.cm = 300;
  FontPlusAccessor.exttype = '';
  FontPlusAccessor.ukey = 'gBaf4X~siMM=';
  FontPlusAccessor.rl = 0;
  FontPlusAccessor.lsv = '';
  FontPlusAccessor.bfurl = '://webfont-pub.weblife.me/fp-bf/';
  FontPlusAccessor.bfnms = { w: '3d4c80e6', o: '5a348afb', t: 'ed60cc0b', e: 'fb73ed3f' };
  FontPlusAccessor.init();

  return FontPlusAccessor;
})();
FONTPLUS = (function () {
  var a = FontPlus_97d4bf1d6ad5a1d3ee4828c3dea3b807,
    options = { selector: '*', complete: false, callbacks: {}, timeoutfunc: false, sync: true, size: false };
  var e = {
    config: function (b) {
      for (var c in b) {
        if (!b.hasOwnProperty(c)) continue;

        options[c] = b[c];
      }
    },
    reload: function (c) {
      a.reload(c);
    },
    attachCompleteEvent: function (c) {
      options.complete = c;
    },
    targetSelector: function (c) {
      options.selector = c;
    },
    load: function (d, b, c) {
      a.designate_load(d, b, c);
    },
    isloading: function () {
      return a.isloading();
    },
    setFonts: function (c) {
      a.setFonts(c);
    },
    ontimeout: function (c) {
      options.timeoutfunc = c;
    },
    async: function () {
      options.sync = false;
    },
    start: function () {
      a.reload(true);
    },
    size: function (c) {
      options.size = c;
    },
  };
  a.setting(options);

  return e;
})();
