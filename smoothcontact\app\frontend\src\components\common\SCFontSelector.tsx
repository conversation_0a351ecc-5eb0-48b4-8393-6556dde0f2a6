import { displayFontFamily } from '@/utils/helper';
import { Button, List, ListItem, Menu, MenuItem, Typography } from '@mui/material';
import { Box, Stack } from '@mui/system';
import { useState } from 'react';

export enum WebFontType {
  JAPAN_FONT = 'JAPAN_FONT',
  JAPAN_FREE_FONT = 'JAPAN_FREE_FONT',
  GOOGLE_FONT = 'GOOGLE_FONT',
  SHOPIFY_FONT = 'SHOPIFY_FONT',
}

const FontTypeName: Record<WebFontType, string> = {
  JAPAN_FONT: '日本語フォント',
  JAPAN_FREE_FONT: '日本語フリーフォント',
  GOOGLE_FONT: 'Googleフォント',
  SHOPIFY_FONT: 'Shopifyフォント',
};

export type WebFontItem = {
  fontFamily: string;
  fontName: string;
  type?: WebFontType;
};

type FontSelectProps = {
  name: string;
  fontName: string;
  fontFamily: string;
  source: Record<WebFontType, WebFontItem[]>;
  onFontChange: (font: WebFontItem) => void;
};

const SCFontSelector = ({ name, fontName, fontFamily, source, onFontChange }: FontSelectProps) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedGroup, setSelectedGroup] = useState<WebFontType | ''>('');
  const [selectedFont, setSelectedFont] = useState<WebFontItem>({ fontName, fontFamily });

  const handleOpenMenu = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
    setSelectedGroup('');
  };

  const handleGroupClick = (group: any) => {
    setSelectedGroup(group);
  };

  const handleFontSelect = (font: WebFontItem) => {
    handleCloseMenu();
    setSelectedFont(font);
    onFontChange(font);
  };

  return (
    <Box key={name}>
      <Button variant="contained" color="primary" fullWidth onClick={handleOpenMenu} sx={{ textTransform: 'none' }}>
        <Typography style={{ fontFamily: selectedFont?.fontFamily ?? 'inherit' }}>{selectedFont?.fontName ?? 'Select Font'}</Typography>
      </Button>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'left' }}
      >
        {!selectedGroup && source && (
          <Box>
            {Object.keys?.(source)?.map?.((group) => (
              <MenuItem key={group} onClick={() => handleGroupClick(group)} style={{ fontWeight: 'bold' }}>
                {FontTypeName[group]}
              </MenuItem>
            ))}
          </Box>
        )}

        {selectedGroup && (
          <Stack>
            <List
              sx={{
                width: '100%',
                maxHeight: 200,
                overflowY: 'auto',
                padding: 0,
              }}
            >
              {source?.[selectedGroup]?.length > 0 ? (
                source?.[selectedGroup]?.map?.((font) => (
                  <ListItem
                    key={font?.fontFamily}
                    button
                    onClick={() => handleFontSelect(font)}
                    style={{ fontFamily: displayFontFamily(font?.fontFamily) }}
                  >
                    {font?.fontName}
                  </ListItem>
                ))
              ) : (
                <ListItem button sx={{ width: '100%' }}>
                  No font available
                </ListItem>
              )}
            </List>
          </Stack>
        )}
      </Menu>
    </Box>
  );
};

export default SCFontSelector;
