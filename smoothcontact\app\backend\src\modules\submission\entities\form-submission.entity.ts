import { Column, Entity } from 'typeorm';

import { BaseEntity } from '@/core/entity/base.entity';
import { FormBuilderEntity } from '@/modules/form-builder/entities/form-builder.entity';
import { FormControlNames } from '@/types/controlName.type';

export interface SubmissionFormValue {
  id?: string;
  controlName: FormControlNames;
  labelName: string;
  value: any;
  valueDetail?: any;
}

export enum SubmissionStatus {
  NOT_YET_SUPPORT = 0,
  WAITING_REPLY = 1,
  DEVELOPMENT_SUPPORT_REQUIRED = 2,
  COMPLETE = 3,
}

@Entity('form_submission')
export class FormSubmissionEntity extends BaseEntity {
  @Column({
    name: 'form_ext_id',
    type: 'uuid',
    length: 64,
    nullable: false,
  })
  formExtId: string;

  @Column({
    name: 'form_values',
    type: 'simple-json',
    nullable: true,
  })
  formValues: SubmissionFormValue[];

  @Column({
    name: 'status',
    type: 'tinyint',
    nullable: false,
  })
  status: SubmissionStatus;

  @Column({
    name: 'mode',
    type: 'varchar',
    length: 6,
    nullable: true,
  })
  mode: string;

  formBuilder: FormBuilderEntity;
}
