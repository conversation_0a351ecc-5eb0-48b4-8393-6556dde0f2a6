import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import * as path from 'path';

import { MailService } from './mail.service';

@Global()
@Module({
  imports: [
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => ({
        transports: {
          default: {
            host: config.get('SMTP_HOST'),
            port: config.get('SMTP_PORT'),
            secure: true, // true for 465, false for other ports
            auth: {
              user: config.get('SMTP_USER'),
              pass: config.get('SMTP_PASSWORD'),
            },
            from: config.get('SYSTEM_SENDER'),
          },
        },
        defaults: {
          from: config.get('SYSTEM_SENDER'),
        },
        template: {
          dir: path.resolve('./templates'),
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
    }),
  ],
  controllers: [],
  providers: [MailService, ConfigService],
  exports: [MailService],
})
export class MailModule {}
