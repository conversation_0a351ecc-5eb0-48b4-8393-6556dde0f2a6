import { Button, Typography } from '@mui/material';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import SCModal from './SCModal';

export interface IModalCommon {
  open?: boolean;
  title?: string;
  subtitle?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  loading?: boolean;
  width?: number;
  type?: 'success' | 'error' | 'warning' | 'info';
}

export type ModalCommonRef = {
  openModal: (newModalProps?: IModalCommon) => void;
};

// eslint-disable-next-line react/display-name
export const SCModalCommon = forwardRef<ModalCommonRef, React.PropsWithChildren<IModalCommon>>((props, ref) => {
  const [modalProps, setModalProps] = useState<IModalCommon>({ open: false });
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [loading, setLoading] = useState(false);

  const handleSetModalProps = (newModalProps: IModalCommon = {}) => {
    const newModalPropsTemp = { ...newModalProps };
    setModalProps((prevModalProps) => ({ ...prevModalProps, ...newModalPropsTemp }));
  };

  useImperativeHandle(ref, () => ({
    openModal: (newModalProps?: IModalCommon) => {
      handleSetModalProps(newModalProps);
    },
  }));

  useEffect(() => {
    if (!modalProps.open) {
      setLoading(false);
    }
  }, [modalProps.open]);

  useEffect(() => {
    setLoading(!!modalProps?.loading);
  }, [modalProps?.loading]);

  useEffect(() => {
    if (!props) {
      return;
    }

    handleSetModalProps(props);
  }, [props]);

  const onCancel = () => {
    typeof modalProps?.onCancel === 'function' && modalProps?.onCancel && modalProps?.onCancel();
    handleSetModalProps({ open: false });
  };

  const onOk = () => {
    typeof modalProps?.onConfirm === 'function' && modalProps?.onConfirm && modalProps?.onConfirm?.();
    handleSetModalProps({ open: false });
  };

  return (
    <>
      <SCModal
        title={modalProps?.title}
        width={modalProps?.width ?? 400}
        isOpen={modalProps?.open}
        onClose={onCancel}
        closeBtnLabel={modalProps?.cancelText}
        primaryAction={
          <Button color="primary" onClick={onOk}>
            {modalProps?.confirmText}
          </Button>
        }
      >
        <Typography variant="body1">{modalProps?.subtitle}</Typography>
      </SCModal>
      {props.children && <div onClick={() => handleSetModalProps({ open: true })}>{props.children}</div>}
    </>
  );
});
