import { COLOR_DEFAULT } from '@/common/constants';
import ResponsiveStack from '@/components/common/ResponsiveStack';
import AsteriskFillIcon from '@/components/icon/AsteriskFillIcon';
import { FormDescriptionSetting, FormGeneralSetting, FormLabelsSetting } from '@/types/FormTemplateTypes';
import { displayFontFamily } from '@/utils/helper';
import { Stack } from '@mui/material';
import Box from '@mui/material/Box';
import { Theme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { SxProps } from '@mui/system';
import React from 'react';
import { makeStyles } from 'tss-react/mui';

interface RenderItemWithLayoutProps {
  label: string;
  description?: string;
  isBottomDescription?: boolean;
  children: React.ReactNode;
  labelStyle?: SxProps<Theme>;
  isRequired?: boolean;
  generalSettings?: FormGeneralSetting;
  labelSettings?: FormLabelsSetting;
  descriptionSettings?: FormDescriptionSetting;
  layout?: 'vertical' | 'horizontal';
  showLabel?: boolean;
  hasCondition?: boolean;
}

const useVerticalStyles = makeStyles<{ descriptionSettings: FormDescriptionSetting; labelSettings: FormLabelsSetting }>()(
  (_, { descriptionSettings }) => ({
    inputDescription: {
      fontSize: `${descriptionSettings?.fontSize ?? '12'}${descriptionSettings?.fontSizeUnit ?? 'px'}`,
      color: descriptionSettings?.color ?? COLOR_DEFAULT,
      fontFamily: displayFontFamily(descriptionSettings?.fontFamily),
    },
    requiredMark: {
      color: '#c00',
      fontSize: '1em',
    },
  })
);

const RenderItemWithLayout: React.FC<RenderItemWithLayoutProps> = ({
  label,
  description,
  isBottomDescription,
  children,
  isRequired,
  labelSettings,
  descriptionSettings,
  layout,
  showLabel = true,
  hasCondition = false,
}) => {
  const { classes } = useVerticalStyles({ descriptionSettings, labelSettings });
  const labelStyle = {
    fontSize: `${labelSettings?.fontSize ?? '14'}${labelSettings?.fontSizeUnit ?? 'px'}`,
    color: labelSettings?.color ?? '#000',
    fontFamily: displayFontFamily(labelSettings?.fontFamily),
    wordBreak: 'break-all',
  };
  if (layout === 'horizontal') {
    return (
      <ResponsiveStack sx={{ paddingLeft: hasCondition ? 3 : 0 }} direction="row" mobileDirection="column" spacing={1}>
        <Box sx={{ width: '200px', flexShrink: 0 }}>
          <Typography sx={labelStyle} fontWeight="bold">
            {!!showLabel && label}
            {!!isRequired && <AsteriskFillIcon className={classes.requiredMark} />}
          </Typography>
        </Box>
        <Box className="input-container" flexGrow={1}>
          {!isBottomDescription && description && (
            <Box sx={{ mb: 1 }}>
              <div className={classes.inputDescription} dangerouslySetInnerHTML={{ __html: description }}></div>
            </Box>
          )}
          {children}
          {!!isBottomDescription && description && (
            <Box sx={{ mt: 1 }}>
              <div className={classes.inputDescription} dangerouslySetInnerHTML={{ __html: description }}></div>
            </Box>
          )}
        </Box>
      </ResponsiveStack>
    );
  }

  return (
    <Box sx={{ paddingLeft: hasCondition ? 3 : 0 }}>
      <Stack mb={1}>
        <Typography sx={labelStyle} fontWeight="bold">
          {!!showLabel && label}
          {!!isRequired && <AsteriskFillIcon className={classes.requiredMark} />}
        </Typography>
        {!isBottomDescription && <div className={classes.inputDescription} dangerouslySetInnerHTML={{ __html: description }}></div>}
      </Stack>
      {children}
      <Box>
        <Stack mt={1}>
          {!!isBottomDescription && <div className={classes.inputDescription} dangerouslySetInnerHTML={{ __html: description }}></div>}
        </Stack>
      </Box>
    </Box>
  );
};

export default RenderItemWithLayout;
