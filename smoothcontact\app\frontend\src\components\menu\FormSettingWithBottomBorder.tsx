import React, { FC, memo } from 'react';
import { isArray } from 'lodash';
import SCChip from '../common/SCChip';
import { getStatusColor, getRealStatus } from '@/utils/helper';
import { ISO, formatDate } from '@/utils/dateTime';
import { FormStatus, FormStatusLabel, TemplateType } from '@/types/FormTemplateTypes';
import { Chip, Table, TableBody, TableCell, TableRow } from '@mui/material';
import { Link } from 'react-router-dom';
import styled from '@emotion/styled';

interface FormSettingWithBottomBorderProps {
  item: TemplateType;
}

interface FormSetting {
  name: string;
  value: string | string[];
}

const StyledLink = styled(Link)`
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
`;

const createFormSetting = (name: string, value: any) => ({ name, value });

const createFormSettingsArray = (item: TemplateType) => {
  return [
    createFormSetting('公開URL', item.status === FormStatus.PUBLISHED ? `${import.meta.env.VITE_APP_URL}/front/output/${item.extId}` : '非公開'),
    createFormSetting(
      '公開時間',
      item.status === FormStatus.PUBLISHED
        ? `${formatDate(item?.releaseStartDate, ISO.DATE_TIME_WITH_DOT)} ~ ${formatDate(item?.releaseEndDate, ISO.DATE_TIME_WITH_DOT)}`
        : '公開時間を追加'
    ),
    createFormSetting(
      'ステータス',
      getRealStatus(
        item?.status,
        item?.releaseStartDate,
        item?.releaseEndDate,
        Number(item?.submissionCount ?? 0) >= Number(item?.formScheduleSetting?.maximumNumberFormsReceived ?? 9999999999)
      )
    ),
    createFormSetting('フォームの最大受信数', (item.formScheduleSetting?.maximumNumberFormsReceived ?? 9999999999).toLocaleString()),
    createFormSetting('回答結果の受信メールアドレス', item?.formMailSetting?.emailAddress ?? []),
    createFormSetting('作成日', item?.createdAt ? formatDate(item.createdAt, ISO.DATE_TIME_WITH_DOT) : ''),
    createFormSetting('最終更新日', item?.updatedAt ? formatDate(item.updatedAt, ISO.DATE_TIME_WITH_DOT) : ''),
    createFormSetting('作成者', item?.creator ?? ''),
  ];
};

const renderSettingValue = (setting: FormSetting) => {
  const { name, value } = setting;

  const renderStatusSetting = () => {
    if (isArray(value)) {
      const realStatus = Number(value[0]);
      const statusText = (FormStatusLabel as { [key: number]: string })[realStatus];
      const statusColor = getStatusColor(realStatus);

      return <SCChip size="small" label={statusText} color={statusColor} sx={{ fontSize: '0.76rem' }} />;
    } else {
      const realStatus = Number(value);
      const statusText = (FormStatusLabel as { [key: number]: string })[realStatus];
      const statusColor = getStatusColor(realStatus);

      return <SCChip size="small" label={statusText} color={statusColor} sx={{ fontSize: '0.76rem' }} />;
    }
  };

  const renderEmailsSetting = () => {
    if (isArray(value)) {
      return value?.map?.((email) => <Chip key={email} size="small" label={email.trim()} sx={{ margin: '2px' }} />);
    } else {
      return value;
    }
  };

  const renderPublicUrlSetting = () => {
    if (value === '非公開') {
      return value;
    } else {
      const url = isArray(value) ? value[0] : value;

      return (
        <StyledLink to={url} style={{ textDecoration: 'none' }} target="_blank" rel="noopener noreferrer" color="secondary">
          {url}
        </StyledLink>
      );
    }
  };

  switch (name) {
    case 'ステータス':
      return renderStatusSetting();

    case '回答結果の受信メールアドレス':
      return renderEmailsSetting();

    case '公開URL':
      return renderPublicUrlSetting();

    default:
      return value;
  }
};

const FormSettingWithBottomBorder: FC<FormSettingWithBottomBorderProps> = memo(({ item }) => {
  const formSettingWithBottomBorder = createFormSettingsArray(item);

  return (
    <Table aria-label="form details with border" sx={{ ml: 2, mb: 4 }}>
      <TableBody>
        {formSettingWithBottomBorder?.map?.((setting) => (
          <TableRow key={setting.name}>
            <TableCell component="th" sx={{ maxWidth: '150px', pb: '10px' }}>
              {setting.name}
            </TableCell>
            <TableCell sx={{ pb: '10px' }}>{renderSettingValue(setting)}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
});

export default FormSettingWithBottomBorder;
