import React, { FC, useEffect, useState } from 'react';
import { Bar<PERSON><PERSON> } from '@mui/x-charts/BarChart';
import { axisClasses } from '@mui/x-charts/ChartsAxis';

interface ReportBarChartLastMonthProps {
  chartData: {
    [key: string]: number;
  };
}

interface DatasetEntry {
  date: string;
  submissionsTotal: number;
}

const chartSetting = {
  legend: {
    hidden: true,
  },
  series: [{ dataKey: 'submissionsTotal', label: '' }],
  height: 400,
  borderRadius: 10,
  sx: {
    [`.${axisClasses.left} .${axisClasses.label}`]: {
      transform: 'translate(-5px, 0)',
    },
  },
  yAxis: [
    {
      min: 0,
      tickMinStep: 1,
    },
  ],
};

const generateLastMonthData = (chartData: Record<string, number>): DatasetEntry[] => {
  const currData: DatasetEntry[] = [];

  // Get the current date
  const currentDate = new Date();

  // Calculate the date for 30 days ago
  const thirtyDaysAgo = new Date(currentDate);
  thirtyDaysAgo.setDate(currentDate.getDate() - 30);

  // Loop to generate data from 30 days ago up to today
  const currentDay = thirtyDaysAgo;
  while (currentDay <= currentDate) {
    const date = currentDay.toISOString().split('T')[0]; // Format as "YYYY-MM-DD"

    // Get submissionsTotal for the date from chartData, default to 0 if not available
    const submissionsTotal = chartData[date] || 0;
    // Push the entry to the dataset array
    currData.push({
      date,
      submissionsTotal,
    });

    // Move to the next day
    currentDay.setDate(currentDay.getDate() + 1);
  }

  return currData;
};

const ReportBarChartLastMonth: FC<ReportBarChartLastMonthProps> = ({ chartData }) => {
  // Initialize dataset state with last month data
  const [dataset, setDataset] = useState<DatasetEntry[]>([]);

  useEffect(() => {
    const data = generateLastMonthData(chartData);
    setDataset(data);
  }, [chartData]);

  return (
    <BarChart
      dataset={dataset as any}
      xAxis={[
        {
          scaleType: 'band',
          dataKey: 'date',
          valueFormatter: (value: any) => {
            const date = new Date(value);
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const dateFormat = `${month}-${day}`;

            return `${dateFormat}`;
          },
          categoryGapRatio: 0.4,
          colorMap: {
            type: 'piecewise',
            thresholds: [new Date(2024, 1, 1), new Date(2050, 1, 1)],
            colors: ['#24CBD4'],
          },
          tickLabelStyle: {
            angle: -45,
            textAnchor: 'end',
            fontSize: 12,
          },
          tickSize: 10,
        } as any,
      ]}
      series={[{ label: 'submissionsTotal' }] as any}
      {...chartSetting}
    />
  );
};

export default ReportBarChartLastMonth;
