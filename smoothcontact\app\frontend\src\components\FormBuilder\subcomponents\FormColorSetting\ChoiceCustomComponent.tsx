import SCColorPicker from '@/components/common/SCColorPicker';
import { Grid, Typography } from '@mui/material';
import { FC } from 'react';
import { CustomModeComponentProps } from './CustomModeComponent';

const ChoiceCustomComponent: FC<CustomModeComponentProps> = (props) => {
  const { form } = props;

  return (
    <>
      <Typography variant="body2">チェックボックスとラジオボタン</Typography>
      <Grid container alignItems="center">
        <Grid item xs={4} container justifyContent="flex-end">
          <Typography variant="body2">選択時の色</Typography>
        </Grid>
        <Grid item xs={6} sx={{ pl: 2 }}>
          <SCColorPicker name="choiceSettings.color" color={form?.values?.choiceSettings?.color} form={form} />
        </Grid>
      </Grid>
    </>
  );
};

export default ChoiceCustomComponent;
