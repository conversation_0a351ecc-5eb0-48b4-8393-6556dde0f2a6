import SCModal from '@/components/common/SCModal';
import { useFormHandler } from '@/hooks/useFormHandler';
import { ISO, isValidDate } from '@/utils/dateTime';
import { removeEmptyAttributes } from '@/utils/helper';
import { Autocomplete, Box, Button, FormControl, Stack, TextField, Typography } from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs from 'dayjs';
import * as Yup from 'yup';
import { FilterStatusLabels, FilterStatusOptions } from '../store/FormBuilderListProvider';

interface FilterFormModalProps {
  open: boolean;
  onSearch: (filter: any) => void;
  onClose: () => void;
  filter?: any;
}

const FilterFormBuilderModal = ({ open, onSearch, onClose, filter }: FilterFormModalProps) => {
  const validationSchema = Yup.object({
    from: Yup.date()
      .optional()
      .typeError('開始日時が無効です。')
      .test('is-valid', '開始日時が無効です。', function (value) {
        if (!value) return true;

        return isValidDate(value);
      }),
    to: Yup.date()
      .optional()
      .typeError('終了日時が無効です。')
      .test('is-valid', '終了日時が無効です。', function (value) {
        if (!value) return true;

        return isValidDate(value);
      })
      .test('is-later', '終了日が開始日より後になる', function (value) {
        if (!value) return true;

        const { from } = this.parent;

        if (!from || !value) return true;

        const start = new Date(from);
        const end = new Date(value);

        return end > start;
      }),
  });

  const form = useFormHandler<any>({
    initialValues: {
      status: filter?.status ?? [],
      from: filter?.from ?? '',
      to: filter?.to ?? '',
    },
    validationSchema,
    validateOnBlur: false,
    validateOnChange: true,
    onSubmit: () => {},
  });

  return (
    <SCModal
      key={`form-builder-filter-modal`}
      width={550}
      isOpen={open}
      onClose={() => {
        form?.setFieldValue('status', []);
        form?.setFieldValue('from', '');
        form?.setFieldValue('to', '');
      }}
      onIconClose={onClose}
      enableIconClose={true}
      title="フィルター"
      closeBtnLabel="クリア"
      primaryAction={
        <Button
          disabled={!form?.isValid || !form?.dirty}
          variant="contained"
          onClick={() => onSearch(removeEmptyAttributes(form.values))}
          sx={{ color: '#fff', boxShadow: 'none' }}
        >
          フィルターを適用
        </Button>
      }
    >
      <Stack component="form" onSubmit={form.handleSubmit} direction="column" spacing={2}>
        <Box>
          <FormControl fullWidth>
            <Autocomplete
              key={'form-builder-filter-status-autocomplete'}
              multiple
              options={[
                FilterStatusOptions.ALL,
                FilterStatusOptions.DRAFT,
                FilterStatusOptions.PREPUBLISHED,
                FilterStatusOptions.PUBLISHED,
                FilterStatusOptions.EXPIRED,
              ]}
              value={form?.values?.status}
              getOptionLabel={(option: FilterStatusOptions) => {
                return FilterStatusLabels[option];
              }}
              {...form?.register('status')}
              onChange={(_, value) => {
                form.setFieldValue('status', value);
              }}
              isOptionEqualToValue={(option: any, value: any) => {
                return option === value;
              }}
              renderInput={(params) => <TextField {...params} label="フォームのステータス" placeholder="フォームのステータス" />}
            />
          </FormControl>
        </Box>
        <Box>
          <Typography pb={1} variant="body2">
            表示期間
          </Typography>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Stack direction="row" justifyContent={'space-between'} alignItems={'baseline'} gap={2}>
              <DateTimePicker
                slotProps={{
                  textField: {
                    fullWidth: true,
                    placeholder: '公開開始日時',
                    variant: 'outlined',
                    error: !!form?.errors?.from,
                    helperText: form?.errors?.from ? String(form?.errors?.from) : '',
                  },
                }}
                timeSteps={{ minutes: 5 }}
                format={ISO.DATE_TIME}
                {...form?.register('from')}
                value={!form?.values?.from ? null : dayjs(form?.values?.from)}
                onChange={(value) => {
                  form.setFieldValue('from', isValidDate(value) ? value.utc().toISOString() : '');
                }}
                closeOnSelect={false}
              />
              —
              <DateTimePicker
                slotProps={{
                  textField: {
                    fullWidth: true,
                    placeholder: '公開終了日時',
                    variant: 'outlined',
                    error: !!form?.errors?.to,
                    helperText: form?.errors?.to ? String(form?.errors?.to) : '',
                  },
                }}
                timeSteps={{ minutes: 5 }}
                format={ISO.DATE_TIME}
                {...form?.register('to')}
                value={!form?.values?.to ? null : dayjs(form?.values?.to)}
                onChange={(value) => {
                  form.setFieldValue('to', isValidDate(value) ? value.utc().toISOString() : '');
                }}
                closeOnSelect={false}
              />
            </Stack>
          </LocalizationProvider>
        </Box>
      </Stack>
    </SCModal>
  );
};

export default FilterFormBuilderModal;
