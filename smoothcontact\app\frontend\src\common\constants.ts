export const PER_PAGE = 5;
export const HELP_URL = 'http://www.digitalstage.jp/go/smoothcontact/help.html';
export const JAPAN_POSTAL_CODE_LINK = 'https://www.post.japanpost.jp/zipcode/index.html';
export const FONT_DEFAULT: string = 'Noto Sans JP';
export const FONT_SIZE_DEFAULT: number = 12;
export const COLOR_DEFAULT: string = '#000000';
export enum FormBorderColors {
  DEFAULT = '#CCC',
  BLACK = '#000',
}
export const DOMAIN_REGEX = /^((?!-))(xn--)?[a-z0-9][a-z0-9-_]{0,61}[a-z0-9]{0,1}\.(xn--)?([a-z0-9-]{1,61}|[a-z0-9-]{1,30}\.[a-z]{2,})$/;
export const URL_REGEX = /^(https?:\/\/)?([\w-]+(\.[\w-]+)+)(:[0-9]{1,5})?(\/[^\s]*)?$/i;
export const SHOPIFY_FONTS: Record<string, string>[] = [
  { label: 'Noto Sans JP', value: 'Noto Sans JP' },
  { label: 'Noto Serif JP', value: 'Noto Serif JP' },
  { label: 'M PLUS 1', value: 'M PLUS 1' },
  { label: 'M PLUS Rounded 1c', value: 'M PLUS Rounded 1c' },
  { label: 'Sawarabi Mincho', value: 'Sawarabi Mincho' },
  { label: 'BIZ UDPMincho', value: 'BIZ UDPMincho' },
  { label: 'Dela Gothic One', value: 'Dela Gothic One' },
  { label: 'Hina Mincho', value: 'Hina Mincho' },
  { label: 'Kaisei Decol', value: 'Kaisei Decol' },
  { label: 'Kaisei Opti', value: 'Kaisei Opti' },
  { label: 'Zen Old Mincho', value: 'Zen Old Mincho' },
  { label: 'Zen Kurenaido', value: 'Zen Kurenaido' },
  { label: 'Zen Maru Gothic', value: 'Zen Maru Gothic' },
  { label: 'Zen Kaku Gothic New', value: 'Zen Kaku Gothic New' },
  { label: 'Shippori Mincho', value: 'Shippori Mincho' },
  { label: 'Murecho', value: 'Murecho' },
  { label: 'Mochiy Pop P One', value: 'Mochiy Pop P One' },
  { label: 'Kaisei Tokumin', value: 'Kaisei Tokumin' },
];

export enum Course {
  FREE = 1,
  PRO = 2,
  ENTERPRISE = 3,
}
