{"compilerOptions": {"module": "esnext", "allowJs": true, "lib": ["dom", "dom.iterable", "esnext"], "esModuleInterop": true, "target": "esnext", "noImplicitAny": true, "moduleResolution": "node", "sourceMap": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "outDir": "dist", "baseUrl": ".", "paths": {"react": ["./node_modules/@types/react"], "@/*": ["./src/*"]}, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "types": ["vite/client"]}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules/**", "dist/**"]}