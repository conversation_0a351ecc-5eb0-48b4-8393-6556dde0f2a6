import { MigrationInterface, QueryRunner } from 'typeorm';

export class ModifyExtIdFieldOnSubmissionTable1727061315078 implements MigrationInterface {
  TABLE_NAME: string = `${process.env.ENTITY_PREFIX || ''}form_submission`;

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE ${this.TABLE_NAME} MODIFY COLUMN form_ext_id varchar(64)`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE ${this.TABLE_NAME} MODIFY COLUMN form_ext_id varchar(6)`);
  }
}
