import { FC, useState } from 'react';
import SCModal from '@/components/common/SCModal';
import PrintIcon from '@mui/icons-material/Print';
import SCButton from '@/components/common/SCButton';
import DownloadIcon from '@mui/icons-material/Download';
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import { Box, Checkbox, FormControlLabel, Grid, Stack, Typography } from '@mui/material';

interface ReportHeaderProps {
  extId: string;
  formTitle: string;
  formSubmissionTotal: number;
  downloadCsv: ({ extId, isCombine }: { extId: string; isCombine: boolean }) => void;
  downloadStatictisCsv: ({ extId }: { extId: string }) => void;
  onCrossTabulationClick: () => void;
  onSwapClick: () => void;
  isDisabledSwitchButton: boolean;
}

const ReportHeader: FC<ReportHeaderProps> = ({
  extId,
  formTitle,
  formSubmissionTotal,
  downloadCsv,
  downloadStatictisCsv,
  onCrossTabulationClick,
  onSwapClick,
  isDisabledSwitchButton,
}) => {
  const [isCombineAllInput, setIsCombineAllInput] = useState(false);
  const [isDisplayConfirmModal, setIsDisplayConfirmModal] = useState(false);

  const handleDownloadCSV = () => {
    downloadCsv({ extId, isCombine: isCombineAllInput });

    setIsDisplayConfirmModal(false);
  };

  const handleDownloadStatictisCSV = () => {
    downloadStatictisCsv({ extId });

    setIsDisplayConfirmModal(false);
  };

  const handlePrint = () => {
    setIsDisplayConfirmModal(false);
    setTimeout(() => {
      window.print();
    }, 0);
  };

  const handleCSVDownload = () => {
    setIsDisplayConfirmModal(true);
  };

  return (
    <>
      <Stack direction="row" alignItems="center" sx={{ width: '100%', gap: 3 }}>
        <Box sx={{ display: 'flex', flex: 1, justifyContent: 'flex-start' }}>
          <Stack direction="row" alignItems="center" spacing={3}>
            <Typography variant="h6" sx={{ wordBreak: 'break-word' }}>
              レポート : {formTitle}
            </Typography>
            <Typography variant="h6" sx={{ whiteSpace: 'nowrap' }}>
              総回答{formSubmissionTotal}回
            </Typography>
          </Stack>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
          <SCButton className="btn-grey" variant="outlined" onClick={onSwapClick} disabled={isDisabledSwitchButton}>
            <SwapHorizIcon />
          </SCButton>
          <SCButton className="btn-grey" variant="outlined" onClick={onCrossTabulationClick}>
            クロス集計を設定
          </SCButton>
          <SCButton className="btn-grey" color="secondary" onClick={handleCSVDownload}>
            <DownloadIcon /> CSVをダウンロード
          </SCButton>
        </Box>
      </Stack>

      <SCModal
        title={'レポートをダウンロード'}
        width={600}
        isOpen={isDisplayConfirmModal}
        onClose={() => setIsDisplayConfirmModal((prev) => !prev)}
        closeBtnLabel={'閉じる'}
      >
        <Box sx={{ flexGrow: 1 }}>
          <Typography variant="body1" sx={{ py: 1 }}>
            レポートの出力形式を選択してください
          </Typography>
          <Typography variant="body1" sx={{ py: 1 }}>
            項目設定
          </Typography>
          <FormControlLabel
            control={<Checkbox value={true} checked={isCombineAllInput} onChange={() => setIsCombineAllInput((prev) => !prev)} />}
            label="入力項目を一つにまとめる"
            sx={{ pl: '5px' }}
          />
          <Typography variant="body2" color="text.secondary">
            チェックを入れると、名前や住所の項目を１つのセルにまとめます
          </Typography>
        </Box>
        <Grid container spacing={2} sx={{ pt: 3 }}>
          <Grid item>
            <SCButton variant="outlined" startIcon={<PrintIcon />} sx={{ color: '#000', fontSize: '14px' }} onClick={handlePrint}>
              印刷
            </SCButton>
          </Grid>
          <Grid item>
            <SCButton variant="contained" startIcon={<DownloadIcon />} sx={{ color: '#fff', fontSize: '14px' }} onClick={handleDownloadCSV}>
              CSV (UTF-8)形式でダウンロード
            </SCButton>
          </Grid>
          <Grid item>
            <SCButton variant="contained" sx={{ color: '#fff', fontSize: '14px' }} onClick={handleDownloadStatictisCSV}>
              集計結果出力
            </SCButton>
          </Grid>
        </Grid>
      </SCModal>
    </>
  );
};

export default ReportHeader;
