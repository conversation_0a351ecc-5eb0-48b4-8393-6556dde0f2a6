import { Controller, Get, Param, Request } from '@nestjs/common';

import { BaseController } from '@/core/controllers/api.controller';

import { AddressService } from './address.service';

@Controller('api/address')
export class AddressController extends BaseController {
  constructor(private readonly addressService: AddressService) {
    super();
  }

  @Get('/:postalCode')
  async getOne(@Request() request: Request, @Param('postalCode') postalCode: string) {
    const result = await this.addressService.getAddress(postalCode);

    return this.successResponse({ data: result });
  }
}
