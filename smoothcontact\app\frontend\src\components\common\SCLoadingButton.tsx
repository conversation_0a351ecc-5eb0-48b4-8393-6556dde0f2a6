import LoadingButton from '@mui/lab/LoadingButton';
import styled from '@mui/system/styled';

const SCLoadingButton = styled(LoadingButton)(({ theme }) => {
  return {
    '&:focus': {
      outline: 'none',
    },
    '&.btn-grey': {
      transition: `all 0.3s ease`,
      color: theme.palette.secondary.main,
      backgroundColor: theme.palette.grey[100],
      '&.MuiButton-outlined': {
        color: theme.palette.secondary.main,
        borderColor: theme.palette.grey[100],
        backgroundColor: theme.palette.common.white,
      },
      '&:hover': {
        opacity: 0.6,
      },
      '&:disabled': {
        color: theme.palette.grey[200],
        backgroundColor: theme.palette.grey[100],
      },
    },
    '&.btn-black': {
      transition: `all 0.3s ease`,
      color: theme.palette.common.white,
      backgroundColor: theme.palette.grey[900],
      '&.MuiButton-outlined': {
        color: theme.palette.common.white,
        borderColor: theme.palette.grey[100],
        backgroundColor: theme.palette.grey[900],
      },
      '&:hover': {
        opacity: 0.6,
      },
      '&:disabled': {
        color: theme.palette.grey[200],
        backgroundColor: theme.palette.grey[100],
      },
    },
  };
});

export default SCLoadingButton;
