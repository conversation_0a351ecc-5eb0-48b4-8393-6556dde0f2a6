import { useState } from 'react';
import { Chip, Stack, Typography, styled } from '@mui/material';
import { FC, createElement } from 'react';
import { SvgIconComponent, ImageSharp } from '@mui/icons-material';

type CardStyle = 'solid' | 'dashed';
interface SCCardProps {
  icon?: SvgIconComponent;
  image?: string;
  title: string;
  label?: string;
  cardStyle?: CardStyle;
  className?: string;
  onClick?: () => void;
}

const IconContainer = styled('div')(({ theme }) => {
  return {
    color: theme.palette.primary.main,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
  };
});

const DefaultIconContainer = styled(IconContainer)(({ theme }) => {
  return {
    color: theme.palette.grey[200],
    background: '#fff',
    borderRadius: '3px',
  };
});

const ImageContainer = styled('div')(() => {
  return {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    img: {
      width: '100%',
      height: '100%',
      objectFit: 'cover',
    },
  };
});

const Title = styled(Typography, {
  shouldForwardProp: (prop) => prop !== 'isHovered',
})<{ isHovered: boolean }>`
  cursor: pointer;
  color: ${(props: any) => (props.isHovered ? props.theme.palette.primary.main : props.theme.palette.secondary.main)};
  transition: color 0.3s ease;
`;

const CardContent = styled('div', {
  shouldForwardProp: (prop) => prop !== 'isHovered' && prop !== 'cardStyle',
})<{ isHovered: boolean; cardStyle: CardStyle }>`
  overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: border-color 0.3s ease;
  background: ${(props: any) => props.theme.palette.grey[50]};
  border-radius: 10px;
  border-width: 2px;
  border-style: ${(props: any) => props.cardStyle};
  border-color: ${(props: any) => (props.isHovered ? props.theme.palette.primary.main : props.theme.palette.grey[100])};
  // Maintain 16:9 aspect ratio
  ::after {
    content: '';
    display: block;
    padding-top: 56.25%; // 9 / 16 = 0.5625
  }
`;

const SCCard: FC<SCCardProps> = ({ icon, image, title, label, cardStyle, onClick, className }) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  const handleCardClick = () => {
    if (onClick) {
      onClick();
    }
  };

  const isDefault = !icon && !image;

  return (
    <div>
      <CardContent
        className={className ?? ''}
        isHovered={isHovered}
        cardStyle={cardStyle ?? 'solid'}
        onClick={handleCardClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {icon && <IconContainer>{createElement(icon, { style: { fontSize: 34 } })}</IconContainer>}
        {image && (
          <ImageContainer>
            <img src={image} alt={title} />
          </ImageContainer>
        )}
        {isDefault && (
          <DefaultIconContainer>
            <ImageSharp style={{ fontSize: 34 }} />
          </DefaultIconContainer>
        )}
      </CardContent>
      <Stack direction="row" spacing={2} justifyContent="center" alignItems="center" p={1}>
        <Title isHovered={isHovered} fontWeight="bold" onClick={handleCardClick} onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
          {title}
        </Title>
        {label && <Chip label={label} color="primary" sx={{ color: 'white' }} size="small" />}
      </Stack>
    </div>
  );
};

export default SCCard;
