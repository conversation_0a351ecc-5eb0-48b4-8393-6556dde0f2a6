import { ChangePasswordRequestDTO } from '@/modules/auth/changePassword/dto/request.dto';
import { BackupCodeVerifyRequestDTO, LoginRequestDTO, MfaVerifyRequestDTO } from '@/modules/auth/login/dto/request.dto';
import { RegisterRequestDto } from '@/modules/auth/register/dto/request.dto';
import { ResetPasswordRequestDTO } from '@/modules/auth/resetPassword/dto/request.dto';
import { AxiosRequestConfig } from 'axios';

export const shopifyLoginRequestConfig = (params: any): AxiosRequestConfig => ({
  url: `/api/account/shopify-login`,
  method: 'get',
  params,
});

export const loginRequestConfig = (data: LoginRequestDTO): AxiosRequestConfig => ({
  url: `/api/account/login`,
  method: 'post',
  data,
});

export const verifyMfaRequestConfig = (data: MfaVerifyRequestDTO): AxiosRequestConfig => ({
  url: `/api/account/verify-mfa`,
  method: 'post',
  data,
});

export const verifyBackupRequestConfig = (data: BackupCodeVerifyRequestDTO): AxiosRequestConfig => ({
  url: `/api/account/verify-backup-code`,
  method: 'post',
  data,
});

export const registerRequestConfig = (data: RegisterRequestDto): AxiosRequestConfig => ({
  url: `/api/account/register`,
  method: 'post',
  data,
});

export const getMeRequestConfig: AxiosRequestConfig = {
  url: `/api/account/me`,
  method: 'get',
};

export const renewToken: AxiosRequestConfig = {
  url: `/api/account/refresh-token`,
  method: 'put',
};

export const updateProfileRequestConfig = (id: any, data: any): AxiosRequestConfig => ({
  url: `/api/account/profile/${id}`,
  method: 'put',
  headers: {
    'Content-Type': 'multipart/form-data',
  },
  data,
});

export const completeTourRequestConfig = (id: number): AxiosRequestConfig => ({
  url: `/api/account/complete-tour/${id}`,
  method: 'put',
});

export const resetPasswordRequestConfig = (data: ResetPasswordRequestDTO): AxiosRequestConfig => ({
  url: `/api/account/reset-password`,
  method: 'post',
  data,
});

export const changePasswordRequestConfig = (data: ChangePasswordRequestDTO): AxiosRequestConfig => ({
  url: `/api/account/change-password`,
  method: 'post',
  data,
});
