import { HttpException as RootHttpException, HttpStatus } from '@nestjs/common';

import { MessageCode } from '@/common/constant';
import { assignDataToInstance } from '@/common/helper';

import { APIResponseBase } from './response.type';

export class HttpException<T> extends RootHttpException {
  public statusCode?: number;
  public message: string;
  public messageCode?: MessageCode;
  messageErrors?: Partial<Record<keyof T, string>>;
  public data?: T | null;

  constructor(dataResponse?: Omit<APIResponseBase<T>, 'success'>) {
    super(dataResponse?.messageCode?.toString?.() ?? '', HttpStatus.BAD_REQUEST);
    assignDataToInstance(dataResponse, this);
  }
}
