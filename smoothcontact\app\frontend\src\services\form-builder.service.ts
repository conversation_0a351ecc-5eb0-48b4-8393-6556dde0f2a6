import { FormBuilderFilterRequestDto } from '@/modules/formBuilder/list/dto/request.dto';
import { AxiosRequestConfig } from 'axios';

export const getAllRequestConfig = (params: FormBuilderFilterRequestDto): AxiosRequestConfig => ({
  url: `/api/form-builder`,
  method: 'get',
  params,
});

export const createEmptyFormRequestConfig = (data: any): AxiosRequestConfig => ({
  url: `/api/form-builder/create-empty`,
  method: 'post',
  data,
});

export const getRequestConfig = (extId: string): AxiosRequestConfig => ({
  url: `/api/form-builder/${extId}`,
  method: 'get',
});

export const updateFormRequestConfig = (data: any): AxiosRequestConfig => ({
  url: `/api/form-builder`,
  method: 'put',
  data,
});

export const duplicateFormBuilderRequestConfig = (extId: string): AxiosRequestConfig => ({
  url: `/api/form-builder/duplicate/${extId}`,
  method: 'post',
});

export const deleteFormBuilderRequestConfig = (extId: string): AxiosRequestConfig => ({
  url: `/api/form-builder/${extId}`,
  method: 'delete',
});

export const updateFormBuilderMemoRequestConfig = (data: any): AxiosRequestConfig => ({
  url: `/api/form-builder/memo`,
  method: 'put',
  data,
});
