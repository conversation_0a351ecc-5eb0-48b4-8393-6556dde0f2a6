import { Stack } from '@mui/material';
import TextField from '@mui/material/TextField';
import { FormikValues } from 'formik';
import { FC } from 'react';

interface AutoReplyEmailComponentProps {
  form: FormikValues;
}

const AutoReplyEmailComponent: FC<AutoReplyEmailComponentProps> = (props) => {
  const { form } = props;

  return (
    <Stack direction="column" spacing={2}>
      <TextField
        name="autoReplyEmailAddress"
        label="返信用メールアドレス"
        error={!!form?.errors?.autoReplyEmailAddress}
        helperText={form?.errors?.autoReplyEmailAddress}
        value={form?.values?.autoReplyEmailAddress ?? ''}
        {...form.register('autoReplyEmailAddress')}
      />
      <TextField
        name="autoReplySenderName"
        label={'送信者名'}
        error={!!form?.errors?.autoReplySenderName}
        helperText={form?.errors?.autoReplySenderName}
        value={form?.values?.autoReplySenderName ?? ''}
        {...form.register('autoReplySenderName')}
      />
      <TextField
        name="autoReplySubject"
        label={'件名'}
        error={!!form?.errors?.autoReplySubject}
        helperText={form?.errors?.autoReplySubject}
        value={form?.values?.autoReplySubject ?? ''}
        {...form.register('autoReplySubject')}
      />
      <TextField
        name="textMailBody"
        label="メール本文"
        multiline
        rows={8}
        error={!!form?.errors?.textMailBody}
        helperText={form?.errors?.textMailBody}
        value={form?.values?.textMailBody ?? ''}
        {...form.register('textMailBody')}
      />
    </Stack>
  );
};

export default AutoReplyEmailComponent;
