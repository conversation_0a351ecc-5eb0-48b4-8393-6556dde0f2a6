import { Course, HELP_URL } from '@/common/constants';
import { IModalCommon } from '@/components/common/SCModalCommon';
import { useAppSelector } from '@/store/hook';
import {
  FormButtonType,
  FormColorSetting,
  FormContainerType,
  FormElement,
  FormElementChildrenType,
  FormEmbedAppSetting,
  FormMailSetting,
  FormScheduleSetting,
  FormStatus,
  FormType,
  GeneralSetting,
  SideBarLabel,
  SideBarType,
  TemplateType,
} from '@/types/FormTemplateTypes';
import { FormControlTitle, FormItemTypes } from '@/utils/formBuilderUtils';
import { generateID, getQueryParam, hasChangeBetweenTwoObj, isInEnum, openInNewTab } from '@/utils/helper';
import React, { ReactNode, createContext, useContext, useEffect, useMemo, useState } from 'react';
import { FormScheduleSettingRequestDTO } from '../dto/request.dto';

interface FormBuilderProviderProps {
  children: ReactNode;
  template: TemplateType;
  save: (value?: any, message?: string) => any;
  loading: boolean;
  setOpenModal: (newModalCommon: IModalCommon) => void;
}

interface FormBuilderContextType {
  loading?: boolean;
  handleItemAdded: (item: FormElementChildrenType | FormContainerType, containerId: string, insertIndex: number, addByDraggable: boolean) => void;
  deleteControl: (controlId: string, containerId: string) => void;
  duplicateControl: (controlId: string, containerId: string) => void;
  selectControl: (item: FormElementChildrenType | FormContainerType | FormButtonType | null) => void;
  setSelectedParentControl: (control: FormElementChildrenType) => void;
  editContainerProperties: (item: FormContainerType) => void;
  editControlProperties: (item: FormElementChildrenType) => void;
  editButtonProperties: (item: FormButtonType) => void;
  editGeneralSetting: (generalSetting: GeneralSetting) => void;
  editColorSetting: (colorSetting: FormColorSetting) => void;
  editMailSetting: (mailSetting: FormMailSetting) => void;
  editScheduleSetting: (scheduleSetting: FormScheduleSetting) => void;
  editEmbedAppSetting: (embedAppSetting: FormEmbedAppSetting) => void;
  moveControl: (item: FormElementChildrenType, dragIndex: number, hoverIndex: number, containerId: string) => void;
  publishForm: (value: any, message?: string) => any;
  saveForm: (value?: any, message?: string) => any;
  setSelectedTemplate: (value?: any) => any;
  setSideBarType: (value?: any) => any;
  setError: (value?: any) => any;
  changeSettingType: (value?: any) => any;
  resetSelectedTemplate: (template?: any) => any;
  setSharingModal: (value: boolean) => void;
  setOpenModal: (newModalCommon: IModalCommon) => void;
  getSideBarTitle: (key?: SideBarType) => string;
  setSelectedSwitchContact: (value: any) => void;
  setSelectedControl: (value: any) => void;
  selectedTemplate: TemplateType | null;
  formElements: FormElement[];
  formColorSetting: FormColorSetting;
  formMailSetting: FormMailSetting;
  formEmbedAppSetting: FormEmbedAppSetting;
  formScheduleSetting: FormScheduleSetting;
  formGeneralSetting: GeneralSetting;
  selectedControl: FormElementChildrenType | FormContainerType | FormButtonType | null;
  selectedParentControl: any;
  sideBarType: SideBarType;
  error: boolean;
  isFormChanged: boolean;
  sharingModal: boolean;
  isHtmlForm: boolean;
  isFromBindUp?: boolean;
  selectedSwitchContact: any;
  hasSwitchContact: boolean;
}

const FormBuilderContext = createContext<FormBuilderContextType | undefined>(undefined);

const FormBuilderProvider: React.FC<FormBuilderProviderProps> = ({ children, template, save, loading, setOpenModal }) => {
  const [sideBarType, setSideBarType] = useState<SideBarType>(template?.mode !== FormType.HTML ? SideBarType.ELEMENT : SideBarType.MAIL);
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateType | null>(null);
  const [selectedControl, setSelectedControl] = useState<null | FormContainerType | FormElementChildrenType | FormButtonType>(null);
  const [selectedSwitchContact, setSelectedSwitchContact] = useState(null);
  const [selectedParentControl, setSelectedParentControl] = useState<FormElementChildrenType | null>(null);
  const [error, setError] = useState<boolean>(false);
  const [sharingModal, setSharingModal] = React.useState(false);
  const { profile } = useAppSelector((state) => ({
    profile: state.app.profile,
  }));

  useEffect(() => {
    setSelectedTemplate(template);
  }, [template]);

  const isFormChanged = useMemo(() => {
    return selectedTemplate && hasChangeBetweenTwoObj(template, selectedTemplate);
  }, [selectedTemplate]);

  const isHtmlForm = useMemo(() => {
    return selectedTemplate?.mode === FormType.HTML;
  }, [selectedTemplate]);

  const formElements = useMemo(() => {
    return selectedTemplate?.formElements;
  }, [selectedTemplate]);

  const formColorSetting = useMemo(() => {
    return selectedTemplate?.formColorSetting;
  }, [selectedTemplate]);

  const formMailSetting = useMemo(() => {
    return selectedTemplate?.formMailSetting;
  }, [selectedTemplate]);

  const formEmbedAppSetting = useMemo(() => {
    return selectedTemplate?.formEmbedAppSetting;
  }, [selectedTemplate]);

  const formScheduleSetting = useMemo(() => {
    return selectedTemplate?.formScheduleSetting;
  }, [selectedTemplate]);

  const formGeneralSetting = useMemo(() => {
    return selectedTemplate?.formGeneralSetting;
  }, [selectedTemplate]);

  const hasSwitchContact = useMemo(() => {
    return (
      selectedTemplate?.formElements?.[0]?.children?.filter?.((element) => {
        return element?.switchContacts?.filter?.((item) => item?.enabled === true)?.length;
      }).length > 0 && profile?.course === Course.ENTERPRISE
    );
  }, [selectedTemplate]);

  useEffect(() => {
    const currentSideBarType = getQueryParam('type') as SideBarType;

    if (currentSideBarType && isInEnum(SideBarType, currentSideBarType)) {
      setSideBarType(currentSideBarType);
    }
  }, []);

  const handleItemAdded = (
    item: FormElementChildrenType | FormContainerType,
    containerId: string,
    hoverIndex: number,
    addByDraggable: boolean = true
  ) => {
    if (item.itemType === FormItemTypes.CONTAINER) {
      const newState = selectedTemplate?.formElements?.slice();
      newState.push({
        container: { ...(item as FormContainerType), id: generateID() },
        children: [],
      });
      setSelectedTemplate((pre) => ({ ...pre, formElements: newState }));
    } else if (item.itemType === FormItemTypes.CONTROL) {
      const newState = selectedTemplate?.formElements?.slice();
      const formContainerId = newState.findIndex((f) => f.container.id === containerId);
      const formContainer = { ...newState[formContainerId] };
      const obj = { ...(item as FormElementChildrenType), id: generateID(), containerId: containerId };
      const childItem = item as FormElementChildrenType;
      const hoverItem = formContainer?.children?.[hoverIndex];

      // Update child items
      if (childItem.items) {
        obj.items = JSON.parse(JSON.stringify(childItem.items));
      }

      if (addByDraggable && hoverItem && hoverIndex < formContainer.children.length) {
        obj.parentId = hoverItem?.parentId;
        obj.level = hoverItem?.level ?? 0;
      }

      const newChildren = formContainer.children.slice();
      newChildren.splice(hoverIndex, 0, obj);
      formContainer.children = newChildren;
      newState[formContainerId] = formContainer;

      setSelectedTemplate((pre) => ({ ...pre, formElements: newState }));
      setSelectedControl(obj);
    }

    setSelectedSwitchContact(null);
  };

  const deleteControl = (controlId: string, containerId: string) => {
    const newState = selectedTemplate?.formElements?.slice();
    const formContainerId = newState.findIndex((f) => f.container.id === containerId);
    const formContainer = { ...newState[formContainerId] };
    const deletedItems = getNestedItemsByParentId(formContainer?.children, controlId);
    const deletedItemIds = deletedItems?.map((item) => item.id);
    const newChildren = formContainer?.children?.filter((cont) => !deletedItemIds.includes(cont.id));
    formContainer.children = newChildren;
    newState[formContainerId] = formContainer;

    if (selectedControl?.id === controlId) {
      setSelectedControl(null);
      setSelectedSwitchContact(null);
    }

    setSelectedTemplate((pre) => ({ ...pre, formElements: newState }));
  };

  const moveControl = (dragItem: FormElementChildrenType, dragIndex: number, hoverIndex: number, containerId: string) => {
    const newState = selectedTemplate?.formElements?.slice();
    const formContainerId = newState.findIndex((f) => f.container.id === containerId);
    const formContainer = { ...newState[formContainerId] };
    const newChildren = formContainer.children.slice();
    const cloneNewChildren = JSON.parse(JSON.stringify(newChildren));

    // Remove the dragItem and its children from their current position
    const dragChildItems = getNestedItemsByParentId(cloneNewChildren, dragItem.id);
    const removedItems = cloneNewChildren.splice(dragIndex, dragChildItems.length);
    const newHoverIndex = hoverIndex; // > dragIndex ? hoverIndex - dragChildItems.length : hoverIndex;
    cloneNewChildren.splice(newHoverIndex, 0, ...removedItems);

    const hoverItem = newChildren[newHoverIndex];
    const hoverChildItems = getNestedItemsByParentId(newChildren, hoverItem.id);
    const isMoveUp = dragIndex > hoverIndex;
    const hoverLevel = hoverItem?.level ?? 0;
    const firstRemovedItemLevel = removedItems[0]?.level ?? 0;
    let levelChange = 0;
    let updatedParentId = null;

    if (!isMoveUp) {
      levelChange = hoverChildItems.length > 1 ? hoverLevel - firstRemovedItemLevel + 1 : hoverLevel - firstRemovedItemLevel;
      updatedParentId = hoverItem.parentId ? hoverItem.parentId : hoverChildItems.length > 1 ? hoverItem.id : null;
    } else {
      levelChange = hoverLevel - firstRemovedItemLevel;
      updatedParentId = hoverItem.parentId ? hoverItem.parentId : null;
    }

    removedItems[0].parentId = updatedParentId;
    removedItems.forEach((item: any) => {
      item.level = (item?.level ?? 0) + levelChange;
    });

    newChildren.splice(dragIndex, dragChildItems.length);
    newChildren.splice(newHoverIndex, 0, ...removedItems);

    formContainer.children = newChildren;
    newState[formContainerId] = formContainer;

    setSelectedTemplate((pre) => ({ ...pre, formElements: newState }));
  };

  const getNestedItemsByParentId = (items: FormElementChildrenType[], parentId: string): FormElementChildrenType[] => {
    const filteredItems: FormElementChildrenType[] = [];

    if (!items) {
      return filteredItems;
    }

    for (const item of items) {
      if (item.id === parentId) {
        filteredItems.push(item);
      } else {
        if (item?.parentId === parentId) {
          const childrenToDelete = getNestedItemsByParentId(items, item.id);
          filteredItems.push(...childrenToDelete);
        }
      }
    }

    return filteredItems;
  };

  const duplicateControl = (controlId: string, containerId: string) => {
    const newState = selectedTemplate?.formElements.slice();
    const formContainerId = newState.findIndex((f) => f.container.id === containerId);
    const formContainer = { ...newState[formContainerId] };
    const currentChildList = formContainer.children.slice();
    const duplicatedIndex = currentChildList.findIndex((f) => f.id === controlId);
    const duplicateChild = { ...currentChildList[duplicatedIndex] } as FormElementChildrenType;
    const duplicateItems = getNestedItemsByParentId(formContainer.children, duplicateChild.id);

    for (let i = 0; i < duplicateItems.length; i++) {
      const newId = generateID();

      for (let index = i + 1; index < duplicateItems.length; index++) {
        if (duplicateItems[index].parentId === duplicateItems[i].id) {
          duplicateItems[index] = { ...duplicateItems[index], parentId: newId };
        }
      }

      duplicateItems[i] = { ...duplicateItems[i], id: newId };
    }

    currentChildList.splice(duplicatedIndex + duplicateItems.length, 0, ...duplicateItems);
    formContainer.children = currentChildList;
    newState[formContainerId] = formContainer;

    setSelectedTemplate((pre) => ({ ...pre, formElements: newState }));
    setSelectedControl(duplicateItems?.[0]);
  };

  const selectControl = (item: FormElementChildrenType | FormContainerType | FormButtonType | null) => {
    if (selectedControl && item?.id === selectedControl?.id) {
      return;
    }

    if (error) {
      setOpenModal?.({
        open: true,
        title: 'このページから移動しますか？',
        subtitle: '行った変更が保存されない可能性があります',
        confirmText: 'このページから移動する',
        cancelText: 'このページに留まる',
        onConfirm: () => {
          setSelectedTemplate((pre) => ({ ...pre, ...template }));
          setSelectedControl(item);
          setError(false);
        },
        type: 'warning',
      });

      return;
    }

    setSelectedControl(item);
    setSelectedSwitchContact(null);
  };

  const changeSettingType = (key: SideBarType) => {
    if (key === SideBarType.HELP) {
      if (profile?.oemgo) {
        const oemgoUrl = profile.oemgo.endsWith('/') ? profile.oemgo : `${profile.oemgo}/`;
        openInNewTab(`${oemgoUrl}smoothcontact/help.html`);

        return;
      }

      openInNewTab(HELP_URL);

      return;
    }

    if (key !== sideBarType) {
      if (isFormChanged || error) {
        setOpenModal?.({
          open: true,
          title: 'このページから移動しますか？',
          subtitle: '行った変更が保存されない可能性があります',
          confirmText: 'このページから移動する',
          cancelText: 'このページに留まる',
          onConfirm: () => {
            setSelectedTemplate((pre) => ({ ...pre, ...template }));
            setSideBarType(key);
            setSelectedControl(null);
            setError(false);
          },
          type: 'warning',
        });

        return;
      }

      setSelectedControl(null);
      setSelectedSwitchContact(null);
      setSideBarType(key);
    }
  };

  const editContainerProperties = (item: FormContainerType) => {
    const newState = selectedTemplate?.formElements.slice();
    const formContainerId = newState.findIndex((f) => f.container.id === item.id);
    const formContainer = { ...newState[formContainerId] };

    formContainer.container = item;
    newState[formContainerId] = formContainer;

    setSelectedTemplate((pre) => ({ ...pre, formElements: newState }));
  };

  const editControlProperties = (item: FormElementChildrenType) => {
    const newState = selectedTemplate?.formElements?.slice();
    const formContainerId = newState?.findIndex?.((f) => f.container.id === item.containerId);
    const formContainer = { ...newState[formContainerId] };
    const newChildren = formContainer?.children?.slice?.();
    newChildren[newChildren?.findIndex?.((child) => child.id === item.id)] = item;
    formContainer.children = newChildren;
    newState[formContainerId] = formContainer;

    setSelectedTemplate((pre) => ({ ...pre, formElements: newState }));
  };

  const editButtonProperties = (item: FormButtonType) => {
    const buttonSettings = { ...selectedTemplate?.formColorSetting?.buttonSettings, confirmText: item.confirmText, submitText: item.submitText };

    setSelectedTemplate((pre) => ({ ...pre, formColorSetting: { ...pre.formColorSetting, buttonSettings } }));
  };

  const editGeneralSetting = (formGeneralSetting: GeneralSetting) => {
    setSelectedTemplate((pre) => ({ ...pre, formGeneralSetting }));
  };

  const editColorSetting = (formColorSetting: FormColorSetting) => {
    setSelectedTemplate((pre) => ({ ...pre, formColorSetting }));
  };

  const editMailSetting = (formMailSetting: FormMailSetting) => {
    setSelectedTemplate((pre) => ({ ...pre, formMailSetting }));
  };

  const editScheduleSetting = (formScheduleSetting: FormScheduleSettingRequestDTO) => {
    setSelectedTemplate((pre) => ({
      ...pre,
      formScheduleSetting: {
        ...selectedTemplate?.formScheduleSetting,
        displayTextBeforePublicForm: formScheduleSetting?.displayTextBeforePublicForm ?? '',
        displayTextAfterPublicForm: formScheduleSetting?.displayTextAfterPublicForm ?? '',
        displayTextHiddenForm: formScheduleSetting?.displayTextHiddenForm ?? '',
        hideHiddenText: formScheduleSetting?.hideHiddenText ?? false,
        maximumNumberFormsReceived: formScheduleSetting?.maximumNumberFormsReceived ?? null,
      },
      releaseStartDate: formScheduleSetting.releaseStartDate,
      releaseEndDate: formScheduleSetting.releaseEndDate,
    }));
  };

  const editEmbedAppSetting = (formEmbedAppSetting: FormEmbedAppSetting) => {
    setSelectedTemplate((pre) => ({ ...pre, formEmbedAppSetting }));
  };

  const publishForm = async (update: any, message: string) => {
    const publishRequest = {
      ...selectedTemplate,
      ...update,
      status: FormStatus.PUBLISHED,
    };

    const result = await save(publishRequest, message);

    if (result) {
      setSelectedTemplate(publishRequest);

      return true;
    }

    return false;
  };

  const saveForm = async (update: any, message?: string) => {
    const saveRequest = {
      ...selectedTemplate,
      ...update,
    };

    const result = await save(saveRequest, message);

    if (result) {
      setSelectedTemplate(saveRequest);

      return true;
    }

    return false;
  };

  const resetSelectedTemplate = () => {
    setSelectedControl(null);
    setSelectedSwitchContact(null);
    setError(false);
    setSelectedTemplate((pre) => ({ ...pre, ...template }));
  };

  const getSideBarTitle = () => {
    if (sideBarType === SideBarType.ELEMENT && selectedControl) {
      return FormControlTitle[selectedControl?.controlName];
    }

    if (sideBarType === SideBarType.ELEMENT && !!selectedSwitchContact) {
      return '個別送信先指定';
    }

    return SideBarLabel[sideBarType];
  };

  const isFromBindUp: boolean = useMemo(() => {
    return !!(profile?.callbackUrl && profile?.setting);
  }, [profile]);

  return (
    <FormBuilderContext.Provider
      value={{
        handleItemAdded,
        deleteControl,
        duplicateControl,
        selectControl,
        setSelectedParentControl,
        editContainerProperties,
        editControlProperties,
        editButtonProperties,
        editGeneralSetting,
        editColorSetting,
        editMailSetting,
        editScheduleSetting,
        editEmbedAppSetting,
        moveControl,
        publishForm,
        saveForm,
        setSelectedTemplate,
        setSideBarType,
        changeSettingType,
        setError,
        resetSelectedTemplate,
        setOpenModal,
        setSharingModal,
        getSideBarTitle,
        setSelectedSwitchContact,
        setSelectedControl,
        selectedTemplate,
        formElements,
        formColorSetting,
        formMailSetting,
        formEmbedAppSetting,
        formScheduleSetting,
        formGeneralSetting,
        selectedControl,
        selectedParentControl,
        sideBarType,
        error,
        isFormChanged,
        loading,
        sharingModal,
        isHtmlForm,
        isFromBindUp,
        selectedSwitchContact,
        hasSwitchContact,
      }}
    >
      {children}
    </FormBuilderContext.Provider>
  );
};

function useFormBuilder() {
  const context = useContext(FormBuilderContext);
  if (!context) {
    throw new Error('useFormBuilder must be used within a FormBuilderProvider');
  }

  return context;
}

export { FormBuilderProvider, useFormBuilder };
