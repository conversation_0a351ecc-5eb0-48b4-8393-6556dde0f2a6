import { useFormik, FormikVal<PERSON>, FormikConfig, FormikHelpers } from 'formik';

type RegisterOption = {
  nameOfValueProps?: string;
  isNumber?: boolean;
  isShowConfirmChanged?: boolean;
  preventTruncate?: boolean;
};

export function useFormHandler<Values extends FormikValues>({
  initialValues,
  validateOnBlur,
  onSubmit,
  validationSchema,
  ...rest
}: FormikConfig<Values>) {
  const handleSubmit = (values: Values, formikHelpers: FormikHelpers<Values>) => {
    onSubmit(values, formikHelpers);
    formikHelpers.resetForm({ values });
  };

  const form = useFormik<Values>({
    initialValues: initialValues as any,
    validationSchema,
    onSubmit: handleSubmit,
    ...rest,
  });

  const handleOnChange = (name: string, isNumber?: boolean) => (e: any) => {
    if (e?.target) {
      if (e.target.value && isNumber) {
        form.setFieldValue(name, Number(e.target.value) || 0);

        return;
      }

      return form.handleChange(e);
    }

    if (isNumber) {
      e = Number(e) || e;
    }

    form.setFieldValue(name, e);
  };

  const handleOnblur = (name: string, preventTruncate?: boolean) => (e: any) => {
    e.preventDefault();
    if (!validateOnBlur) return;

    let value = e;
    if (e.target) {
      value = e.target.value;
    }

    if (typeof value === 'string' && value && !preventTruncate) {
      value = value.trim();
      form.setFieldValue(name, value, true);
    }
  };

  const setData = (data: { [key: string]: any }) => {
    Object.keys(data).forEach((key: any) => {
      const value = data[key];
      form.setFieldValue(key, value);
    });
  };

  const register = (name: string, option?: RegisterOption) => {
    return {
      name,
      onChange: handleOnChange(name, option?.isNumber),
      [option?.nameOfValueProps || 'value']: form.values[name],
      onBlur: handleOnblur(name, option?.preventTruncate),
    };
  };

  return { ...form, register, setData };
}
