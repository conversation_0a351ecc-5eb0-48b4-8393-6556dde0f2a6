# SmoothContact

#### Requirements

1. You must [download and install Node.js](https://nodejs.org/en/download/) if you don't already have it. Version >= 18.x is required

#### Local Development

1. Install YARN version 4.0.1

   ```shell
   yarn set version 4.0.1
   ```

   ```shell
   yarn install
   ```

2. Install all the packages

   ```shell
   yarn install-all
   ```

3. Config database: Create a `.env` file in `app/backend` folder, check the `.env.example` file for details

4. Start local server

   ```shell
   yarn dev
   ```

5. Run the database migration script:

   ```shell
   yarn migrate-db
   ```
