import { styled } from '@mui/material';
import Chip, { ChipProps } from '@mui/material/Chip';

const ChipStyled = styled((props: ChipProps) => {
  if (props.avatar) {
    return <Chip {...props} variant="outlined" />;
  }

  return (
    <Chip
      {...props}
      sx={{
        px: 1,
      }}
    />
  );
})(({ theme }) => ({
  // custom by props color
  '&.MuiChip-colorSuccess': {
    backgroundColor: '#69F0AE45',
    color: '#1B5E20',
  },
  '&.MuiChip-colorInfo': {
    backgroundColor: 'rgba(36, 203, 212, 0.12)',
    color: theme.palette.primary.main,
  },
  '&.MuiChip-colorWarning': {
    backgroundColor: '#EF6C001F',
    color: '#EF6C00',
  },
  '&.MuiChip-outlinedDefault': {
    backgroundColor: theme.palette.grey[100],
    borderColor: theme.palette.grey[200],
  },
  '.MuiAvatar-colorDefault': {
    backgroundColor: theme.palette.grey[200],
    color: theme.palette.common.white,
  },
}));

export default ChipStyled;
