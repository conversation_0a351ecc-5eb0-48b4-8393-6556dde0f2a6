import { Controller, Get } from '@nestjs/common';

import { BaseController } from '@/core/controllers/api.controller';

import { WebFontService } from './web-font.service';

@Controller('api/web-font')
export class WebFontController extends BaseController {
  constructor(private readonly webFontService: WebFontService) {
    super();
  }

  @Get('')
  async getWebFonts() {
    const result = await this.webFontService.getWebFonts();

    return this.successResponse({ data: result });
  }
}
