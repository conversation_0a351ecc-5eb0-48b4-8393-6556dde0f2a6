import SCLoadingButton from '@/components/common/SCLoadingButton';
import { useFormHandler } from '@/hooks/useFormHandler';
import { checkFieldErrorHelper } from '@/utils/validate';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { Grid, Stack } from '@mui/material';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { Link } from 'react-router-dom';

export interface Props {
  formHandler: ReturnType<typeof useFormHandler>;
  loading?: boolean;
}

function ResetPassword({ formHandler, loading }: Props) {
  return (
    <Container component="main">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          border: '1px solid #e0e0e0',
          borderRadius: 2,
          padding: '20px',
          minWidth: '400px',
        }}
      >
        <Typography component="h1" variant="h5">
          パスワードを再設定
        </Typography>
        <Typography sx={{ mt: 2 }}>
          パスワード再設定用URLを送信しますので、ご登録いただいてるメールアドレスを入力し「送信」ボタンをクリックしてください
        </Typography>
        <Box component="form" onSubmit={formHandler?.handleSubmit} noValidate sx={{ mt: 2, width: '100%' }}>
          <TextField
            fullWidth
            size="small"
            placeholder="<EMAIL>"
            name="email"
            autoFocus
            {...formHandler?.register('email')}
            value={formHandler?.values?.email}
            error={!!checkFieldErrorHelper(formHandler, 'email')}
            helperText={checkFieldErrorHelper(formHandler, 'email')}
            InputLabelProps={{ shrink: true }}
          />
          <SCLoadingButton
            disabled={!formHandler?.isValid || !formHandler?.dirty}
            loading={loading}
            className="btn-black"
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 2, mb: 2 }}
          >
            送信
          </SCLoadingButton>
          <Grid container>
            <Grid item>
              <Link style={{ textDecoration: 'none' }} to="login">
                <Stack direction={'row'} alignItems={'center'}>
                  <ArrowBackIcon />
                  <Typography>ログイン情報に戻る</Typography>
                </Stack>
              </Link>
            </Grid>
            <Grid item xs></Grid>
          </Grid>
        </Box>
      </Box>
    </Container>
  );
}

export default ResetPassword;
