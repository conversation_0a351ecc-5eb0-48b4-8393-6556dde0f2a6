import FormElementTitle from '@/components/FormBuilder/subcomponents/FormElementTitle';
import { FormColorSetting, FormElement } from '@/types/FormTemplateTypes';
import { displayFontFamily } from '@/utils/helper';
import { Box, Stack, Typography } from '@mui/material';
import Container from '@mui/system/Container/Container';
import { FC } from 'react';
import { makeStyles } from 'tss-react/mui';

interface FormDisplayMessageProps {
  formElements: FormElement[];
  colorSetting: FormColorSetting;
  message?: string;
}

const useStyles = makeStyles<{ colorSetting: any }>()((_theme, { colorSetting }) => ({
  title: {
    fontSize: `${colorSetting?.titleSettings?.fontSize}${colorSetting?.titleSettings?.fontSizeUnit}`,
    color: colorSetting?.titleSettings?.color,
    fontFamily: displayFontFamily(colorSetting?.titleSettings?.fontFamily),
  },
  general: {
    fontSize: `${colorSetting?.generalSettings?.fontSize}${colorSetting?.generalSettings?.fontSizeUnit}`,
    color: colorSetting?.generalSettings?.color,
    fontFamily: displayFontFamily(colorSetting?.generalSettings?.fontFamily),
  },
  label: {
    fontSize: `${colorSetting?.labelSettings?.fontSize}${colorSetting?.labelSettings?.fontSizeUnit}`,
    color: colorSetting?.labelSettings?.color,
    fontFamily: displayFontFamily(colorSetting?.labelSettings?.fontFamily),
  },
}));

const FormDisplayMessage: FC<FormDisplayMessageProps> = ({ formElements, colorSetting, message }) => {
  const formattedMessage = message?.replace(/\n/g, '<br>') ?? '';
  const formElement = formElements?.[0];
  const { classes } = useStyles({ colorSetting });

  return (
    <Container
      sx={{ width: '100%', maxWidth: '1200px', marginRight: 'auto', marginLeft: 'auto', paddingLeft: '0!important', paddingRight: '0!important' }}
    >
      <Stack spacing={2}>
        <Box>
          <FormElementTitle
            heading={formElement?.container?.heading}
            description={formElement?.container?.description}
            headingClass={classes?.title}
            descriptionClass={classes?.general}
            display={formElement?.container?.display}
            lineColor={colorSetting?.generalSettings?.borderColor}
          />
        </Box>
      </Stack>
      <Box textAlign="center" p={2}>
        <Typography
          variant="body2"
          sx={{ whiteSpace: 'pre-line', wordBreak: 'break-all' }}
          dangerouslySetInnerHTML={{
            __html: formattedMessage ?? '',
          }}
        ></Typography>
      </Box>
    </Container>
  );
};

export default FormDisplayMessage;
