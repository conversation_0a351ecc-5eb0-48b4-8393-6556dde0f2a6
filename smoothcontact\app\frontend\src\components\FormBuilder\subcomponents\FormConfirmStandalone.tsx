import React, { FC, useCallback, useState } from 'react';
import { FormColorSetting, FormElement, FormElementChildrenType, FormItemValue } from '@/types/FormTemplateTypes';
import { Box, CircularProgress, Container, FormGroup, Stack, Typography, useTheme } from '@mui/material';
import Divider from '@mui/material/Divider';
import RenderItem from '@/components/FormBuilder/subcomponents/FormConfirm/RenderItem';
import FormElementTitle from '@/components/FormBuilder/subcomponents/FormElementTitle';
import { FormControlNames } from '@/utils/formBuilderUtils';
import useFormStyles from '@/hooks/useFormStyle';

interface FormConfirmStandaloneProps {
  formElements: FormElement[];
  colorSetting: FormColorSetting;
  values?: Record<string, FormItemValue>;
  error?: string;
  submitting?: boolean;
  onSubmit?: () => void;
  onBack?: () => void;
}

const FORM_CONFIRM_NOT_ALLOW_CONTROL = [FormControlNames.COMMENT];

const FormConfirmStandalone: FC<FormConfirmStandaloneProps> = ({ colorSetting, formElements, values, submitting, error, onSubmit, onBack }) => {
  const theme = useTheme();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [componentIndex, _] = useState(0);
  const formElement = formElements?.[componentIndex] || null;
  const formElementChildrenFiltered = formElement?.children.filter((child) => !FORM_CONFIRM_NOT_ALLOW_CONTROL.includes(child.controlName)) || [];
  const { classes } = useFormStyles({ colorSetting });

  const handleGoBack = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    onBack && onBack();
  };

  const handleSubmit = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    onSubmit && onSubmit();
  };

  const isShowFormElement = useCallback(
    (element: FormElementChildrenType) => {
      if (!element.parentId || element.level === 0) return true;

      const parent = formElement?.children?.find((child) => child?.id?.toString() === element?.parentId?.toString());

      if (!parent) return true;

      switch (parent.controlName) {
        case FormControlNames.CHECKLIST:

        case FormControlNames.RADIO:

        case FormControlNames.DROPDOWN:
          const checklist: any = (Array.isArray(values[parent.id]) ? values[parent.id] : [values[parent.id]]) || [];

          return Array.isArray(element?.condition)
            ? element?.condition?.every?.((cond) => checklist.includes(cond))
            : checklist.includes(element?.condition);

        default:
          const value = values[parent.id] as string;

          return element.condition?.includes(value);
      }
    },
    [values]
  );

  return (
    <Container
      sx={{ width: '100%', maxWidth: '1200px', marginRight: 'auto', marginLeft: 'auto', paddingLeft: '0!important', paddingRight: '0!important' }}
    >
      <Stack spacing={2}>
        <Box>
          <FormElementTitle
            heading={formElement.container.heading}
            description={formElement.container.description}
            headingClass={classes.title}
            descriptionClass={classes.general}
            display={formElement.container.display}
            lineColor={colorSetting?.generalSettings?.borderColor}
          />
        </Box>
        <Stack direction="column" spacing={2}>
          {formElementChildrenFiltered?.map?.((child, index) => {
            if (!isShowFormElement(child)) return null;

            let defaultValue: any = '';
            switch (child.controlName) {
              case FormControlNames.CHECKLIST:
                defaultValue = [];
                break;

              case FormControlNames.ADDRESS:

              case FormControlNames.FILE_UPLOAD:
                defaultValue = null;
                break;
            }

            return (
              <React.Fragment key={child.id}>
                <FormGroup>
                  <Stack mb={0}>
                    <Typography fontWeight="bold" className={classes.label}>
                      {child.labelName}
                      {!!child.required && <span style={{ color: 'red' }}>*</span>}
                    </Typography>
                    <Box className={classes.general} sx={{ mt: 1 }}>
                      <RenderItem item={child} value={values?.[child.id] ?? defaultValue} />
                    </Box>
                  </Stack>
                </FormGroup>
                {index !== formElement.children.length - 1 && <Divider color={colorSetting?.generalSettings?.color} />}
              </React.Fragment>
            );
          })}
          {error && (
            <Box textAlign="center" color={theme.palette.error.main}>
              {error}
            </Box>
          )}
          <Stack spacing={2} direction="row">
            <Box flexGrow={1}>
              <button type="button" className={classes.button} onClick={handleGoBack}>
                戻る
              </button>
            </Box>
            <Box flexGrow={1}>
              <button type="submit" className={classes.button} onClick={handleSubmit} disabled={submitting}>
                {submitting && <CircularProgress size={25} disableShrink />}
                {colorSetting?.buttonSettings?.submitText || '送信'}
              </button>
            </Box>
          </Stack>
        </Stack>
      </Stack>
    </Container>
  );
};

export default FormConfirmStandalone;
