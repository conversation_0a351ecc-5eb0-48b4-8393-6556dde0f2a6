import React, { useCallback, useState } from 'react';
import { SketchPicker, ColorResult } from 'react-color';
import reactCSS from 'reactcss';

interface SCColorPickerProps {
  name: string;
  color: string;
  form: any;
}

const styles = reactCSS({
  default: {
    color: {
      width: '40px',
      height: '26px',
      borderRadius: '2px',
      backgroundColor: 'transparent',
    },
    swatch: {
      background: '#fff',
      borderRadius: '1px',
      boxShadow: '0 0 0 1px rgba(0,0,0,.1)',
      display: 'inline-block',
      cursor: 'pointer',
    },
    popover: {
      position: 'absolute',
      zIndex: '2',
      left: '15px',
    },
    cover: {
      position: 'fixed',
      top: '0px',
      right: '0px',
      bottom: '0px',
      left: '0px',
    },
  },
});

const SCColorPicker: React.FC<SCColorPickerProps> = React.memo(({ name, color, form }) => {
  const [displayColorPicker, setDisplayColorPicker] = useState(false);

  const toggleColorPicker = useCallback(() => {
    setDisplayColorPicker((prevState) => !prevState);
  }, []);

  const handleClose = useCallback(() => {
    setDisplayColorPicker(false);
  }, []);

  const handleOnchange = (color: ColorResult) => {
    form?.setFieldValue(name, color.hex);
  };

  const dynamicStyles = { ...styles, color: { ...styles.color, backgroundColor: color } };

  return (
    <div>
      <div style={dynamicStyles.swatch} onClick={toggleColorPicker}>
        <div style={dynamicStyles.color} />
      </div>
      {displayColorPicker && (
        <div style={{ position: 'absolute', zIndex: '2', left: '15px' }}>
          <div style={{ position: 'fixed', top: '0px', right: '0px', bottom: '0px', left: '0px' }} onClick={handleClose} />
          <SketchPicker disableAlpha={true} color={color} onChangeComplete={handleOnchange} />
        </div>
      )}
    </div>
  );
});

export default SCColorPicker;
