import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { HttpStatusCode } from 'axios';
import { cloneDeep } from 'lodash';
import { AppException } from 'src/common/exceptions/app.exception';
import { StringUtil } from 'src/utils/string.util';
import { Brackets, Repository } from 'typeorm';

import { RootService } from '@/core/services/root.service';
import { FormScheduleSetting, FormType } from '@/modules/form-builder/common/common';
import { UploadService } from '@/modules/upload/upload.service';
import { generateID } from '@/utils/helpers';

import { AccountService } from '../account/account.service';
import { AccountEntity } from '../account/entities/account.entity';
import { SubmissionService } from '../submission/submission.service';
import {
  CreateFormBuilderRequestDTO,
  FilterStatusOptions,
  GetFormBuilderRequestDTO,
  UpdateFormBuilderRequestDTO,
  UpdateMemoFormBuilderRequestDTO,
} from './dto/dto.request';
import { GetFormBuilderResponseDto } from './dto/dto.response';
import { FormBuilderEntity, PublishStatus } from './entities/form-builder.entity';

@Injectable()
export class FormBuilderService extends RootService {
  @InjectRepository(FormBuilderEntity)
  private readonly formBuilderRepository: Repository<FormBuilderEntity>;

  @InjectRepository(AccountEntity)
  public readonly accountRepository: Repository<AccountEntity>;

  constructor(
    private readonly uploadService: UploadService,
    private readonly accountService: AccountService,
    private readonly submissionService: SubmissionService,
  ) {
    super();
  }

  async getFormBuilder(userId: number, extId: string) {
    const queryBuilder = this.formBuilderRepository
      .createQueryBuilder('formBuilder')
      .select('formBuilder.id', 'id')
      .addSelect('formBuilder.ext_id', 'extId')
      .addSelect('formBuilder.name', 'name')
      .addSelect('formBuilder.status', 'status')
      .addSelect('formBuilder.release_start_date', 'releaseStartDate')
      .addSelect('formBuilder.release_end_date', 'releaseEndDate')
      .addSelect('formBuilder.created_at', 'createdAt')
      .addSelect('formBuilder.updated_at', 'updatedAt')
      .addSelect('formBuilder.form_elements', 'formElements')
      .addSelect('formBuilder.form_general_setting', 'formGeneralSetting')
      .addSelect('formBuilder.form_color_setting', 'formColorSetting')
      .addSelect('formBuilder.form_mail_setting', 'formMailSetting')
      .addSelect('formBuilder.form_schedule_setting', 'formScheduleSetting')
      .addSelect('formBuilder.form_embed_app_setting', 'formEmbedAppSetting')
      .addSelect('formBuilder.memo', 'memo')
      .addSelect('formBuilder.mode', 'mode')
      .addSelect((subQuery) => {
        return subQuery
          .select('COUNT(*)', 'submissionCount')
          .from('FormSubmissionEntity', 'formSubmission')
          .where('formSubmission.form_ext_id = formBuilder.ext_id');
      }, 'submissionCount')
      .where('formBuilder.extId = :extId', { extId: extId })
      .andWhere('formBuilder.createdBy = :createdBy', { createdBy: userId });

    const result = await queryBuilder.getRawOne();

    if (!result) {
      this.response({
        message: 'フォームが見つかりません',
        statusCode: HttpStatusCode.NotFound,
      });
    }

    return result;
  }

  async getFormBuilderByExtId(extId: string) {
    const queryBuilder = this.formBuilderRepository
      .createQueryBuilder('formBuilder')
      .select('formBuilder.id', 'id')
      .addSelect('formBuilder.ext_id', 'extId')
      .addSelect('formBuilder.name', 'name')
      .addSelect('formBuilder.status', 'status')
      .addSelect('formBuilder.release_start_date', 'releaseStartDate')
      .addSelect('formBuilder.release_end_date', 'releaseEndDate')
      .addSelect('formBuilder.created_at', 'createdAt')
      .addSelect('formBuilder.updated_at', 'updatedAt')
      .addSelect('formBuilder.form_elements', 'formElements')
      .addSelect('formBuilder.form_general_setting', 'formGeneralSetting')
      .addSelect('formBuilder.form_color_setting', 'formColorSetting')
      .addSelect('formBuilder.form_mail_setting', 'formMailSetting')
      .addSelect('formBuilder.form_schedule_setting', 'formScheduleSetting')
      .addSelect('formBuilder.form_embed_app_setting', 'formEmbedAppSetting')
      .addSelect('formBuilder.memo', 'memo')
      .addSelect('formBuilder.mode', 'mode')
      .addSelect((subQuery) => {
        return subQuery
          .select('COUNT(*)', 'submissionCount')
          .from('FormSubmissionEntity', 'formSubmission')
          .where('formSubmission.form_ext_id = formBuilder.ext_id');
      }, 'submissionCount')
      .where('formBuilder.extId = :extId', { extId: extId });

    const result: any = await queryBuilder.getRawMany();

    if (!result) {
      this.response({
        message: 'フォームが見つかりません',
      });
    }

    return result?.[0];
  }

  async getAvailableFormByExtId(extId: string, formType?: FormType): Promise<GetFormBuilderResponseDto> {
    const mode = formType ?? FormType.MANUAL;
    const queryBuilder = this.formBuilderRepository
      .createQueryBuilder('formBuilder')
      .select('formBuilder.id', 'id')
      .addSelect('formBuilder.ext_id', 'extId')
      .addSelect('formBuilder.name', 'name')
      .addSelect('formBuilder.status', 'status')
      .addSelect('formBuilder.release_start_date', 'releaseStartDate')
      .addSelect('formBuilder.release_end_date', 'releaseEndDate')
      .addSelect('formBuilder.created_at', 'createdAt')
      .addSelect('formBuilder.updated_at', 'updatedAt')
      .addSelect('formBuilder.form_elements', 'formElements')
      .addSelect('formBuilder.form_general_setting', 'formGeneralSetting')
      .addSelect('formBuilder.form_color_setting', 'formColorSetting')
      .addSelect('formBuilder.form_mail_setting', 'formMailSetting')
      .addSelect('formBuilder.form_schedule_setting', 'formScheduleSetting')
      .addSelect('formBuilder.form_embed_app_setting', 'formEmbedAppSetting')
      .addSelect('formBuilder.memo', 'memo')
      .addSelect('formBuilder.mode', 'mode')
      .addSelect((subQuery) => {
        return subQuery
          .select('COUNT(*)', 'submissionCount')
          .from('FormSubmissionEntity', 'formSubmission')
          .where('formSubmission.form_ext_id = formBuilder.ext_id');
      }, 'submissionCount')
      .where('formBuilder.extId = :extId AND (formBuilder.mode = :mode OR formBuilder.mode IS NULL)', { extId: extId, mode });

    const result: FormBuilderEntity = await queryBuilder.getRawOne();

    if (!result) {
      this.response({
        message: 'フォームが見つかりませんでした。フォームURLが誤っているか、フォームが非公開または削除された可能性があります。',
      });
    }

    if (result?.status === PublishStatus.DRAFT) {
      this.response({
        message: !result?.formScheduleSetting?.hideHiddenText ? result?.formScheduleSetting?.displayTextHiddenForm : '',
      });
    }

    return result;
  }

  async deleteFormBuilder({ extId, userId }: { extId: string; userId: number }) {
    const formBuilder = await this.formBuilderRepository.findOneBy({ createdBy: userId, extId });

    if (!formBuilder) {
      return this.response({ message: 'フォームが見つかりません' });
    }

    return this.formBuilderRepository.softDelete({ id: formBuilder.id });
  }

  // eslint-disable-next-line max-lines-per-function
  async getFormBuilders(
    userId: number,
    dto: GetFormBuilderRequestDTO,
    isFromBindUp: boolean = false,
  ): Promise<{
    data: FormBuilderEntity[];
    statistics: { draftCount: number; publishedCount: number; expiredCount: number };
    count: number;
  }> {
    const orderByOptions = {
      id: 'formBuilder.id',
      name: 'formBuilder.name',
      status: 'formBuilder.status',
      updateddate: 'formBuilder.updated_at',
      view: 'submissionCount',
    };
    const queryBuilder = this.formBuilderRepository
      .createQueryBuilder('formBuilder')
      .select('formBuilder.id', 'id')
      .addSelect('formBuilder.ext_id', 'extId')
      .addSelect('formBuilder.name', 'name')
      .addSelect('formBuilder.status', 'status')
      .addSelect('formBuilder.release_start_date', 'releaseStartDate')
      .addSelect('formBuilder.release_end_date', 'releaseEndDate')
      .addSelect('formBuilder.created_at', 'createdAt')
      .addSelect('formBuilder.updated_at', 'updatedAt')
      .addSelect('formBuilder.form_general_setting', 'formGeneralSetting')
      .addSelect('formBuilder.form_color_setting', 'formColorSetting')
      .addSelect('formBuilder.form_mail_setting', 'formMailSetting')
      .addSelect('formBuilder.form_schedule_setting', 'formScheduleSetting')
      .addSelect('formBuilder.form_embed_app_setting', 'formEmbedAppSetting')
      .addSelect('formBuilder.memo', 'memo')
      .addSelect('formBuilder.mode', 'mode')
      .addSelect((subQuery) => {
        return subQuery
          .select('COUNT(*)', 'submissionCount')
          .from('FormSubmissionEntity', 'formSubmission')
          .where('formSubmission.form_ext_id = formBuilder.ext_id');
      }, 'submissionCount')
      .addSelect((subQuery2) => {
        return subQuery2.select('name', 'creator').from('AccountEntity', 'account').where('formBuilder.createdBy = account.id');
      }, 'creator')
      .where('formBuilder.createdBy = :createdBy', { createdBy: userId })
      .take(dto.perPage)
      .skip(dto.getSkip());

    if (isFromBindUp) {
      queryBuilder.andWhere(
        new Brackets((subQuery) => {
          subQuery.where('formBuilder.mode IS NULL');
          subQuery.orWhere('formBuilder.mode = :mode', { mode: FormType.MANUAL });
        }),
      );
    }

    queryBuilder.orderBy(orderByOptions[dto.orderBy ?? 'updateddate'], dto.order);

    const getDraftQuery = (parentQuery) => {
      return parentQuery.andWhere('formBuilder.status = :draftStatus', { draftStatus: PublishStatus.DRAFT });
    };

    const getPublishedQuery = (parentQuery) => {
      return parentQuery.andWhere('formBuilder.status = :publishedStatus', { publishedStatus: PublishStatus.PUBLISHED }).andWhere(
        new Brackets((query) => {
          query.where(
            new Brackets((subQuery) => {
              subQuery.where('formBuilder.releaseStartDate <= NOW()');
              subQuery.orWhere('formBuilder.releaseStartDate IS NULL');
            }),
          );
          query.andWhere(
            new Brackets((subQuery) => {
              subQuery.where('formBuilder.releaseEndDate >= NOW()');
              subQuery.orWhere('formBuilder.releaseEndDate IS NULL');
            }),
          );
        }),
      );
    };

    const getPrePublishedQuery = (parentQuery) => {
      return parentQuery.andWhere('formBuilder.status = :status', { status: PublishStatus.PUBLISHED }).andWhere(
        new Brackets((query) => {
          query.where(
            new Brackets((subQuery) => {
              subQuery.where('formBuilder.releaseStartDate > NOW()');
            }),
          );
          query.andWhere(
            new Brackets((subQuery) => {
              subQuery.where('formBuilder.releaseEndDate >= formBuilder.releaseStartDate');
              subQuery.orWhere('formBuilder.releaseEndDate IS NULL');
            }),
          );
        }),
      );
    };

    const getExpiredQuery = (parentQuery) => {
      return parentQuery.andWhere(
        new Brackets((query) => {
          query.where(
            new Brackets((subQuery) => {
              subQuery.where('formBuilder.status = :expireStatus', { expireStatus: PublishStatus.EXPIRED });
            }),
          );
          query.orWhere(
            new Brackets((subQuery) => {
              subQuery.where('formBuilder.status = :publishStatus', { publishStatus: PublishStatus.PUBLISHED });
              subQuery.andWhere('formBuilder.releaseEndDate < NOW()');
            }),
          );
        }),
      );
    };

    if (dto.status.length > 0) {
      queryBuilder.andWhere(
        new Brackets((query) => {
          dto.status.forEach((element) => {
            switch (element) {
              case FilterStatusOptions.DRAFT:
                query.orWhere(
                  new Brackets((query) => {
                    getDraftQuery(query);
                  }),
                );
                break;

              case FilterStatusOptions.PUBLISHED:
                query.orWhere(
                  new Brackets((query) => {
                    getPublishedQuery(query);
                  }),
                );
                break;

              case FilterStatusOptions.PREPUBLISHED:
                query.orWhere(
                  new Brackets((query) => {
                    getPrePublishedQuery(query);
                  }),
                );
                break;

              case FilterStatusOptions.EXPIRED:
                query.orWhere(
                  new Brackets((query) => {
                    getExpiredQuery(query);
                  }),
                );
                break;

              default:
            }
          });
        }),
      );
    }

    if (dto.from) {
      queryBuilder.andWhere(
        new Brackets((query) => {
          query.where('formBuilder.releaseStartDate <= :releaseStartDate', {
            releaseStartDate: dto.from,
          });
          query.orWhere('formBuilder.releaseStartDate IS NULL');
        }),
      );
    }

    if (dto.to) {
      queryBuilder.andWhere(
        new Brackets((query) => {
          query.where('formBuilder.releaseStartDate <= :releaseStartDate', {
            releaseStartDate: dto.to,
          });
          query.orWhere('formBuilder.releaseStartDate IS NULL');
        }),
      );
    }

    const result = await queryBuilder.getRawMany();
    const total = await queryBuilder.getCount();
    const statistics = await this.getStatisticsFormBuilder(userId, isFromBindUp);

    return { data: result, statistics, count: total };
  }

  async createEmpty({ userId, data }: { userId: number; data: CreateFormBuilderRequestDTO }): Promise<FormBuilderEntity> {
    const user = await this.accountService.getById(userId);

    if (!user) {
      return this.response({ message: 'アカウントが存在していません' });
    }

    const newData = { ...data, name: data.name ?? '新規作成フォーム', formMailSetting: { ...data.formMailSetting } };

    return await this.create(userId, {
      ...newData,
      status: PublishStatus.DRAFT,
    });
  }

  async duplicate({ extId, userId }: { extId: string; userId: number }) {
    const updateFormBuilder: FormBuilderEntity = await this.formBuilderRepository.findOneBy({
      createdBy: userId,
      extId,
    });

    if (!updateFormBuilder || updateFormBuilder?.deletedAt) {
      throw new AppException('フォームが見つかりません');
    }

    let formBuilder: FormBuilderEntity = new FormBuilderEntity();
    formBuilder.name = `${updateFormBuilder.name} [コピー]`;
    formBuilder.extId = StringUtil.getUid();
    formBuilder.createdBy = userId;
    formBuilder.status = updateFormBuilder.status;
    formBuilder.formElements = updateFormBuilder.formElements;
    formBuilder.formGeneralSetting = updateFormBuilder.formGeneralSetting;
    formBuilder.formColorSetting = updateFormBuilder.formColorSetting;
    formBuilder.formMailSetting = updateFormBuilder.formMailSetting;
    formBuilder.formScheduleSetting = updateFormBuilder.formScheduleSetting;
    formBuilder.formEmbedAppSetting = updateFormBuilder.formEmbedAppSetting;
    formBuilder.releaseStartDate = updateFormBuilder?.releaseStartDate;
    formBuilder.releaseEndDate = updateFormBuilder?.releaseEndDate;

    await this.formBuilderRepository.manager.transaction(async (manager) => {
      formBuilder = await manager.save(formBuilder);
    });

    return {
      id: formBuilder.id,
      extId: formBuilder.extId,
      name: formBuilder.name,
      status: formBuilder.status,
      releaseStartDate: formBuilder.releaseStartDate,
      releaseEndDate: formBuilder.releaseEndDate,
      formElements: formBuilder.formElements,
      formGeneralSetting: formBuilder.formGeneralSetting,
      formColorSetting: formBuilder.formColorSetting,
      formMailSetting: formBuilder.formMailSetting,
      formScheduleSetting: formBuilder.formScheduleSetting,
      formEmbedAppSetting: formBuilder.formEmbedAppSetting,
      memo: formBuilder.memo,
    };
  }

  async create(userId: number, data: CreateFormBuilderRequestDTO): Promise<FormBuilderEntity> {
    let formBuilder: FormBuilderEntity = new FormBuilderEntity();
    formBuilder.name = data.name;
    formBuilder.status = data.status;
    formBuilder.createdBy = userId;
    formBuilder.extId = StringUtil.getUid();
    formBuilder.formElements = data.formElements;
    formBuilder.formGeneralSetting = data.formGeneralSetting;
    formBuilder.formColorSetting = data.formColorSetting;
    formBuilder.formMailSetting = data.formMailSetting;
    formBuilder.formScheduleSetting = data.formScheduleSetting;
    formBuilder.formEmbedAppSetting = data.formEmbedAppSetting;
    formBuilder.mode = data.mode;

    formBuilder = await this.formBuilderRepository.save(formBuilder);

    return formBuilder;
  }

  async update({ userId, data }: { userId: number; data: UpdateFormBuilderRequestDTO }) {
    if (!data.id) {
      return this.response({ message: 'フォームが見つかりません' });
    }

    const formBuilder: FormBuilderEntity = await this.formBuilderRepository.findOneBy({
      createdBy: userId,
      id: data.id,
    });

    let updateFormBuilder: cloneDeep<FormBuilderEntity> = cloneDeep(formBuilder);

    if (!updateFormBuilder) {
      return this.response({ message: 'フォームが見つかりません' });
    }

    const elementChildren = data.formElements?.[0]?.children || [];
    const switchContactElements = elementChildren.filter((element) => {
      return element?.switchContacts?.filter?.((item) => item?.enabled === true)?.length;
    });

    updateFormBuilder.name = data.name;
    updateFormBuilder.status = data.status;
    updateFormBuilder.releaseStartDate = data?.releaseStartDate;
    updateFormBuilder.releaseEndDate = data?.releaseEndDate;
    updateFormBuilder.formElements = data.formElements;
    updateFormBuilder.formGeneralSetting = data.formGeneralSetting;
    updateFormBuilder.formColorSetting = data.formColorSetting;
    updateFormBuilder.formMailSetting = {
      ...data.formMailSetting,
      isAutoReply: data.mode !== formBuilder.mode || !!switchContactElements?.length ? false : data.formMailSetting.isAutoReply,
      receiveEmailField: data.mode !== formBuilder.mode ? '' : data.formMailSetting.receiveEmailField,
    };
    updateFormBuilder.formScheduleSetting = data.formScheduleSetting;
    updateFormBuilder.formEmbedAppSetting = data.formEmbedAppSetting;
    updateFormBuilder.mode = data.mode ?? FormType.MANUAL;

    await this.formBuilderRepository.manager.transaction(async (manager) => {
      if (formBuilder.formGeneralSetting.oGPImage !== data.formGeneralSetting.oGPImage) {
        await this.uploadService.confirmFile(data.formGeneralSetting.oGPImage);
        if (formBuilder.formGeneralSetting.oGPImage) {
          await this.uploadService.deleteFile(formBuilder.formGeneralSetting.oGPImage);
        }
      }

      updateFormBuilder = await manager.save(updateFormBuilder);
    });

    return {
      id: updateFormBuilder.id,
      extId: updateFormBuilder.extId,
      name: updateFormBuilder.name,
      status: updateFormBuilder.status,
      releaseStartDate: updateFormBuilder.releaseStartDate,
      releaseEndDate: updateFormBuilder.releaseEndDate,
      formElements: updateFormBuilder.formElements,
      formGeneralSetting: updateFormBuilder.formGeneralSetting,
      formColorSetting: updateFormBuilder.formColorSetting,
      formMailSetting: updateFormBuilder.formMailSetting,
      formScheduleSetting: updateFormBuilder.formScheduleSetting,
      formEmbedAppSetting: updateFormBuilder.formEmbedAppSetting,
      memo: updateFormBuilder.memo,
      updatedAt: updateFormBuilder.updatedAt,
      mode: updateFormBuilder.mode,
    };
  }

  async updateScheduleSetting(id: number, data: FormScheduleSetting) {
    let updateFormBuilder: FormBuilderEntity = await this.formBuilderRepository.findOneBy({
      id,
    });

    if (!updateFormBuilder) {
      throw new AppException('フォームが見つかりません');
    }

    updateFormBuilder.formScheduleSetting = data;

    await this.formBuilderRepository.manager.transaction(async (manager) => {
      updateFormBuilder = await manager.save(updateFormBuilder);
    });

    return {
      id: updateFormBuilder.id,
      extId: updateFormBuilder.extId,
      name: updateFormBuilder.name,
      status: updateFormBuilder.status,
      releaseStartDate: updateFormBuilder.releaseStartDate,
      releaseEndDate: updateFormBuilder.releaseEndDate,
      formElements: updateFormBuilder.formElements,
      formGeneralSetting: updateFormBuilder.formGeneralSetting,
      formColorSetting: updateFormBuilder.formColorSetting,
      formMailSetting: updateFormBuilder.formMailSetting,
      formScheduleSetting: updateFormBuilder.formScheduleSetting,
      formEmbedAppSetting: updateFormBuilder.formEmbedAppSetting,
      memo: updateFormBuilder.memo,
    };
  }

  async updateMemo({ userId, data }: { userId: number; data: UpdateMemoFormBuilderRequestDTO }) {
    if (!data.id) {
      throw new AppException('フォームが見つかりません');
    }

    const formBuilder: FormBuilderEntity = await this.formBuilderRepository.findOneBy({
      createdBy: userId,
      id: data.id,
    });

    let updateFormBuilder: cloneDeep<FormBuilderEntity> = cloneDeep(formBuilder);

    updateFormBuilder.memo = data.memo ?? '';

    await this.formBuilderRepository.manager.transaction(async (manager) => {
      updateFormBuilder = await manager.save(updateFormBuilder);
    });

    return {
      id: updateFormBuilder.id,
      extId: updateFormBuilder.extId,
      name: updateFormBuilder.name,
      status: updateFormBuilder.status,
      releaseStartDate: updateFormBuilder.releaseStartDate,
      releaseEndDate: updateFormBuilder.releaseEndDate,
      formElements: updateFormBuilder.formElements,
      formGeneralSetting: updateFormBuilder.formGeneralSetting,
      formColorSetting: updateFormBuilder.formColorSetting,
      formMailSetting: updateFormBuilder.formMailSetting,
      formScheduleSetting: updateFormBuilder.formScheduleSetting,
      formEmbedAppSetting: updateFormBuilder.formEmbedAppSetting,
      memo: updateFormBuilder.memo,
    };
  }

  async getStatisticsFormBuilder(userId: number, isFromBindUp: boolean = false) {
    const draftCount = await this.getDraftQuery(userId, isFromBindUp).getRawOne();
    const expiredCount = await this.getExpiredQuery(userId, isFromBindUp).getRawOne();
    const publishedCount = await this.getPublishedQuery(userId, isFromBindUp).getRawOne();
    const prePublishedCount = await this.getPrePublishedQuery(userId, isFromBindUp).getRawOne();

    return {
      draftCount: Number(draftCount?.count ?? 0),
      publishedCount: Number(publishedCount?.count ?? 0),
      expiredCount: Number(expiredCount?.count ?? 0),
      prePublishedCount: Number(prePublishedCount?.count ?? 0),
    };
  }

  async deleteByUserId(userId: number) {
    try {
      const formBuilders = await this.formBuilderRepository.findBy({ createdBy: userId });

      if (!formBuilders) {
        return;
      }

      formBuilders.forEach(async (formBuilder) => {
        await this.submissionService.deleteByFormExtId(formBuilder.extId);
        await this.formBuilderRepository.delete({ id: formBuilder.id });
      });

      return formBuilders.length;
    } catch (error) {
      return this.response({ message: error?.message });
    }
  }

  async getFormBuilderById(userId: number, id: number) {
    return await this.formBuilderRepository.findOneByOrFail({
      id,
      createdBy: userId,
    });
  }

  getContactTemplateForm(): any {
    return {
      name: 'お問い合わせフォーム',
      status: PublishStatus.PUBLISHED,
      publishHistory: [],
      mode: FormType.MANUAL,
      lastPublishedAt: new Date().getTime(),
      formElements: this.getFormElements(),
      formGeneralSetting: this.getFormGeneralSetting(),
      formColorSetting: this.getFormColorSetting(),
      formMailSetting: this.getFormMailSetting(),
      formScheduleSetting: this.getFormScheduleSetting(),
      formEmbedAppSetting: this.getFormEmbedAppSetting(),
    };
  }

  private getFormEmbedAppSetting() {
    return {
      isEnableGA4Setting: false,
      gA4TrackingID: '',
      isEnableGoogleAdsSetting: false,
      globalSiteTag: '',
      eventSnippet: '',
      isLinkageYahoo: false,
      conversionMeasurementTags: '',
    };
  }

  private getFormScheduleSetting() {
    return {
      displayTextBeforePublicForm: 'まもなく受付開始いたします。もうしばらくおまちください。',
      displayTextAfterPublicForm: '受付は終了しました。たくさんの投稿ありがとうございました。',
      displayTextHiddenForm: 'フォームが見つかりませんでした。フォームURLが誤っているか、フォームが非公開または削除された可能性があります。',
      hideHiddenText: false,
      maximumNumberFormsReceived: null,
    };
  }

  private getFormMailSetting() {
    return {
      screenAfterSendingType: 'display_message',
      emailAddress: [],
      message: 'フォームが正常に送信されました。',
      specifiedUrl: '',
      isAutoReply: false,
      autoReplyEmailAddress: '',
      autoReplySenderName: '',
      autoReplySubject: '',
      isTextMail: false,
      textMailBody: `お問い合わせいただきありがとうございました。
２〜３営業日以内に担当者よりご連絡させていただきます。
引き続きどうぞよろしくお願いします。`,
      isHtmlMail: false,
      isVerifyMailDomain: false,
      mailDomain: '',
      txtRecord: '',
      receiveEmailField: '',
      useCustomSMTP: false,
      smtpHost: '',
      smtpPort: 25,
      smtpUsername: '',
      smtpPassword: '',
      smtpFromEmail: '',
    };
  }

  private getFormColorSetting() {
    return {
      generalSettings: {
        spacing: 30,
        spacingUnit: 'px',
        fontSize: 12,
        fontSizeUnit: 'px',
        color: '#000',
        borderColor: '#CCC',
        fontFamily: 'Noto Sans JP',
      },
      animationSettings: {
        textEntryArea: 'no_animation',
        itemButton: 'no_animation',
      },
      buttonSettings: {
        text: '送信',
        submitText: '送信',
        confirmText: '確認',
        fontSize: 12,
        fontSizeUnit: 'px',
        borderRadius: 4,
        borderRadiusUnit: 'px',
        color: '#FFF',
        bgColor: '#1B367B',
        borderColor: '#000',
        fontFamily: 'Noto Sans JP',
      },
      entryFormSettings: {
        color: '#000',
        fontSize: 14,
        fontSizeUnit: 'px',
        borderRadius: 8,
        borderRadiusUnit: 'px',
        bgColor: '#FFF',
        borderColor: '#CCC',
      },
      labelSettings: {
        fontSize: 12,
        fontSizeUnit: 'px',
        color: '#000',
        fontFamily: 'Noto Sans JP',
      },
      descriptionSettings: {
        fontSize: 12,
        fontSizeUnit: 'px',
        color: '#000',
        fontFamily: 'Noto Sans JP',
      },
      choiceSettings: {
        color: '#333333',
      },
      layoutMode: 'vertical',
      bgColor: '#FFF',
      titleSettings: {
        fontSize: 16,
        fontSizeUnit: 'px',
        color: '#000',
        fontFamily: 'Noto Sans JP',
      },
      optionMode: 'custom_mode',
      templateModeColor: 'BASIC',
    };
  }

  private getFormGeneralSetting() {
    return {
      oGPImage: '',
      linkageDomain: '',
      isDisplaySearchEngine: true,
      isSettingReCAPTCHA: true,
      isSettingPrivacyPolicy: false,
      isDisplayTermsUse: false,
      isCombineIntoOneCheckbox: false,
      isDisplaySignUp: false,
      isDisplaySignUpSample: false,
      termsUse: '',
      policyLink: '',
      contactPerson: [],
      whitelistedDomain: [],
      captchaKey: '',
    };
  }

  private getFormElements() {
    return [
      {
        container: {
          id: generateID(),
          display: 'left',
          controlName: 'step-container',
          itemType: 'container',
          displayText: 'ワークフローステップ',
          heading: 'お問い合わせフォーム',
          description:
            'お問い合わせありがとうございます。 <br><br>ご返信までに３〜４営業日いただいております。<br> あらかじめご了承ください。<br>※ お客さまの入力情報はSSL暗号化通信により安全に送信されます。',
        },
        children: [
          {
            id: '',
            controlName: 'text-field',
            displayText: 'お名前',
            placeholder: 'お名前',
            description: '',
            labelName: 'お名前',
            itemType: 'control',
            dataType: 'text',
            icon: null,
            required: true,
            category: 'TEXT_ELEMENT',
            containerId: '',
            characterType: 'text',
            index: 0,
          },
          {
            id: '',
            controlName: 'email',
            displayText: 'テキスト',
            placeholder: 'メールアドレス',
            description: '',
            labelName: 'メールアドレス',
            itemType: 'control',
            dataType: 'text',
            icon: null,
            required: true,
            category: 'PERSONAL_ELEMENT',
            containerId: '',
            characterType: 'text',
            index: 1,
            requiredEmailConfirm: false,
          },
          {
            id: '',
            controlName: 'multiline-text-field',
            displayText: '段落テキスト',
            description: '',
            placeholder: 'お問い合わせ内容 ',
            labelName: 'お問い合わせ内容 ',
            rows: 4,
            itemType: 'control',
            icon: null,
            required: true,
            category: 'TEXT_ELEMENT',
            containerId: '',
            index: 2,
          },
        ],
      },
    ];
  }

  async deleteSCForm(scFormIds) {
    const deletePromises = scFormIds.map((scFormId) => this.formBuilderRepository.softDelete({ id: scFormId }));
    await Promise.all(deletePromises);
  }

  async getFormOwnerByExtId(extId: string) {
    const formBuilder = await this.formBuilderRepository.findOneBy({ extId });

    if (!formBuilder) {
      return null;
    }

    const formOwner = await this.accountRepository.findOneBy({ id: formBuilder.createdBy });

    return formOwner;
  }

  private getDraftQuery(userId: number, isFromBindUp: boolean = false) {
    const queryBuilder = this.formBuilderRepository
      .createQueryBuilder('formBuilder')
      .where('formBuilder.createdBy = :createdBy', { createdBy: userId })
      .select('COUNT(*)', 'count');

    if (isFromBindUp) {
      queryBuilder.andWhere(
        new Brackets((subQuery) => {
          subQuery.where('formBuilder.mode IS NULL');
          subQuery.orWhere('formBuilder.mode = :mode', { mode: FormType.MANUAL });
        }),
      );
    }

    return queryBuilder.andWhere('formBuilder.status = :draftStatus', { draftStatus: PublishStatus.DRAFT });
  }

  private getPublishedQuery(userId: number, isFromBindUp: boolean = false) {
    const queryBuilder = this.formBuilderRepository
      .createQueryBuilder('formBuilder')
      .where('formBuilder.createdBy = :createdBy', { createdBy: userId })
      .select('COUNT(*)', 'count');

    if (isFromBindUp) {
      queryBuilder.andWhere(
        new Brackets((subQuery) => {
          subQuery.where('formBuilder.mode IS NULL');
          subQuery.orWhere('formBuilder.mode = :mode', { mode: FormType.MANUAL });
        }),
      );
    }

    return queryBuilder.andWhere('formBuilder.status = :publishedStatus', { publishedStatus: PublishStatus.PUBLISHED }).andWhere(
      new Brackets((query) => {
        query.where(
          new Brackets((subQuery) => {
            subQuery.where('formBuilder.releaseStartDate <= NOW()');
            subQuery.orWhere('formBuilder.releaseStartDate IS NULL');
          }),
        );
        query.andWhere(
          new Brackets((subQuery) => {
            subQuery.where('formBuilder.releaseEndDate >= NOW()');
            subQuery.orWhere('formBuilder.releaseEndDate IS NULL');
          }),
        );
      }),
    );
  }

  private getPrePublishedQuery(userId: number, isFromBindUp: boolean = false) {
    const queryBuilder = this.formBuilderRepository
      .createQueryBuilder('formBuilder')
      .where('formBuilder.createdBy = :createdBy', { createdBy: userId })
      .select('COUNT(*)', 'count');

    if (isFromBindUp) {
      queryBuilder.andWhere(
        new Brackets((subQuery) => {
          subQuery.where('formBuilder.mode IS NULL');
          subQuery.orWhere('formBuilder.mode = :mode', { mode: FormType.MANUAL });
        }),
      );
    }

    return queryBuilder.andWhere('formBuilder.status = :status', { status: PublishStatus.PUBLISHED }).andWhere(
      new Brackets((query) => {
        query.where(
          new Brackets((subQuery) => {
            subQuery.where('formBuilder.releaseStartDate > NOW()');
          }),
        );
        query.andWhere(
          new Brackets((subQuery) => {
            subQuery.where('formBuilder.releaseEndDate >= formBuilder.releaseStartDate');
            subQuery.orWhere('formBuilder.releaseEndDate IS NULL');
          }),
        );
      }),
    );
  }

  private getExpiredQuery(userId: number, isFromBindUp: boolean = false) {
    const queryBuilder = this.formBuilderRepository
      .createQueryBuilder('formBuilder')
      .where('formBuilder.createdBy = :createdBy', { createdBy: userId })
      .select('COUNT(*)', 'count');

    if (isFromBindUp) {
      queryBuilder.andWhere(
        new Brackets((subQuery) => {
          subQuery.where('formBuilder.mode IS NULL');
          subQuery.orWhere('formBuilder.mode = :mode', { mode: FormType.MANUAL });
        }),
      );
    }

    return queryBuilder.andWhere(
      new Brackets((query) => {
        query.where(
          new Brackets((subQuery) => {
            subQuery.where('formBuilder.status = :expireStatus', { expireStatus: PublishStatus.EXPIRED });
          }),
        );
        query.orWhere(
          new Brackets((subQuery) => {
            subQuery.where('formBuilder.status = :publishStatus', { publishStatus: PublishStatus.PUBLISHED });
            subQuery.andWhere('formBuilder.releaseEndDate < NOW()');
          }),
        );
      }),
    );
  }
}
