import React from 'react';
import { styled } from '@mui/system';
import { alpha } from '@mui/material/styles';
import InputBase from '@mui/material/InputBase';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';

interface FocusInInputProps {
  name?: string;
  className?: string;
  type?: string;
  placeholder?: string;
  error?: boolean;
  helperText?: string;
  value?: any;
  onChange?: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement>;
  onBlur?: React.FocusEventHandler<HTMLTextAreaElement | HTMLInputElement>;
  multiline?: boolean;
  minRows?: number;
  label?: string;
}

const FocusInInput = styled(InputBase)(({ theme }) => ({
  '& .MuiInputBase-input': {
    borderRadius: 4,
    position: 'relative',
    border: '1px solid #3aaaff',
    padding: '12px 12px',
    '&:focus': {
      boxShadow: `${alpha(theme.palette.primary.main, 0.25)} 0 0 0 2px`,
      borderColor: theme.palette.primary.main,
    },
  },
}));

const FocusInInputTabComponent: React.FC<FocusInInputProps> = ({ className, error, helperText, multiline, minRows, value, ...inputProps }) => {
  return (
    <FormControl error={error} className={className} style={{ width: '100%' }}>
      <FocusInInput
        name={inputProps.name}
        type={inputProps.type ?? ''}
        placeholder={inputProps.placeholder}
        value={value}
        onChange={inputProps.onChange}
        onBlur={inputProps.onBlur}
        error={error}
        multiline={multiline}
        rows={minRows}
      />
      {error && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default FocusInInputTabComponent;
