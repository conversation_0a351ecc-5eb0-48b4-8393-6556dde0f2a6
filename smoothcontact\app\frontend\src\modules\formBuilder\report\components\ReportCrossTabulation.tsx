/* eslint-disable no-irregular-whitespace */
import React, { FC, useEffect } from 'react';
import ReportSelectionType from './ReportSelectionType';
import useLogic from '@/modules/formBuilder/report/useLogic';
import { Box, CircularProgress, Grid, Stack, Typography } from '@mui/material';

interface ReportCrossTabulationProps {
  targetTabulationSelection: {
    key: string;
    value: string;
  };
  crossTabulationSelection: {
    key: string;
    value: string;
  };
}

const ReportCrossTabulation: FC<ReportCrossTabulationProps> = ({ targetTabulationSelection, crossTabulationSelection }) => {
  const { loading, crossTabulationData, getCrossTabulationSelections } = useLogic();

  useEffect(() => {
    if (targetTabulationSelection.key && crossTabulationSelection.key) {
      getCrossTabulationSelections({
        targetTabulation: targetTabulationSelection.key,
        crossTabulation: crossTabulationSelection.key,
      });
    }
  }, [targetTabulationSelection.key, crossTabulationSelection.key]);

  if (loading) {
    return (
      <Box
        sx={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (!crossTabulationData || !Object.keys(crossTabulationData).length) {
    return null;
  }

  return (
    <>
      <Typography variant="h4" sx={{ textAlign: 'center', marginTop: '50px' }}>
        属性データ 「{targetTabulationSelection.value}」　x　「{crossTabulationSelection.value}」
      </Typography>

      <Stack sx={{ marginTop: '5px' }}>
        <Grid container rowSpacing={1} columnSpacing={{ xs: 1, sm: 2, md: 3 }}>
          {Object.entries(crossTabulationData).map(([crossValue, targetValues], index) => (
            <ReportSelectionType key={index} label={crossValue} statistics={targetValues as Record<string, number>} isFullWidth={false} />
          ))}
        </Grid>
      </Stack>
    </>
  );
};

export default ReportCrossTabulation;
