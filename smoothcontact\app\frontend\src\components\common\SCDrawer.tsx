import React, { FC } from 'react';
import Drawer from '@mui/material/Drawer';
import KeyboardTabIcon from '@mui/icons-material/KeyboardTab';
import { Box, Stack, useMediaQuery, useTheme } from '@mui/material';
import IconButton from '@/components/common/SCIconButton';

interface SCDrawerProps {
  position: 'left' | 'right';
  isOpen: boolean;
  onClose: () => void;
  width?: number;
  primaryAction?: React.ReactNode;
  secondaryAction?: React.ReactNode;
  children?: React.ReactNode;
}

const SCDrawer: FC<SCDrawerProps> = ({ position, isOpen, onClose, width = 500, primaryAction, secondaryAction, children }) => {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const drawerWidth = isSmallScreen ? '100%' : width;

  return (
    <React.Fragment>
      <Drawer
        anchor={position}
        open={isOpen}
        onClose={onClose}
        sx={{ width: drawerWidth, flexShrink: 0 }}
        PaperProps={{ style: { width: drawerWidth } }}
      >
        <Box width="100%" p={1}>
          <Box display="flex" justifyContent="space-between">
            <Stack direction="row" spacing={2}>
              {primaryAction}
            </Stack>
            <Stack direction="row" spacing={2}>
              {secondaryAction}
              <IconButton onClick={onClose}>
                <KeyboardTabIcon />
              </IconButton>
            </Stack>
          </Box>
          <Box p={2}>{children}</Box>
        </Box>
      </Drawer>
    </React.Fragment>
  );
};

export default SCDrawer;
