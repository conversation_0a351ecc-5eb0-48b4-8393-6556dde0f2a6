import { useFormBuilder } from '@/modules/formBuilder/edit/store/FormBuilderProvider';
import { FormElementChildrenType } from '@/types/FormTemplateTypes';
import { FormControlNames } from '@/utils/formBuilderUtils';
import { Box, Checkbox, FormControl, FormControlLabel, FormGroup, Radio, RadioGroup, Typography } from '@mui/material';
import { FormikValues } from 'formik';
import { FC, useMemo } from 'react';

interface ConditionPropertyProps {
  form: FormikValues;
}

const ConditionProperty: FC<ConditionPropertyProps> = (props) => {
  const { form } = props;
  const { selectedControl, formElements } = useFormBuilder();

  const parentItem = useMemo(() => {
    return formElements?.[0]?.children?.filter?.(
      (item) => item?.id?.toString() === form?.values?.parentId?.toString()
    )?.[0] as FormElementChildrenType;
  }, [form]);

  const renderItems = (items: any, type: any) => {
    return items?.map?.((item: { label: string; value: string }) => {
      const checkBoxChecked = Array.isArray(form?.values?.condition)
        ? form?.values?.condition?.includes(item?.value?.toString())
        : form?.values?.condition?.toString() === item?.value?.toString();

      return [FormControlNames.RADIO, FormControlNames.DROPDOWN].includes(type) ? (
        <FormControlLabel key={item.value} value={item.value} control={<Radio value={item.value} />} label={item.value} />
      ) : (
        <FormControlLabel
          key={item.value}
          control={<Checkbox name="condition" {...form?.register('condition')} value={item.value} checked={checkBoxChecked} />}
          label={item.value}
        />
      );
    });
  };

  if (!selectedControl) {
    return null;
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column' }} gap={2}>
      <Typography variant="body2">条件を設定</Typography>
      <FormControl>
        {[FormControlNames.RADIO, FormControlNames.DROPDOWN].includes(parentItem?.controlName) && (
          <RadioGroup name="condition" {...form.register('condition')} value={form?.values?.condition ?? 'none'} defaultValue={'none'} row>
            <FormControlLabel value="none" control={<Radio value="none" />} label="None" />
            {renderItems(parentItem.items, parentItem.controlName)}
          </RadioGroup>
        )}

        {parentItem?.controlName === FormControlNames.CHECKLIST && <FormGroup row>{renderItems(parentItem.items, parentItem.controlName)}</FormGroup>}
      </FormControl>
    </Box>
  );
};

export default ConditionProperty;
