import Joyride from 'react-joyride';
import { tourSteps } from '../store/FormBuilderListProvider';

type FormBuilderTourProps = {
  runTour: boolean;
  handleTourCallback: (data: any) => void;
};

export default function FormBuilderTour({ runTour, handleTourCallback }: FormBuilderTourProps) {
  return (
    <Joyride
      callback={handleTourCallback}
      run={runTour}
      steps={tourSteps}
      continuous={true}
      scrollToFirstStep={true}
      disableOverlay={true}
      styles={{
        beacon: {
          color: 'rgba(0,0,0,0.6)',
        },
        spotlight: {
          borderRadius: 50,
          width: '',
        },
        buttonNext: {
          fontSize: '13px',
          borderRadius: 4,
          backgroundColor: '#272937',
          fontWeight: 500,
          padding: '4px 10px',
          lineHeight: '1.5',
        },
        buttonBack: {
          fontSize: '13px',
          borderRadius: 4,
        },
        buttonClose: {
          width: '12px',
          height: '12px',
        },
        options: {
          beaconSize: 20,
          primaryColor: '#24cbd4',
          textColor: '#004a14',
          width: 300,
          zIndex: 1000,
        },
        tooltipContent: {
          fontSize: 12,
          padding: '15px 5px',
          color: '#272937',
        },
        tooltipContainer: {
          textAlign: 'left',
        },
        tooltip: {
          padding: '30px 10px 10px',
          maxWidth: 'calc(100vh/13.75rem)',
          maxHeight: 'calc(100vw/16.875rem)',
        },
      }}
      locale={{
        back: '戻る',
        next: '次へ',
        last: '完了',
      }}
    />
  );
}
